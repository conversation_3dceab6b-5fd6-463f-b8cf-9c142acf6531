spring.graphql.graphiql.enabled=true
spring.graphql.graphiql.path=/graphiql
server.port=7005

#
#spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
##spring.jpa.show-sql=true
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#### config database
spring.datasource.url=************************************************************************************
spring.datasource.username=dsonserver
spring.datasource.password=wO5icApi7O134uQInOJekutAw4F83O
#
##spring.jpa.hibernate.naming.physical-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
##spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy

asana.pat=2/*****************************************************************
asana.project=1205169798884764
asana.workspace=644193264866426
asana.section=1205169798884769
