extend type Query {
    tableNames: [Table]
    resHero(id: [Int]!): [ResHeroEntity]
}

extend type Mutation {
    syncWithId(tableName: String! id: [Int]!): String

    addResHeroMain(id: [Int]!): Boolean
    deleteResHeroMain(id: [Int]!): Boolean
}

type Table {
    name: String!
}

type ResHeroEntity {
    id: Int!
    clazz: String!
    name: String!
    type: String!
    star: Int!
    avatar: Int!
    village: Int!
    attackScaleOrigin: Float
    hpScaleOrigin: Float
    armorScaleOrigin: Float
    speedOrigin: Float
    skinId: String
    rank: Int!
    normal: Int
    active: Int
    passive1: Int
    passive2: Int
    passive3: Int
}