package grep.helper;

import grep.IConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ListUtil {

    public static <T> String joinElement(List<T> values) {
        return values.stream().map(value -> String.valueOf(value)).collect(Collectors.joining(","));
    }

    public static void moveIntElement(List<Integer> aSource, List<Integer> aTarget, int index) {
        aTarget.add(aSource.get(index));
        aSource.remove(index);
    }

    public static void moveElement(List<Long> aSource, List<Long> aTarget, int index) {
        aTarget.add(aSource.get(index));
        aSource.remove(index);
    }

    public static boolean hasDuplicateValue(List<Object> values) {
        for (int i = 0; i < values.size() - 1; i++) {
            for (int j = i + 1; j < values.size(); j++) {
                if (values.get(i).equals(values.get(j))) return true;
            }
        }
        return false;
    }

    public static List<Integer> converLstLongToInt(List<Long> lst) {
        List<Integer> retList = new ArrayList<>();
        lst.stream().forEach(ln -> retList.add(Math.toIntExact(ln)));
        return retList;
    }


    public static boolean hasDuplicateValueInteger(List<Integer> values) {
        for (int i = 0; i < values.size() - 1; i++) {
            for (int j = i + 1; j < values.size(); j++) {
                if (values.get(i).equals(values.get(j))) return true;
            }
        }
        return false;
    }


    public static List<Long> toMutableList(List<Long> aLong) {
        return aLong.stream().collect(Collectors.toList());
    }

    public static List<Long> arrToListLong(int[] values) {
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < values.length; i++) {
            ids.add((long) values[i]);
        }
        return ids;
    }

    public static List<Long> arrIntegerToListLong(Integer[] values) {
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < values.length; i++) {
            ids.add((long) values[i]);
        }
        return ids;
    }

    public static List<Long> listIntegerToListLong(List<Integer> values) {
        List<Long> ids = new ArrayList<>();
        for (Integer value : values) {
            ids.add(value.longValue());
        }
        return ids;
    }

    public static List<Long> arrIntToListLong(int[] values) {
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < values.length; i++) {
            ids.add((long) values[i]);
        }
        return ids;
    }

    public static List<Integer> arrIntegerToList(Integer[] values) {
        List<Integer> ids = new ArrayList<>();
        for (int i = 0; i < values.length; i++) {
            ids.add(values[i]);
        }
        return ids;
    }

    public static int countHeroInList(List<Long> ids, int numberTeam) {
        // remove pet
        List<Long> realHeroes = getListHeroes(ids, numberTeam);
        return realHeroes.stream().filter(hero -> hero > 0).collect(Collectors.toList()).size();
    }

    public static List<Long> getListHeroes(List<Long> ids, int numberTeam) {
        List<Long> idHero = new ArrayList<>();
        idHero.addAll(ids);
        for (int i = numberTeam; i > 0; i--) {
            idHero.remove(IConfig.TEAM_INPUT * i - 1);
        }
        return idHero;
    }

    public static String convertIdQuery(int idFrom, int idTo) {
        String s = "(";
        for (int i = idFrom; i <= idTo; i++) {
            s += i + ",";
        }
        s = s.substring(0, s.length() - 1);
        return s + ")";
    }
}
