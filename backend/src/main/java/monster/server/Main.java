package monster.server;

import grep.database.DBJPA;
import grep.helper.Filer;
import grep.log.Config;
import grep.log.Logs;
import grep.net.http.HttpSnoopServer;
import grep.timer.TimeCounter;
import monster.backdoor.HttpBackdoor;
import monster.cache.memcached.MCache;
import monster.cache.redis.JCache;
import monster.cache.redis.JCachePubSub;
import monster.config.CfgCluster;
import monster.config.CfgServer;
import monster.controller.AHandler;
import monster.dao.mapping.ConfigLocalEntity;
import monster.dao.mapping.main.ConfigEntity;
import monster.server.config.Guice;
import monster.service.common.SystemService;
import monster.service.monitor.*;
import monster.util.TimeDebug;
import org.jboss.netty.handler.ssl.SslContext;
import org.jboss.netty.handler.ssl.util.SelfSignedCertificate;

import java.lang.reflect.Method;
import java.util.*;

public class Main {

    static TimeDebug timeDebug = new TimeDebug("StartServer");

    public static void main(String[] args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+07:00"));

        init();
        startNetwork();
    }

    public static int index = 0;

    static void startNetwork() throws Exception {
        System.out.println("*********************************************");
        System.out.println("************ Start " + CfgServer.serverType + " Server **************");
        System.out.println("************ Port " + CfgServer.runningPort + " **************");
        System.out.println("*********************************************");

        if (CfgServer.serverType.equals("event")) {
            for (int i = 0; i < 15; i++) {
                System.out.println("i = " + i);
                new Thread(() -> new SuperEventMonitor(++index).doExpireTurn(1)).start();
            }
        } else {
            // Configure SSL.
            boolean SSL = false;
            final SslContext sslCtx;
            if (SSL) {
                SelfSignedCertificate ssc = new SelfSignedCertificate();
                sslCtx = SslContext.newServerContext(ssc.certificate(), ssc.privateKey());
            } else {
                sslCtx = null;
            }

            new HttpSnoopServer().run(CfgServer.runningPort, getMapController(), sslCtx);
        }
    }

    static void init() throws Exception {
        //        DOMConfigurator.configure("log4j.xml");

        //        AppConfig.load("config.json");
        //        Config.load("config.xml");
        //        CfgServer.serverId = Config.getInt("config.server.id");
        //        CfgServer.runningPort = Config.getInt("config.server.port");
        //        CfgServer.serverType = Config.getString("config.server.type");

        AppInit.initAll();

        if (!CfgServer.serverType.equals("event")) {
            CfgServer.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_server")).getV());
            CfgCluster.loadConfig(((ConfigLocalEntity) DBJPA.getUnique("config_local", ConfigLocalEntity.class, "k", "config_cluster")).getV());
            initTimer();
            initConfig();

            JCache.getInstance();
            JCachePubSub.getInstance().subscriberGameServer();
            MCache.getInstance();
            timeDebug.addCheckPoint("JMCache");
            AppInit.initGameTask();
            timeDebug.addCheckPoint("gametask");
            if (CfgServer.isRealServer() || CfgServer.isTestServer()) Telegram.sendNotify("Server start ^_^");
            if (AppConfig.cfg.backdoor > 0) HttpBackdoor.start();
            timeDebug.log();
            System.out.println("======> " + Guice.getInstance(SystemService.class).listServerOpen7Days());
        } else {
            TimeCounter.getInstance().start();
        }
    }

    static void initTimer() {
        TimeCounter.getInstance().start();
        //        if (CfgServer.serverType.equals("event")) EventMonitor.getInstance().addTimer();
        if (!CfgServer.isSimulateServer()) {
            Medal.getInstance().addTimer();
            TopMonitor.getInstance().addTimer();
            AvatarTimer.getInstance().doExpireTurn(-1);
            GameTask.getInstance().addTimer();
        }
        if (AppConfig.cfg.battleFake) {
            BattleFakeProcess.getInstance().addTimer();
        }
        timeDebug.addCheckPoint("initTimer");
    }

    public static void initConfig() throws Exception {
        List<ConfigLocalEntity> listLocalConfig = DBJPA.getList("config_local", ConfigLocalEntity.class);
        listLocalConfig.forEach(localConfig -> setConfig(localConfig.getK(), localConfig.getV()));
        timeDebug.addCheckPoint("localConfig");
        List<ConfigEntity> listConfig = DBJPA.getList(CfgServer.getCfgTable(), ConfigEntity.class);
        for (ConfigEntity config : listConfig) {
            boolean hasLocalConfig = false;
            for (ConfigLocalEntity localConfig : listLocalConfig) {
                if (localConfig.getK().equals(config.getK())) {
                    hasLocalConfig = true;
                    break;
                }
            }
            if (!hasLocalConfig) setConfig(config.getK(), config.getV());
        }
        timeDebug.addCheckPoint("mainConfig");

        loadResource();
        timeDebug.addCheckPoint("loadResource");
    }

    public static void setConfig(String key, String value) {
        try {
            if (key.startsWith("config")) {
                if (key.contains(":")) key = key.substring(key.indexOf(":") + 1);
                String cfg = key;
                cfg = cfg.substring(cfg.indexOf("_") + 1);
                cfg = cfg.substring(0, 1).toUpperCase() + cfg.substring(1);
                try {
                    Logs.info("--> Init " + "config.Cfg" + cfg);
                    Method m = Class.forName("monster.config.Cfg" + cfg).getDeclaredMethod("loadConfig", String.class);
                    m.invoke(null, value);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else if (key.startsWith("game")) {
                String cfg = key;
                cfg = cfg.substring(cfg.indexOf("_") + 1);
                cfg = cfg.substring(0, 1).toUpperCase() + cfg.substring(1);
                try {
                    Method m = Class.forName("monster.config.GameCfg" + cfg).getDeclaredMethod("loadConfig", String.class);
                    m.invoke(null, value);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else if (key.startsWith("api_server")) {
                //                ApiServer.loadConfig(value);
            } else if (key.contains(":event")) {
                int serverId = Integer.parseInt(key.substring(0, key.indexOf(":")));
                key = key.substring(key.indexOf(":") + 1);
                String cfg = key;
                cfg = cfg.substring(cfg.indexOf("_") + 1);
                cfg = cfg.substring(0, 1).toUpperCase() + cfg.substring(1);
                try {
                    Method m = Class.forName("config.Event" + cfg).getDeclaredMethod("loadConfig", int.class, String.class);
                    m.invoke(null, serverId, value);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else {
                Config.setString(key, value);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    static Map<Integer, AHandler> getMapController() throws Exception {
        Map<Integer, AHandler> mHandler = new HashMap<>();
        List<String> packages = Arrays.asList("monster.controller", "monster.game");
        for (String aPackage : packages) {
            Class[] allClasses = Filer.getClasses(aPackage, aPackage.contains("game") ? true : false);
            for (Class allClass : allClasses) {
                try {
                    String simpleName = allClass.getSimpleName();
                    if (!simpleName.equals("AHandler") && !simpleName.contains("$") && simpleName.contains("Handler")) {
                        //                        allClass.getDeclaredMethod("initAction", Map.class).invoke(allClass.newInstance(), mHandler);
                        allClass.getMethod("initAction", Map.class).invoke(allClass.getConstructors()[0].newInstance(), mHandler);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        return mHandler;
    }

    static void loadResource() throws Exception {
        Class[] allClasses = Filer.getClasses("monster.service.resource");
        for (Class allClass : allClasses) {
            if (allClass.getName().contains("$")) continue;
            try {
                allClass.getDeclaredMethod("init").invoke(null);
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
        timeDebug.addCheckPoint("resource");
        for (Class allClass : allClasses) {
            try {
                allClass.getDeclaredMethod("lazyInit").invoke(null);
                timeDebug.addCheckPoint("lazy" + allClass.getSimpleName());
            } catch (NoSuchMethodException ex) {
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
        timeDebug.addCheckPoint("lazyInit");
    }

    //    public static void initDB() {
    //        if (CfgServer.isTestServer()) {
    //            DBJPA.init(AppConfig.cfg.db.entity1);
    //            DBJPA.init(AppConfig.cfg.db.entity2);
    //            DBJPA.init("resource");
    //        } else if (CfgServer.isSimulateServer()) {
    //            DBJPA.init("grepjob1");
    //            DBJPA.init("grepjob2");
    //            DBJPA.init("resource");
    //        } else {
    //            if (AppConfig.cfg.db.entity1.contains("hikari")) {
    //                DBJPA.init2(AppConfig.cfg.db.entity1, AppConfig.cfg.db.mysqlUsername);
    //            } else DBJPA.init(AppConfig.cfg.db.entity1);
    //
    //            DBJPA.init(AppConfig.cfg.db.entity2);
    //            DBJPA.init("resource");
    //        }
    //    }
}
