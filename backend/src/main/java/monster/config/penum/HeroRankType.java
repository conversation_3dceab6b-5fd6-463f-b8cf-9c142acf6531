package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum HeroRankType {
    R(1),
    SR(2),
    SSR(3);

    public int value;

    HeroRankType(int value) {
        this.value = value;
    }

    // lookup
    static Map<Integer, HeroRankType> lookup = new HashMap<>();

    static {
        for (HeroRankType itemType : values()) {
            lookup.put(itemType.value, itemType);
        }
    }

    public static HeroRankType get(int type) {
        return lookup.get(type);
    }
}
