package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum AvatarConditionType {
    HAS_HERO(0),
    ACHIVE_LEVEL_CAMPAIGN(1),
    HAS_CHECKIN(2),
    SEND_HEART(3),
    MAX_POWER(4),
    HAS_VIP(5),
    SPEND_DIAMOND(6),
    USER_LEVEL(7),
    LEVEL_TƠWER1(8),
    LEVEL_TƠWER2(9),
    NUMBER_WIN_THACH_DAU(10),
    NUMBER_WIN_LIEN_DAU(11),
    NUMBER_QUEST_SSS(12),
    NUMBER_LUYEN_TAP_DEM(13),
    NUMBER_THU_THACH(14),
    ACHIVE_LEVEL_OCHIMARU(15);

    public int value;

    AvatarConditionType(int value) {
        this.value = value;
    }

    // lookup
    static Map<Integer, AvatarConditionType> lookup = new HashMap<>();

    static {
        for (AvatarConditionType itemType : values()) {
            lookup.put(itemType.value, itemType);
        }
    }

    public static AvatarConditionType get(int type) {
        return lookup.get(type);
    }
}
