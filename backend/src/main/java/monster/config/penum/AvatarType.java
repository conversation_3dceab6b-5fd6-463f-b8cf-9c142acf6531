  package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum AvatarType {
    HERO(0),
    SPECIAL(1),
    FRAME(2),
    HERO_SKIN(3),
    ;

    public int value;

    AvatarType(int value) {
        this.value = value;
    }

    // lookup
    static Map<Integer, AvatarType> lookup = new HashMap<>();

    static {
        for (AvatarType itemType : values()) {
            lookup.put(itemType.value, itemType);
        }
    }

    public static AvatarType get(int type) {
        return lookup.get(type);
    }
}