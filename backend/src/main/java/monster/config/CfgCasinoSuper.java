package monster.config;

import com.google.gson.Gson;
import lombok.Data;
import monster.config.penum.MaterialType;
import monster.service.user.Bonus;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class CfgCasinoSuper {
    public static int SHARD_ARTIFACT_ORANGE = 0;
    public static int SHARD_HERO_4STAR = 1;
    public static int SHARD_HERO_5STAR = 2;
    public static int SHARD_ARTIFACT_RED_EXCLUSIVE = 3;
    public static int GOLD = 4;
    public static int MONSTER_SOUL = 5;
    public static int PROPHET_ORB = 6;
    public static int HEROIC_SUMMON = 7;

    public static List<Integer> ENABLE1 = Arrays.asList(SHARD_HERO_5STAR, SHARD_ARTIFACT_ORANGE, SHARD_ARTIFACT_RED_EXCLUSIVE);

    public static JSONObject json;
    public static DataConfig config;

    public static protocol.Pbmethod.CommonVector.Builder protoRate() {
        protocol.Pbmethod.CommonVector.Builder builder = protocol.Pbmethod.CommonVector.newBuilder();
        for (int i = 0; i < config.showRate.length; i++) {
            builder.addAString(String.valueOf(config.showRate[i]));
        }
        return builder;
    }

    public static boolean isEnableOne(int index) {
        return ENABLE1.contains(index);
    }

    public static int getRandomIndex() {
        float random = new Random().nextFloat() * 100;
        for (int i = 0; i < config.casinoRate.length; i++) {
            if (random < config.casinoRate[i]) {
                return i;
            }
        }
        return 0;
    }

    public static JSONArray getCasinoBonusShow() {
        JSONArray arrBonus = new JSONArray();

        arrBonus.add(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_ORANGE, 5));
        arrBonus.add(Bonus.viewMaterial(new Random().nextInt(2) == 1 ? MaterialType.HERO_SHARD_4DARK : MaterialType.HERO_SHARD_4LIGHT, 30));
        arrBonus.add(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_HERO_SHARD, config.hero5Star.getEqualRandom(), 50));
        arrBonus.add(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_RED_EXCLUSIVE, 5));
        arrBonus.add(Bonus.viewGold(config.gold.getValue()));
        arrBonus.add(Bonus.viewMaterial(MaterialType.CHAOS_STONE, config.monsterSoul.getValue())); // arrBonus.add(Bonus.viewMaterial(MaterialType.MONSTER_SOUL, config.monsterSoul.getValue()));
        arrBonus.add(Bonus.viewMaterial(MaterialType.PROPHET_ORB, 1));
        arrBonus.add(Bonus.viewMaterial(MaterialType.HEROIC_SUMMON, 1));

        return arrBonus;
    }

    public static void loadConfig(String value) {
        json = JSONObject.fromObject(value);
        config = new Gson().fromJson(value, DataConfig.class);

        config.init();
        config.gold.init();
        config.hero5Star.init();
        config.monsterSoul.init();
    }

    @Data
    public class DataConfig {
        public int timeFreeRefresh;
        public float[] casinoRate;
        public float[] showRate;
        public int[] feeRefresh;
        public int[] feeRotate;
        public int priceChip;
        public SuperCasinoRate gold, hero5Star, monsterSoul;

        public void init() {
            showRate = new float[casinoRate.length];
            for (int i = 0; i < casinoRate.length; i++) {
                showRate[i] = casinoRate[i];
            }
            for (int i = 1; i < casinoRate.length; i++) {
                casinoRate[i] += casinoRate[i - 1];
            }
        }
    }

    public class SuperCasinoRate {
        float[] rate;
        int[] value;

        public void init() {
            for (int i = 1; i < rate.length; i++) {
                rate[i] += rate[i - 1];
            }
        }

        public int getValue() {
            float rand = new Random().nextFloat() * 100;
            for (int i = 0; i < rate.length; i++) {
                if (rand < rate[i]) return value[i];
            }
            return value[0];
        }

        public int getEqualRandom() {
            return value[new Random().nextInt(value.length)];
        }
    }

}