package monster.config;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import lombok.Data;
import lombok.Getter;
import monster.server.AppConfig;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class CfgDice {
    public static DataConfig config;
    public static List<Integer> newLevelDice = new ArrayList<>();
    public static List<Integer> newReceived = new ArrayList<>();

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
    }

    @Getter
    public class DataConfig {
        private List<List<List<Long>>> dataLevel;
        private Integer numberStep, maxNumberBuy;
        private List<Long> feeBuyDice;
        private Reward reward;
        private String open, close;
        private Integer freeDaily;
        private Integer maxLap;
        private List<Integer> rate;

        public void init() {
            newReceived.clear();
            for (int i = 0; i < reward.getStar().size(); i++) {
                newReceived.add(0);
            }

            newLevelDice.clear();
            for (int i = 0; i < numberStep; i++) {
                newLevelDice.add(1);
            }
        }
    }

    @Data
    public class Reward {
        private List<Integer> star;
        private List<List<Long>> bonus;
    }

    /**
     * @return ngày mở event
     * @throws ParseException
     */
    public static Date getOpenDate() throws ParseException {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(config.open);
    }

    /**
     * @return ngày đóng event
     * @throws ParseException
     */
    public static Date getCloseDate() throws ParseException {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(config.close);
    }

    /**
     * @return đang đóng???
     * @throws ParseException
     */
    public static boolean isClose() {
        if (AppConfig.isServerRealTest()) return false;
        try {
            return new Date().after(getCloseDate());
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        }

        return true;
    }

    /**
     * @return đang mở???
     * @throws ParseException
     */
    public static boolean isOpen() throws ParseException {
        if (AppConfig.isServerRealTest()) return true;
        return new Date().after(getOpenDate()) && new Date().before(getCloseDate());
    }

    /**
     * Nếu chưa đến thời gian mở thì trả về count down, qua rồi thì trả về 0
     *
     * @return
     */
    public static long getCountDownOpen() throws ParseException {
        return isClose() ? (getOpenDate().getTime() - new Date().getTime()) / 1000 : 0;
    }

    /**
     * Nếu chưa đến thời gian mở thì trả về count down, qua rồi thì trả về
     *
     * @return
     */
    public static long getCountDownClose() throws ParseException {
        return isOpen() ? (getCloseDate().getTime() - new Date().getTime()) / 1000 : 0;
    }

    /**
     * vị trí từ 1 -> 22, level từ 1 -> 3. Nên lúc đấy data phải -1 để ra index.
     *
     * @param position vị trí
     * @param level    level
     * @return data
     */
    public static List<Long> getData(int position, int level) {
        return config.dataLevel.get(level - 1).get(position - 1);
    }

    /**
     * @return list vị trí của ô tăng sao
     */
    public static List<Integer> getPositionStar() {
        List<Integer> aInt = new ArrayList<>();
        for (int i = 0; i < config.dataLevel.get(0).size(); i++) {
            if (config.dataLevel.get(0).get(i).get(0) == 1 && config.dataLevel.get(0).get(i).get(2) == -100) {
                aInt.add(i + 1);
            }
        }

        return aInt;
    }

    /**
     * @return list vị trí trap
     */
    public static List<Integer> getPositionTrap() {
        List<Integer> aInt = new ArrayList<>();
        for (int i = 0; i < config.dataLevel.get(0).size(); i++) {
            if (config.dataLevel.get(0).get(i).get(0) == 2) {
                aInt.add(i + 1);
            }
        }

        return aInt;
    }


    /**
     * @return list vị trí nguyên liệu
     */
    public static List<Integer> getPositionMaterial() {
        List<Integer> aInt = new ArrayList<>();
        for (int i = 0; i < config.dataLevel.get(0).size(); i++) {
            if (config.dataLevel.get(0).get(i).get(0) == 1 && config.dataLevel.get(0).get(i).get(2) != -100) {
                aInt.add(i + 1);
            }
        }

        return aInt;
    }

    /**
     * @param position vị trí
     * @return vị trí có phải trap hay ko?
     */
    public static boolean isTrap(int position) {
        for (int i = 0; i < getPositionTrap().size(); i++) {
            if (position == getPositionTrap().get(i)) return true;
        }

        return false;
    }

    /**
     * @param position vị trí
     * @return vị trí có phải sao hay ko?
     */
    public static boolean isStar(int position) {
        for (int i = 0; i < getPositionStar().size(); i++) {
            if (position == getPositionStar().get(i)) return true;
        }

        return false;
    }

    /**
     * @param position vị trí
     * @return vị trí có phải nguyên liệu hay ko?
     */
    public static boolean isMaterial(int position) {
        for (int i = 0; i < getPositionMaterial().size(); i++) {
            if (position == getPositionMaterial().get(i)) return true;
        }

        return false;
    }

    /**
     * Đổi data thành dạng bonus view
     *
     * @param data
     * @return bonus
     */
    public static List<Long> toBonusView(List<Long> data) {
        List<Long> aLong = new ArrayList<>(data);
        if (aLong.size() >= 4) {
            aLong.set(0, 3L);
            aLong.set(1, 1L);
        }

        return aLong;
    }

    /**
     * @return level tối đa của ô
     */
    public static int getMaxLevel() {
        return config.dataLevel.size();
    }

    public static String reloadConfig() {
        String value;
        if (CfgServer.isTestServer()) {
            value = DBJPA.getUniqueColumn(CfgServer.DB_MAIN + "config_dev", Arrays.asList("k", "config_dice"), "v");
        } else {
            value = DBJPA.getUniqueColumn(CfgServer.DB_MAIN + "config", Arrays.asList("k", "config_dice"), "v");
        }

        config = new Gson().fromJson(value, DataConfig.class);
        config.init();

        return "Reload_event_dice";
    }
}
