package monster.config;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.log.Logs;
import monster.cache.CacheStore;
import monster.dao.mapping.UserArenaTrialEntity;
import monster.object.RewardEntity;
import net.sf.json.JSONObject;

import jakarta.persistence.EntityManager;
import java.util.*;
import java.util.concurrent.TimeUnit;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgArenaTrial {
    public static JSONObject json;
    public static DataConfig config;
    public static int minWinPoint = 0, maxWinPoint = 100;
    public static int minLoosePoint = 0, maxLoosePoint = -50;
    public static Set<String> jobSeasonRankingDone = new HashSet<>();

    //region Cache UserArena
    private static Map<Integer, LoadingCache<Integer, UserArenaTrialEntity>> mCache = new HashMap<>();

    private static synchronized LoadingCache<Integer, UserArenaTrialEntity> getCache(int serverId) {
        if (!mCache.containsKey(serverId)) {
            mCache.put(serverId, CacheBuilder.newBuilder().maximumSize(3000).expireAfterAccess(1, TimeUnit.HOURS).build(new CacheLoader<Integer, UserArenaTrialEntity>() {
                @Override
                public UserArenaTrialEntity load(Integer userId) throws Exception {
                    EntityManager session = null;
                    try {
                        session = DBJPA.getEntityManager();
                        int eventId = getEventId();
                        List<UserArenaTrialEntity> aUserArena = session.createNativeQuery("select * from user_arena_trial where user_id=" + userId + " and " +
                                        "event_id=" + eventId,
                                UserArenaTrialEntity.class).getResultList();
                        if (aUserArena.isEmpty()) {
                            UserArenaTrialEntity userArena = new UserArenaTrialEntity(userId, serverId);
                            session.getTransaction().begin();
                            session.persist(userArena);
                            session.getTransaction().commit();
                            return userArena;
                        }
                        return aUserArena.get(0);
                    } catch (Exception ex) {
                        Logs.error(GUtil.exToString(ex));
                    } finally {
                        DBJPA.closeSession(session);
                    }
                    return null;
                }
            }));
        }
        return mCache.get(serverId);
    }

    public static UserArenaTrialEntity getArenaTrial(int serverId, int userId) {
        try {
            return getCache(serverId).get(userId);
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }

    public static void clearCache(int serverId) {
        getCache(serverId).invalidateAll();
    }
    //endregion

    /**
     * Ngày cuối sự kiện chỉ mở tới 9h tối
     *
     * @return
     */
    public static boolean isOpen() {
        if (CfgServer.isTestServer()) return true;

        if (Calendar.getInstance().get(Calendar.DAY_OF_WEEK) == config.eventDays.get(config.eventDays.size() - 1)) // ngày cuối sự kiện
        {
            int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
            if (hour < 22) return true;
            return false;
        }
        return config.eventDays.contains(Calendar.getInstance().get(Calendar.DAY_OF_WEEK));
    }

    public static List<Long> getSeasonReward(int rank) {
        for (RewardEntity reward : config.seasonReward) {
            if (reward.inRank(rank)) return new ArrayList<>(reward.bonus);
        }
        return null;
    }

    public static int getEventId() {
        Calendar ca = Calendar.getInstance();
        while (ca.get(Calendar.DAY_OF_WEEK) != config.eventDays.get(0)) {
            ca.add(Calendar.DATE, -1);
        }
        return (int) DateTime.numberDayPassed(config.startDate, ca.getTime());
    }

    public static int getLastEventId() {
        if (isOpen()) return getEventId() - 7;
        return getEventId();
    }

    public static long getCountdown() {
        if (isOpen()) {
            return (lastEventDay().getTimeInMillis() - System.currentTimeMillis()) / 1000 - DateTime.HOUR_SECOND * 2;
        }
        return (firstEventDay().getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    private static Calendar firstEventDay() {
        Calendar ca = Calendar.getInstance();
        while (ca.get(Calendar.DAY_OF_WEEK) != config.eventDays.get(0)) {
            ca.add(Calendar.DATE, 1);
        }
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);
        return ca;
    }

    private static Calendar lastEventDay() {
        Calendar ca = Calendar.getInstance();
        while (ca.get(Calendar.DAY_OF_WEEK) != config.eventDays.get(config.eventDays.size() - 1)) {
            ca.add(Calendar.DATE, 1);
        }
        ca.set(Calendar.HOUR_OF_DAY, 23);
        ca.set(Calendar.MINUTE, 59);
        ca.set(Calendar.SECOND, 59);
        ca.set(Calendar.MILLISECOND, 99);
        return ca;
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
    }

    public class DataConfig {
        public int maxRank;
        public List<Integer> eventDays;
        public RewardEntity[] seasonReward;
        public String strStartDate;
        public int numberEventDay;
        public Date startDate;
        public long eventTimeInMilli;

        public void init() {
            eventTimeInMilli = numberEventDay * DateTime.DAY_MILLI_SECOND;
            try {
                startDate = DateTime.get_yyyyMMdd().parse(strStartDate);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

}
