package monster.config;

import com.google.gson.Gson;
import grep.helper.ListUtil;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.config.penum.SummonType;
import monster.controller.AHandler;
import monster.dao.mapping.UserMaterialEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.resource.ResArtifact;
import monster.service.resource.ResHero;
import monster.service.resource.ResSummonNew;
import monster.service.user.Bonus;

import java.util.ArrayList;
import java.util.List;

public class CfgArtifact {
    public static DataConfig config;

    public static int getUpgradePoint(int artifactId, int artifactStar) {
        if (artifactId == 1) return 10;
        int quality = ResArtifact.getArtifact(artifactId).getQuality() - 1;
        return config.baseValue[quality] + config.upgradeCost[quality] * (artifactStar - 1);
    }

    public static boolean summonHeroShard(MyUser mUser, AHandler handler, int shardId, int number) {
        int[] dataRequired = new int[]{20, 30, 50};
        UserMaterialEntity material = mUser.getResources().getMaterial(MaterialType.TYPE_HERO_SHARD, shardId);
        if (material == null) {
            handler.addErrResponse(handler.getLang(Lang.err_params) + "(1)");
            return false;
        }
        int requiredFragment = 0;
        if (shardId < 10000) { // specific hero
            ResHeroEntity resHero = ResHero.getHero(shardId);
            if (resHero == null) {
                Logs.error(String.format("summonHeroShard shardId=%s number=%s", shardId, number));
            }
            requiredFragment = dataRequired[resHero.getStar() - 3];
        } else if (shardId >= 40000 && shardId <= 40015) {
            requiredFragment = 50;
        } else { // random hero
            requiredFragment = dataRequired[shardId / 10000 - 1];
        }
        if (requiredFragment < 20 || requiredFragment > 50 || material.getNumber() < requiredFragment * number) {
            handler.addErrResponse(handler.getLang(Lang.err_not_enough_material) + "(2)");
            return false;
        }

        //if (shardId == MaterialType.HERO_SS_L1.id || shardId == MaterialType.HERO_SS_L2.id) {
        //    number = 1;
        //}

        List<Integer> aHeroKey = new ArrayList<>();
        // 1 số mảnh đặc biệt lấy id theo số lần đã quay
        int numberSummon = 0;
        if (shardId == MaterialType.HERO_SS_L1.id) {
            numberSummon = mUser.getSummonCounter().getSsl1();
            aHeroKey.addAll(getHeroKey(mUser, shardId, number));
        } else if (shardId == MaterialType.HERO_SS_L2.id) {
            numberSummon = mUser.getSummonCounter().getSsl2();
            aHeroKey.addAll(getHeroKey(mUser, shardId, number));
        } else if (shardId == MaterialType.HERO_SHARD_GOD.id) {
            numberSummon = mUser.getSummonCounter().getShardGod();
            aHeroKey.addAll(getHeroKey(mUser, shardId, number));
        } else {
            if (shardId < 10000) { // 1 luc chi mo dc 1 loai shardId
                for (int i = 0; i < number; i++) {
                    aHeroKey.add(shardId);
                }
            } else {
                aHeroKey.addAll(getHeroKey(mUser, shardId, number));
            }
        }
        MaterialService materialService = Guice.getInstance(MaterialService.class);
        ServiceResult<List<Long>> resultUsedFragment = materialService.useFragmentMaterial(mUser, material.getMaterialId(), requiredFragment * number, "hero_shard");
        if (resultUsedFragment.success) {
            List<Long> bonus = new ArrayList<>();
            aHeroKey.forEach(heroKey -> bonus.addAll(Bonus.viewHero(heroKey)));
            ServiceResult<List<Long>> resultAddHero = materialService.addBonus(mUser, bonus, "hero_shard");
            if (resultAddHero.success) {
                handler.addResponse(protocol.Pbmethod.CommonVector.newBuilder()
                        .addAllALong(resultUsedFragment.data).addAllALong(resultAddHero.data)
                        .build());

                if (shardId == MaterialType.HERO_SS_L1.id) {
                    mUser.getSummonCounter().setSsl1(numberSummon).update();
                } else if (shardId == MaterialType.HERO_SS_L2.id) {
                    mUser.getSummonCounter().setSsl2(numberSummon).update();
                } else if (shardId == MaterialType.HERO_SHARD_GOD.id) {
                    mUser.getSummonCounter().setShardGod(numberSummon).update();
                }
                Bonus.checkEventHero(mUser, aHeroKey, "hero_shard");
                return true;
            } else {
                materialService.addMaterial(mUser, material.getMaterialId(), requiredFragment * number, "hero_shard_fail");
                resultAddHero.writeResponse(handler);
                return false;
            }
        } else resultUsedFragment.writeResponse(handler);
        return false;
    }

    public static boolean summonHeroById(MyUser mUser, AHandler handler, int summonId, int number) {
        List<Integer> aHeroKey = ListUtil.converLstLongToInt(ResSummonNew.bonusSummonNew(SummonType.get(summonId), mUser, number, false));
        List<Long> bonus = new ArrayList<>();
        aHeroKey.forEach(heroKey -> bonus.addAll(Bonus.viewHero(heroKey)));
        MaterialService materialService = Guice.getInstance(MaterialService.class);
        ServiceResult<List<Long>> resultAddHero = materialService.addBonus(mUser, bonus, "hero_summon_point");
        if (resultAddHero.success) {
            handler.addResponse(protocol.Pbmethod.CommonVector.newBuilder().addAllALong(resultAddHero.data).build());
            Bonus.checkEventHero(mUser, aHeroKey, "hero_summon_point");
            return true;
        } else {
            resultAddHero.writeResponse(handler);
            return false;
        }
    }

    public static List<Integer> getHeroKey(MyUser mUser, int shardId, int number) {
        if (shardId >= 40001 && shardId <= 40015) {
            List<Integer> ids = ListUtil.converLstLongToInt(ResSummonNew.bonusSummonNew(SummonType.get(42 + shardId - 40001), mUser, number, false));
            return ids;
        }
        SummonType summonType = null;
        switch (MaterialType.getHeroShard(shardId)) {
            case HERO_SHARD_3:
                summonType = SummonType.HERO_STAR3;
                break;
            case HERO_SHARD_4:
                summonType = SummonType.HERO_STAR4;
                break;
            case HERO_SHARD_4ABYSS:
                summonType = SummonType.HERO_STAR4_ABYSS;
                break;
            case HERO_SHARD_4FOREST:
                summonType = SummonType.HERO_STAR4_FOREST;
                break;
            case HERO_SHARD_4DARK:
                summonType = SummonType.HERO_STAR4_DARK;
                break;
            case HERO_SHARD_4LIGHT:
                summonType = SummonType.HERO_STAR4_LIGHT;
                break;
            case HERO_SHARD_5:
                summonType = SummonType.HERO_STAR5;
                break;
//            case HERO_SHARD_5LIGHTNING:
//                summonType = SummonType.HERO_STAR5_SHADOW;
//                break;
            case HERO_SHARD_5FIRE:
                summonType = SummonType.HERO_STAR5_ABYSS;
                break;
//            case HERO_SHARD_5LAND:
//                summonType = SummonType.HERO_STAR5_FORTRESS;
//                break;
            case HERO_SHARD_5WIND:
                summonType = SummonType.HERO_STAR5_FOREST;
                break;
            case HERO_SHARD_5LUNISOLAR:
                summonType = SummonType.HERO_STAR5_DARK;
                break;
            case HERO_SHARD_5WATER:
                summonType = SummonType.HERO_STAR5_LIGHT;
                break;
            case HERO_SHARD_GOD:
                summonType = SummonType.SHARD_GOD;
                break;
        }
        if (summonType != null)
            return ListUtil.converLstLongToInt(ResSummonNew.bonusSummonNew(summonType, mUser, number, false));
        return null;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public int[] baseValue, upgradeCost, maxUpgrade;
    }

}