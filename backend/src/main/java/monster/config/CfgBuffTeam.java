package monster.config;

import com.google.gson.Gson;
import monster.object.PointDescArtifactEntity;

import java.util.List;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgBuffTeam {
    public static DataConfig config;

    public static BuffInfo getPointDesc(int[] countFaction) {
        for (BuffInfo buffInfo : config.buffTeam) {
            boolean isOk = true;
            for (int i = 0; i < buffInfo.require.length; i += 2) {
                int reqNumber = buffInfo.require[i];
                int factionIndex = buffInfo.require[i + 1];
                if (countFaction[factionIndex] != reqNumber) {
                    isOk = false;
                    break;
                }
            }
            if (isOk) return buffInfo;
        }
        return null;
    }

    public static void loadConfig(String value) {
        config = new Gson().fromJson(value, DataConfig.class);
    }

    public class DataConfig {
        public List<BuffInfo> buffTeam;
    }

    public class BuffInfo {
        public int id;
        String name, desc;
        int[] require;
        public PointDescArtifactEntity buff;
    }
}
