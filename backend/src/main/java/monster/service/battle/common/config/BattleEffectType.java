package monster.service.battle.common.config;

import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.Point;

import java.util.*;

public enum BattleEffectType {
    BUFF_ATTACK_INC(1, BattleConfig.TYPE_SHOW_ROUND, Point.ATTACK),
    BUFF_ATTACK_DEC(1001, BattleConfig.TYPE_SHOW_ROUND, Point.ATTACK),
    BUFF_ARMOR_INC(2, BattleConfig.TYPE_SHOW_ROUND, Point.ARMOR),
    BUFF_ARMOR_DEC(1002, BattleConfig.TYPE_SHOW_ROUND, Point.ARMOR),
    BUFF_HP_INC(3, BattleConfig.TYPE_SHOW_HP_CHANGED),
    BUFF_HP_DEC(1003, BattleConfig.TYPE_SHOW_HP_CHANGED),
    BUFF_SPEED_INC(4, BattleConfig.TYPE_SHOW_ROUND, Point.SPEED),
    BUFF_SPEED_DEC(1004, BattleConfig.TYPE_SHOW_ROUND, Point.SPEED),
    BUFF_ANGER_INC(5, BattleConfig.TYPE_SHOW_ANGER_CHANGED),
    BUFF_ANGER_DEC(1005, BattleConfig.TYPE_SHOW_ANGER_CHANGED),
    BUFF_SKILL_DAMAGE_INC(6, BattleConfig.TYPE_SHOW_ROUND, Point.SKILL_DAMAGE),
    BUFF_SKILL_DAMAGE_DEC(1006, BattleConfig.TYPE_SHOW_ROUND, Point.SKILL_DAMAGE),
    BUFF_PRECISION_INC(7, BattleConfig.TYPE_SHOW_ROUND, Point.PRECISION),
    BUFF_PRECISION_DEC(1007, BattleConfig.TYPE_SHOW_ROUND, Point.PRECISION),
    BUFF_BLOCK_INC(8, BattleConfig.TYPE_SHOW_ROUND, Point.BLOCK),
    BUFF_BLOCK_DEC(1008, BattleConfig.TYPE_SHOW_ROUND, Point.BLOCK),
    BUFF_CRIT_INC(9, BattleConfig.TYPE_SHOW_ROUND, Point.CRIT),
    BUFF_CRIT_DEC(1009, BattleConfig.TYPE_SHOW_ROUND, Point.CRIT),
    BUFF_CRIT_DAMAGE_INC(10, BattleConfig.TYPE_SHOW_ROUND, Point.CRIT_DAMAGE),
    BUFF_CRIT_DAMAGE_DEC(1010, BattleConfig.TYPE_SHOW_ROUND, Point.CRIT_DAMAGE),
    BUFF_ARMOR_BREAK_INC(11, BattleConfig.TYPE_SHOW_ROUND, Point.ARMOR_BREAK),
    BUFF_ARMOR_BREAK_DEC(1011, BattleConfig.TYPE_SHOW_ROUND, Point.ARMOR_BREAK),
    BUFF_CONTROL_IMMUNE_INC(12, BattleConfig.TYPE_SHOW_ROUND, Point.CONTROL_IMMUNE),
    BUFF_CONTROL_IMMUNE_DEC(1012, BattleConfig.TYPE_SHOW_ROUND, Point.CONTROL_IMMUNE),
    BUFF_REDUCE_DAMAGE_INC(13, BattleConfig.TYPE_SHOW_ROUND, Point.REDUCE_DAMAGE),
    BUFF_REDUCE_DAMAGE_DEC(1013, BattleConfig.TYPE_SHOW_ROUND, Point.REDUCE_DAMAGE),
    BUFF_HOLY_DAMAGE_INC(14, BattleConfig.TYPE_SHOW_ROUND, Point.HOLY_DAMAGE),
    BUFF_HOLY_DAMAGE_DEC(1014, BattleConfig.TYPE_SHOW_ROUND, Point.HOLY_DAMAGE),
    BUFF_MORE_HEAL_INC(15, BattleConfig.TYPE_SHOW_ROUND, Point.SPEC_MORE_HEAL),
    BUFF_MORE_HEAL_DEC(1015, BattleConfig.TYPE_SHOW_ROUND, Point.SPEC_MORE_HEAL),
    BUFF_ANTI_ARMOR_BREAK_INC(16, BattleConfig.TYPE_SHOW_ROUND, Point.ANTI_ARMOR_BREAK),
    BUFF_ANTI_ARMOR_BREAK_DEC(1016, BattleConfig.TYPE_SHOW_ROUND, Point.ANTI_ARMOR_BREAK),

    EFFECT_STUN(50, BattleConfig.TYPE_SHOW_ROUND, "Choáng"),
    EFFECT_SILENCE(51, BattleConfig.TYPE_SHOW_ROUND, "Câm lặng"),
    EFFECT_DOT_FIRE(52, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_DOT_BLOOD(53, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_DOT_POISON(54, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_EXTRA_BURNING(55, BattleConfig.TYPE_SHOW_ROUND), // Gây thêm sát thương nếu địch bị thiêu đốt
    EFFECT_EXTRA_BLEED(56, BattleConfig.TYPE_SHOW_ROUND), // Gây thêm sát thương nếu địch bị chảy máu
    EFFECT_EXTRA_POISON(67, BattleConfig.TYPE_SHOW_ROUND), // Gây thêm sát thương nếu địch bị trúng độc
    EFFECT_ROUND_MARK(58, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_LIGHTNING_MARK_SKEREI(59, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_STONE(60, BattleConfig.TYPE_SHOW_ROUND, "Hoá đá"),
    EFFECT_REVIVE(61, BattleConfig.TYPE_SHOW_REVIVE, BattleConfig.TYPE_SPECIAL_EFFECT),
    EFFECT_FREEZE(62, BattleConfig.TYPE_SHOW_ROUND, "Đóng băng"),
    EFFECT_CRITICAL_STRIKE_MARK(63, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_WATCHER_MARK_MORE_DAMAGE_TAKEN(64, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_CONTINUE_HEAL(65, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_WEAKEN(66, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_EXACT_DOT_FIRE(67, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_EXACT_DOT_BLOOD(68, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_EXACT_DOT_POISON(69, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_IMMORTAL(70, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_DEATH_MARK(71, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ANGER_END_ROUND(72, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_FEAR(73, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_SKILL_END_ROUND(74, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ENERGY_SEAL(75, BattleConfig.TYPE_SHIELD),
    EFFECT_TRUY_SAT(76, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_SUY_NHUOC(77, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_COUNTER_SHIELD(78, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_IMMORTAL_BY_OPP_TURN(79, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ENERGY_SEAL_2(83, BattleConfig.TYPE_SHIELD),
    EFFECT_KILLER(84, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ATTACK_FRONT_LINE_ALLY(85, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_CRIT_TIME_REDUCE2(86, BattleConfig.TYPE_SHOW_ROUND, Point.SPEC_CRIT_TIME_REDUCE2),
    EFFECT_HOANG_LOAN(87, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_MAX_DAMAGE_SUFFER(88, BattleConfig.TYPE_SHOW_ROUND),
    ATTACK_BLOCK(100, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_EXTRA_STONE(101, BattleConfig.TYPE_SHOW_ROUND), // Gây thêm sát thương nếu địch bị hóa đá
    EFFECT_CAM_LO(102, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_LOAN_KHONG(103, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ROUND_MARK2(104, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_PHA_CAM(105, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_REDUCE_DAMAGE_NORMAL_ATTACK(106, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_REDUCE_DAMAGE_ACTIVE_SKILL(107, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ATTACK_FRONT_LINE_ALLY2(109, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ENERGY_SEAL_3(110, BattleConfig.TYPE_SHIELD),
    EFFECT_ENERGY_SEAL_4(111, BattleConfig.TYPE_SHIELD),
    EFFECT_COUNTER_MARK(112, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_IMMORTAL2(114, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_LOAN_KHONG2(119, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_DIS_ARM(120, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_LINK(121, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_DOT_TRUE_ATK(122, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_DOT_TRUE_HP(123, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ICE_MARK(126, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ENERGY_SEAL_5(130, BattleConfig.TYPE_SHIELD),
    EFFECT_ENERGY_SEAL_6(131, BattleConfig.TYPE_SHIELD),
    EFFECT_ENERGY_SEAL_7(132, BattleConfig.TYPE_SHIELD),
    EFFECT_ENERGY_SEAL_8(133, BattleConfig.TYPE_SHIELD),
    EFFECT_HOPE_MARK(142, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_BUFF_BLOCKER(154, BattleConfig.TYPE_SHOW_ROUND),
    BUFF_ANTI_CONTROL_IMMUNE_INC(155, BattleConfig.TYPE_SHOW_ROUND, Point.ANTI_CONTROL_IMMUNE),
    BUFF_ANTI_CONTROL_IMMUNE_DEC(1155, BattleConfig.TYPE_SHOW_ROUND, Point.ANTI_CONTROL_IMMUNE),
    DMG_SHIELD(156, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_MAX_DAMAGE_SUFFER2(157, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_FIRE_MARK(200, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_FASCINATE(201, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_SURVIVAL(202, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_CC_BY_HIT(203, BattleConfig.TYPE_SHOW_ROUND),
    BUFF_MAX_HP(204, BattleConfig.TYPE_SHOW_MAX_HP_CHANGED, Point.HP),
    EFFECT_SHIELD_BY_MAX_HP(205, BattleConfig.TYPE_SHIELD),
    EFFECT_DEBUFF_BLOCKER(206, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_MARK_OPPRESS(207, BattleConfig.TYPE_SHOW_ROUND), // thêm cho khớp chưa code
    BUFF_BREAK_IMMORTAL(208, BattleConfig.TYPE_SHOW_ROUND, Point.BREAK_IMMORTAL), // thêm cho khớp chưa code
    breakImmortalByHp(209, BattleConfig.TYPE_SHOW_ROUND), // thêm cho khớp chưa code
    EFFECT_HOPE_MARK2(210, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_BLACK_BURN(211, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_EXCITED(212, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_SHIELD_BY_ATTACK(213, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ONLY_SINGLE_TARGET(214, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_ONOKI_MARK(215, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_BLOCK_REVIVE(216, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_SHARE_DMG(217, BattleConfig.TYPE_SHOW_ROUND, Point.REDUCE_DAMAGE),
    EFFECT_PUDDING_MARK(218, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_HASHIRAMA_MARK(219, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_REDUCE_END_ROUND_DMG(220, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_HASHIRAMA_REVIVAL(221, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_STUN2(222, BattleConfig.TYPE_SHOW_ROUND),
    BUFF_ANTI_CRIT(223, BattleConfig.TYPE_SHOW_ROUND, Point.ANTI_CRIT),
    BUFF_SPEC_MORE_ANGER(224, BattleConfig.TYPE_SHOW_ROUND, Point.SPEC_MORE_ANGER),
    BUFF_SPEC_MORE_DAMAGE_A(225, BattleConfig.TYPE_SHOW_ROUND, Point.SPEC_MORE_DAMAGE_A),
    EFFECT_SOUL_BURN(226, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_CONTROL_BLOCK(227, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_SOUL_BURN_MARK(228, BattleConfig.TYPE_SHOW_ROUND),
    BUFF_MORE_DAMAGE_ACTIVE_SKILL(229, BattleConfig.TYPE_SHOW_ROUND),
    BUFF_MORE_TURN(230, BattleConfig.TYPE_SHOW_ROUND),
    buffDmgMoreHP(231, BattleConfig.TYPE_SHOW_ROUND),
    reduceRound(232, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_MAX_DAMAGE_SUFFER3(233, BattleConfig.TYPE_SHOW_ROUND),
    taunt(234, BattleConfig.TYPE_SHOW_ROUND),
    kisameMark(235, BattleConfig.TYPE_SHOW_ROUND),
    SKILL_DAMAGE_NUMBER_BURN_ENEMY(236, BattleConfig.TYPE_SHOW_ROUND),
    ATTACK_PER_LEVEL_NUMBER_BURN_ENEMY(237, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_HOPE_MARK1(238, BattleConfig.TYPE_SPECIAL_EFFECT),
    killAddRoundForHopeMark1(239, BattleConfig.TYPE_SPECIAL_EFFECT),
    atkAndBrkByDot(240, BattleConfig.TYPE_SHOW_ROUND),
    EFFECT_PRE_HOPE_MARK1(241, BattleConfig.TYPE_SPECIAL_EFFECT),
    ;

    public int value, pointIndex = -1;
    public int baseIndex = -1;
    public int typeForClient;
    public String name = "";

    BattleEffectType(int value, int typeForClient) {
        this.value = value;
        this.typeForClient = typeForClient;
    }

    BattleEffectType(int value, int typeForClient, int pointIndex) {
        this.value = value;
        this.pointIndex = pointIndex;
        this.baseIndex = pointIndex + 18;
        this.typeForClient = typeForClient;
    }

    BattleEffectType(int value, int typeForClient, String name) {
        this.value = value;
        this.typeForClient = typeForClient;
        this.name = name;
    }

    //region lookup
    public static List<Integer> aIndexEffect = new ArrayList<>();
    public static List<BattleEffectType> aCCEffect = Arrays.asList(EFFECT_STUN, EFFECT_SILENCE, EFFECT_STONE, EFFECT_FREEZE, EFFECT_FEAR, EFFECT_ICE_MARK);
    public static List<BattleEffectType> aEffectDamageByRound = Arrays.asList(EFFECT_DOT_FIRE, EFFECT_DOT_POISON, EFFECT_DOT_BLOOD);
    public static List<BattleEffectType> aEffectExactDamageByRound = Arrays.asList(EFFECT_EXACT_DOT_FIRE, EFFECT_EXACT_DOT_BLOOD, EFFECT_EXACT_DOT_POISON, EFFECT_DOT_TRUE_ATK, EFFECT_DOT_TRUE_HP);
    public static List<BattleEffectType> aEffectExtra = Arrays.asList(EFFECT_DOT_FIRE, EFFECT_DOT_POISON, EFFECT_DOT_BLOOD, EFFECT_STUN, EFFECT_STONE, EFFECT_FREEZE, EFFECT_DEATH_MARK, EFFECT_FEAR);
    public static List<BattleEffectType> aBadEffect = Arrays.asList(
            EFFECT_STUN, EFFECT_SILENCE, EFFECT_DOT_FIRE, EFFECT_DOT_BLOOD, EFFECT_DOT_POISON,
            EFFECT_STONE, EFFECT_FREEZE,
            EFFECT_EXACT_DOT_FIRE, EFFECT_EXACT_DOT_BLOOD, EFFECT_EXACT_DOT_POISON
    );
    public static List<BattleEffectType> aAccEffect = Arrays.asList(
            EFFECT_STUN, EFFECT_SILENCE, EFFECT_STONE, EFFECT_FREEZE
    );

    public static List<BattleEffectType> aStunEffect = Arrays.asList(
            EFFECT_STUN, EFFECT_STONE, EFFECT_FREEZE, EFFECT_ICE_MARK
    );

    public static final List<BattleEffectType> listShieldEffect = List.of(EFFECT_SHIELD_BY_MAX_HP, EFFECT_ENERGY_SEAL, EFFECT_ENERGY_SEAL_2,
            EFFECT_ENERGY_SEAL_3, EFFECT_ENERGY_SEAL_4, EFFECT_ENERGY_SEAL_5, EFFECT_ENERGY_SEAL_6, EFFECT_ENERGY_SEAL_7, EFFECT_ENERGY_SEAL_8);

    static Map<Integer, BattleEffectType> lookup = new HashMap<>();

    static {
        for (BattleEffectType target : BattleEffectType.values()) {
            lookup.put(target.value, target);
            if (target.pointIndex >= 0) aIndexEffect.add(target.value);
        }
        aIndexEffect.remove(Integer.valueOf(BUFF_HP_INC.value));
        aIndexEffect.remove(Integer.valueOf(BUFF_HP_DEC.value));
        aIndexEffect.remove(Integer.valueOf(BUFF_ANGER_INC.value));
        aIndexEffect.remove(Integer.valueOf(BUFF_ANGER_DEC.value));
    }

    public static BattleEffectType get(int value) {
        return lookup.get(value);
    }

    public static boolean isBuffIndex(int value) {
        return aIndexEffect.contains(value);
    }
    //endregion
}

