package monster.controller;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import grep.log.slib_Logger;
import monster.config.CfgHero;
import monster.config.CfgSpecialItem;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserHeroEntity;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import org.slf4j.Logger;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * User Infomation
 */
public class HeroTextHandler extends AHandler {

    TextResultFee result = new TextResultFee();

    private enum TypeItem {Preview, Receive}

    @Override
    public AHandler newInstance() {
        return new HeroTextHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(HERO_HANDLER_TEXT);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
    }

    @Override
    public String handleText(SocketAddress address, String uri, byte[] requestData) {
        super.handleText(address, uri, requestData);
        String ip = ((InetSocketAddress) address).getAddress().toString();
        if (ip.startsWith("/")) ip = ip.substring(1);
        String input = new String(requestData);
        getLogger().info(String.format("%s -> %s -> input=[%s], output=[%s]", ip, uri, input, result.toString()));
        try {
            String args[] = uri.split("/");
            if (args[1].equals("hero")) {
                String sessionId = args[2];
                if (!validateSession(sessionId)) {
                    result.error("Session không hợp lệ");
                } else {
                    checkTimeMonitor("s");
                    switch (args[3]) {
                        case "reuphero": {
                            int heroid = Integer.parseInt(args[4]);
                            int typeIndex = Integer.parseInt(args[5]);
                            TypeItem type = TypeItem.Preview;
                            if (typeIndex == 1) type = TypeItem.Receive;
                            reupHero(heroid, type);
                            break;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.error(ex.toString());
            getLogger().error(String.format("%s -> %s -> %s -> %s", ip, uri, input, GUtil.exToString(ex)));
        }
        getLogger().info(String.format("%s -> %s -> input=[%s], output=[%s]", ip, uri, input, result.toString()));
        return result.toString();
    }

    private void reupHero(int idHero, TypeItem type) {
        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (CfgHero.isLinhChi(uHero.getHeroId())) {
            result.error("Không áp dụng với lính chì");
            return;
        }
        if (uHero == null) {
            result.error("Anh hùng không tồn tại");
            return;
        }
        if (uHero.getLevel() <= 1) {
            result.error("Cấp độ anh hùng phải lớn hơn 1");
            return;
        }

        if (uHero.getStar() > 15) {
            result.error("Tính năng chỉ áp dụng cho anh hùng 15 sao trở xuống");
            return;
        }

        if (uHero.isVoidUpgraded()) {
            addErrResponse(getLang(Lang.hero_void_upgraded));
            return;
        }
        
        int gem = CfgHero.getFeeReUpGem(uHero);
        int stone = CfgHero.GetFeeReUpStone(uHero);
        String[] fee = new String[3];
        List<Long> mergeBonus = Bonus.merge(CfgHero.ViewReupBonus(uHero), CfgSpecialItem.getBonusUsedMaterial(uHero));
        fee[0] = Bonus.view(Bonus.BONUS_GEM, gem).toString();
        fee[1] = Bonus.viewMaterial(MaterialType.REPLACE_STONE, stone).toString();
        fee[2] = CfgSpecialItem.getBonusReupFee(uHero).toString();
        if (type == TypeItem.Preview) {
            result.success(fee, mergeBonus.toString());
        } else {
            List<Long> aBonus = Bonus.view(Bonus.BONUS_GEM, -gem);
            aBonus.addAll(Bonus.viewMaterial(MaterialType.REPLACE_STONE, -stone));
            aBonus.addAll(Bonus.viewMaterial(MaterialType.FEE_REUP_SPECIAL_ITEM, -CfgSpecialItem.getFeeReup(uHero)));
            String msg = Bonus.checkMoney(mUser, aBonus);
            if (msg != null) {
                result.error(msg);
            } else {
                aBonus.addAll(mergeBonus);
                if (uHero.getStar() <= 5) {
                    if (dbDecay(uHero)) {
                        mUser.getResources().removeHero(uHero.getId());
                        result.success(null, Bonus.receiveListItem(mUser, "reupHero", aBonus).toString());
                        uHero.calculatePointHero(mUser);
                        Actions.save(user, "hero", "level", "id", uHero.getId(), "key", uHero.getHeroId(), "level", uHero.getLevel(), "tier", uHero.getTier());
                    }
                } else {
                    if (dbDecay(uHero)) {
                        mUser.getResources().removeHero(uHero.getId());
                        uHero.calculatePointHero(mUser);
                        result.success(null, Bonus.receiveListItem(mUser, "reupHeros", aBonus).toString());
                        Actions.save(user, "hero", "decay", "heroId", uHero.getId(), "heroKey", uHero.getHeroId(), "star", uHero.getStar());
                    }
                }
            }
        }
    }

    //endregion
    // region database
    private boolean dbDecay(UserHeroEntity decayHero) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("delete from user_hero where user_id=" + user.getId() + " and id=" + decayHero.getId()).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    // endregion
    //<editor-fold desc="Logic">
    private Logger getLogger() {
        return slib_Logger.access();
    }
    //</editor-fold>

    //<editor-fold desc="Database">
    //</editor-fold>

    class TextResultFee {
        int status;
        String message, bonus;
        String[] fee;

        private TextResultFee success(String[] fee, String bonus) {
            this.status = 1;
            this.message = "";
            this.bonus = bonus;
            this.fee = fee;
            return this;
        }

        private TextResultFee error(String message) {
            this.status = 0;
            this.message = message;
            this.bonus = "";
            this.fee = null;
            return this;
        }

        public String toString() {
            return new Gson().toJson(this);
        }
    }
}
