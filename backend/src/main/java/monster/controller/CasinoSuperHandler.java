package monster.controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.cache.memcached.MCache;
import monster.config.*;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.MaterialType;
import monster.config.penum.MathPuzzleDropType;
import monster.config.penum.QuestType;
import monster.dao.mapping.UserCasinoSuperEntity;
import monster.dao.mapping.UserMaterialEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.protocol.CommonProto;
import monster.service.monitor.EventMonitor;
import monster.service.resource.ResHero;
import monster.service.user.Bonus;
import protocol.Pbmethod.CommonVector;
import protocol.Pbmethod.ListCommonVector;
import protocol.Pbmethod.ResponseData;

import jakarta.persistence.EntityManager;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Mail and Friend
 */
public class CasinoSuperHandler extends AHandler {

    final String KEY_DATA = "casino_super";
    final String KEY_CACHE_RECORD = "RECORD_CASINO_SUPER";

    final int INDEX_FEE_CASINO_1_TIMES = 0;
    final int INDEX_FEE_CASINO_10_TIMES = 1;
    UserCasinoSuperEntity casino;

    @Override
    public AHandler newInstance() {
        return new CasinoSuperHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(CASINO_SUPER_STATUS, CASINO_SUPER_REFRESH, CASINO_SUPER_ROTATE, CASINO_SUPER_RECORD);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");

        if (user.getLevel() < 80 && user.getVip() < 3) {
            addErrResponse(String.format(getLang(Lang.user_function_level_or_vip_required), 80, 3));
            return;
        }

        this.casino = getUserCasino();
        if (casino != null) {
            try {
                switch (actionId) {
                    case CASINO_SUPER_STATUS:
                        status();
                        break;
                    case CASINO_SUPER_REFRESH:
                        refresh();
                        break;
                    case CASINO_SUPER_ROTATE:
                        rotate();
                        break;
                    case CASINO_SUPER_RECORD:
                        record();
                        break;
                }
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
    }

    //region Handle service
    //region casino
    void status() {
        ListCommonVector.Builder builder = ListCommonVector.newBuilder();
        {
            int feeRefresh = casino.checkFreeRefresh() ? 0 : CfgCasinoSuper.config.feeRefresh[0];
            //
            builder.addAVector(CommonVector.newBuilder().addALong((long) CfgCasinoSuper.config.feeRotate[INDEX_FEE_CASINO_1_TIMES]).addAString("1 " + getLang(Lang.label_spin)).addALong((long) CfgCasinoSuper.config.feeRotate[INDEX_FEE_CASINO_10_TIMES])
                    .addAString("10 " + getLang(Lang.label_spin)).addALong(casino.casinoCountdown()).addALong(feeRefresh).addALong(CfgCasinoSuper.config.priceChip));
        }

        {
            if (casino.checkRefresh()) {
                casino.setCasinoBonus(CfgCasinoSuper.getCasinoBonusShow().toString());
                casino.update();
            }

            builder.addAVector(CommonProto.getCommonVectorProto(parseListBonusCasino(casino.getCasinoBonus())));
            builder.addAVector(CommonProto.getCommonVectorProto(casino.getListCasinoEnable()));
            builder.addAVector(CfgCasinoSuper.protoRate());
        }

        addResponse(CASINO_SUPER_STATUS, builder.build());
    }

    void rotate() {
        ListCommonVector.Builder builder = ListCommonVector.newBuilder();
        List<Long> results = new ArrayList<>();
        int numberRotate = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        if (numberRotate != 1 && numberRotate != 10) numberRotate = 1;
        int indexResult = 0;
        //
        int feeRotate = 0;
        if (numberRotate == 1) {
            feeRotate = CfgCasinoSuper.config.feeRotate[INDEX_FEE_CASINO_1_TIMES];
        } else if (numberRotate == 10) {
            feeRotate = CfgCasinoSuper.config.feeRotate[INDEX_FEE_CASINO_10_TIMES];

            if (!(user.getLevel() >= CfgLimitFunction.config.level.casinoX10 || user.getVip() >= CfgLimitFunction.config.vip.casinoX10)) {
                addErrResponse(String.format(getLang(Lang.user_function_level_or_vip_required), CfgLimitFunction.config.level.casinoX10, CfgLimitFunction.config.vip.casinoX10));
                return;
            }
        }

        UserMaterialEntity uMaterial = mUser.getResources().getMaterial(MaterialType.SUPER_CHIP);
        if (uMaterial == null || uMaterial.getNumber() < feeRotate) {
            addErrResponse(getLang(Lang.not_enough_chip));
            return;
        }
        results = Bonus.receiveListItem(mUser, "casino_super_rotate", Bonus.viewMaterial(MaterialType.SUPER_CHIP, -feeRotate));
        if (results.isEmpty()) {
            addErrResponse();
            return;
        }
        //
        JsonArray bonus = new JsonArray();
        JsonArray arrBonusCasino = GsonUtil.parseJsonArray(casino.getCasinoBonus());
        //
        List<Long> lstEnable = casino.getListCasinoEnable();
        //
        boolean has5StarHero = false;
        for (int i = 0; i < numberRotate; i++) {
            indexResult = CfgCasinoSuper.getRandomIndex();
            //
            while (lstEnable.get(indexResult) != 1) {
                indexResult = CfgCasinoSuper.getRandomIndex();
            }
            debug("\nIndexSlot===>" + indexResult);
            //
            bonus.addAll(arrBonusCasino.get(indexResult).getAsJsonArray());
            if (indexResult == 6 || indexResult == 7) {
                int heroKey = arrBonusCasino.get(indexResult).getAsJsonArray().get(2).getAsInt();
                ResHeroEntity resHero = ResHero.getHero(heroKey);
                if (resHero != null && resHero.getStar() == 5) has5StarHero = true;
            }
            //
            if (CfgCasinoSuper.isEnableOne(indexResult))
                lstEnable.set(indexResult, 0L);
        }
        //
        results.add(0, (long) indexResult);
        casino.setCasinoEnable(new Gson().toJson(lstEnable));
        boolean isOk = casino.updateCasinoEnable();
        if (isOk) {
            //
            List<Long> retBonus = Bonus.receiveListItem(mUser, bonus, "casino_super_rotate");
            if (retBonus.isEmpty()) {
                addErrResponse();
                return;
            }
            //
            results.addAll(retBonus);
            builder.addAVector(CommonProto.getCommonVectorProto(results)).addAVector(CommonVector.newBuilder().build());
            addResponse(builder.build());
            EventMonitor.getInstance().addDropItem(user.getId(), EventType.SUPER_CASINO, numberRotate);
            CfgDailyQuest.addQuest(mUser, QuestType.SUPER_CASINO, numberRotate);
            CfgMathPuzzlesChallenge.addChallenge(this, mUser , MathPuzzleDropType.USE_LUCKY_SPIN , numberRotate);
        } else {
            Bonus.receiveListItem(mUser, "casino_super_rotate_fail", Bonus.viewMaterial(MaterialType.CHIP, feeRotate));
            addErrResponse();
        }
    }

    void record() {
        addResponse(CommonProto.getCommonVectorProto(null, Arrays.asList("record")));
    }

    void refresh() {
        int feeRefresh = casino.checkFreeRefresh() ? 0 : CfgCasinoSuper.config.feeRefresh[0];

        if (mUser.getUser().getGem() < feeRefresh) {
            addErrResponse(getLang(Lang.err_not_enough_gem));
            return;
        }

        if (casino.checkFreeRefresh())
            casino.setTimeFreeRefresh(System.currentTimeMillis());
        casino.refreshCasino();
        casino.setCasinoBonus(CfgCasinoSuper.getCasinoBonusShow().toString());

        boolean isOk = casino.update();
        if (isOk) {
            JsonArray bonus = GsonUtil.parseFromListLong(Bonus.view(Bonus.BONUS_GEM, -feeRefresh));
            List<Long> lstResult = Bonus.receiveListItem(mUser, bonus, "casino_super_refresh");
            if (lstResult.isEmpty()) {
                addErrResponse();
                return;
            }
            addResponse(CommonProto.getCommonVectorProto(Arrays.asList(mUser.getUser().getGem())));
            status();
        } else addErrResponse();
    }

    //end region casino

    //region Logic
    UserCasinoSuperEntity getUserCasino() {
        UserCasinoSuperEntity casino = (UserCasinoSuperEntity) mUser.getCache().get(KEY_DATA);
        if (casino == null) {
            casino = dbGetUserCasino();
            if (casino == null) {
                addErrResponse();
                return null;
            }
            mUser.getCache().set(KEY_DATA, casino);
        }
//        if (casino != null) {
//            casino.checkRefresh();
//        }
        return casino;
    }

    List<Long> parseListBonusCasino(String listBonus) {
        JsonArray arrBonus = GsonUtil.parseJsonArray(listBonus);
        List<Long> lstLong = new ArrayList<>();

        for (int i = 0; i < arrBonus.size(); i++) {
            for (int j = 0; j < arrBonus.get(i).getAsJsonArray().size(); j++) {
                lstLong.add(arrBonus.get(i).getAsJsonArray().get(j).getAsLong());
            }
        }
        return lstLong;
    }
    //endregion

    //region Database
    UserCasinoSuperEntity dbGetUserCasino() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserCasinoSuperEntity> aCasino = session.createNativeQuery("select * from user_casino_super where user_id=" + user.getId(), UserCasinoSuperEntity.class).getResultList();
            if (aCasino.isEmpty()) {
                UserCasinoSuperEntity userCasino = new UserCasinoSuperEntity(user.getId());
                session.getTransaction().begin();
                session.persist(userCasino);
                session.getTransaction().commit();
                return userCasino;
            }
            return aCasino.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

}
