package monster.dao;

import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import monster.config.CfgClan;
import monster.config.CfgMsgTemplate;
import monster.config.penum.ClanPosition;
import monster.dao.mapping.ClanEntity;
import monster.dao.mapping.UserClanEntity;
import monster.dao.mapping.UserEntity;

import java.util.List;

@SuppressWarnings("unchecked")
public class ClanDAO extends AbstractDAO {


    public List<UserClanEntity> getTopDonate(int clanId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select c.* from user u, user_clan c where u.clan=" + clanId + " and u.id=c.user_id order by mill_donate desc", UserClanEntity.class).getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public ClanEntity getClan(String name) {
        return DBJPA.getUnique("clan", ClanEntity.class, "name", name);
    }

    public ClanEntity getClan(int clanId) {
        return DBJPA.getUnique("clan", ClanEntity.class, "id", clanId);
    }

    public int getClanAvatar(int clanId) {
        ClanEntity clan = getClan(clanId);
        return clan == null ? 0 : clan.getFlag();
    }

    public List<ClanEntity> topClan(int server) {
        return DBJPA.getList("select * from clan where server=:server order by trophy desc limit 0,100", ClanEntity.class, "server", server);
    }

    public List<UserEntity> getListMember(int clanId) {
        return DBJPA.getList("select * from user where clan=:clanId order by level desc", UserEntity.class, "clanId", clanId);
    }

    public boolean updateClanTrophy(int clanId, int trophy) {
        return DBJPA.update("update clan set trophy=" + trophy + " where id=" + clanId);
    }

    public boolean updateClanJoinRule(int clanId, int rule, int trophy) {
        return DBJPA.update("update clan set join_rule=" + rule + ",join_trophy=" + trophy + " where id=" + clanId);
    }

    public List<ClanEntity> suggestClan(int server) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            //            Query query = session.createNativeQuery(String.format("select * from clan where server=%s and member<%s ORDER BY RAND() limit 5", server, CfgClan.config.maxMember), ClanEntity.class);
            Query query = session.createNativeQuery(String.format("select * from clan where server=%s AND number_member>0  ORDER BY RAND() limit 5", server, CfgClan.config.maxMember), ClanEntity.class);
            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public List<ClanEntity> findClan(int server, String clanName) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            Query query = session.createNativeQuery("select * from clan where server=" + server + " and name like :likedName", ClanEntity.class);
            query.setParameter("likedName", "%" + clanName + "%");
            return query.getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public int createClan(UserEntity user, String clanName, int gem, String status, int avatar, int joinRule, int joinTropy) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            //            String[] msg = CfgMsgTemplate.tplClanCreate(clanName);
            ClanEntity clan = new ClanEntity(clanName, user, status, avatar, joinRule, joinTropy);
            clan.setServer(user.getServer());
            session.persist(clan);

            Query query = session.createNativeQuery("update user set gem=gem-" + gem + ", clan=" + clan.getId() + ", clan_position=:clanPosition, clan_name=:clanName where id=" + user.getId());
            query.setParameter("clanName", clanName);
            query.setParameter("clanPosition", ClanPosition.LEADER.value);
            query.executeUpdate();

            session.getTransaction().commit();
            return clan.getId();
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public boolean acceptMember(UserEntity user, ClanEntity clan) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            Query tmp = session.createNativeQuery("update user set clan=" + clan.getId() + ", clan_name=?, clan_join=now() where id=" + user.getId());
            tmp.setParameter(0, clan.getName());
            tmp.executeUpdate();

            session.getTransaction().commit();
            updateClanLog(clan.getId(), addClanLog(clan, CfgMsgTemplate.tplClanJoin(user.getName())));
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public ClanEntity updateClanLog(int clanId, String log) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update clan set activity_log=? where id=" + clanId);
            query.setParameter(0, log);
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    //    public boolean hasCommonBonus(int clanId) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            String sql = "select count(*) from clan_battle_bonus where clan_id=" + clanId + " and date_created > date_add(now(), interval -2 day);";
    //            return Integer.parseInt(session.createSQLQuery(sql).uniqueResult().toString()) > 0;
    //        } catch (Exception ex) {
    //            Logs.error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return false;
    //    }
    //
    public boolean promote(long userId, int myPosition, UserEntity promoteUser, int newPosition) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user set clan_position=" + newPosition + " where id=" + promoteUser.getId()).executeUpdate();
            if (myPosition != -1) {
                session.createNativeQuery("update user set clan_position=" + myPosition + " where id=" + userId).executeUpdate();
            }
            if (ClanPosition.isLeader(newPosition)) {
                Query query = session.createNativeQuery("update clan set master=:master, master_name=:masterName, master_id=:masterId where id=:id");
                query.setParameter("id", promoteUser.getClan());
                query.setParameter("masterId", promoteUser.getId());
                query.setParameter("masterName", promoteUser.getName());
                query.setParameter("master", promoteUser.getUsername());
                query.executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            closeSession(session);
        }
        return false;
    }

    //
    //    public static synchronized boolean increaseClanEvent(int clanId, int eventId, long eventIndex, int server, int count) {
    ////        System.out.println("increase event------>");
    //        Session session = null;
    //        try {
    //            if (clanId == 0) {
    //                return false;
    //            }
    //            int index = (int) (eventIndex / (1000 * 60));
    //            if (selectClanEvent(clanId, eventId, index, server).size() > 0) {
    //                session = HibernateUtil.getSessionFactory().openSession();
    //                session.beginTransaction();
    //                session.createSQLQuery("update clan_event set value = value+ " + count + " where clan_id=" + clanId + " and event_index = " + index + " and event_id =" + eventId + " and server_id = " + server).executeUpdate();
    //                session.getTransaction().commit();
    //                return true;
    //            } else {
    //                return createClanEvent(clanId, eventId, index, server, count);
    //            }
    //        } catch (Exception ex) {
    //            Logs.error(Util.exToString(ex));
    //        } finally {
    //            try {
    //                if (session != null) {
    //                    session.close();
    //                }
    //            } catch (Exception ex) {
    //            }
    //        }
    //        return false;
    //    }
    //
    //    private static List<ClanEventEntity> selectClanEvent(int clanId, int eventId, int eventIndex, int server) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            SQLQuery query = session.createSQLQuery("select * from clan_event where server_id=" + server + " and clan_id =" + clanId + " and event_id =" + eventId + " and event_index =" + eventIndex).addEntity(ClanEventEntity.class);
    //            return query.list();
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            try {
    //                if (session != null) {
    //                    session.close();
    //                }
    //            } catch (Exception ex) {
    //            }
    //        }
    //        return new ArrayList<>();
    //    }
    //
    //    public static boolean createClanEvent(int clanId, int eventId, int eventIndex, int server, int count) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //
    ////            String[] msg = CfgMsgTemplate.tplClanCreate(clanName, String.valueOf(user.getTrophy()));
    //            ClanEventEntity clanEvent = new ClanEventEntity(clanId, eventId, server, eventIndex, 0, count, 0, new Date(), false);
    //            session.save(clanEvent);
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            try {
    //                if (session != null) {
    //                    session.close();
    //                }
    //            } catch (Exception ex) {
    //            }
    //        }
    //        return false;
    //    }
    //
    //    public boolean destroyClan(int clanId) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            session.createSQLQuery("update user set clan=0, clan_name='',soul_donated=0 where clan=" + clanId).executeUpdate();
    //            session.createSQLQuery("delete from clan where id=" + clanId).executeUpdate();
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return false;
    //    }
    //
    //    public static synchronized boolean addLevelClan(Clan clan, long diem, int userId, int type, long number, User user) {
    ////        long[] lv = {0, 100000, 300000, 600000, 1000000};
    ////        lv= C
    //        int id = clan.getId();
    //        List<Long> lv = new ArrayList<Long>();
    //        lv = GameCfgClan.lstUpdateClan;
    ////        lv.add(10000000000L);
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            long exp = Long.parseLong(session.createSQLQuery("select exp from clan where id=" + id).uniqueResult().toString());
    //            int level = Integer.parseInt(session.createSQLQuery("select level from clan where id=" + id).uniqueResult().toString());
    //            long diemBonus = 0;
    //            if (diem + exp > lv.get(level)) {
    //                level++;
    //                diemBonus = diem + exp - lv.get(level - 1);
    //                exp = diemBonus;
    //            } else {
    //                diemBonus = diem;
    //                exp = exp + diemBonus;
    //            }
    //            session.beginTransaction();
    //            session.createSQLQuery("update clan set exp=" + exp + ", level=" + level + " where id=" + id).executeUpdate();
    //            session.createSQLQuery("update user set clan_donated=clan_donated+" + diem + "  where id=" + userId).executeUpdate();
    //            switch (type) {
    //                case 0:
    //                    session.createSQLQuery("update user set silver=silver-" + number + "  where id=" + userId).executeUpdate();
    //                    break;
    //                case 1:
    //                    session.createSQLQuery("update user set gold=gold-" + number + "  where id=" + userId).executeUpdate();
    //                    break;
    //                case 2:
    //                    session.createSQLQuery("update user set trophy=trophy-" + number + "  where id=" + userId).executeUpdate();
    //                    break;
    //                default:
    //                    break;
    //            }
    //            session.getTransaction().commit();
    //            if (diem > 0) {
    //                EventConfig.updateData(EventConfig.TYPE_MLL, (int) diem, user, Util.getFirstHero(user.getTeamRequired()));
    //            }
    //            clan.setExp(exp);
    //            clan.setLevel(level);
    //            MyCache.getInstance().set("CLANCACHE" + id, clan);
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //            return false;
    //        } finally {
    //            try {
    //                if (session != null) {
    //                    session.close();
    //                }
    //            } catch (Exception ex) {
    //
    //            }
    //
    //        }
    //
    //    }
    //
    //    public boolean addKungfuUser(int id, String kungfu) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            session.createSQLQuery("update user set kungfu_clan='" + kungfu + "'  where id=" + id).executeUpdate();
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //            return false;
    //        } finally {
    //            closeSession(session);
    //        }
    //    }
    //
    //    public boolean updateConghienUser(int id, int number) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            session.createSQLQuery("update user set clan_donated=clan_donated-" + number + "  where id=" + id).executeUpdate();
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //            return false;
    //        } finally {
    //            closeSession(session);
    //        }
    //    }
    //
    //    public boolean leaveClan(int id) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            session.createSQLQuery("update user set clan_donated=0  where id=" + id).executeUpdate();
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //            return false;
    //        } finally {
    //            closeSession(session);
    //        }
    //    }
    //
    //    public boolean updateConghienClan(int id, long number) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            session.createSQLQuery("update clan set exp=exp-" + number + "  where id=" + id).executeUpdate();
    //            session.getTransaction().commit();
    //            Clan clan = getClan(id);
    //            if (clan != null) {
    //                MyCache.getInstance().set("CLANCACHE" + id, clan);
    //            }
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //            return false;
    //        } finally {
    //            closeSession(session);
    //        }
    //    }
    //
    //    public boolean addKungfuClan(int id, String kungfu) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            session.createSQLQuery("update clan set kungfu='" + kungfu + "'  where id=" + id).executeUpdate();
    //            session.getTransaction().commit();
    //            Clan clan = (Clan) MyCache.getInstance().get("CLANCACHE" + id);
    //            if (clan != null) {
    //                clan.setKungfu(kungfu);
    //                MyCache.getInstance().set("CLANCACHE" + id, clan);
    //
    //            }
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //            return false;
    //        } finally {
    //            closeSession(session);
    //        }
    //    }
    //
    //    public int getClanTrophy(int clanId) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            int sum = Integer.parseInt(session.createSQLQuery("select sum(trophy) from user where clan=" + clanId).uniqueResult().toString());
    //            return sum;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return -1;
    //    }
    //
    //    public boolean updateClanStatus(int clanId, String status) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            SQLQuery query = session.createSQLQuery("update clan set status=? where id=" + clanId);
    //            query.setString(0, status);
    //            query.executeUpdate();
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return false;
    //    }
    //
    //
    //
    public int removeMember(ClanEntity clan, UserEntity user, boolean isKick) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user set clan=0, clan_name='', clan_position=0 where id=" + user.getId()).executeUpdate();
            session.getTransaction().commit();
            user.setClan(0);
            user.setClanName("");
            user.setClanPosition(0);

            // doi lai tat ca nguoi choi phai cho 2 ngay moi duoc join clan moi
            //            String key = ClanHandler.KEY_CLAN_LEAVE + user.getId();
            //            JCache.getInstance().setValue(key, String.valueOf(System.currentTimeMillis()), JCache.EXPIRE_1H * 12);
            //            MCache.getInstance().set(key, System.currentTimeMillis(), MCache.EXPIRE_1H * 12);

            return 0;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public int acceptMemberReq(ClanEntity clan, UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            Query tmp = session.createNativeQuery("update user set clan=" + clan.getId() + ", clan_name=:clanName, clan_position=0, clan_join=now(), clan_position=0 where id=" + user.getId());
            tmp.setParameter("clanName", clan.getName());
            tmp.executeUpdate();

            session.getTransaction().commit();
            return 0;
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    //    public boolean updateClanReq(int clanId, String req) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            session.beginTransaction();
    //            SQLQuery query = session.createSQLQuery("update clan set request=? where id=" + clanId);
    //            query.setString(0, req);
    //            query.executeUpdate();
    //            session.getTransaction().commit();
    //            return true;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return false;
    //    }
    //
    //
    //
    //
    //
    //    public int getClanIcon(int clanId) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            Integer icon = (Integer) session.createSQLQuery("select rank from clan where id=" + clanId).addScalar("rank", IntegerType.INSTANCE).uniqueResult();
    //            if (icon == null) icon = -1;
    //            return icon;
    ////            int icon = Integer.parseInt(session.createSQLQuery("select rank from clan where id=" + clanId).uniqueResult().toString());
    ////            return icon;
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return -1;
    //    }
    //
    //    private void closeSession(Session session) {
    //        try {
    //            if (session != null) {
    //                session.close();
    //            }
    //        } catch (Exception ex) {
    //        }
    //    }
    //
    //    public Clan getClanByScreenname(String username) {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getSessionFactory().openSession();
    //            SQLQuery query = session.createSQLQuery("select * from clan where master=?");
    //            query.setString(0, username);
    //            return (Clan) query.addEntity(Clan.class).uniqueResult();
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return null;
    //    }
    //
    //    public String lockClanEventFormation() {
    //        Session session = null;
    //        try {
    //            session = HibernateUtil.getMainSessionFactory().openSession();
    //            session.beginTransaction();
    //            SQLQuery query = session.createSQLQuery("select c.value from config c where c.key=?");
    //            query.setString(0, "clan_event_formation_lock");
    //            return query.uniqueResult().toString();
    //        } catch (Exception ex) {
    //            ex.printStackTrace();
    //            getLogger().error(Util.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return null;
    //    }

    public String addClanLog(ClanEntity clan, String msg) {
        JsonArray arr = new JsonParser().parse(clan.getActivityLog()).getAsJsonArray();
        arr.add(msg);
        if (arr.size() > 10) {
            arr.remove(0);
        }
        clan.setActivityLog(arr.toString());
        return arr.toString();
    }

}
