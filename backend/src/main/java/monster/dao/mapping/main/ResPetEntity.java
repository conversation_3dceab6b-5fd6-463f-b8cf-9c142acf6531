package monster.dao.mapping.main;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import grep.helper.GsonUtil;
import lombok.*;
import monster.config.lang.Lang;
import monster.service.battle.dependence.Point;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "res_pet")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResPetEntity implements java.io.Serializable {

    @Id
    @Getter
    private int id;
    private String name, nameEn;
    @Getter
    private int feeUnlock, activeSkill;
    private int scaleAttack, scaleHp, scaleSpeed;
    private int scaleAttack2, scaleHp2, scaleSpeed2;
    @Getter
    private String passive1, passive2, passive3, passive4;

    public int getScaleAttack(int level) {
        return scaleAttack * Math.min(60, level) + scaleAttack2 * Math.max(0, level - 60);
    }

    public int getScaleHp(int level) {
        return scaleHp * Math.min(60, level) + scaleHp2 * Math.max(0, level - 60);
    }

    public int getScaleSpeed(int level) {
        return scaleSpeed * Math.min(60, level) + scaleSpeed2 * Math.max(0, level - 60);
    }

    public void init() {
        arrPassive = new JsonArray[4];
        arrPassive[0] = GsonUtil.parseJsonArray(this.passive1);
        arrPassive[1] = GsonUtil.parseJsonArray(this.passive2);
        arrPassive[2] = GsonUtil.parseJsonArray(this.passive3);
        arrPassive[3] = GsonUtil.parseJsonArray(this.passive4);
    }

    public String getName(Lang lang) {
        return lang.isVi() ? name : nameEn;
    }

    @Transient
    private JsonArray[] arrPassive;

    private JsonArray getObjectPassive(int index, int level) {
        return arrPassive[index].get(level - 1).getAsJsonArray();
    }

    public Point getPoint(int passive1, int passive2, int passive3, int passive4) {
        Point point = new Point();
        List<Integer> aPassive = Arrays.asList(passive1, passive2, passive3, passive4);
        for (int index = 0; index < aPassive.size(); index++) {
            if (aPassive.get(index) > 0) {
                JsonArray arr = getObjectPassive(index, aPassive.get(index));
                for (int i = 0; i < arr.size(); i++) {
                    JsonObject obj = arr.get(i).getAsJsonObject();
                    int value = obj.get("num").getAsInt();
                    switch (obj.get("type").getAsString()) {
                        case "hp":
                            point.add(Point.HP, value);
                            break;
                        case "atk":
                            point.add(Point.ATTACK, value);
                            break;
                        case "spd":
                            point.add(Point.SPEED, value);
                            break;
                        case "armP":
                            point.add(Point.ARMOR, (int) (obj.get("num").getAsFloat() * 1000));
                            break;
                        case "brk":
                            point.add(Point.ARMOR_BREAK, value);
                            break;
                        case "hit":
                            point.add(Point.PRECISION, value);
                            break;
                        case "crit":
                            point.add(Point.CRIT, value);
                            break;
                        case "critTime":
                            point.add(Point.CRIT_DAMAGE, value);
                            break;
                        case "miss":
                            point.add(Point.BLOCK, value);
                            break;
                        case "sklP":
                            point.add(Point.SKILL_DAMAGE, value);
                            break;
                    }
                }
            }
        }
        return point;
    }
}
