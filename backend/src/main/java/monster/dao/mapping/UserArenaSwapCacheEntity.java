package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "user_arena_swap_cache")
@Data
@NoArgsConstructor
public class UserArenaSwapCacheEntity implements java.io.Serializable {

    @Id
    private int id;
    private int eventId, userId, hour;
    private int receive, cluster, physicalServer;
    @Column(name = "user_rank")
    private int rank;

    public UserArenaSwapCacheEntity(int eventId, int userId, int hour, int rank) {
        this.eventId = eventId;
        this.userId = userId;
        this.hour = hour;
        this.rank = rank;
        this.receive = 0;
    }
}
