package monster.dao.mapping;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgLabyrinth;
import monster.config.CfgServer;
import monster.config.penum.TeamType;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResRelicEntity;
import monster.object.LabyrinthHero;
import monster.object.LabyrinthMap;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.common.LabyrinthService;
import monster.service.monitor.FileData;
import monster.service.resource.ResHero;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_labyrinth")
@Data
@NoArgsConstructor
public class UserLabyrinthEntity implements Serializable {
    @Id
    private int userId, eventId;
    private int floor, posRow, posSlot, mapMode;
    private int stageLevel, stageMap;
    private int numberPlay, heroRevive;
    private String relic;
    // maps;
    // private String myHeroes; // [[idHero, heroId, star, level, percentHp, percentAnger]]
    private Date dateModified = new Date();
    private int maxFloor, maxRow, maxHardRow;

    @Transient
    List<List<LabyrinthMap>> aMap;
    @Transient
    Map<Long, LabyrinthHero> mHero;
    @Transient
    String myHeroes, maps;
    @Transient
    Pbmethod.PbHero addedSupportHero = null;

    public UserLabyrinthEntity(UserEntity user, int eventId) {
        this.userId = user.getId();
        this.eventId = eventId;
        this.numberPlay = 1;
        this.mapMode = 1;
        fileUpdateMyHeroes("");
        fileUpdateMaps("");
    }

    public int getMarketKey() {
        return eventId * 10 + floor;
    }

    public int getInputRow(int row) {
        return floor == 0 ? row : row + 10;
    }

    //region Main Logic
    public boolean genNewEvent(MyUser mUser, int stageLevel) {
        Map<Long, LabyrinthHero> mHero = new HashMap<>();
        for (UserHeroEntity hero : mUser.getResources().heroes) {
            if (hero.getLevel() >= 30) {
                mHero.put(hero.getId(), LabyrinthHero.builder().id(hero.getId()).percentHp(LabyrinthHero.MAX_PERCENT_HP).anger(100).build());
            }
        }
        String strMyHeroes = new Gson().toJson(mHero);
        String strMaps = genMap(mUser, mHero, CfgLabyrinth.mapModeEasy, 0); // gen lại map từ floor 0
        if (fileUpdateMaps(strMaps) && fileUpdateMyHeroes(strMyHeroes)) {
            this.floor = 0;
            this.posRow = 0;
            this.posSlot = 0;
            this.relic = "[]";
            this.mapMode = CfgLabyrinth.mapModeEasy;
            setAMap(null);
            setMHero(null);
            dbUpdate("floor", 0, "pos_row", 0, "pos_slot", 0, "map_mode", CfgLabyrinth.mapModeEasy, "relic", this.relic);
            return true;
        }
        return false;
    }

    /**
     * Gen lần đầu khi mới tạo map
     *
     * @param mUser
     * @param mHero
     * @return
     */
    public String genMap(MyUser mUser, Map<Long, LabyrinthHero> mHero, int mapMode, int floor) {
        //        int xBonus = 0, xPower = 0;
        //        if (mapMode == CfgLabyrinth.mapModeHard) {
        //            xBonus = CfgLabyrinth.config.hardModeXBonus;
        //            xPower = CfgLabyrinth.config.hardModeXPower;
        //        }
        //        JsonArray maps = CfgLabyrinth.getMap(floor, mapMode);
        //        List<List<LabyrinthMap>> aMap = new ArrayList<>();
        //        for (int index = 0; index < maps.size(); index++) {
        //            JsonArray arr = maps.get(index).getAsJsonArray();
        //            List<LabyrinthMap> map = new ArrayList<>();
        //            for (int i = 0; i < arr.size(); i++) {
        //                map.add(LabyrinthMap.builder().type(arr.get(i).getAsInt()).row(index).slotIndex(i).build());
        //            }
        //            aMap.add(map);
        //        }
        //        List<LabyrinthHero> aHero = mHero.values().stream().collect(Collectors.toList());
        //        LabyrinthHero supportCloneHero = getCloneSupportHero(mUser, aHero);
        //        List<UserTeamEntity> aUserTeam = dbGetUserTeam(mUser.getUser().getPower());
        //        if (aUserTeam.isEmpty()) aUserTeam.add(UserTeam.getFirstTeam());
        //        while (!aUserTeam.isEmpty() && aUserTeam.size() < 50) {
        //            aUserTeam.add(aUserTeam.get(new Random().nextInt(aUserTeam.size())));
        //        }
        //        Collections.sort(aUserTeam, Comparator.comparingLong(UserTeamEntity::getPower));
        //        aUserTeam.stream().filter(userTeamEntity -> userTeamEntity.battleTeam != null).forEach(userTeamEntity -> {
        //            for (HeroInfoEntity hero : userTeamEntity.battleTeam.getAHero()) {
        //                if (hero != null) hero.skin = 0;
        //            }
        //        });
        ////        for (int i = aUserTeam.size() - 1; i >= 1; i--) {
        ////            if (aUserTeam.get(i).userId == aUserTeam.get(i - 1).getUserId()) aUserTeam.remove(i);
        ////        }
        //        int index = 0;
        //        for (List<LabyrinthMap> labyrinthMaps : aMap) {
        //            for (LabyrinthMap map : labyrinthMaps) {
        //                LabyrinthMapType mapType = LabyrinthMapType.get(map.type);
        //                switch (mapType) {
        //                    case NORMAL_MONSTER:
        //                        UserTeamEntity uTeam = aUserTeam.get(index);
        //                        map.team = new Gson().fromJson(uTeam.data, BattleTeam.class);
        //                        map.team.increasePowerPercent(CfgLabyrinth.config.getXPower(floor, map.row) + xPower);
        //                        map.aBonus = Bonus.xBonusPercent(CfgLabyrinth.config.getLevelBonus(floor, map.row), xBonus);
        //                        if (index < aUserTeam.size() - 2) ;
        //                        index++;
        //                        break;
        //                }
        //            }
        //            for (LabyrinthMap map : labyrinthMaps) {
        //                LabyrinthMapType mapType = LabyrinthMapType.get(map.type);
        //                switch (mapType) {
        //                    case ELITE_MONSTER:
        //                        UserTeamEntity uTeam = aUserTeam.get(index);
        //                        map.team = new Gson().fromJson(uTeam.data, BattleTeam.class);
        //
        //                        map.team.increasePowerPercent(CfgLabyrinth.config.getXPower(floor, map.row) + xPower + 10);
        //                        map.aBonus = Bonus.xBonusPercent(CfgLabyrinth.config.getLevelBonus(floor, map.row), xBonus);
        //                        index++;
        //                        break;
        //                }
        //            }
        //            for (LabyrinthMap map : labyrinthMaps) {
        //                LabyrinthMapType mapType = LabyrinthMapType.get(map.type);
        //                switch (mapType) {
        ////                    case WAGON: { // tướng hỗ trợ
        ////                        map.aHero = toWagonHero(mUser, supportCloneHero);
        ////                        for (HeroInfoEntity hero : map.aHero) {
        ////                            reCalculateSpecialPoint(mUser, hero);
        ////                        }
        ////                        break;
        ////                    }
        //                    case BOSS:
        //                        UserTeamEntity uTeam = aUserTeam.get(index);
        //                        map.team = new Gson().fromJson(uTeam.data, BattleTeam.class);
        //                        for (HeroInfoEntity hero : map.team.getAHero()) {
        //                            if (hero != null) reCalculateSpecialPoint(mUser, hero);
        //                        }
        //                        map.team.increasePowerPercent(CfgLabyrinth.config.getXPower(floor, map.row) + xPower);
        //                        map.aBonus = Bonus.xBonusPercent(CfgLabyrinth.config.getLevelBonus(floor, map.row), xBonus);
        //                        index++;
        //                        break;
        ////                    case MINI_BOSS:
        ////                        map.team = CfgLabyrinth.getMiniBoss(mUser, supportCloneHero);
        ////                        map.team.increasePowerPercent(CfgLabyrinth.config.miniBossXPower + xPower);
        ////                        map.team.increaseHpPercent(50);
        ////
        ////                        map.aBonus = Bonus.xBonusPercent(CfgLabyrinth.config.getLevelBonus(floor, map.row), xBonus);
        ////                        map.aBonus.addAll(Bonus.xBonusPercent(CfgLabyrinth.config.getMiniBossBonus(), xBonus));
        ////                        break;
        //                }
        //            }
        //        }
        //        return new Gson().toJson(aMap);
        return null;
    }

    private List<HeroInfoEntity> toWagonHero(MyUser mUser, LabyrinthHero cloneHero) {
        List<HeroInfoEntity> aHero = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            if (cloneHero.id > 0) {
                UserHeroEntity uHero = mUser.getResources().getHero(cloneHero.id);
                aHero.add(uHero.toHeroInfo(1, 0));
            } else {
                aHero.add(cloneHero.hero.cloneHero());
            }
        }
        List<Integer> usedIds = new ArrayList<>();
        for (HeroInfoEntity hero : aHero) {
            ResHeroEntity resHero = ResHero.getHero(CfgLabyrinth.getCloneHeroId());
            while (usedIds.contains(resHero.getHero5Star())) {
                resHero = ResHero.getHero(CfgLabyrinth.getCloneHeroId());
            }
            usedIds.add(resHero.getHero5Star());

            if (hero.star <= 5) hero.heroId = resHero.getHero5Star();
            else if (hero.star >= 6 && hero.star <= 8) hero.heroId = resHero.getHero6Star();
            else hero.heroId = resHero.getHeroId10Star();
        }
        aHero.forEach(hero -> hero.skin = 0);
        return aHero;
    }

    private LabyrinthHero getCloneSupportHero(MyUser mUser, List<LabyrinthHero> aHero) {
        aHero.sort(Comparator.comparingLong(hero -> hero.getPower(mUser)));
        if (aHero.size() > 1) return aHero.get(aHero.size() - 2);
        return aHero.get(0);
    }

    public boolean isFinalMap() {
        return floor + 1 == CfgLabyrinth.maxFloor && posRow == getListMap().size() - 2;
    }

    private void reCalculateSpecialPoint(MyUser mUser, HeroInfoEntity hero) {
        UserHeroEntity uHero = new UserHeroEntity(mUser.getUser().getId(), hero);
        uHero.calculatePointHero(mUser);
        hero.point.copySpecialValue(uHero.getPoint());
    }
    //endregion

    //region Getter Setter
    public List<Integer> getListRelic() {
        if (StringHelper.isEmpty(relic) || relic.length() <= 2) relic = "[]";
        return GsonUtil.strToListInt(relic);
    }

    public int getBuffFountain() {
        int totalBuff = 0;
        List<Integer> relicIds = getListRelic();
        for (int i = 0; i < relicIds.size(); i++) {
            ResRelicEntity resRelic = CfgLabyrinth.getRelic(relicIds.get(i));
            if (resRelic != null) totalBuff += resRelic.getBuffFountain();
        }
        return totalBuff;
    }

    public int getBuffHpAfterBattle() {
        int totalBuff = 0;
        List<Integer> relicIds = getListRelic();
        for (int i = 0; i < relicIds.size(); i++) {
            ResRelicEntity resRelic = CfgLabyrinth.getRelic(relicIds.get(i));
            if (resRelic != null) totalBuff += resRelic.getBuffAfterBattle();
        }
        return totalBuff;
    }

    public Map<Long, LabyrinthHero> getMHero() {
        if (mHero == null) {
            if (StringHelper.isEmpty(myHeroes) || myHeroes.length() <= 2) myHeroes = "[]";
            mHero = new Gson().fromJson(myHeroes, new TypeToken<Map<Long, LabyrinthHero>>() {
            }.getType());
        }
        return mHero;
    }

    public List<List<LabyrinthMap>> getListMap() {
        if (aMap == null) {
            if (StringHelper.isEmpty(maps) || maps.length() <= 2) maps = "[]";
            aMap = new Gson().fromJson(maps, new TypeToken<List<List<LabyrinthMap>>>() {
            }.getType());
        }
        return aMap;
    }

    public LabyrinthMap getMap(int row, int slotIndex) {
        List<List<LabyrinthMap>> aMap = getListMap();
        if (row < aMap.size()) {
            List<LabyrinthMap> map = aMap.get(row);
            if (slotIndex < map.size()) return map.get(slotIndex);
        }
        return null;
    }

    public LabyrinthHero getMyHero(long idHero) {
        return getMHero().get(idHero);
    }
    //endregion

    //region proto
    public Pbmethod.PbLabyrinth toProto(MyUser mUser) {
        Pbmethod.PbLabyrinth.Builder builder = Pbmethod.PbLabyrinth.newBuilder();
        builder.setInfo(Pbmethod.CommonVector.newBuilder().addALong(heroRevive == 0 ? 100 : -1));
        builder.setMapMode(mapMode == CfgLabyrinth.mapModeEasy ? 0 : 1);
        if (floor == 0) builder.setFishingStatus(2);
        else builder.setFishingStatus(0);

        builder.setCountdown(CfgLabyrinth.getCountdown()).setFloor(floor + 1).setMaxFloor(CfgLabyrinth.maxFloor);
        getListRelic().forEach(relicId -> builder.addRelicId(relicId));
        for (LabyrinthHero hero : getMHero().values()) {
            protocol.Pbmethod.PbHero.Builder heroBuilder = hero.toProto();
            if (heroBuilder != null) builder.addAHero(heroBuilder);
        }
        //        fileUpdateMyHeroes(new Gson().toJson(getMHero()));
        List<List<LabyrinthMap>> aMap = getListMap();
        if (aMap.isEmpty()) Guice.getInstance(LabyrinthService.class).genMap(mUser);
        int startIndex = floor == 0 ? 0 : 10; // posRow <= 10 ? 0 : 11;
        int endIndex = floor == 0 ? 10 : 20; // posRow <= 11 ? 11 : aMap.size();
        for (int i = startIndex; i <= endIndex; i++) {
            for (LabyrinthMap labyrinthMap : aMap.get(i)) {
                if (labyrinthMap.generate == true && labyrinthMap.needRegen()) {
                    Guice.getInstance(LabyrinthService.class).genMapData(labyrinthMap, this);
                }
                builder.addMaps(labyrinthMap.toProto(floor, posRow, posSlot));
            }
        }
        return builder.build();
    }
    //endregion

    //region File Data
    public boolean getFileData() {
        maps = FileData.readFile(userId, "labyrinth", "maps");
        myHeroes = FileData.readFile(userId, "labyrinth", "my_heroes");
        return maps != null && myHeroes != null;
    }

    public boolean fileUpdateMyHeroes(String data) {
        if (FileData.writeFile(userId, "labyrinth", "my_heroes", data)) {
            this.myHeroes = data;
            return true;
        }
        return false;
    }

    public boolean fileUpdateMaps(String data) {
        if (FileData.writeFile(userId, "labyrinth", "maps", data)) {
            this.maps = data;
            return true;
        }
        return false;
    }
    //endregion

    //region Database
    public boolean dbUpdatePos(int row, int slotIndex) {
        if (dbUpdate("pos_row", row, "pos_slot", slotIndex)) {
            this.posRow = row;
            this.posSlot = slotIndex;
            return true;
        }
        return false;
    }

    public boolean dbUpdate(Object... values) {
        return dbUpdate(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean dbUpdate(List<Object> values) {
        return DBJPA.update("user_labyrinth", values, Arrays.asList("user_id", userId));
    }

    public List<UserTeamEntity> dbGetUserTeam(long power) {
        List<UserTeamEntity> aUserTeam = new ArrayList<>();
        int condition = CfgServer.isRealServer() ? 4 : 1;
        aUserTeam.addAll(DBJPA.getList("user_team", new ArrayList<>(), "where server_id>=" + condition + " and team_id=" + TeamType.ARENA_CRYSTAL_ATK.value + " and power>" + power + " order by power asc limit 25", UserTeamEntity.class));
        aUserTeam.addAll(DBJPA.getList("user_team", new ArrayList<>(), "where server_id>=" + condition + " and team_id=" + TeamType.ARENA_CRYSTAL_ATK.value + " and power<" + power + " order by power desc limit 25", UserTeamEntity.class));
        return aUserTeam;
    }
    //endregion

}