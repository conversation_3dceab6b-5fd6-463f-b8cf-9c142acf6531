package monster.dao.mapping;

import grep.helper.NumberUtil;
import lombok.Getter;
import lombok.Setter;
import monster.config.CfgMathPuzzlesChallenge;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Table(name = "user_math_puzzles_challenge")
@Entity
@Getter
@Setter
public class UserMathPuzzlesChallengeEntity  implements Serializable  {
    @Id
    int userId;
    @Id
    long eventId ;
    String challenge;
    String status;

    Date firstTimeLoginInDay;

    public UserMathPuzzlesChallengeEntity(){

    }

    public UserMathPuzzlesChallengeEntity(int userId , long eventId){
        int max = CfgMathPuzzlesChallenge.config.total;
        this.userId = userId;
        this.challenge = NumberUtil.genListInt(max , 0).toString();
        this.status = NumberUtil.genListInt(max , 0).toString();
        this.firstTimeLoginInDay = new Date();
        this.eventId = eventId;
    }


    public void resetDataNewDay(int userId) {
        int max = CfgMathPuzzlesChallenge.config.total;
        this.userId = userId;
        this.challenge = NumberUtil.genListInt(max , 0).toString();
        this.status = NumberUtil.genListInt(max , 0).toString();
        this.firstTimeLoginInDay = new Date();
    }
}
