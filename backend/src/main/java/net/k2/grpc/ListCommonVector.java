// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: dtq.proto

package net.k2.grpc;

/**
 * Protobuf type {@code proto.ListCommonVector}
 */
public  final class ListCommonVector extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:proto.ListCommonVector)
    ListCommonVectorOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ListCommonVector.newBuilder() to construct.
  private ListCommonVector(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ListCommonVector() {
    aVector_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ListCommonVector(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
              aVector_ = new java.util.ArrayList<net.k2.grpc.CommonVector>();
              mutable_bitField0_ |= 0x00000001;
            }
            aVector_.add(
                input.readMessage(net.k2.grpc.CommonVector.parser(), extensionRegistry));
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
        aVector_ = java.util.Collections.unmodifiableList(aVector_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return net.k2.grpc.DtqOuterClass.internal_static_proto_ListCommonVector_descriptor;
  }

  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return net.k2.grpc.DtqOuterClass.internal_static_proto_ListCommonVector_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            net.k2.grpc.ListCommonVector.class, net.k2.grpc.ListCommonVector.Builder.class);
  }

  public static final int AVECTOR_FIELD_NUMBER = 1;
  private java.util.List<net.k2.grpc.CommonVector> aVector_;
  /**
   * <code>repeated .proto.CommonVector aVector = 1;</code>
   */
  public java.util.List<net.k2.grpc.CommonVector> getAVectorList() {
    return aVector_;
  }
  /**
   * <code>repeated .proto.CommonVector aVector = 1;</code>
   */
  public java.util.List<? extends net.k2.grpc.CommonVectorOrBuilder> 
      getAVectorOrBuilderList() {
    return aVector_;
  }
  /**
   * <code>repeated .proto.CommonVector aVector = 1;</code>
   */
  public int getAVectorCount() {
    return aVector_.size();
  }
  /**
   * <code>repeated .proto.CommonVector aVector = 1;</code>
   */
  public net.k2.grpc.CommonVector getAVector(int index) {
    return aVector_.get(index);
  }
  /**
   * <code>repeated .proto.CommonVector aVector = 1;</code>
   */
  public net.k2.grpc.CommonVectorOrBuilder getAVectorOrBuilder(
      int index) {
    return aVector_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < aVector_.size(); i++) {
      output.writeMessage(1, aVector_.get(i));
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < aVector_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, aVector_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof net.k2.grpc.ListCommonVector)) {
      return super.equals(obj);
    }
    net.k2.grpc.ListCommonVector other = (net.k2.grpc.ListCommonVector) obj;

    boolean result = true;
    result = result && getAVectorList()
        .equals(other.getAVectorList());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getAVectorCount() > 0) {
      hash = (37 * hash) + AVECTOR_FIELD_NUMBER;
      hash = (53 * hash) + getAVectorList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static net.k2.grpc.ListCommonVector parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static net.k2.grpc.ListCommonVector parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static net.k2.grpc.ListCommonVector parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static net.k2.grpc.ListCommonVector parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(net.k2.grpc.ListCommonVector prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code proto.ListCommonVector}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:proto.ListCommonVector)
      net.k2.grpc.ListCommonVectorOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return net.k2.grpc.DtqOuterClass.internal_static_proto_ListCommonVector_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return net.k2.grpc.DtqOuterClass.internal_static_proto_ListCommonVector_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              net.k2.grpc.ListCommonVector.class, net.k2.grpc.ListCommonVector.Builder.class);
    }

    // Construct using net.k2.grpc.ListCommonVector.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAVectorFieldBuilder();
      }
    }
    public Builder clear() {
      super.clear();
      if (aVectorBuilder_ == null) {
        aVector_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        aVectorBuilder_.clear();
      }
      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return net.k2.grpc.DtqOuterClass.internal_static_proto_ListCommonVector_descriptor;
    }

    public net.k2.grpc.ListCommonVector getDefaultInstanceForType() {
      return net.k2.grpc.ListCommonVector.getDefaultInstance();
    }

    public net.k2.grpc.ListCommonVector build() {
      net.k2.grpc.ListCommonVector result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public net.k2.grpc.ListCommonVector buildPartial() {
      net.k2.grpc.ListCommonVector result = new net.k2.grpc.ListCommonVector(this);
      int from_bitField0_ = bitField0_;
      if (aVectorBuilder_ == null) {
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          aVector_ = java.util.Collections.unmodifiableList(aVector_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.aVector_ = aVector_;
      } else {
        result.aVector_ = aVectorBuilder_.build();
      }
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof net.k2.grpc.ListCommonVector) {
        return mergeFrom((net.k2.grpc.ListCommonVector)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(net.k2.grpc.ListCommonVector other) {
      if (other == net.k2.grpc.ListCommonVector.getDefaultInstance()) return this;
      if (aVectorBuilder_ == null) {
        if (!other.aVector_.isEmpty()) {
          if (aVector_.isEmpty()) {
            aVector_ = other.aVector_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAVectorIsMutable();
            aVector_.addAll(other.aVector_);
          }
          onChanged();
        }
      } else {
        if (!other.aVector_.isEmpty()) {
          if (aVectorBuilder_.isEmpty()) {
            aVectorBuilder_.dispose();
            aVectorBuilder_ = null;
            aVector_ = other.aVector_;
            bitField0_ = (bitField0_ & ~0x00000001);
            aVectorBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAVectorFieldBuilder() : null;
          } else {
            aVectorBuilder_.addAllMessages(other.aVector_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      net.k2.grpc.ListCommonVector parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (net.k2.grpc.ListCommonVector) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<net.k2.grpc.CommonVector> aVector_ =
      java.util.Collections.emptyList();
    private void ensureAVectorIsMutable() {
      if (!((bitField0_ & 0x00000001) == 0x00000001)) {
        aVector_ = new java.util.ArrayList<net.k2.grpc.CommonVector>(aVector_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        net.k2.grpc.CommonVector, net.k2.grpc.CommonVector.Builder, net.k2.grpc.CommonVectorOrBuilder> aVectorBuilder_;

    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public java.util.List<net.k2.grpc.CommonVector> getAVectorList() {
      if (aVectorBuilder_ == null) {
        return java.util.Collections.unmodifiableList(aVector_);
      } else {
        return aVectorBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public int getAVectorCount() {
      if (aVectorBuilder_ == null) {
        return aVector_.size();
      } else {
        return aVectorBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public net.k2.grpc.CommonVector getAVector(int index) {
      if (aVectorBuilder_ == null) {
        return aVector_.get(index);
      } else {
        return aVectorBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder setAVector(
        int index, net.k2.grpc.CommonVector value) {
      if (aVectorBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAVectorIsMutable();
        aVector_.set(index, value);
        onChanged();
      } else {
        aVectorBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder setAVector(
        int index, net.k2.grpc.CommonVector.Builder builderForValue) {
      if (aVectorBuilder_ == null) {
        ensureAVectorIsMutable();
        aVector_.set(index, builderForValue.build());
        onChanged();
      } else {
        aVectorBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder addAVector(net.k2.grpc.CommonVector value) {
      if (aVectorBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAVectorIsMutable();
        aVector_.add(value);
        onChanged();
      } else {
        aVectorBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder addAVector(
        int index, net.k2.grpc.CommonVector value) {
      if (aVectorBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAVectorIsMutable();
        aVector_.add(index, value);
        onChanged();
      } else {
        aVectorBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder addAVector(
        net.k2.grpc.CommonVector.Builder builderForValue) {
      if (aVectorBuilder_ == null) {
        ensureAVectorIsMutable();
        aVector_.add(builderForValue.build());
        onChanged();
      } else {
        aVectorBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder addAVector(
        int index, net.k2.grpc.CommonVector.Builder builderForValue) {
      if (aVectorBuilder_ == null) {
        ensureAVectorIsMutable();
        aVector_.add(index, builderForValue.build());
        onChanged();
      } else {
        aVectorBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder addAllAVector(
        java.lang.Iterable<? extends net.k2.grpc.CommonVector> values) {
      if (aVectorBuilder_ == null) {
        ensureAVectorIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aVector_);
        onChanged();
      } else {
        aVectorBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder clearAVector() {
      if (aVectorBuilder_ == null) {
        aVector_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        aVectorBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public Builder removeAVector(int index) {
      if (aVectorBuilder_ == null) {
        ensureAVectorIsMutable();
        aVector_.remove(index);
        onChanged();
      } else {
        aVectorBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public net.k2.grpc.CommonVector.Builder getAVectorBuilder(
        int index) {
      return getAVectorFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public net.k2.grpc.CommonVectorOrBuilder getAVectorOrBuilder(
        int index) {
      if (aVectorBuilder_ == null) {
        return aVector_.get(index);  } else {
        return aVectorBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public java.util.List<? extends net.k2.grpc.CommonVectorOrBuilder> 
         getAVectorOrBuilderList() {
      if (aVectorBuilder_ != null) {
        return aVectorBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(aVector_);
      }
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public net.k2.grpc.CommonVector.Builder addAVectorBuilder() {
      return getAVectorFieldBuilder().addBuilder(
          net.k2.grpc.CommonVector.getDefaultInstance());
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public net.k2.grpc.CommonVector.Builder addAVectorBuilder(
        int index) {
      return getAVectorFieldBuilder().addBuilder(
          index, net.k2.grpc.CommonVector.getDefaultInstance());
    }
    /**
     * <code>repeated .proto.CommonVector aVector = 1;</code>
     */
    public java.util.List<net.k2.grpc.CommonVector.Builder> 
         getAVectorBuilderList() {
      return getAVectorFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        net.k2.grpc.CommonVector, net.k2.grpc.CommonVector.Builder, net.k2.grpc.CommonVectorOrBuilder> 
        getAVectorFieldBuilder() {
      if (aVectorBuilder_ == null) {
        aVectorBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            net.k2.grpc.CommonVector, net.k2.grpc.CommonVector.Builder, net.k2.grpc.CommonVectorOrBuilder>(
                aVector_,
                ((bitField0_ & 0x00000001) == 0x00000001),
                getParentForChildren(),
                isClean());
        aVector_ = null;
      }
      return aVectorBuilder_;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:proto.ListCommonVector)
  }

  // @@protoc_insertion_point(class_scope:proto.ListCommonVector)
  private static final net.k2.grpc.ListCommonVector DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new net.k2.grpc.ListCommonVector();
  }

  public static net.k2.grpc.ListCommonVector getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ListCommonVector>
      PARSER = new com.google.protobuf.AbstractParser<ListCommonVector>() {
    public ListCommonVector parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ListCommonVector(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ListCommonVector> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ListCommonVector> getParserForType() {
    return PARSER;
  }

  public net.k2.grpc.ListCommonVector getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

