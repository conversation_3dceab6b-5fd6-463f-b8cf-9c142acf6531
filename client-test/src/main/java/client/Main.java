package client;

import client.game.IndentureTest;
import grep.helper.Filer;
import grep.helper.GsonUtil;
import grep.helper.HttpHelper;
import protocol.Pbmethod;

public class Main {

    public static ClientConfig clientConfig;

    public static void main(String[] args) throws Exception {
        clientConfig = GsonUtil.parse(ClientConfig.class, Filer.readFile("client-test/config.json"));

        Client client = new Client(clientConfig.account, clientConfig);
        if (client.loginById()) {
            client.loginToGameServer();
        }

        while (true) {
            Thread.sleep(500);
            if (client.living) { // login success to game server
                //                                new CandyBreakTest(client).test();
                new IndentureTest(client).test();
                //                new UserRequest(client).test();
                //                new ClanTest(client).test();
                //                new SnakewayTest(client).test();
                //                new Tower1Test(client).test();
                //                new ArenaTrial(client).test();
                //                new HeroSummon(client).test();
                //                new BallQuest(client).test();
                //                new Labyrinth(client).test();
                //                Thread.sleep(10000);
                break;
            }
        }
    }

    public static void sendRequest(protocol.Pbmethod.PbAction.Builder action) {
        try {
            byte[] responseData = HttpHelper.getByteContent("http://34.126.181.161:6667", protocol.Pbmethod.RequestData.newBuilder()
                    .setSession("").addActions(Pbmethod.PbAction.newBuilder().setActionId(80).build()).build().toByteArray());
            System.out.println("responseData = " + responseData);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
