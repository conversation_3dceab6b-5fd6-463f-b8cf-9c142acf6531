package client.entity;

import client.Client;
import grep.log.Logs;
import monster.protocol.CommonProto;
import monster.server.IAction;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.BlockingQueue;

public class ResponseHandler extends IAction implements Runnable {

    BlockingQueue<protocol.Pbmethod.ResponseData> queue;
    Client client;
    List<Integer> ignoresAction = Arrays.asList(ONLINE_BONUS_STATUS, QUESST2_DONE, CRYSTAL_SAVE_DEFENSE_TEAM,
            HERO_EQUIP_AUTO, USER_UPDATE_NOTIFY, HERO_TIER_UP, HERO_LIST, MATERIAL_LIST);

    public ResponseHandler(Client client, BlockingQueue<protocol.Pbmethod.ResponseData> queue) {
        this.client = client;
        this.queue = queue;
    }

    @Override
    public void run() {
        try {
            while (true) process();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process() throws Exception {
        protocol.Pbmethod.ResponseData actions = queue.take();
        for (protocol.Pbmethod.PbAction pbAction : actions.getAActionList()) {
            System.out.println("receive = " + pbAction.getActionId());
            switch (pbAction.getActionId()) {
                case LOGIN -> client.loginSuccess(pbAction.getData().toByteArray());
                case HERO_LIST -> client.receiveHero(pbAction.getData().toByteArray());
                case USER_NOTIFY -> System.out.println("USER_NOTIFY: " + CommonProto.parseListCommonVector(pbAction.getData().toByteArray()).getAVector(0).getALongList());
                case MSG_TOAST -> System.out.println("Toast message: " + CommonProto.parseCommonVector(pbAction.getData().toByteArray()).getAString(0));
                default -> {
                    if (!ignoresAction.contains(pbAction.getActionId())) {
                        if (client.currentTest != null) client.currentTest.receiveRequest(pbAction);
                        else Logs.debug(String.format("unknown action=%s", pbAction.getActionId()));
                    }
                }
            }
        }
    }

}
