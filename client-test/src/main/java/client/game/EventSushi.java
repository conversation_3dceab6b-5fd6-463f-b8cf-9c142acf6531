package client.game;

import client.Client;
import monster.protocol.CommonProto;
import protocol.Pbmethod;

public class Event<PERSON>ushi extends AbstractTest {

    public EventSushi(Client client) {
        super(client);
    }

    @Override
    public void test() {
        infoClanUser();
    }

    void infoClanUser() {
        sendRequest(Pbmethod.PbAction.newBuilder().setActionId(EVENT_SUSHI_INFO_USER_PARTY));
    }

    @Override
    public void receiveRequest(protocol.Pbmethod.PbAction pbAction) {
        switch (pbAction.getActionId()) {
            case EVENT_SUSHI_INFO_USER_PARTY -> {
                System.out.println(CommonProto.parseListCommonVector(pbAction.getData().toByteArray()));
            }
        }
    }
}
