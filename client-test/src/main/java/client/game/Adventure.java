package client.game;

import client.Client;
import monster.protocol.CommonProto;

public class Adventure extends AbstractTest{
    public Adventure(Client client) {
        super(client);
    }

    @Override
    public void test() {
        adventureStatus();
    }

    void adventureStatus() {
        protocol.Pbmethod.PbAction.Builder builder = protocol.Pbmethod.PbAction.newBuilder();
        builder.setActionId(ADVENTURE_AUTO_FILL);
        builder.setData(protocol.Pbmethod.CommonVector.newBuilder()
                .addALong(281)
                .build().toByteString());
        sendRequest(builder);
    }

    @Override
    public void receiveRequest(protocol.Pbmethod.PbAction pbAction) {
        System.out.println("receive actionId=" + pbAction.getActionId());
        switch (pbAction.getActionId()) {
            case ADVENTURE_STATUS->
                    System.out.println(CommonProto.parseListCommonVector(pbAction.getData().toByteArray()));
            case ADVENTURE_REFRESH, ADVENTURE_AUTO_FILL ->
                    System.out.println(CommonProto.parseCommonVector(pbAction.getData().toByteArray()));
        }

//        System.out.println(CommonProto.parseListCommonVector(pbAction.getData().toByteArray()));
    }
}
