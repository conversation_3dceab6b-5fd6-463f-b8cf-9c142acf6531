name: DeployMain

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    #if: contains(github.event.head_commit.message, 'deploy')
    steps:
      - name: Send message start
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid }}
          token: ${{ secrets.tele }}
          message: setup environment for build Jutsu (real)
      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.KEY }}
          known_hosts: 'just-a-placeholder-so-we-dont-get-errors'
      - name: Adding Known Hosts
        run: |
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU2 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU3 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU4 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU5 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU6 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU7 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU8 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT }} -H ${{ secrets.JUTSU9 }} >> ~/.ssh/known_hosts
      - uses: actions/checkout@v3
      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'temurin'
          cache: maven
      - name: Send message before build
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid }}
          token: ${{ secrets.tele }}
          message: setup done. build project. waits
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Send message after build
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid }}
          token: ${{ secrets.tele }}
          message: build done. clean up
      - name: Clean build
        run: rm -rf ./server-core/target/*.jar && rm -rf ./server-core/target/unwoven-*
      - name: Send message before deploy
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid }}
          token: ${{ secrets.tele }}
          message: clean up done. deploy
      - name: Backup old class
        run: ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU2 }} "./game/backup.sh"
      - name: Deploy with rsync
        run:  rsync -avh --delete -e "ssh -p ${{ secrets.PORT }}" ./server-core/target/ root@${{ secrets.JUTSU2 }}:/home/<USER>/data/game/target/
      - name: Restart Server
        run: |
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU2 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU3 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU4 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU5 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU6 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU7 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU8 }} "./fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT }} root@${{ secrets.JUTSU9 }} "./fromNFS.sh && ./rungame.sh"
      - name: Ending job
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid }}
          token: ${{ secrets.tele }}
          message: Job is successfully finished
