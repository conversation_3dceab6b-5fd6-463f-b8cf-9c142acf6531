name: Deploy<PERSON>ea

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    #if: contains(github.event.head_commit.message, 'deploy')
    steps:
      - name: Send message start
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid_sea }}
          token: ${{ secrets.tele }}
          message: setup environment for build Demon (real)
      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.KEY }}
          known_hosts: 'just-a-placeholder-so-we-dont-get-errors'
      - name: Adding Known Hosts
        run: |
          ssh-keyscan -p ${{ secrets.PORT_SEA }} -H ${{ secrets.SEA2 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT_SEA }} -H ${{ secrets.SEA3 }} >> ~/.ssh/known_hosts &
          ssh-keyscan -p ${{ secrets.PORT_SEA }} -H ${{ secrets.SEA4 }} >> ~/.ssh/known_hosts &
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: maven
      - name: Send message before build
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid_sea }}
          token: ${{ secrets.tele }}
          message: setup done. build project. waits
      - name: Build with Maven
        run: mvn -B package --file pom.xml
      - name: Send message after build
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid_sea }}
          token: ${{ secrets.tele }}
          message: build done. clean up
      - name: Clean build
        run: rm -rf ./server-core/target/*.jar && rm -rf ./server-core/target/unwoven-*
      - name: Send message before deploy
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid_sea }}
          token: ${{ secrets.tele }}
          message: clean up done. deploy
      - name: Backup old class
        run: ssh -p ${{ secrets.PORT_SEA }} root@${{ secrets.SEA2 }} "./game/backup.sh"
      - name: Deploy with rsync
        run:  rsync -avh --delete -e "ssh -p ${{ secrets.PORT_SEA }}" ./server-core/target/ root@${{ secrets.SEA2 }}:/home/<USER>/data/game/target/
      - name: Restart Server
        run: |
          ssh -p ${{ secrets.PORT_SEA }} root@${{ secrets.SEA2 }} "./script/fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT_SEA }} root@${{ secrets.SEA3 }} "./script/fromNFS.sh && ./rungame.sh" &&
          ssh -p ${{ secrets.PORT_SEA }} root@${{ secrets.SEA4 }} "./script/fromNFS.sh && ./rungame.sh"
      - name: Ending job
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.chatid_sea }}
          token: ${{ secrets.tele }}
          message: Job is successfully finished
