<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">


    <modelVersion>4.0.0</modelVersion>
    <groupId>grep.monster</groupId>
    <artifactId>jjk</artifactId>
    <version>1.0-SNAPSHOT</version>

    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>
    </properties>

    <distributionManagement>
        <repository>
            <id>github</id>
            <name>Grepgame JJK package</name>
            <url>https://maven.pkg.github.com/grepgame/jjk</url>
        </repository>
    </distributionManagement>

    <modules>
        <module>server-core</module>
        <module>client-test</module>
        <module>server-tool</module>
    </modules>


</project>
