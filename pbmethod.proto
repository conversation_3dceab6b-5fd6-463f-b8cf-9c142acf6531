package pbdson;

option java_package = "protocol";

message RequestData {
  optional string session = 1;
  repeated PbAction actions = 2;
}

message ResponseData {
  repeated PbAction aAction = 2;
}

message PbAction {
  optional int32 actionId = 1;
  optional bytes data = 2;
}

message PbListBattleResult {
  repeated PbBattleResult aBattle = 1;
  optional int32 teamWin = 2;
  repeated int64 aBonus = 3;
  optional CommonVector attacker = 4;
  optional CommonVector defender = 5;
  optional int64 battleId = 6;
  optional int32 battleType = 7;
  optional CommonVector info = 8; // update thong tin khac //bonus
  repeated int64 aFakeBonus = 9; // fake
  optional int32 battleMode = 10;
  optional int64 attackerPower = 11;
  optional int64 defenderPower = 12;
  repeated int64 heroExp = 13;
  optional int32 battleStar = 14;
  optional int32 defLostHpPercent = 15;
  optional string battleRank = 16;
  optional bool replay = 17;
}

message PbBattleResult {
  optional int32 teamWin = 1;
  repeated PbBattleHero team1 = 2;
  repeated PbBattleHero team2 = 3;
  repeated PbHeroTurn aTurn = 4;
  optional int64 team1Point = 5;
  optional int64 team2Point = 6;
  optional int32 star = 7;
  optional int32 heroSilver = 8;
  optional int32 maxRound = 9;
  repeated int64 team1Statistic = 10;
  repeated int64 team2Statistic = 11;
  repeated int32 buffTeam = 12;
  optional PbBattleHero atkPet = 13;
  optional PbBattleHero defPet = 14;
  optional int64 team1Power = 15;
  optional int64 team2Power = 16;
}

message PbHeroTurn {
  optional int32 round = 1;
  optional int32 team = 2;
  optional int32 position = 3;
  optional int32 anger = 4;
  repeated PbHeroAttack aAttack = 5;
}

message PbHeroAttack {
  optional int32 team = 1;
  optional int32 position = 2;
  optional int32 skillIndex = 3;
  repeated PbTarget aTarget = 4;
  repeated ListPbEffect aListEffect = 5;
  optional int32 round = 6;
}

message PbTarget {
  optional int32 team = 1;
  optional int32 position = 2;
}

message PbEffect {
  optional int32 id = 1;
  optional int32 team = 2;
  optional int32 position = 3;
  optional int32 typeForClient = 4;
  repeated int64 info = 5;
}

message ListPbEffect {
  repeated PbEffect aEffect = 1;
}

message Login {
  optional string session = 1;
  optional PbUser user = 2;
  repeated int32 aHeroId = 3;
  repeated string aMsg = 4;
  repeated PbHero aHero = 5;
}

message PbListUser {
  repeated PbUser aUser = 1;
  optional string name = 2;
  optional int32 myRank = 3;
  optional PbUser myInfo = 4;
}

message PbUser {
  optional int32 id = 1;
  optional string username = 2;
  optional string name = 3;
  optional int64 gold = 4;
  optional int64 gem = 5;
  optional int64 ruby = 6;
  optional int64 power = 7;
  optional int64 exp = 8;
  optional int32 level = 9;
  repeated int32 avatar = 10;
  repeated int32 vipInfo = 11;
  repeated PbTeamHeroInfo team = 12;
  optional CommonVector clanInfo = 13;
  optional CommonVector info = 14;
  repeated int32 teamPet = 15;
  optional string facebook = 16;
  optional CommonVector userInfo = 17;
}

message PbTeamHeroInfo {
  optional int64 id = 1;
  optional int32 heroId = 2;
  optional int32 level = 3;
  optional int32 star = 4;
  repeated int64 point = 5;
  optional int32 skin = 6;
  optional int32 tier = 7;
}

message ClanMember {
  optional int32 id = 1;
  optional string name = 2;
  optional int32 trophy = 3;
  optional int32 warPoint = 4;
  optional int32 level = 5;
  optional int32 receiveTroop = 6;
  optional int32 sendTroop = 7;
  optional bool isNew = 8;
  optional int32 position = 9;
  optional int32 clanDonated = 23;
  optional string kungfuClan = 24;
  optional int64 lastAction = 25;
  optional int32 vip = 10;
  optional int32 rankTrophy = 11;
  repeated int32 avatar = 12;
  optional int32 curDonated = 13;
  repeated int32 avatarFrame = 14;
}

message ClanInfor {
  optional int32 id = 1;
  optional string name = 2;
  optional string owner = 3;
  optional string ownerName = 4;
  optional int32 numberMember = 5;
  optional int32 maxMember = 6;
  optional int32 trophy = 7;
  optional int32 joinRule = 8;
  optional int32 joinTrophy = 9;
  repeated ClanMember member = 10;
  optional string status = 11;
  repeated string activityLog = 12;
  optional int32 level = 13;
  optional int64 exp = 14;
  optional int64 maxExp = 15;
  repeated string info = 16;
  optional int32 avatar = 17;
}

message ClanList {
  repeated ClanInfor clan = 1;
}

message PbListHero {
  repeated PbHero aHero = 1;
}

message PbHero {
  optional int64 id = 1;
  optional int32 heroId = 2;
  optional int32 position = 3;
  repeated float point = 4;
  optional int32 level = 5;
  optional int32 tier = 6;
  optional int32 star = 7;
  repeated int32 aItem = 8;
  optional int32 treasure = 9;
  repeated int32 locked = 10;
  repeated int32 info = 11;
  repeated int32 skills = 12;
  optional int32 skinUse = 13;
  repeated PbEquipment equipments = 14;
  optional int32 starPart = 15;
  optional int64 exp = 16;
  optional bool isShare = 17;
  optional int32 artifactId = 18;
  optional int64 energyCore = 19;
  optional int64 artifactStar = 20;
  optional int64 power = 21;
  repeated int64 sigmaMark = 22;
  repeated int32 skins = 23;
  optional ListCommonVector vector = 24;
  optional int32 maxLevel = 25;
  repeated float addPercent = 26;
}

message  PbEquipment{
  optional  int32 level = 1;
  optional  int32 tier = 2;
  optional  int32 star = 3;
}

message PbItem {
  optional int32 id = 1;
  optional int32 itemId = 2;
  optional int64 heroId = 3;
}

message PbStoneSoul {
  optional int32 id = 1;
  optional int32 stoneId = 2;
  optional int64 heroId = 3;
  optional int32 level = 4;
  optional int32 number = 5;
}

message PbIdentifySlot {
  optional int32 id = 1;
  optional int32 level = 2;
  optional int32 point = 3;
  optional int32 count = 4;
  optional string desc = 5;
}

message ListActivity {
  repeated ActivityLog aLog = 1;
}

message PbListMail {
  repeated PbMail aMail = 1;
}

message PbMail {
  optional int32 id = 1;
  optional string title = 2;
  optional string message = 3;
  repeated int32 bonus = 4;
  optional int32 receive = 5;
  optional int64 time = 6;
  optional int32 senderId = 7;
}

message ActivityLog {
  optional int32 id = 1;
  optional int32 oppId = 2;
  optional string oppName = 3;
  optional string clanName = 4;
  repeated int32 hero = 5;
  optional int32 trophy = 6;
  optional int32 silver = 7;
  optional int32 hornor = 8;
  optional int32 revenge = 9;
  optional int32 type = 10;
  optional int64 time = 11;
  optional bool replay = 12;
  optional int32 aevent = 13;
  optional bool isWin = 14;
  optional int32 wanted = 15;
  optional int32 wantedgold = 16;
}

message ListMsg {
  repeated MsgLog aMsg = 1;
}

message MsgLog {
  optional int32 id = 1;
  optional string message = 2;
  optional int64 time = 3;
}

message ListTower {
  repeated Tower aTowr = 1;
}

message Tower {
  optional int32 id = 1;
  optional int32 type = 2;
  optional string username = 3;
  optional int32 level = 4;
  optional int64 silver = 5;
  optional int32 countdown = 6;
  repeated DefenseLog attacker = 7;
}

message DefenseLog {
  optional string name = 1;
  optional int64 silver = 2;
  optional string message = 3;
  optional int64 time = 4;
}

message ChatHistory {
  repeated ChatMsg aChat = 1;
  optional int32 number = 2;
}

message ChatMsg {
  optional int64 reqTime = 1;
  optional string message = 2;
  optional int32 type = 3;
  optional int32 msgId = 4;
  repeated int32 removeIds = 5;
  optional PbUser user = 6;
  optional CommonVector info = 7;
  optional PbTeamHeroInfo hero = 8;
}

message ListCommonVector {
  repeated CommonVector aVector = 1;
}

message BigListCommonVector {
  repeated ListCommonVector bigVector = 1;
}

message CommonVector {
  repeated int64 aLong = 1;
  repeated string aString = 2;
}

message ListTopBattle {
  repeated TopBattle aTop10 = 1;
  repeated TopBattle aTop = 2;
}

message TopBattle {
  optional int32 userId = 1;
  optional string name = 2;
  optional int32 rank = 3;
  optional int32 level = 4;
  optional int32 trophy = 5;
  optional int32 stealTrophy = 6;
}

message ListBonusProto {
  optional string title = 1;
  repeated BonusProto aBonus = 2;
}

message BonusProto {
  optional string title = 1;
  repeated int64 aDetail = 2;
}

message PbBattleHero {
  optional int64 heroId = 1;
  optional int32 pos = 2;
  repeated float point = 3;
  optional int32 level = 4;
  optional int32 skin = 5;
  optional int64 totalDamage = 6;
  optional int64 totalHeal = 7;
  optional int64 totalDef = 8;
  optional int32 star = 9;
  optional int32 tier = 10;
  optional int32 type = 11;
}

message PbBattlePrepare {
  optional int32 battleType = 1;
  repeated int32 ranInt = 2;
  repeated PbBattleHero team1 = 3;
  repeated PbBattleHero team2 = 4;
  optional int32 battleId = 5;
  optional int32 serviceVerify = 6;
  optional int32 attackId = 7;
  optional int32 oppId = 8;
  optional int32 numberRound = 9;
  repeated PbInput inputs = 10;
  optional int32 heroSilver = 11;
  optional int32 autoMode = 12;
}

message PbInput {
  optional int32 turn = 1;
  repeated int32 aPos = 2;
}

message PbBattleVerify {
  optional int32 battleId = 1;
  repeated PbInput aInput = 2;
}

message PbBattleReplay {
  optional PbBattlePrepare preparedBattle = 1;
  repeated PbInput aInput = 2;
}

message PbBattleInfo {
  optional int32 mode = 1; // chuan bi hay replay
  optional bytes data = 2;
}

message PbListBattleInfo {
  repeated PbBattleInfo aBattle = 1;
}

message PbBraveTrial {
  optional int32 stage = 1;
  optional int32 chestAward = 2; // 0 chưa được nhận, 1 được nhận rồi
  repeated PbStage aStage = 3;
  repeated PbHero aHero = 4;
  repeated int32 chestBonus = 5;
  repeated int32 badgeInfo = 6;
  repeated CommonVector aMarket = 7;
}

message PbStage {
  optional string username = 1;
  optional string name = 2;
  optional int32 vip = 3;
  optional int32 level = 4;
  optional int32 rankPvp = 5;
  repeated int32 awards = 6;
  optional int64 atkPower = 7;
  repeated PbHero aHero = 8;
  optional int32 serverId = 9;
  repeated int32 avatar = 10;
  optional string clanName = 11;
  optional int32 petId = 12;
  repeated int32 extraAwards = 13;
  optional CommonVector userInfo = 14;
}

message PbListWarClanUser {
  optional int32 clanId = 1;
  optional int32 memberRequired = 2;
  repeated PbWarClanUser aUser = 3;
}

message PbWarClanUser {
  optional int32 userId = 1;
  optional string name = 2;
  optional int32 level = 3;
  repeated PbTeamHeroInfo aHero = 4;
  optional int64 power = 5;
  optional int32 numberAttack = 6;
  repeated int32 avatar = 7;
  optional int32 clanPosition = 8;
  optional CommonVector atk1 = 9;
  optional CommonVector atk2 = 10;
  optional CommonVector mostAtk = 11;
  repeated int64 bonusAtk = 12;
  repeated int32 teamPet = 13;
  optional int32 status = 14;
  optional int32 teamStatus = 15;
}

// Labyrinth
message PbLabyrinth {
  optional int32 userId = 1;
  optional int64 countdown = 2;
  optional int32 floor = 3;
  optional int32 maxFloor = 4;
  repeated int32 relicId = 5;
  repeated PbHero aHero = 6;
  repeated PbLabyrinthStage maps = 7;
  optional int32 fishingStatus = 8;
  optional int32 mapMode = 9;
  optional CommonVector info = 10;
}

message PbLabyrinthStage {
  optional int32 type = 1;
  repeated int64 aBonus = 2;
  repeated PbHero aHero = 3;
  optional int32 petId = 4;
  optional int32 petTier = 5;
  optional bool playerPosition = 6;
  optional bool validPath = 7;
  optional int32 row = 8;
  optional int32 slotIndex = 9;
  optional int64 atkPower = 10;
  optional PbHero supportHero = 11;
  optional int32 bonusHero = 12;
}

message PbPot{
  repeated int32 relicId = 1;
  repeated PbHero listUserHero = 2;
  repeated PbPotMonster listMonster = 3;
}

message PbPotMonster{
  optional int32 objectId = 1;
  repeated PbHero listMonsterHero = 2;
  optional int32 leaderHero = 3;
  optional int32 leaderSkill = 4;
}