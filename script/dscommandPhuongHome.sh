#!/bin/bash
cd $(dirname $0)

function goto() {
  local label=$1
  cmd=$(sed -En "/^[[:space:]]*#[[:space:]]*$label:[[:space:]]*#/{:a;n;p;ba};" "$0")
  eval "$cmd"
  exit
}

devHost=demon1
clientPath=/mnt/d/grepcode/client/DragonClient
serverPath=/mnt/c/GrepGame/DevDs/server-core/target/
cmsPath=/mnt/d/grepcode/server/cmsapi/target/
servicePath=/mnt/d/grepcode/server/DragonService/target/
hostBackup=demon3
realHostName=('demon3' 'demon4' 'demon5' 'demon6' 'demon7' 'demon8' 'demon9' 'demon10')
devHostSea=demonsea1
hostBackupSea=demonsea2
realHostNameSea=('demonsea2' 'demonsea3' 'demonsea4')
devHostTw=demontw1
hostBackupTw=demontw2
realHostNameTw=('demontw2' 'demontw3' 'demontw4')

cmd=$1
echo "processing command '$cmd'"
runcmd=$2

case "$1" in
  nfs)
    goto 'nfs'
    ;;
  dev)
    goto 'dev'
    ;;
  devr)
    goto 'devr'
    ;;
  gametask)
    goto 'gametask'
    ;;
  real)
    goto 'real'
    ;;
  realr)
    goto 'realr'
    ;;
  reset)
    goto 'reset'
    ;;
  resetSea)
    goto 'resetSea'
    ;;
  stop)
    goto 'stop'
    ;;
  svn)
    goto 'svn'
    ;;
  test)
    goto 'test'
    ;;
  cms)
    goto 'cms'
    ;;
  backup)
    goto 'backup'
    ;;
  jstack)
    goto 'jstack'
    ;;
  command)
    goto 'command'
    ;;
  submit)
    goto 'submit'
    ;;
  service)
    goto 'service'
    ;;  
  searealr)
    goto 'searealr'
    ;;
  seatestr)
    goto 'seatestr'
    ;;
  twrealr)
    goto 'twrealr'
    ;;
  twtestr)
    goto 'twtestr'
    ;;
*)

echo $"Usage: $0 {reset|resetSea|stop|deploy|backup|jstack|command|test|dev|devr|svn|searealr|seatestr|twrealr|twtestr}"
exit 1
;;

esac
  #nfs:#
  echo "deploy server nfs"
  remotedir=$hostBackup:/home/<USER>/data/game/target
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  goto 'end'

  #dev:#
  echo "deploy server dev"
  remotedir=$devHost:/home/<USER>/test/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  goto 'end'

  #devr:#
  echo "deploy server dev and reset"
  remotedir=$devHost:/home/<USER>/test/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  ssh $devHost /home/<USER>/test/run.sh
  goto 'end'

  #gametask:#
  echo "deploy gametask"
  remotedir=demon2:/root/gametask/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  #ssh ft1 ./mashi/test/run.sh
  goto 'end'

  #real:#
  echo "deploy All server real"
  ssh $hostBackup "./game/backup.sh"
  remotedir=$hostBackup:/home/<USER>/data/game/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  for realHost in "${realHostName[@]}"; do
    echo "$realHost - sync from NFS and restart"
    (ssh $realHost "./fromNFS.sh") & done
  goto 'end'

  #realr:#
  echo "deploy All server real"
  ssh $hostBackup "./game/backup.sh"
  remotedir=$hostBackup:/home/<USER>/data/game/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  for realHost in "${realHostName[@]}"; do
    echo "$realHost - sync from NFS and restart"
    ssh $realHost $runcmd
    sleep 5
    (ssh $realHost "./fromNFS.sh" && ssh $realHost pkill -f gameserver && ssh $realHost ./rungame.sh) &	done
  goto 'end'

  #command2:#
  echo "running '$runcmd'"
  for realHost in "${realHostName[@]}"; do
  echo "===============Server $realHost======================"
  ssh $realHost $runcmd
  sleep 5
  done
  goto 'end'

  #searealr:#
  echo "deploy All server real"
  ssh $hostBackupSea "./game/backup.sh"
  remotedir=$hostBackupSea:/home/<USER>/data/game/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  for realHost in "${realHostNameSea[@]}"; do
    echo "$realHost - sync from NFS and restart"
    ssh $realHost $runcmd
    sleep 5
    (ssh $realHost "./fromNFS.sh" && ssh $realHost pkill -f gameserver && ssh $realHost ./rungame.sh) & done
  goto 'end'

  #seatestr:#
  echo "deploy and restart server test sea"
  remotedir=$devHostSea:/home/<USER>/test/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  ssh $devHostSea /home/<USER>/test/run.sh
  goto 'end'

  #twrealr:#
  echo "deploy All server real Tw"
  ssh $hostBackupTw "./game/backup.sh"
  remotedir=$hostBackupTw:/home/<USER>/data/game/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  for realHost in "${realHostNameTw[@]}"; do
    echo "$realHost - sync from NFS and restart"
    (ssh $realHost "./fromNFS.sh" && ssh $realHost pkill -f gameserver && ssh $realHost ./rungame.sh) & done
  goto 'end'
  
  #twtestr:#
  echo "deploy and restart server test Tw"
  remotedir=$devHostTw:/home/<USER>/test/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  ssh $devHostTw /home/<USER>/test/run.sh
  goto 'end'

  #submit:#
  echo "deploy server submit and reset"
  remotedir=ftsubmit:/home/<USER>/game/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  ssh ftsubmit ./game/run.sh
  goto 'end'

  #svn:#
  echo "update svn path=$clientPath"
  svn update --accept tf $clientPath --password 6JLPs40S195DkPqRePdw
  goto 'end'

  #service:#
  echo "deploy server DragonService"
  remotedir=ft2:/home/<USER>/gameservice/target/
  echo "rsync $servicePath -> $remotedir"
  rsync -avh --delete $servicePath $remotedir
  #ssh vm84 "./game/run.sh"
  goto 'end'

  #cms:#
  echo "deploy server real"
  remotedir=ft1:/home/<USER>/mashi/cmsapi/target
  echo "rsync $cmsPath -> $remotedir"
  rsync -avh --delete $cmsPath $remotedir
  #ssh vm84 "cd cmsapi2 && docker-compose restart"
  goto 'end'

  #test:#
  echo "deploy server test '$runcmd'"
  remotedir=grep:/home/<USER>/test/target/
  echo "rsync $serverPath -> $remotedir"
  rsync -avh --delete $serverPath $remotedir
  goto 'end'

  #command:#
  echo "running '$runcmd'"
  for realHost in "${realHostName[@]}"; do
    echo "===============Server $realHost======================"
    ssh $realHost $runcmd
  done
  goto 'end'

  #backup:#
  echo "backup"
  ssh $hostBackup "./game/backup.sh"
  goto 'end'

  #stop:#
  echo "stop all server"
  for realHost in "${realHostName[@]}"; do
    remotedir=$realHost:/home/<USER>/game/target/  
    echo "stopping $realHost"
    ssh $realHost pkill -f gameserver
  done
  goto 'end'

  #reset:#
  echo "stop all server"
  for realHost in "${realHostName[@]}"; do
    remotedir=$realHost:/home/<USER>/game/target/  
    echo "starting $realHost"
    ssh $realHost $runcmd
    sleep 5
    ssh $realHost ./rungame.sh
  done
  goto 'end'

  #resetSea:#
  echo "stop all server"
  for realHost in "${realHostNameSea[@]}"; do
    remotedir=$realHost:/home/<USER>/game/target/
    echo "starting $realHost"
    ssh $realHost ./rungame.sh
  done
  goto 'end'

  #jstack:#
  echo "profiler java process by jstack"
  for realHost in "${realHostName[@]}"; do
    echo "jstack $realHost"
    ssh $realHost ./jstack.sh
  done
  goto 'end'

  #end:#
  echo "done"
