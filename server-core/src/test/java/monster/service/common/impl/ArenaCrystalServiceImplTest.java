package monster.service.common.impl;

import monster.config.CfgArenaCrystal;
import monster.game.arena.entity.UserArenaCrystalEntity;
import monster.game.arena.service.ArenaService;
import monster.util.AbstractTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.Assert.assertTrue;

class ArenaCrystalServiceImplTest extends AbstractTest {
    ArenaService arenaService = null;

    @BeforeEach
    void setUp() {
        initUser(314172);
    }

    @Test
    void findOpponent() {
        UserArenaCrystalEntity arenaCrystalEntity = CfgArenaCrystal.getArenaCrystal(mUser.getUser().getId());
        List<UserArenaCrystalEntity> aOpp = arenaService.findOpponent(mUser, arenaCrystalEntity);
        for (UserArenaCrystalEntity userArenaCrystalEntity : aOpp) {
            System.out.println(userArenaCrystalEntity.getUserId() + " " + userArenaCrystalEntity.getPoint() + " " + userArenaCrystalEntity.getEventId() + " " + " " + userArenaCrystalEntity.getServerId());
        }
        assertTrue(!aOpp.isEmpty());
    }
}