package monster.game.challenge.entity;

import grep.helper.DateTime;
import monster.util.AbstractTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class GroupChallengeTypeTest extends AbstractTest {

    @BeforeEach
    void setUp() {
        // Initialize any necessary setup
    }

    @Test
    void testGetEventId() {
        // Test for different challenge types

        // Test for challengeType = 1 (vĩnh viễn)
        GroupChallengeType type1 = GroupChallengeType.THU_THACH;
        type1.challengeType = 1;
        assertEquals(0, type1.getEventId(), "For challengeType 1, getEventId should return 0");

        // Test for challengeType = 2 (rs theo ngày)
        GroupChallengeType type2 = GroupChallengeType.DAILY_QUEST;
        type2.challengeType = 2;
        int currentTimeKey = DateTime.getTimeKey();
        assertEquals(currentTimeKey, type2.getEventId(),
                "For challengeType 2, getEventId should return the current DateTime.getTimeKey()");

        // Test for challengeType = 3 (rs theo mùa)
        GroupChallengeType type3 = GroupChallengeType.ARENA_BALANCE;
        type3.challengeType = 3;
        assertEquals(0, type3.getEventId(),
                "For challengeType 3, getEventId should return 0 (since the code is commented out)");

        // Test for challengeType = 4 (rs theo tuần)
        GroupChallengeType type4 = GroupChallengeType.WEEKLY_QUEST;
        type4.challengeType = 4;
        int weeklyEventId = type4.getEventId();
        assertTrue(weeklyEventId > 0,
                "For challengeType 4, getEventId should return a positive value based on the week calculation");

        // Print the weekly event ID for reference
        System.out.println("Weekly event ID: " + weeklyEventId);

        // Verify the calculation for challengeType 4
        long currentTime = System.currentTimeMillis();
        long timeStartFirstWeek = GroupChallengeType.timeStartFirstWeek;
        long timePass = currentTime - timeStartFirstWeek;
        int expectedWeeklyEventId = (int) (timePass / DateTime.WEEK_MILLI_SECOND) + 1;

        assertEquals(expectedWeeklyEventId, weeklyEventId,
                "For challengeType 4, getEventId should match the calculated weekly event ID");
    }
}
