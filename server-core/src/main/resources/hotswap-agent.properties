# Default agent properties
# You can override them in your application by creating hotswap-agent.properties file in class root
# and specifying new property values.

# Add a directory prior to application classpath (load classes and resources).
#
# This may be useful for example in multi module maven project to load class changes from upstream project
# classes. Set extraClasspath to upstream project compiler output and .class file will have precedence to
# classes from built JAR file.
extraClasspath=

# Watch for changes in a directory (resources only). If not set, changes of resources won't be observed.
#
# Similar to extraClasspath this property adds classpath when searching for resources (not classes).
# While extra classpath just modifies the classloader, this setting does nothing until the resource
# is really changed.
#
# Sometimes it is not possible to point extraClasspath to your i.e. src/main/resources, because there are multiple
# replacements of resources in a building step (maven filtering resource option).
# This setting will leave i.e. src/target/classes as default source for resources, but after the resource is modified
# in src/main/resources, the new changed resource is served instead.
watchResources=

# Load static web resources from different directory.
#
# This setting is dependent on application server plugin(<PERSON><PERSON>, <PERSON>cat, ...).
# <PERSON><PERSON>s and Glassfish are not yet supported.
# Use this setting to set to serve resources from source directory directly (e.g. src/main/webapp).
webappDir=


# Comma separated list of disabled plugins
# Use plugin name - e.g. Hibernate, Spring, ZK, Hotswapper, AnonymousClassPatch, Tomcat, Logback ....
disabledPlugins=

# Watch for changed class files on watchResources path and reload class definition in the running application.
#
# Usually you will launch debugging session from your IDE and use standard hotswap feature.
# This property is useful if you do not want to use debugging session for some reason or
# if you want to enable hotswap at runtime environment.
#
# Internally this uses java Instrumentation API to reload class bytecode. If you need to use JPDA API instead,
# specify autoHotswap.port with JPDA port.
autoHotswap=true

# The base package prefix of your spring application (e.g. org.hotswap.).
# Needed when component scan is turned off, so we can still know which classes is your beans
# Can also be set to filter beans we handle to improve performance (So that we won't create proxy for thirty party lib's beans).
# Comma separated.
#spring.basePackagePrefix=

# Create Java Platform Debugger Architecture (JPDA) connection on autoHotswap.port, watch for changed class files
# and do the hotswap (reload) in background.
#
# You need to specify JPDA port at startup
# <pre>java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8000</pre>
# autoHotswap.port=8000

# Enables debugging in OsgiEquinox
# osgiEquinox.debugMode=true

# Setup reloading strategy of bean INSTANCE(s) in Weld CONTEXT(s). While bean class is redefined by DCEVM, reloading of bean instances
# can be customized by this parameter. Available values:
#   - CLASS_CHANGE - reload bean instance on any class modification, plus reaload on changes specified in
#     METHOD_FIELD_SIGNATURE_CHANGE and FIELD_SIGNATURE_CHANGE strategies
#   - METHOD_FIELD_SIGNATURE_CHANGE - reload bean instance on any method/field change. Includes changes specified in
#     strategy FIELD_SIGNATURE_CHANGE
#   - FIELD_SIGNATURE_CHANGE - reload bean instance on any field signature change. Includes also field annotation changes
#   - NEVER - never reload bean (default)
# weld.beanReloadStrategy=NEVER

# Logger setup - use entries in the format of
# format:  LOGGER.my.package=LEVEL
# e.g.     LOGGER.org.hotswap.agent.plugin.myPlugin=trace
# root level
LOGGER=info
# DateTime format using format of SimpleDateFormat, default value HH:mm:ss.SSS
# LOGGER_DATETIME_FORMAT=HH:mm:ss.SSS

# Print output into logfile (with choice to append - false by default)
# LOGFILE=agent.log
# LOGFILE.append=true

# Comma separated list of class loaders to exclude from initialization, in the form of RegEx patterns.
#excludedClassLoaderPatterns=jdk.nashorn.*