package grep.helper;

import lombok.Builder;

import java.util.Date;

@Builder
public class NumberCountdown {

    long maxValue;
    int numberRecover; // maxValue, số điểm hồi 1 lần recover
    long timeRecover; // seconds th<PERSON><PERSON> gian recover
    long lastTimeRecover; // last time recover
    long value; // số hiện tại

    // return true if has update
    private boolean calculate() {
        if (value >= maxValue) {
            lastTimeRecover = System.currentTimeMillis();
            return false;
        }
        long numberAdd = (System.currentTimeMillis() - lastTimeRecover) / (timeRecover * 1000);
        if (numberAdd > 0) {
            value = (int) Math.min(value + numberAdd * numberRecover, maxValue);
            lastTimeRecover += (numberAdd * timeRecover * 1000);
            return true;
        }
        return false;
    }

    public long countdownToNextRecover() {
        calculate();
        if (value >= maxValue) return 0;
        return timeRecover - (System.currentTimeMillis() - lastTimeRecover) / 1000;
    }

    public long getValue() {
        calculate();
        return value;
    }

    public void addValue(long value) {
        this.value += value;
    }

    public String getDateTimeRecover() {
        return DateTime.getFullDate(new Date(lastTimeRecover));
    }

    public long getLastTimeRecover() {
        return lastTimeRecover;
    }

    public void setTimeRecover(long timeRecover) {
        this.timeRecover = timeRecover;
    }
}