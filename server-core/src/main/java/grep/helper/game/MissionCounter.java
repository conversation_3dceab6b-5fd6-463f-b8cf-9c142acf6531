package grep.helper.game;

import java.util.List;

/**
 * <PERSON><PERSON><PERSON> nhiệm vụ, lấy status theo chuẩn chung
 * type nhiệm vụ (cần làm type chung)
 * curNumber, maxNumber
 * status: 0 1 2 (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, đ<PERSON> nhậ<PERSON>)
 */
public class MissionCounter {

    List<Long> ids, number;
    MissionInfo missionInfo;

    // data mission
    public MissionCounter(String value, MissionInfo missionInfo) {
        this.missionInfo = missionInfo;
    }


}
