package grep.helper;

import java.math.BigDecimal;

public class CryptoNumber {

    /**
     * crypto value -> long / 10e18
     *
     * @param value
     * @return
     */
    public static long toLong(BigDecimal value) {
        BigDecimal power = new BigDecimal("1000000000000000000");
        return value.divide(power).longValue();
    }

    public static void main(String[] args) {
        System.out.println(toLong(new BigDecimal("15000000000001000000")));
    }

    /**
     * long value -> crypto by * 10e18
     *
     * @param value
     * @return
     */
    public static BigDecimal getBigDecimal(long value) {
        return new BigDecimal(value).scaleByPowerOfTen(18);
    }

}
