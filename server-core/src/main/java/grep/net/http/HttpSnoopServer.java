/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package grep.net.http;

import monster.controller.AHandler;
import org.jboss.netty.bootstrap.ServerBootstrap;
import org.jboss.netty.channel.ChannelFactory;
import org.jboss.netty.channel.socket.nio.NioServerSocketChannelFactory;
import org.jboss.netty.handler.execution.OrderedMemoryAwareThreadPoolExecutor;
import org.jboss.netty.handler.ssl.SslContext;

import java.net.InetSocketAddress;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * An HTTP server that sends back the content of the received HTTP request in a pretty plaintext form.
 */
public class HttpSnoopServer {

    public static final int MAX_READ_BUFFER_SIZE = 1048576;
    public static final int THREAD_POOL_SIZE = 64;
    public static final int CHANNEL_MEMORY_LIMIT = 0;
    public static final long GLOBAL_MEMORY_LIMIT = 0;

    public static int runningPort;

    public void run(int port, Map<Integer, AHandler> mHandler, SslContext sslCtx) throws Exception {
        HttpSnoopServer.runningPort = port;
        HttpRequestHandler.mHandler = mHandler;

        // Configure the server.
        //        ChannelFactory factory = new NioServerSocketChannelFactory(Executors.newCachedThreadPool(), Executors.newCachedThreadPool());
        ChannelFactory factory = new NioServerSocketChannelFactory(Executors.newVirtualThreadPerTaskExecutor(), Executors.newVirtualThreadPerTaskExecutor());
        // Executors.newVirtualThreadPerTaskExecutor()
        ServerBootstrap bootstrap = new ServerBootstrap(factory);
        // setting buffer size can improve I/O
        bootstrap.setOption("child.sendBufferSize", 436900);
        bootstrap.setOption("child.receiveBufferSize", 436900);
        bootstrap.setOption("child.keepAlive", false);
        bootstrap.setOption("child.tcpNoDelay", true);

        // if the server is sending 1000 messages per sec, optimum write buffer water marks will
        // prevent unnecessary throttling, Check NioSocketChannelConfig doc
        // bootstrap.setOption("writeBufferLowWaterMark", 32 * 1024);
        // bootstrap.setOption("writeBufferHighWaterMark", 64 * 1024);
        bootstrap.setOption("soLinger", 20000);
        bootstrap.setOption("reuseAddress", true);
        // bootstrap.setOption("tcpNoDelay", true);
        //bootstrap.setOption("keepAlive", true);
        bootstrap.setOption("sendBufferSize", 436900);
        bootstrap.setOption("receiveBufferSize", 436900);

        var threadPoolExecutor = new OrderedMemoryAwareThreadPoolExecutor(THREAD_POOL_SIZE, CHANNEL_MEMORY_LIMIT, GLOBAL_MEMORY_LIMIT, 5000, TimeUnit.MILLISECONDS);
        bootstrap.setPipelineFactory(new HttpServerPipelineFactory(threadPoolExecutor, sslCtx));
        bootstrap.bind(new InetSocketAddress(port));
    }

}
