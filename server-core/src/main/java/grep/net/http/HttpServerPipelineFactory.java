package grep.net.http;

import org.jboss.netty.channel.ChannelPipeline;
import org.jboss.netty.channel.ChannelPipelineFactory;
import org.jboss.netty.handler.codec.http.HttpChunkAggregator;
import org.jboss.netty.handler.codec.http.HttpRequestDecoder;
import org.jboss.netty.handler.codec.http.HttpResponseEncoder;
import org.jboss.netty.handler.execution.ExecutionHandler;
import org.jboss.netty.handler.execution.OrderedMemoryAwareThreadPoolExecutor;
import org.jboss.netty.handler.ssl.SslContext;
import org.jboss.netty.handler.timeout.IdleStateHandler;
import org.jboss.netty.util.HashedWheelTimer;
import org.jboss.netty.util.Timer;

import javax.net.ssl.SSLEngine;

import java.util.concurrent.TimeUnit;

import static org.jboss.netty.channel.Channels.pipeline;

public class HttpServerPipelineFactory implements ChannelPipelineFactory {
    private final OrderedMemoryAwareThreadPoolExecutor eventExecutor;
    private final SslContext sslCtx;

    public HttpServerPipelineFactory(OrderedMemoryAwareThreadPoolExecutor eventExecutor, SslContext sslCtx) {
        this.eventExecutor = eventExecutor;
        this.sslCtx = sslCtx;
    }

    public ChannelPipeline getPipeline() throws Exception {
        // Create a default pipeline implementation.
        ChannelPipeline pipeline = pipeline();
        // Uncomment the following line if you want HTTPS
        // SSLEngine engine = SecureChatSslContextFactory.getServerContext().createSSLEngine();
        //engine.setUseClientMode(false);
        //pipeline.addLast("ssl", new SslHandler(engine));

        if (sslCtx != null) {
            pipeline.addLast("ssl", sslCtx.newHandler());
        }

        pipeline.addLast("decoder", new HttpRequestDecoder());
        // Uncomment the following line if you don't want to handle HttpChunks.
        pipeline.addLast("aggregator", new HttpChunkAggregator(HttpSnoopServer.MAX_READ_BUFFER_SIZE));
        pipeline.addLast("encoder", new HttpResponseEncoder());
        // Remove the following line if you don't want automatic content compression.
        // pipeline.addLast("deflater", new HttpContentCompressor());
        pipeline.addLast("executor", new ExecutionHandler(eventExecutor));
        pipeline.addLast("handler", new HttpRequestHandler());
        return pipeline;
    }
}
