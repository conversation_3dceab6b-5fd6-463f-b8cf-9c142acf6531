package monster.dao;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.LogicUtil;
import grep.helper.NumberUtil;
import grep.log.Logs;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import monster.config.*;
import monster.dao.mapping.*;
import monster.game.clan.entity.UserClanEntity;
import monster.game.hero.entity.UserBreathEntity;
import monster.game.minigame.config.ConfigMiniGame;
import monster.game.minigame.entity.UserMiniGameEntity;
import monster.game.pot.UserPotMapEntity;
import monster.game.ranking.MiniUserEntity;
import monster.game.ranking.UserRankingEntity;
import monster.game.ranking.entity.UserHeroRankingEntity;
import monster.game.royalpalace.entity.UserRoyalPalaceEntity;
import monster.game.storymission.service.StoryMissionService;
import monster.game.swordsmanship.entity.UserSwordsmanshipBookEntity;
import monster.game.swordsmanship.entity.UserSwordsmanshipEntity;
import monster.game.territory.entity.UserTerritoryEntity;
import monster.game.user.entity.UserInfoCache;
import monster.object.MyUser;
import monster.object.UserResources;
import monster.server.config.Guice;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
public class UserDAO extends AbstractDAO {

    public boolean initUser(MyUser mUser) {
        int userId = mUser.getUser().getId();
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            UserDataEntity uData = DBJPA.getUnique(session, "user_data", UserDataEntity.class, "user_id", userId);
            if (uData == null) {
                uData = new UserDataEntity(userId);
                session.getTransaction().begin();
                session.persist(uData);
                session.getTransaction().commit();
            }
            UserDailyDataEntity userDailyData = DBJPA.getUnique(session, "user_daily_data", UserDailyDataEntity.class, "user_id", userId);
            if (userDailyData == null) {
                userDailyData = new UserDailyDataEntity(userId);
                session.getTransaction().begin();
                session.persist(userDailyData);
                session.getTransaction().commit();
            }
            UserSummonCounter summonCounter = DBJPA.getUnique(session, "user_summon_counter", UserSummonCounter.class, "user_id", userId);
            if (summonCounter == null) {
                summonCounter = UserSummonCounter.builder().userId(userId).build();
                session.getTransaction().begin();
                session.persist(summonCounter);
                session.getTransaction().commit();
            }
            //            if (userId == 6802) userId = 7296;
            mUser.setResources(new UserResources(mUser));
            mUser.getResources().images = session.createNativeQuery("select * from user_album where user_id=" + userId, UserAlbumEntity.class).getResultList();
            //            mUser.getResources().heroes = session.createNativeQuery("select * from user_hero where user_id=" + userId, UserHeroEntity.class).getResultList();
            mUser.getResources().items = session.createNativeQuery("select * from user_item where user_id=" + userId, UserItemEntity.class).getResultList();
            //            mUser.getResources().materials = session.createNativeQuery("select * from user_material where user_id=" + userId, UserMaterialEntity.class).getResultList();
            mUser.getResources().artifacts = session.createNativeQuery("select * from user_artifact where user_id=" + userId + " and artifact_id>0", UserArtifactEntity.class).getResultList();
            mUser.getResources().aBadge = session.createNativeQuery("select * from user_badge where user_id=" + userId + " and badge_id>0", UserBadgeEntity.class).getResultList();
            mUser.getResources().gems = session.createNativeQuery("select * from user_gem u where u.user_id=:userId and is_deleted=0", UserGemEntity.class).setParameter("userId", userId).getResultList();
            mUser.getResources().heroSkins = session.createNativeQuery("select * from user_hero_skin where user_id=" + userId, UserHeroSkinEntity.class).getResultList();
            mUser.getResources().setMPet(session.createNativeQuery("select * from user_pet where user_id=" + userId, UserPetEntity.class).getResultList());
            mUser.getResources().userRoyalPalace = session.createNativeQuery("select * from user_royal_palace u where u.user_id=:userId", UserRoyalPalaceEntity.class).setParameter("userId", userId).getResultList();
            mUser.setUData(uData);
            mUser.setUserDailyData(userDailyData);
            mUser.setSummonCounter(summonCounter);
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    public boolean updateLanguageCode(int id, String lang) {
        return doUpdate(em -> em.createNativeQuery(String.format("update dson_main.user set metadata=IFNULL('{\"lang\": \"%s\"}',JSON_SET(metadata, '$.lang', '%s')) where id=%s;", lang, lang, id)).executeUpdate() > 0);
    }

    public UserEntity getFirstUser() {
        EntityManager session = null;
        try {
            session = getEntityManager();
            List<UserEntity> list = session.createNativeQuery("select * from user limit 1", UserEntity.class).getResultList();
            return list.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public long getTotalMoneyCharge(int userId) {
        EntityManager session = null;
        try {
            session = getEntityManager();
            List list = session.createNativeQuery("select sum(money) from dson_main.log_nap_koin where user_id=" + userId).getResultList();
            if (list.isEmpty()) return 0;
            if (list.get(0) == null) return 0;
            return ((BigInteger) list.get(0)).longValue();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return -1;
    }

    public MiniUserEntity getUserMini(int userId) {
        return DBJPA.getUnique("user", MiniUserEntity.class, "id", userId);
    }

    public UserTower2Entity getTower2Entity(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserTower2Entity> aUserTower = session.createNativeQuery("select * from user_tower2 where user_id=" + user.getId(), UserTower2Entity.class).getResultList();
            if (aUserTower.isEmpty()) {
                UserTower2Entity uTower = new UserTower2Entity(user);
                session.getTransaction().begin();
                session.persist(uTower);
                session.getTransaction().commit();
                return uTower;
            }
            return aUserTower.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }
    public UserDungeonEntity getUserDungeon(UserEntity user, int eventId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserDungeonEntity> aDungeon = session.createNativeQuery("select * from user_dungeon where user_id=" + user.getId() + " and event_id=" + eventId, UserDungeonEntity.class).getResultList();
            if (aDungeon.isEmpty()) {
                UserDungeonEntity dungeon = new UserDungeonEntity(user, eventId);
                session.getTransaction().begin();
                session.persist(dungeon);
                session.getTransaction().commit();
                return dungeon;
            }
            return aDungeon.get(0);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserRaidEntity getUserRaid(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserRaidEntity> aRaid = session.createNativeQuery("select * from user_raid where user_id=" + userId, UserRaidEntity.class).getResultList();
            if (aRaid.isEmpty()) {
                UserRaidEntity userRaid = new UserRaidEntity(userId);
                session.getTransaction().begin();
                session.persist(userRaid);
                session.getTransaction().commit();
                return userRaid;
            }
            return aRaid.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserRankingEntity getUserRanking(MyUser mUser) {
        EntityManager em = getEntityManager();
        try {
            List<UserRankingEntity> result = em.createQuery("select u from UserRankingEntity u where u.userId=:userId", UserRankingEntity.class).setParameter("userId", mUser.getUser().getId()).getResultList();
            if (result.isEmpty()) {
                UserRankingEntity uRank = new UserRankingEntity(mUser.getUser().getId());
                em.getTransaction().begin();
                em.persist(uRank);
                em.getTransaction().commit();
                return uRank;
            }
            return result.get(0);
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            closeSession(em);
        }
        return null;
    }

    public UserHeroRankingEntity getUserHeroRanking(MyUser mUser) {
        EntityManager em = getEntityManager();
        try {
            List<UserHeroRankingEntity> result = em.createQuery("select u from UserHeroRankingEntity u where u.userId=:userId", UserHeroRankingEntity.class).setParameter("userId", mUser.getUser().getId()).getResultList();
            UserHeroRankingEntity ret = null;
            if (result.isEmpty()) {
                UserHeroRankingEntity uRank = new UserHeroRankingEntity(mUser.getUser());
                em.getTransaction().begin();
                em.persist(uRank);
                em.getTransaction().commit();
                ret = uRank;
            } else ret = result.get(0);
            return ret;
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            closeSession(em);
        }
        return null;
    }

    public UserCampaignEntity getUserCampaign(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserCampaignEntity> aCampaign = session.createNativeQuery("select * from user_campaign where user_id=" + user.getId(), UserCampaignEntity.class).getResultList();
            if (aCampaign.isEmpty()) {
                UserCampaignEntity campaign = new UserCampaignEntity(user);
                session.getTransaction().begin();
                session.persist(campaign);
                session.getTransaction().commit();
                return campaign;
            }
            return aCampaign.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserFreedomArenaEntity getUserFreedom(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserFreedomArenaEntity> breeds = session.createNativeQuery("select * from user_freedom_arena where user_id=" + user.getId(), UserFreedomArenaEntity.class).getResultList();
            if (breeds.isEmpty()) {
                UserFreedomArenaEntity freedom = new UserFreedomArenaEntity(user.getId());
                session.getTransaction().begin();
                session.persist(freedom);
                session.getTransaction().commit();
                return freedom;
            }
            breeds.get(0).checkEvent();
            return breeds.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserFuseSsEntity getUserFuseSS(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserFuseSsEntity> aUserFuseSS = session.createNativeQuery("select * from user_fuse_ss where user_id=" + user.getId(), UserFuseSsEntity.class).getResultList();
            if (aUserFuseSS.isEmpty()) {
                UserFuseSsEntity userFuseSS = new UserFuseSsEntity(user);
                session.getTransaction().begin();
                session.persist(userFuseSS);
                session.getTransaction().commit();
                return userFuseSS;
            }
            return aUserFuseSS.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserClanEntity dbGetUserClan(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserClanEntity> aUserClan = session.createNativeQuery("select * from user_clan where user_id=" + userId, UserClanEntity.class).getResultList();
            if (aUserClan.isEmpty()) {
                UserClanEntity uClan = new UserClanEntity(userId);
                session.getTransaction().begin();
                session.persist(uClan);
                session.getTransaction().commit();
                return uClan;
            }
            return aUserClan.get(0);
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserBossInterServerEntity dbGetUserBossInterServerEntity(int userId, int eventId, int clusterId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserBossInterServerEntity> aBoss = session.createNativeQuery(
                    "select * from user_boss_inter_server where user_id=" + userId + " and event_id=" + eventId + " and cluster_id=" + clusterId, UserBossInterServerEntity.class).getResultList();
            if (aBoss.isEmpty()) {
                UserBossInterServerEntity userBoss = new UserBossInterServerEntity(userId, eventId, clusterId);
                session.getTransaction().begin();
                session.persist(userBoss);
                session.getTransaction().commit();
                return userBoss;
            }
            return aBoss.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserQuestionEntity dbGetUQuestion(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserQuestionEntity> aQuestion = session.createNativeQuery("select * from user_question where user_id=" + userId, UserQuestionEntity.class).getResultList();
            if (aQuestion.isEmpty()) {
                UserQuestionEntity uQuestion = new UserQuestionEntity(userId);
                session.getTransaction().begin();
                session.persist(uQuestion);
                session.getTransaction().commit();
                return uQuestion;
            }
            return aQuestion.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserDiceEntity dbGetUDice(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            int eventId = Guice.getInstance(StoryMissionService.class).getEventId(myUser);
            Query query = session.createNativeQuery("select * from user_dice where user_id=:userId and event_id=:eventId", UserDiceEntity.class);
            query.setParameter("userId", myUser.getUser().getId());
            query.setParameter("eventId", eventId);
            List<UserDiceEntity> aDice = query.getResultList();
            if (aDice.isEmpty()) {
                UserDiceEntity uDice = new UserDiceEntity(myUser.getUser().getId(), myUser.getUser().getServer(), eventId);
                session.getTransaction().begin();
                session.persist(uDice);
                session.getTransaction().commit();
                return uDice;
            }
            return aDice.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public UserAbyssEntity dbGetUAbyss(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            int userId = myUser.getUser().getId();
            List<UserAbyssEntity> aUAbyss = session.createNativeQuery("select * from dson.user_abyss where user_id=" + userId, UserAbyssEntity.class).getResultList();
            if (aUAbyss == null || aUAbyss.isEmpty()) {
                session.getTransaction().begin();
                UserAbyssEntity uAbyss = new UserAbyssEntity(myUser);
                session.persist(uAbyss);
                session.getTransaction().commit();
                return uAbyss;
            }

            return aUAbyss.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }


    public List<UserHeroTopEntity> dbGetHeroTop(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_hero_top where user_id=:userId", UserHeroTopEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserInteriorTopEntity> dbGetInteriorTop(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_interior_top where user_id=:userId", UserInteriorTopEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserPetTopEntity> dbGetPetTop(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_pet_top where user_id=:userId", UserPetTopEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserHeroCloneEntity> dbGetHeroClone(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_hero_clone where user_id=:userId", UserHeroCloneEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserPetCloneEntity> dbGetPetClone(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_pet_clone where user_id=:userId", UserPetCloneEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserClanTopEntity> dbGetClanTop(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_clan_top where user_id=:userId", UserClanTopEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserClanCloneEntity> dbGetClanClone(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_clan_clone where user_id=:userId", UserClanCloneEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserClanCloneTestEntity> dbGetClanCloneTest(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_clan_clone_test where user_id=:userId", UserClanCloneTestEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserInteriorCloneEntity> dbGetInteriorClone(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_interior_clone where user_id=:userId", UserInteriorCloneEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserInteriorCloneTestEntity> dbGetInteriorCloneTest(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_interior_clone_test where user_id=:userId", UserInteriorCloneTestEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserAlbumCloneEntity> dbGetAlbumClone(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_album_clone where user_id=:userId", UserAlbumCloneEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserAlbumCloneTestEntity> dbGetAlbumCloneTest(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_album_clone_test where user_id=:userId", UserAlbumCloneTestEntity.class).setParameter("userId", myUser.getUser().getId()).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }

        return null;
    }

    public List<UserInteriorHeroEntity> dbGetUIHero(MyUser mUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user_interior_hero where user_id=" + mUser.getUser().getId(), UserInteriorHeroEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public List<UserInteriorItemEntity> dbGetUInteriorItem(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from dson.user_interior_item where user_id=" + myUser.getUser().getId(), UserInteriorItemEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserInteriorQuestEntity dbGetUInteriorQuest(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            int userId = myUser.getUser().getId();
            Query query = session.createNativeQuery("select * from dson.user_interior_quest where user_id=" + userId, UserInteriorQuestEntity.class);

            List<UserInteriorQuestEntity> listQuest = query.getResultList();
            if (listQuest == null || listQuest.isEmpty()) {
                UserInteriorQuestEntity uIQuest = new UserInteriorQuestEntity(userId);
                session.getTransaction().begin();
                session.persist(uIQuest);
                session.getTransaction().commit();
                return uIQuest;
            }

            return listQuest.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public List<UserInteriorWallpaperEntity> dbGetUInteriorWallpaper(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            int userId = myUser.getUser().getId();
            List<UserInteriorWallpaperEntity> listWallpaper = session.createNativeQuery("select * from dson.user_interior_wallpaper where user_id=" + userId, UserInteriorWallpaperEntity.class).getResultList();
            if (listWallpaper == null || listWallpaper.isEmpty()) {
                UserInteriorWallpaperEntity uIWallpaper = new UserInteriorWallpaperEntity(userId, 1);
                session.getTransaction().begin();
                session.persist(uIWallpaper);
                session.getTransaction().commit();
                return Collections.singletonList(uIWallpaper);
            }
            return listWallpaper;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserInteriorEntity dbGetUInterior(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserInteriorEntity> aUInterior = session.createNativeQuery("select * from dson.user_interior where user_id=" + myUser.getUser().getId(), UserInteriorEntity.class).getResultList();
            if (aUInterior == null || aUInterior.isEmpty()) {
                UserInteriorEntity uInterior = new UserInteriorEntity(myUser);
                session.getTransaction().begin();
                session.persist(uInterior);
                session.getTransaction().commit();
                return uInterior;
            }
            return aUInterior.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public List<UserInteriorCollectionEntity> dbGetUInteriorCollection(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserInteriorCollectionEntity> allOldUICollection = session.createNativeQuery("select * from dson.user_interior_collection where user_id=" + myUser.getUser().getId(), UserInteriorCollectionEntity.class).getResultList();
            List<Integer> allOldHeroKey = CfgInterior.getAllHeroKey();
            List<Integer> allHeroKeyInOldCollections = new ArrayList<>();
            for (UserInteriorCollectionEntity uICollection : allOldUICollection) {
                allHeroKeyInOldCollections.add(uICollection.getHeroKey());
            }


            List<Integer> allNewHeroKey = LogicUtil.getAllValueNotExist(allHeroKeyInOldCollections, allOldHeroKey);
            if (allNewHeroKey.isEmpty()) return allOldUICollection;
            List<UserInteriorCollectionEntity> allNewUICollection = new ArrayList<>();

            String sql = "insert into user_interior_collection (user_id,hero_key) values ";
            for (int i = 0; i < allNewHeroKey.size(); i++) {
                UserInteriorCollectionEntity newUICollection = new UserInteriorCollectionEntity(myUser.getUser().getId(), allNewHeroKey.get(i));
                allNewUICollection.add(newUICollection);
                if (i == 0) {
                    sql += "(" + newUICollection.getUserId() + "," + newUICollection.getHeroKey() + ") ";
                    continue;
                }
                sql += ", (" + newUICollection.getUserId() + "," + newUICollection.getHeroKey() + ") ";
            }

            session.getTransaction().begin();
            session.createNativeQuery(sql).executeUpdate();
            session.getTransaction().commit();
            allOldUICollection.addAll(allNewUICollection);

            return allOldUICollection;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserInteriorWishlistEntity dbGetUIWishlist(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserInteriorWishlistEntity> aBoss = session.createNativeQuery(
                    "select * from user_interior_wishlist where user_id=" + myUser.getUser().getId(), UserInteriorWishlistEntity.class).getResultList();
            if (aBoss.isEmpty()) {
                UserInteriorWishlistEntity uIWishlist = new UserInteriorWishlistEntity(myUser.getUser().getId());
                session.getTransaction().begin();
                session.persist(uIWishlist);
                session.getTransaction().commit();
                return uIWishlist;
            }
            return aBoss.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserImpelDownEntity dbGetUImpelDown(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();

            List<UserImpelDownEntity> aUImpelDown = session.createNativeQuery("select * from dson.user_impel_down where user_id=" + userId, UserImpelDownEntity.class).getResultList();
            if (aUImpelDown == null || aUImpelDown.isEmpty()) {
                UserImpelDownEntity userImpelDown = new UserImpelDownEntity(userId);
                session.getTransaction().begin();
                session.persist(userImpelDown);
                session.getTransaction().commit();
                return userImpelDown;
            }

            return aUImpelDown.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public List<UserImpelDownFloorEntity> dbGetUImpelDownFloor(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserImpelDownFloorEntity> listUserImpelDownFloor = session.createNativeQuery("select * from dson.user_impel_down_floor where user_id=" + myUser.getUser().getId(), UserImpelDownFloorEntity.class).getResultList();
            if (listUserImpelDownFloor == null || listUserImpelDownFloor.isEmpty()) {
                session.getTransaction().begin();
                int userId = myUser.getUser().getId();
                listUserImpelDownFloor = CfgImpelDown.getDefaultImpelDownFloor(myUser);
                String sql = "insert into user_impel_down_floor (user_id,floor,status,status_number) values ";
                for (int i = 0; i < listUserImpelDownFloor.size(); i++) {
                    UserImpelDownFloorEntity userImpelDownFloor = listUserImpelDownFloor.get(i);
                    if (i == 0) {
                        sql += "(" + userId + "," + (i + 1) + ",'" + userImpelDownFloor.getStatus() + "','" + userImpelDownFloor.getStatusNumber() + "') ";
                        continue;
                    }
                    sql += ", (" + userId + "," + (i + 1) + ",'" + userImpelDownFloor.getStatus() + "','" + userImpelDownFloor.getStatusNumber() + "') ";
                }
                session.createNativeQuery(sql).executeUpdate();
                session.getTransaction().commit();
            }
            return listUserImpelDownFloor;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserFishmanIslandEntity dbGetUFishmanIsland(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            int userId = myUser.getUser().getId();
            List<UserFishmanIslandEntity> aUFishmanIsland = session.createNativeQuery("select * from dson.user_fishman_island where user_id=" + userId, UserFishmanIslandEntity.class).getResultList();
            if (aUFishmanIsland == null || aUFishmanIsland.isEmpty()) {
                session.getTransaction().begin();
                UserFishmanIslandEntity uFishmanIsland = new UserFishmanIslandEntity(userId);
                session.persist(uFishmanIsland);
                session.getTransaction().commit();
                return uFishmanIsland;
            }

            return aUFishmanIsland.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserEntity getUser(int userId) {
        return (UserEntity) DBJPA.getUnique("user", UserEntity.class, "id", userId);
    }

    public UserEntity getUser(String username) {
        return (UserEntity) DBJPA.getUnique("user", UserEntity.class, "username", username);
    }

    public List<UserEntity> getListUser(List<Integer> aUserId) {
        return getListUser(aUserId.stream().map(String::valueOf).collect(Collectors.joining(",")));
    }

    public List<UserEntity> getListUser(String ids) {
        if (ids.isEmpty()) return new ArrayList<>();
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from user where id in (" + ids + ")", UserEntity.class).getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return new ArrayList<>();
    }

    public static UserAchievement getUAchievement(MyUser mUser) {
        UserEntity user = mUser.getUser();
        String KEY_DATA = "achievement";
        UserAchievement achieve = (UserAchievement) mUser.getCache().get(KEY_DATA);
        if (achieve == null) {
            achieve = dbGetUserAchieve(user);
            if (achieve == null) {
                return null;
            }
            mUser.getCache().set(KEY_DATA, achieve);
        }
        return achieve;
    }

    static UserDungeonEntity dbGetUserDungeon(MyUser mUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            int eventId = CfgDungeon.getEventId(mUser);
            List<UserDungeonEntity> aDungeon = session.createNativeQuery("select * from user_dungeon where user_id=" + mUser.getUser().getId() + " and event_id=" + eventId, UserDungeonEntity.class).getResultList();
            if (aDungeon.isEmpty()) {
                UserDungeonEntity dungeon = new UserDungeonEntity(mUser.getUser(), eventId);
                session.getTransaction().begin();
                session.persist(dungeon);
                session.getTransaction().commit();
                return dungeon;
            }
            return aDungeon.get(0);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public static UserRaidEntity getUserRaid(MyUser mUser) {
        String KEY_DATA = "event_raid";
        UserRaidEntity raid = (UserRaidEntity) mUser.getCache().get(KEY_DATA);
        if (raid == null) {
            raid = dbGetUserRaid(mUser.getUser());
            if (raid == null) {
                return null;
            }
            mUser.getCache().set(KEY_DATA, raid);
        }
        raid.checkRefresh();
        return raid;
    }

    public static UserRaidEntity dbGetUserRaid(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserRaidEntity> aRaid = session.createNativeQuery("select * from user_raid where user_id=" + user.getId(), UserRaidEntity.class).getResultList();
            if (aRaid.isEmpty()) {
                UserRaidEntity userRaid = new UserRaidEntity(user.getId());
                session.getTransaction().begin();
                session.persist(userRaid);
                session.getTransaction().commit();
                return userRaid;
            }
            return aRaid.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public static UserDungeonEntity getUserDungeon(MyUser mUser) {
        String KEY_DATA = "dungeon";
        UserDungeonEntity uDung = (UserDungeonEntity) mUser.getCache().get(KEY_DATA);
        if (uDung == null) {
            uDung = dbGetUserDungeon(mUser);
            if (uDung == null) {
                return null;
            }
            mUser.getCache().set(KEY_DATA, uDung);
        }
        return uDung;
    }

    public UserOblivionEntity dbGetUserOblivion(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserOblivionEntity> aOblivion = session.createNativeQuery("select * from user_oblivion where user_id=" + user.getId(), UserOblivionEntity.class).getResultList();
            if (aOblivion.isEmpty()) {
                UserOblivionEntity oblivion = new UserOblivionEntity(user);
                session.getTransaction().begin();
                session.persist(oblivion);
                session.getTransaction().commit();
                return oblivion;
            }
            return aOblivion.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserPowerEntity getUserPower(int userId) {
        return doQuery(em -> em.createQuery("select c from UserPowerEntity c where c.userId=:userId", UserPowerEntity.class)
                .setParameter("userId", userId).getResultList().stream().findFirst().orElse(null));
    }

    public UserBreathEntity getUserBreath(int userId) {
        return doQuery(em -> em.createQuery("select c from UserBreathEntity c where c.userId=:userId", UserBreathEntity.class)
                .setParameter("userId", userId).getResultList().stream().findFirst().orElse(null));
    }

    public UserInfoCache getUserInfoCache(int userId, String k, String k1) {
        return doQuery(em -> em.createQuery("select c from UserInfoCache c where c.userId=:userId and c.k=:k and c.k1=:k1", UserInfoCache.class)
                .setParameter("userId", userId).setParameter("k", k).setParameter("k1", k1)
                .getResultList().stream().findFirst()
                .orElse(null));
    }

    static UserAchievement dbGetUserAchieve(UserEntity user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserAchievement> aAchi = session.createNativeQuery("select * from user_achievement where user_id=" + user.getId(), UserAchievement.class).getResultList();
            if (aAchi.isEmpty()) {
                UserAchievement achieve = new UserAchievement(user.getId());
                session.getTransaction().begin();
                session.persist(achieve);
                session.getTransaction().commit();
                return achieve;
            }
            return aAchi.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public UserSummonNewEntity getUserSummonNew(int userId, int summonId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            Query query = session.createNativeQuery("select * from user_summon_new where user_id =:user_id and summon_id =:summon_id", UserSummonNewEntity.class);
            query.setParameter("user_id", userId);
            query.setParameter("summon_id", summonId);
            List<UserSummonNewEntity> aQuest = query.getResultList();
            if (aQuest.isEmpty()) {
                UserSummonNewEntity quest = new UserSummonNewEntity(userId, summonId);
                session.getTransaction().begin();
                session.persist(quest);
                session.getTransaction().commit();
                return quest;
            }
            return aQuest.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion
    //
    //    public static Logger getLogger() {
    //        return slib_Logger.root();
    //    }

    //region user math puzlles challenge
    public UserMathPuzzlesChallengeEntity dbGetUserMathPuzzlesChallenge(MyUser mUser, long eventId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserMathPuzzlesChallengeEntity> aUserPuzzles = session.createNativeQuery("select * from user_math_puzzles_challenge where user_id= " + mUser.getUser().getId() + " and event_id = " + eventId, UserMathPuzzlesChallengeEntity.class).getResultList();

            if (aUserPuzzles.isEmpty()) {
                UserMathPuzzlesChallengeEntity userPuzzle = new UserMathPuzzlesChallengeEntity(mUser.getUser().getId(), eventId);
                session.getTransaction().begin();
                session.persist(userPuzzle);
                session.getTransaction().commit();
                return userPuzzle;
            }

            Date firstLoginInDay = aUserPuzzles.get(0).getFirstTimeLoginInDay();
            long dayDiff = DateTime.getDayDiff(new Date(), firstLoginInDay);

            if (dayDiff != 0) {
                int max = CfgMathPuzzlesChallenge.config.challenges.size();
                session.getTransaction().begin();
                session.createNativeQuery("update user_math_puzzles_challenge set challenge =:newChallenge ,status=:newStatus , first_time_login_in_day=:newFirstLoginInDay where user_id=:userId and event_id=:eventId")
                        .setParameter("userId", mUser.getUser().getId())
                        .setParameter("newChallenge", NumberUtil.genListInt(max, 0).toString())
                        .setParameter("newStatus", NumberUtil.genListInt(max, 0).toString())
                        .setParameter("newFirstLoginInDay", new Date())
                        .setParameter("eventId", eventId)
                        .executeUpdate();
                session.getTransaction().commit();
                aUserPuzzles.get(0).resetDataNewDay(mUser.getUser().getId());
            }
            return aUserPuzzles.get(0);

        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region user math puzlles
    public UserMathPuzzlesEntity dbGetUserMathPuzzles(MyUser user, long eventId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserMathPuzzlesEntity> aUserPuzzles = session.createNativeQuery("select * from user_math_puzzles where user_id=" + user.getUser().getId() + " and event_id = " + eventId, UserMathPuzzlesEntity.class).getResultList();
            if (aUserPuzzles.isEmpty()) {
                UserMathPuzzlesEntity userPuzzle = new UserMathPuzzlesEntity(user.getUser().getId(), 0, eventId);
                session.getTransaction().begin();
                session.persist(userPuzzle);
                session.getTransaction().commit();
                return userPuzzle;
            }

            return aUserPuzzles.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region zen match
    public UserZenMatchEntity dbGetUserZenMatch(MyUser user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            Query query = session.createNativeQuery("select * from user_zen_match where user_id=:userId and event_id=:eventId", UserZenMatchEntity.class);
            query.setParameter("userId", user.getUser().getId());
            query.setParameter("eventId", CfgEventSanji.getEventId());
            List<UserZenMatchEntity> aUserZenMatch = query.getResultList();
            if (aUserZenMatch.isEmpty()) {
                UserZenMatchEntity userZenMatch = new UserZenMatchEntity(user.getUser().getId(), CfgEventSanji.getEventId(), user.getUser().getServer());
                session.getTransaction().begin();
                session.persist(userZenMatch);
                session.getTransaction().commit();
                return userZenMatch;
            }

            return aUserZenMatch.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region saving dog
    public UserSavingDogEntity dbGetUserSavingDog(MyUser user) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            Query query = session.createNativeQuery("select * from user_saving_dog where user_id=:userId and event_id=:eventId", UserSavingDogEntity.class);
            query.setParameter("userId", user.getUser().getId());
            query.setParameter("eventId", CfgSavingDog.getEventId());
            List<UserSavingDogEntity> aUserSavingDog = query.getResultList();
            if (aUserSavingDog.isEmpty()) {
                UserSavingDogEntity userSavingDog = new UserSavingDogEntity(user.getUser().getId(), CfgSavingDog.getEventId(), user.getUser().getServer());
                session.getTransaction().begin();
                session.persist(userSavingDog);
                session.getTransaction().commit();
                return userSavingDog;
            }

            return aUserSavingDog.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    //region saving dog
    public UserMiniGameEntity dbGetUserMiniGame(MyUser mUser, int eventType) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            Query query = session.createNativeQuery("select * from user_mini_game where user_id=:userId and event_type=:eventType and event_id=:eventId", UserMiniGameEntity.class);
            query.setParameter("userId", mUser.getUser().getId());
            query.setParameter("eventType", eventType);
            query.setParameter("eventId", ConfigMiniGame.getEventId(eventType));
            List<UserMiniGameEntity> listUserPushSushi = query.getResultList();
            if (listUserPushSushi.isEmpty()) {
                UserMiniGameEntity userMiniGame = new UserMiniGameEntity(mUser.getUser().getId(), eventType, mUser.getUser().getServer());
                session.getTransaction().begin();
                session.persist(userMiniGame);
                session.getTransaction().commit();
                return userMiniGame;
            }

            return listUserPushSushi.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

    public List<UserPotMapEntity> dbGetUPot(MyUser myUser, List<Integer> aMapId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String str = aMapId.toString();
            str = "(" + str.substring(1, str.length() - 1) + ")";
            int userId = myUser.getUser().getId();
            List<UserPotMapEntity> aUMap = session.createNativeQuery("select * from user_pot_map where user_id=" + userId + " and map_id in " + str, UserPotMapEntity.class).getResultList();

            List<UserPotMapEntity> aNewUMap = new ArrayList<>();
            String sql = "insert into user_pot_map (user_id,map_id,current_position,is_completed, is_new) values ";

            for (int mapId : aMapId) {
                boolean isNew = true;
                for (UserPotMapEntity uMap : aUMap) {
                    if (uMap.getMapId() != mapId) continue;
                    isNew = false;
                }

                if (isNew) {
                    UserPotMapEntity newMap = new UserPotMapEntity(userId, mapId);
                    newMap.fileUpdateListMonster("");
                    newMap.fileUpdateMyHeroes("");
                    newMap.fileUpdateChangedObjects("");
                    aNewUMap.add(newMap);
                }
            }

            for (int i = 0; i < aNewUMap.size(); i++) {
                UserPotMapEntity newMap = aNewUMap.get(i);
                if (i == 0) {
                    sql += "(" + userId + "," + newMap.getMapId() + ",'" + newMap.getCurrentPosition() + "'," + newMap.getIsCompleted() + "," + newMap.getIsNew() + ") ";
                    continue;
                }
                sql += ", (" + userId + "," + newMap.getMapId() + ",'" + newMap.getCurrentPosition() + "'," + newMap.getIsCompleted() + "," + newMap.getIsNew() + ") ";
            }

            if (!aNewUMap.isEmpty()) {
                session.getTransaction().begin();
                session.createNativeQuery(sql).executeUpdate();
                session.getTransaction().commit();
                aUMap.addAll(aNewUMap);
            }

            return aUMap;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public boolean firstUdid(String udid) {
        return DBJPA.count(CfgServer.DB_MAIN + "user", "udid", udid) <= 1;
    }

    public boolean firstMainId(int mainId) {
        return DBJPA.count("user", "main_id", mainId) <= 0;
    }

    public UserFriendDataEntity dbGetUserFriendData(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String querry = "select * from dson.user_friend_data where user_id = " + userId;
            List<UserFriendDataEntity> lstResult = session.createNativeQuery(querry, UserFriendDataEntity.class).getResultList();
            if (lstResult.isEmpty()) {
                UserFriendDataEntity userFriendDataEntity = new UserFriendDataEntity(userId);
                session.getTransaction().begin();
                session.persist(userFriendDataEntity);
                session.getTransaction().commit();
                return userFriendDataEntity;
            }
            return lstResult.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    //region user boss slient ring

    public UserBossSilentRingEntity getUserBossSilentRingEntity(MyUser myUser, long eventId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String query = "select *  from dson.user_boss_silent_ring where user_id = " + myUser.getUser().getId() + " and event_id = " + eventId;
            List<UserBossSilentRingEntity> results = session.createNativeQuery(query, UserBossSilentRingEntity.class).getResultList();
            if (results.isEmpty()) {
                UserBossSilentRingEntity user = new UserBossSilentRingEntity(myUser, eventId);
                session.getTransaction().begin();
                session.persist(user);
                session.getTransaction().commit();
                return user;
            }
            return results.get(0);

        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;

    }
    //endregion

    //region last user boss silent ring challenge
    public UserBossSilentRingChallenge getUserBossSilentRingEntityChallenge(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String query = "select *  from dson.user_boss_silent_ring_challenge where user_id = " + myUser.getUser().getId();
            List<UserBossSilentRingChallenge> results = session.createNativeQuery(query, UserBossSilentRingChallenge.class).getResultList();
            if (results.isEmpty()) {
                UserBossSilentRingChallenge user = new UserBossSilentRingChallenge(myUser);
                session.getTransaction().begin();
                session.persist(user);
                session.getTransaction().commit();
                return user;
            }
            return results.get(0);

        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;

    }
    //endregion

    //region last user boss silent ring
    public UserBossSilentRingEntity getLastUserBossSilentRingEntity(MyUser myUser, long eventId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String query = "select *  from dson.user_boss_silent_ring where user_id = " + myUser.getUser().getId() + " and event_id < " + eventId + " order by event_id desc limit 1";
            List<UserBossSilentRingEntity> results = session.createNativeQuery(query, UserBossSilentRingEntity.class).getResultList();
            if (results.isEmpty()) {
                return null;
            }

            UserBossSilentRingEntity last = null;
            long lastEventId = 0;
            for (int i = 0; i < results.size(); i++) {
                if (results.get(i).getEventId() > lastEventId) {
                    lastEventId = results.get(i).getEventId();
                    last = results.get(i);
                }
            }
            return last;

        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;

    }
    //endregion

    //region User Resonance Item
    public UserResonanceItemEntity getUserResonanceItem(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String query = "select *  from dson.user_resonance_item where user_id = " + myUser.getUser().getId();
            List<UserResonanceItemEntity> results = session.createNativeQuery(query, UserResonanceItemEntity.class).getResultList();
            if (results.isEmpty()) {
                UserResonanceItemEntity user = new UserResonanceItemEntity(myUser.getUser().getId());
                session.getTransaction().begin();
                session.persist(user);
                session.getTransaction().commit();
                return user;
            }
            return results.get(0);

        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;

    }
    //endregion

    //region Territory
    public List<UserTerritoryEntity> dbGetUserTerritory(MyUser myUser) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserTerritoryEntity> listUserTerritory = session.createNativeQuery("select * from dson.user_territory where user_id=" + myUser.getUser().getId(), UserTerritoryEntity.class).getResultList();
            if (listUserTerritory == null || listUserTerritory.isEmpty()) {
                session.getTransaction().begin();
                int userId = myUser.getUser().getId();
                listUserTerritory = CfgTerritory.getDefaultListUserTerritory(userId);
                String sql = "insert into dson.user_territory (user_id,server_id,faction_id,level,number_sweep_turn, number_buy_sweep_turn, reward_status) values ";
                for (int i = 0; i < listUserTerritory.size(); i++) {
                    UserTerritoryEntity userTerritory = listUserTerritory.get(i);
                    if (i == 0) {
                        sql += "(" + userId + "," + myUser.getUser().getServer() + "," + userTerritory.getFactionId() + "," + userTerritory.getLevel() + "," + userTerritory.getNumberSweepTurn() + "," + userTerritory.getNumberBuySweepTurn() + ",'" + userTerritory.getRewardStatus() + "') ";
                        continue;
                    }
                    sql += ", (" + userId + "," + myUser.getUser().getServer() + "," + userTerritory.getFactionId() + "," + userTerritory.getLevel() + "," + userTerritory.getNumberSweepTurn() + "," + userTerritory.getNumberBuySweepTurn() + ",'" + userTerritory.getRewardStatus() + "') ";
                }
                session.createNativeQuery(sql).executeUpdate();
                session.getTransaction().commit();
            }
            return listUserTerritory;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }
    //endregion

    //region Swordsmanship
    public UserSwordsmanshipEntity getUserSwordsmanShip(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String query = "select * from dson.user_swordsmanship where user_id = " + userId;
            List<UserSwordsmanshipEntity> listUserSwordsmanship = session.createNativeQuery(query, UserSwordsmanshipEntity.class).getResultList();
            if (listUserSwordsmanship.isEmpty()) {
                UserSwordsmanshipEntity userSwordsmanship = new UserSwordsmanshipEntity(userId);
                session.getTransaction().begin();
                session.persist(userSwordsmanship);
                session.getTransaction().commit();
                return userSwordsmanship;
            } else return listUserSwordsmanship.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    public List<UserSwordsmanshipBookEntity> getListSwordsmanShipBook(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String query = "select * from dson.user_swordsmanship_book where user_id = " + userId;
            return session.createNativeQuery(query, UserSwordsmanshipBookEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion
}

