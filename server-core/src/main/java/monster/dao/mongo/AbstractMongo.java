package monster.dao.mongo;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import monster.service.db.Mongo;

public abstract class AbstractMongo {

    protected MongoDatabase getDb() {
        return Mongo.getInstance().getDb();
    }

    protected MongoCollection getCollection(String collectionName) {
        return getDb().getCollection(collectionName);
    }

    protected <T> MongoCollection<T> getCollection(String collectionName, Class<T> tClass) {
        return getDb().getCollection(collectionName, tClass);
    }
}
