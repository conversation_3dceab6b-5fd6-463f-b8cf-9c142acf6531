package monster.dao.mapping;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.log.Logs;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgHeroExpert;
import monster.config.lang.Lang;
import monster.config.penum.ExpertQuestType;
import monster.config.penum.NotifyType;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.dao.mapping.main.ResExpertEntity;
import monster.dao.mapping.main.ResExpertQuestEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.object.ExpertAnalysis;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.common.NotifyService;
import monster.service.resource.ResHero;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "user_hero_expert")
@Data
@NoArgsConstructor
public class UserHeroExpertEntity implements Serializable {
    @Id
    private int userId, heroId;
    private int questGroup;
    private String questData, questReceive;
    private boolean isNotify;

    public UserHeroExpertEntity(int userId, ResExpertEntity resExpertEntity) {
        this.userId = userId;
        this.heroId = resExpertEntity.getHeroId();
        this.questData = resExpertEntity.getDefaultData();
        this.questReceive = resExpertEntity.getDefaultData();
    }

    /**
     * update các quest liên quan tới đấu trường
     *
     * @param arenaType
     * @param isWin
     * @param expertData -> log data của arena battle
     */
    public void checkUpdateArena(int arenaType, boolean isWin, ExpertAnalysis.ExpertHeroData expertData) {
        try {

            JsonArray arrData = GsonUtil.parseJsonArray(questData);
            JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);
            if (arrData.size() <= questGroup) return; // đã hoàn thành nhiệm vụ

            JsonArray curData = arrData.get(questGroup).getAsJsonArray();
            JsonArray curReceive = arrReceive.get(questGroup).getAsJsonArray();
            JsonArray questId = CfgHeroExpert.getQuestId(heroId, questGroup);
            boolean hasUpdate = false, hasNotify = false;
            for (int i = 0; i < curReceive.size(); i++) {
                int receive = curReceive.get(i).getAsInt();
                long curValue = curData.get(i).getAsLong();
                ResExpertQuestEntity quest = CfgHeroExpert.mExpertQuest.get(questId.get(i).getAsInt());
                if (receive == 0 && quest != null) { // Chưa nhận thưởng quest
                    ExpertQuestType questType = ExpertQuestType.get(quest.getQuestType());
                    if (questType != null) {
                        long requiredValue = quest.getValue();
                        if (arenaType == 3) { // đánh boss
                            hasUpdate = checkUpdateBossAttack(expertData, questType, curData, curValue, i);
                            curValue = curData.get(i).getAsLong();
                            if (curValue > requiredValue) hasNotify = true;
                        } else {
                            switch (questType) {
                                case HERO_LEVEL:
                                    break;
                                case WIN_ARENA_CRYSTAL:
                                    if (isWin && arenaType == 1) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                case WIN_ARENA_TRIAL:
                                    if (isWin && arenaType == 2) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                case WIN_ARENA:
                                    if (isWin) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                case DAMAGE_MAX_IN_ARENA:
                                    long maxDamage = expertData.getMaxDamage();
                                    if (maxDamage > curValue) {
                                        curData.set(i, new JsonPrimitive(maxDamage));
                                        hasUpdate = true;
                                        if (maxDamage >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                case DAMAGE_IN_ARENA_CRYSTAL: {
                                    long totalDamage = expertData.getTotalDamage();
                                    if (arenaType == 1) {
                                        curData.set(i, new JsonPrimitive(curValue + totalDamage));
                                        hasUpdate = true;
                                        if (curValue + totalDamage >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case DAMAGE_IN_ARENA_TRIAL: {
                                    long totalDamage = expertData.getTotalDamage();
                                    if (arenaType == 2) {
                                        curData.set(i, new JsonPrimitive(curValue + totalDamage));
                                        hasUpdate = true;
                                        if (curValue + totalDamage >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case DAMAGE_IN_ARENA: {
                                    long totalDamage = expertData.getTotalDamage();
                                    if (totalDamage > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalDamage));
                                        hasUpdate = true;
                                        if (curValue + totalDamage >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case HEAL_MAX_IN_ARENA: {
                                    long maxHeal = expertData.getMaxHeal();
                                    if (maxHeal > curValue) {
                                        curData.set(i, new JsonPrimitive(maxHeal));
                                        hasUpdate = true;
                                        if (maxHeal >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case HEAL_IN_ARENA_CRYSTAL: {
                                    long totalHeal = expertData.getTotalHeal();
                                    if (arenaType == 1) {
                                        curData.set(i, new JsonPrimitive(curValue + totalHeal));
                                        hasUpdate = true;
                                        if (curValue + totalHeal >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case HEAL_IN_ARENA_TRIAL: {
                                    long totalHeal = expertData.getTotalHeal();
                                    if (arenaType == 2) {
                                        curData.set(i, new JsonPrimitive(curValue + totalHeal));
                                        hasUpdate = true;
                                        if (curValue + totalHeal >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case HEAL_IN_ARENA: {
                                    long totalHeal = expertData.getTotalHeal();
                                    if (totalHeal > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalHeal));
                                        hasUpdate = true;
                                        if (curValue + totalHeal >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case DEFENSE_IN_ARENA_CRYSTAL: {
                                    long totalDef = expertData.getTotalDef();
                                    if (arenaType == 1) {
                                        curData.set(i, new JsonPrimitive(curValue + totalDef));
                                        hasUpdate = true;
                                        if (curValue + totalDef >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case DEFENSE_IN_ARENA_TRIAL: {
                                    long totalDef = expertData.getTotalDef();
                                    if (arenaType == 2) {
                                        curData.set(i, new JsonPrimitive(curValue + totalDef));
                                        hasUpdate = true;
                                        if (curValue + totalDef >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case DEFENSE_IN_ARENA: {
                                    long totalDef = expertData.getTotalDef();
                                    if (totalDef > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalDef));
                                        hasUpdate = true;
                                        if (curValue + totalDef >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_ONE_KILL: {
                                    long maxKill = expertData.getMaxKill();
                                    if (maxKill >= 1) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_DOUBLE_KILL: {
                                    long maxKill = expertData.getMaxKill();
                                    if (maxKill >= 2) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_TRIPLE_KILL: {
                                    long maxKill = expertData.getMaxKill();
                                    if (maxKill >= 3) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_QUADRA_KILL: {
                                    long maxKill = expertData.getMaxKill();
                                    if (maxKill >= 4) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_PNETA_KILL: {
                                    long maxKill = expertData.getMaxKill();
                                    if (maxKill >= 5) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_HEXA_KILL: {
                                    long maxKill = expertData.getMaxKill();
                                    if (maxKill >= 6) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_NORMAL_ATTACK: {
                                    int totalNumber = expertData.getTotalNormalAttack();
                                    if (totalNumber > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                                        hasUpdate = true;
                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_ACTIVE_SKILL: {
                                    int totalNumber = expertData.getTotalSkillAttack();
                                    if (totalNumber > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                                        hasUpdate = true;
                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_COUNTER_ATTACK: {
                                    int totalNumber = expertData.getTotalCounterAttack();
                                    if (totalNumber > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                                        hasUpdate = true;
                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_NORMAL_ATTACK: {
                                    int totalNumber = expertData.getTotalNormalAttack();
                                    if (arenaType == 2 && totalNumber > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                                        hasUpdate = true;
                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_ACTIVE_SKILL: {
                                    int totalNumber = expertData.getTotalSkillAttack();
                                    if (arenaType == 2 && totalNumber > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                                        hasUpdate = true;
                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_COUNTER_ATTACK: {
                                    int totalNumber = expertData.getTotalCounterAttack();
                                    if (arenaType == 2 && totalNumber > 0) {
                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                                        hasUpdate = true;
                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_WIN_TOGETHER_HERO: {
                                    int requiredHeroId = (int) quest.getValue2();
                                    if (expertData.teammates.contains(requiredHeroId)) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_WIN_TOGETHER_FACTION: {
                                    int requiredFaction = (int) quest.getValue2();
                                    for (Integer teammate : expertData.teammates) {
                                        if (teammate > 0 && teammate != heroId) {
                                            ResHeroEntity resHero = ResHero.getHero(teammate);
                                            if (resHero != null && resHero.getHeroFaction().value == requiredFaction) {
                                                curData.set(i, new JsonPrimitive(curValue + 1));
                                                hasUpdate = true;
                                                if (curValue + 1 >= requiredValue) hasNotify = true;
                                                break;
                                            }
                                        }
                                    }
                                    break;
                                }
                                case ARENA_WIN_TOGETHER_CLASS: {
                                    int requiredClass = (int) quest.getValue2();
                                    for (Integer teammate : expertData.teammates) {
                                        if (teammate > 0 && teammate != heroId) {
                                            ResHeroEntity resHero = ResHero.getHero(teammate);
                                            if (resHero != null && resHero.getHeroClass().value == requiredClass) {
                                                curData.set(i, new JsonPrimitive(curValue + 1));
                                                hasUpdate = true;
                                                if (curValue + 1 >= requiredValue) hasNotify = true;
                                                break;
                                            }
                                        }
                                    }
                                    break;
                                }
                                case ARENA_WIN_TOGETHER_PET: {
                                    int requiredPet = (int) quest.getValue2();
                                    if (expertData.pets.contains(requiredPet)) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_WIN_TOGETHER_HERO: {
                                    int requiredHeroId = (int) quest.getValue2();
                                    if (arenaType == 2 && expertData.teammates.contains(requiredHeroId)) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_WIN_TOGETHER_FACTION: {
                                    int requiredFaction = (int) quest.getValue2();
                                    if (arenaType == 2) {
                                        for (Integer teammate : expertData.teammates) {
                                            if (teammate > 0 && teammate != heroId) {
                                                ResHeroEntity resHero = ResHero.getHero(teammate);
                                                if (resHero != null && resHero.getHeroFaction().value == requiredFaction) {
                                                    curData.set(i, new JsonPrimitive(curValue + 1));
                                                    hasUpdate = true;
                                                    if (curValue + 1 >= requiredValue) hasNotify = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_WIN_TOGETHER_CLASS: {
                                    int requiredClass = (int) quest.getValue2();
                                    if (arenaType == 2) {
                                        for (Integer teammate : expertData.teammates) {
                                            if (teammate > 0 && teammate != heroId) {
                                                ResHeroEntity resHero = ResHero.getHero(teammate);
                                                if (resHero != null && resHero.getHeroClass().value == requiredClass) {
                                                    curData.set(i, new JsonPrimitive(curValue + 1));
                                                    hasUpdate = true;
                                                    if (curValue + 1 >= requiredValue) hasNotify = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    break;
                                }
                                case ARENA_TRIAL_WIN_TOGETHER_PET: {
                                    int requiredPet = (int) quest.getValue2();
                                    if (arenaType == 2 && expertData.pets.contains(requiredPet)) {
                                        curData.set(i, new JsonPrimitive(curValue + 1));
                                        hasUpdate = true;
                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (hasUpdate) {
                arrData.set(questGroup, curData);
                List<Object> updateValues = new ArrayList<>();
                updateValues.addAll(Arrays.asList("quest_data", arrData.toString()));
                if (hasNotify) {
                    updateValues.addAll(Arrays.asList("is_notify", true));
                }
                if (DBJPA.update("user_hero_expert", updateValues, Arrays.asList("user_id", userId, "hero_id", heroId))) {
                    questData = arrData.toString();
                    setNotify(hasNotify);
                    //                    Logs.debug(String.format("udpate %s -> %s -> %s", userId, heroId, questData));
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(ex);
        }
    }

    public boolean checkUpdateBossAttack(ExpertAnalysis.ExpertHeroData expertData, ExpertQuestType questType, JsonArray curData, long curValue, int index) {
        switch (questType) {
            case BOSS_SERVER_NUMBER_ATTACK:
                curData.set(index, new JsonPrimitive(curValue + expertData.number));
                return true;
            case BOSS_SERVER_DEFENSE: {
                long totalDef = expertData.getTotalDef();
                if (totalDef > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalDef));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_HEAL: {
                long totalHeal = expertData.getTotalHeal();
                if (totalHeal > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalHeal));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_DAMAGE:
                long totalDamage = expertData.getTotalDamage();
                if (totalDamage > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalDamage));
                    return true;
                }
                break;
            case BOSS_SERVER_ACTIVE_SKILL: {
                int totalNumber = expertData.getTotalSkillAttack();
                if (totalNumber > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalNumber));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_NORMAL_ATTACK: {
                int totalNumber = expertData.getTotalNormalAttack();
                if (totalNumber > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalNumber));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_COUNTER_ATTACK:
                int totalNumber = expertData.getTotalCounterAttack();
                if (totalNumber > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalNumber));
                    return true;
                }
                break;
        }
        return false;
    }

    /**
     * update hero level
     *
     * @param updateQuestType -> loại quest
     * @param value           -> thông số cần update
     */
    public void checkUpdate(ExpertQuestType updateQuestType, int value) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);
        if (arrData.size() <= questGroup) return; // đã hoàn thành nhiệm vụ
        JsonArray curData = arrData.get(questGroup).getAsJsonArray();
        JsonArray curReceive = arrReceive.get(questGroup).getAsJsonArray();
        JsonArray questId = CfgHeroExpert.getQuestId(heroId, questGroup);

        boolean hasUpdate = false, hasNotify = false;
        for (int i = 0; i < curReceive.size(); i++) {
            int receive = curReceive.get(i).getAsInt();
            long curValue = curData.get(i).getAsLong();
            ResExpertQuestEntity quest = CfgHeroExpert.mExpertQuest.get(questId.get(i).getAsInt());
            if (receive == 0 && quest != null) { // Chưa nhận thưởng quest
                ExpertQuestType questType = ExpertQuestType.get(quest.getQuestType());
                if (questType == updateQuestType) {
                    switch (questType) {
                        case HERO_LEVEL: {
                            long requiredValue = quest.getValue();
                            if (curValue < value) {
                                curData.set(i, new JsonPrimitive(value));
                                hasUpdate = true;
                                if (value >= requiredValue) hasNotify = true;
                            }
                            break;
                        }
                        case WIN_CELESTIAL:
                        case TAVERN_4_STAR:
                        case TAVERN_5_STAR:
                        case TAVERN_6_STAR: {
                            long requiredValue = quest.getValue();
                            curData.set(i, new JsonPrimitive(curValue + value));
                            hasUpdate = true;
                            if (curValue + value >= requiredValue) hasNotify = true;
                            break;
                        }
                    }
                }
            }
        }
        if (hasUpdate) {
            arrData.set(questGroup, curData);
            List<Object> updateValues = new ArrayList<>();
            updateValues.addAll(Arrays.asList("quest_data", arrData.toString()));
            if (hasNotify) {
                updateValues.addAll(Arrays.asList("is_notify", true));
            }
            if (DBJPA.update("user_hero_expert", updateValues, Arrays.asList("user_id", userId, "hero_id", heroId))) {
                questData = arrData.toString();
                setNotify(hasNotify);
            }
        }
    }

    public void receive(AHandler handler, int index) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);

        JsonArray aData = arrData.get(questGroup).getAsJsonArray();
        JsonArray aReceive = arrReceive.get(questGroup).getAsJsonArray();
        JsonArray questId = CfgHeroExpert.getQuestId(heroId, questGroup);
        ResExpertQuestEntity resExpertQuestEntity = CfgHeroExpert.mExpertQuest.get(questId.get(index).getAsInt());
        long number = aData.get(index).getAsLong();
        long maxNumber = resExpertQuestEntity.getValue();
        int receive = aReceive.get(index).getAsInt();
        if (number >= maxNumber) {
            if (receive == 1) {
                handler.addErrResponse(handler.getLang(Lang.bonus_already_received));
            } else {
                aReceive.set(index, new JsonPrimitive(1));
                arrReceive.set(questGroup, aReceive);
                if (DBJPA.update("user_hero_expert", Arrays.asList("quest_receive", arrReceive.toString(), "is_notify", 0), Arrays.asList("user_id", userId, "hero_id", heroId))) {
                    setNotify(false);
                    questReceive = arrReceive.toString();
                    handler.addResponse(handler.getCommonVector(Bonus.receiveListItem(handler.getMUser(), "hero_expert", resExpertQuestEntity.getBonus())));
                    Actions.save(handler.getUser(), "hero_expert", "receive", "heroKey", heroId, "group", questGroup, "index", index);
                    CfgHeroExpert.addPoint(handler.getMUser(), resExpertQuestEntity.getPoint());
                    checkUpdateNotify(handler.getMUser());
                } else handler.addErrResponse();
            }
        } else {
            handler.addErrResponse(handler.getLang(Lang.quest_incomplete));
        }
    }

    /**
     * Sau khi nhận thưởng, kiểm tra lại
     * - Còn notify nữa không
     * - đang sang group mới chưa
     */
    private void checkUpdateNotify(MyUser mUser) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);

        JsonArray aData = arrData.get(questGroup).getAsJsonArray();
        JsonArray aReceive = arrReceive.get(questGroup).getAsJsonArray();
        JsonArray aQuestId = CfgHeroExpert.getQuestId(heroId, questGroup);

        boolean hasNotify = false;
        boolean nextGroup = true;
        for (int i = 0; i < aData.size(); i++) {
            long number = aData.get(i).getAsLong();
            int receive = aReceive.get(i).getAsInt();
            int questId = aQuestId.get(i).getAsInt();

            if (receive == 0) {
                nextGroup = false;
                long maxNumber = CfgHeroExpert.mExpertQuest.get(questId).getValue();
                if (number >= maxNumber) {
                    hasNotify = true;
                    break;
                }
            }
        }
        List<Object> updateValues = new ArrayList<>();
        updateValues.addAll(Arrays.asList("is_notify", hasNotify ? 1 : 0));
        if (nextGroup) {
            updateValues.addAll(Arrays.asList("quest_group", questGroup + 1));
        }
        if (DBJPA.update("user_hero_expert", updateValues, Arrays.asList("user_id", userId, "hero_id", heroId))) {
            isNotify = hasNotify;
            if (nextGroup) {
                questGroup += 1;

                try {
                    ResHeroEntity resHero = ResHero.getHero(heroId);
                    int maxLevel = mUser.getResources().heroes.stream().filter(hero -> resHero.sameHero(hero.getHeroId()))
                            .mapToInt(UserHeroEntity::getLevel).max().orElse(0);
                    if (maxLevel > 0) CfgHeroExpert.checkUpdateLevel(mUser, heroId, maxLevel);
                } catch (Exception ex) {
                    Logs.error(ex);
                }
            }
        }
    }

    public Pbmethod.ListCommonVector toProto(MyUser mUser) {
        Lang lang = mUser.getLang();
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            builder.addAVector(Pbmethod.CommonVector.newBuilder()
                    .addALong(heroId)
                    .addALong(questGroup + 1 > arrData.size() ? arrData.size() : questGroup + 1)
                    .addALong(arrData.size()).addAString(ResHero.getHero(heroId).getName()));
        }
        {
            if (arrData.size() > questGroup) {
                boolean isAllQuestDone = true;
                JsonArray aData = arrData.get(questGroup).getAsJsonArray();
                JsonArray aReceive = arrReceive.get(questGroup).getAsJsonArray();
                JsonArray questId = CfgHeroExpert.getQuestId(heroId, questGroup);
                try {
                    for (int i = 0; i < aData.size(); i++) {
                        ResExpertQuestEntity quest = CfgHeroExpert.mExpertQuest.get(questId.get(i).getAsInt());
                        ExpertQuestType questType = ExpertQuestType.get(quest.getQuestType());
                        long number = aData.get(i).getAsLong();
                        long maxNumber = quest.getValue();
                        int receive = aReceive.get(i).getAsInt();

                        Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
                        cmm.addALong(i).addALong(0); // TODO clientToGo cần thêm config vào đây
                        cmm.addALong(getStatus(receive, number, maxNumber));
                        cmm.addALong(number > maxNumber ? maxNumber : number).addALong(maxNumber).addALong(quest.getPoint()).addAllALong(quest.getBonus());
                        if (questType == ExpertQuestType.COMPLETE_ALL) {
                            cmm.addAString(questType.getName(lang));
                        } else
                            cmm.addAString(questType.getName(lang, heroId, quest.getValue(), quest.getValue2()));
                        //                        cmm.addAString(String.format(questType.getName(lang), quest.getValue())); // ResHero.getHero(heroId).getName(),

                        builder.addAVector(cmm);
                        if (getStatus(receive, number, maxNumber) != Status.MISSION_RECEIVED.value) {
                            isAllQuestDone = false;
                        }
                    }
                    if (isAllQuestDone) checkUpdateNotify(mUser);
                } catch (Exception ex) {
                    Logs.error(questId.toString() + " " + heroId + " " + questGroup);
                    Logs.error(ex);
                }
            } else { // finish status
                int fakeGroup = questGroup - 1;
                JsonArray aData = arrData.get(fakeGroup).getAsJsonArray();
                JsonArray aReceive = arrReceive.get(fakeGroup).getAsJsonArray();
                JsonArray questId = CfgHeroExpert.getQuestId(heroId, fakeGroup);
                for (int i = 0; i < aData.size(); i++) {
                    ResExpertQuestEntity quest = CfgHeroExpert.mExpertQuest.get(questId.get(i).getAsInt());
                    ExpertQuestType questType = ExpertQuestType.get(quest.getQuestType());
                    long number = aData.get(i).getAsLong();
                    long maxNumber = quest.getValue();
                    int receive = aReceive.get(i).getAsInt();

                    Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
                    cmm.addALong(i).addALong(Status.MISSION_RECEIVED.value);
                    cmm.addALong(number > maxNumber ? maxNumber : number).addALong(maxNumber).addALong(quest.getPoint()).addAllALong(quest.getBonus());
                    if (questType == ExpertQuestType.COMPLETE_ALL) {
                        cmm.addAString(questType.getName(lang));
                    } else
                        cmm.addAString(questType.getName(lang, heroId, quest.getValue(), quest.getValue2()));
                    //                        cmm.addAString(String.format(questType.getName(lang), quest.getValue())); // ResHero.getHero(heroId).getName(),

                    builder.addAVector(cmm);
                }
            }
        }
        return builder.build();
    }

    /**
     * status
     * 2 là đã nhận phần thưởng rồi
     * 1 và đủ đk nhận thưởng
     * 0 chưa đủ
     *
     * @param receive
     * @param number
     * @param maxNumber
     * @return
     */
    private int getStatus(int receive, long number, long maxNumber) {
        if (receive == 1) return 2;
        if (number >= maxNumber) return 1;
        return 0;
    }

    public boolean isFinish() {
        return questGroup >= GsonUtil.parseJsonArray(questData).size();
    }
}
