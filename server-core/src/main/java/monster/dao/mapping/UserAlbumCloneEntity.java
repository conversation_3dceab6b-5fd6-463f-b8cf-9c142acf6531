package monster.dao.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "user_album_clone")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAlbumCloneEntity implements Serializable {
    @Id
    public int userId, imageId, albumId;
}
