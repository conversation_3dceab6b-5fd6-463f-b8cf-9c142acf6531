package monster.dao.mapping;

import lombok.Data;
import monster.controller.AHandler;
import monster.object.MyUser;
import monster.service.battle.dependence.BattleResultEntity;
import monster.service.battle.dependence.BattleUtil;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import java.util.Random;

@Entity
@Table(name = "clan_req")
@Data
public class ClanReqEntity implements java.io.Serializable {

    @Id
    private int clanId, userId;
    private Date dateCreated;

    public ClanReqEntity() {
    }

    public ClanReqEntity(int clanId, int userId) {
        this.clanId = clanId;
        this.userId = userId;
        this.dateCreated = new Date();
    }

}
