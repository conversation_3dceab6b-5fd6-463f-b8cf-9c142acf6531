package monster.dao.mapping;

import lombok.Data;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_item")
@Data
public class UserItemEntity implements Serializable {
    @Id
    int userId, itemId;
    int number;
    Date dateCreated;

    @Transient
    int numberUsing;

    public UserItemEntity() {
    }

    public UserItemEntity(int userId, int itemId, int number) {
        this.userId = userId;
        this.itemId = itemId;
        this.number = number;
    }

    public boolean isAvailable() {
        return getNumberAvailable() > 0;
    }

    public boolean isAvailable(int value) {
        return getNumberAvailable() >= value;
    }

    public int getNumberAvailable(){
        return number - numberUsing;
    }

    public void using() {
        numberUsing++;
    }

    public void release() {
        numberUsing--;
    }

    public void add(int value) {
        this.number += value;
    }
}
