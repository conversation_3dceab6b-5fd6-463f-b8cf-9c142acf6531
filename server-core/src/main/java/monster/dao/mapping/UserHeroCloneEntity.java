package monster.dao.mapping;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgHero;
import monster.config.CfgServer;
import monster.dao.mapping.main.ResHeroEntity;
import monster.object.MyUser;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.entity.HeroBattleInterface;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResArtifact;
import monster.service.resource.ResHero;
import protocol.Pbmethod;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_hero_clone")
@Data
@NoArgsConstructor
public class UserHeroCloneEntity implements Serializable, HeroBattleInterface {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    int userId, heroId, positionIndex, level, star, locked, lockedCrystal, lockedTrial, lockedVodau;
    String item, skills, specialItem, heroVoid;
    int treasure, treasureEffect, treasureIndex, powerScale;
    @Transient
    int skin;
    @Transient
    Point heroPoint = new Point();
    @Transient
    SkillEntity activeSkill;
    @Transient
    float[] addPercent = new float[]{1f, 1f, 1f, 1f};
    @Transient
    List<SkillEntity> passiveSkills = new ArrayList<>();

    public UserHeroCloneEntity(int userId, int heroKey, int positionIndex, int level, int star,
                               String item, String skills, String specialItem, String heroVoid, int treasure, int treasureEffect, int treasureIndex, int powerScale) {
        this.userId = userId;
        this.heroId = heroKey;
        this.positionIndex = positionIndex;
        this.level = level;
        this.star = star;
        this.item = item;
        this.skills = skills;
        this.specialItem = specialItem;
        this.heroVoid = heroVoid;
        this.treasure = treasure;
        this.treasureEffect = treasureEffect;
        this.treasureIndex = treasureIndex;
        this.powerScale = powerScale;
    }

    public String getHeroVoid() {
        return StringHelper.isEmpty(heroVoid) ? "[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]" : heroVoid;
    }

    public int getIntVoidLevel() {
        JsonArray arr = GsonUtil.parseJsonArray(getHeroVoid());
        // index các void level 3 7 11 15
        for (int i = 0; i < 4; i++) {
            if (arr.get(15 - i * 4).getAsInt() > 0) return 4 - i;
        }
        return 0;
    }

    public void calculatePointHeroClone(MyUser mUser) {
        IMath.calculateClone(mUser, this, getListSkillIds());
    }

    public String getSkills() {
        if (skills == null) skills = "[0,0,0,0,0,0,0,0,0,0]";

        JsonArray arr = GsonUtil.parseJsonArray(skills);
        if (arr.size() < 10) {
            while (arr.size() < 10) arr.add(0);
            skills = arr.toString();
        }

        return skills;
    }

    public List<Integer> getListSkills() {
        List<Integer> aSkillIndex = new ArrayList<>();
        JsonArray arr = GsonUtil.parseJsonArray(getSkills());
        for (int i = 0; i < star - 10; i++) {
            aSkillIndex.add(arr.get(i).getAsInt());
        }

        return aSkillIndex;
    }

    public List<Integer> getListSkillIds() {
        List<Integer> aSkills = new ArrayList<>();
        JsonArray arr = GsonUtil.parseJsonArray(getSkills());
        for (int i = 0; i < star - 10; i++) {
            int skillId = CfgHero.getESkill(i, arr.get(i).getAsInt());
            if (skillId > 0) aSkills.add(skillId);
        }

        return aSkills;
    }

    @Override
    public HeroInfoEntity toHeroInfo(int team, int position) {
        HeroInfoEntity hero = new HeroInfoEntity();
        hero.id = id;
        hero.heroId = heroId;
        hero.team = team;
        hero.position = position;
        hero.level = getLevel();
        hero.star = star;
        hero.skin = skin;
        hero.levelCalculate = level;
        hero.voidLevel = getIntVoidLevel();
        hero.setHeroInfo(getListSkillIds());
        hero.point = getPoint().cloneInstance();
        hero.addPercent = addPercent.clone();
        hero.clazz = getClazz();
        hero.faction = getFaction();
        hero.isBoss = false;
        hero.bossType = 0;
        return hero;
    }

    public HeroType getFaction() {
        return ResHero.getHero(heroId).getHeroFaction();
    }

    public HeroType getClazz() {
        return ResHero.getHero(heroId).getHeroClass();
    }

    public Point getPoint() {
        return heroPoint;
    }

    public Pbmethod.PbTeamHeroInfo.Builder protoTeamHeroInfo() {
        Pbmethod.PbTeamHeroInfo.Builder builder = Pbmethod.PbTeamHeroInfo.newBuilder();
        builder.setId(getIntVoidLevel());
        builder.setStar(star).setLevel(level).setHeroId(heroId).setSkin(skin);
        builder.addAllPoint(heroPoint.getCurrentValues());
        return builder;
    }

    public boolean useThisArtifact(int artifactId, int artifactStar) {
        JsonArray arr = GsonUtil.parseJsonArray(item);

        return arr.get(ResArtifact.SLOT_ID).getAsInt() == artifactId && arr.get(ResArtifact.SLOT_STAR).getAsInt() == artifactStar;
    }

    public boolean isLocked() {
        return locked == 1 || lockedCrystal == 1 || lockedTrial == 1 || lockedVodau == 1;
    }

    public boolean update(String... values) {
        return DBJPA.update("user_hero", Arrays.stream(values).collect(Collectors.toList()), Arrays.asList("id", id));
    }

    public int getStar() {
        return star;
    }

    public boolean isVoidAvailable() {
        ResHeroEntity resHero = ResHero.getHero(heroId);
        return level != 0 && star >= 15 && resHero != null && (CfgServer.isTestServer() || resHero.getVoidEnable() == 1);
    }

    public int[] getTreasureInfo() {
        return new int[]{treasure, treasureEffect, treasureIndex};
    }

    public int[] getLevelEnhance() {
        return new int[]{getSpecialsInt().get(0), getSpecialsInt().get(2)};
    }

    public int[] getTierUpgrade() {
        return new int[]{getSpecialsInt().get(1), getSpecialsInt().get(3)};
    }

    public List<Integer> getSpecialsInt() {
        if (GsonUtil.strToListInt(specialItem) == null || specialItem == null) {
            specialItem = "[0,0,0,0]";
            return Arrays.asList(0, 0, 0, 0);
        }

        return GsonUtil.strToListInt(specialItem);
    }
}
