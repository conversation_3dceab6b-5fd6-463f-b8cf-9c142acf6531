package monster.dao.mapping;

import grep.database.DBJPA;
import grep.helper.LogicUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgPet;
import monster.dao.mapping.main.ResPetEntity;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Arrays;

@Entity
@Table(name = "user_pet")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserPetEntity implements java.io.Serializable {

    @Id
    private int userId, petId;
    @Builder.Default
    private int level = 1, tier = 1;
    @Builder.Default
    private int passive1 = 1, passive2 = 0, passive3 = 0, passive4 = 0;
    @Builder.Default
    private int rune1 = 0, rune2 = 0, rune3 = 0;


    public UserPetEntity(int userId, int petId) {
        this.userId = userId;
        this.petId = petId;
    }

    public boolean isMaxRuneLevel(int runeIndex) {
        return Arrays.asList(rune1, rune2, rune3).get(runeIndex) >= CfgPet.maxRune;
    }

    public boolean isMaxPassive() {
        return passive1 == CfgPet.maxPassive && passive2 == CfgPet.maxPassive && passive3 == CfgPet.maxPassive && passive4 == CfgPet.maxPassive;
    }

    public void increaseRune(int index, int value) {
        switch (index) {
            case 0:
                rune1 += value;
                break;
            case 1:
                rune2 += value;
                break;
            case 2:
                rune3 += value;
                break;
        }
    }

    public void increasePassive(int index, int value) {
        switch (index) {
            case 1:
                passive1 += value;
                break;
            case 2:
                passive2 += value;
                break;
            case 3:
                passive3 += value;
                break;
            case 4:
                passive4 += value;
                break;
        }
    }

    public int getPassive(int index) {
        switch (index) {
            case 1:
                return passive1;
            case 2:
                return passive2;
            case 3:
                return passive3;
            case 4:
                return passive4;
        }
        return 0;
    }

    public int getRune(int index) {
        return Arrays.asList(rune1, rune2, rune3).get(index);
    }

    public long getPower() {
//        long powerPet = level * 3000 + (passive1 + passive2 + passive3 + passive4) * 2286 + rune1 + rune2 * 6 + rune3;
        return IMath.getPetPower(this);
    }

    public Pbmethod.CommonVector.Builder toProto(UserEntity user) {
        return Pbmethod.CommonVector.newBuilder().addALong(petId).addALong(level).addALong(tier)
                .addALong(passive1).addALong(passive2).addALong(passive3).addALong(passive4)
                .addALong(rune1).addALong(rune2).addALong(rune3).addALong(getPower());
    }

    public Point toPoint() {
        ResPetEntity resPet = ResMonster.getPet(petId);
        Point point = resPet.getPoint(passive1, passive2, passive3, passive4);
        if (isMaxPassive()) {
            point.setCurrentValue(Point.FIX_HP, resPet.getScaleHp(rune1));
            point.setCurrentValue(Point.FIX_ATTACK, resPet.getScaleAttack(rune2));
            point.setCurrentValue(Point.FIX_SPEED, resPet.getScaleSpeed(rune3));
        }
        return point;
    }

    public boolean increaseTier() {
        int newTier = tier + 1;
        if (DBJPA.update("user_pet", Arrays.asList("tier", newTier, "passive" + newTier, 1), Arrays.asList("user_id", userId, "pet_id", petId))) {
            this.tier = newTier;
            switch (newTier) {
                case 2 -> passive2 = 1;
                case 3 -> passive3 = 1;
                case 4 -> passive4 = 1;
            }
            return true;
        }
        return false;
    }

    public boolean updateLevel(int numberUp) {
        int newLevel = level + numberUp;
        if (DBJPA.update("user_pet", Arrays.asList("level", newLevel), Arrays.asList("user_id", userId, "pet_id", petId))) {
            this.level = newLevel;
            return true;
        }
        return false;
    }

    public boolean updatePassive(int passiveIndex, int numberUp) {
        int newPassiveLevel = 1;
        String passiveKey = "passive" + passiveIndex;
        switch (passiveIndex) {
            case 1 -> newPassiveLevel = passive1 + numberUp;
            case 2 -> newPassiveLevel = passive2 + numberUp;
            case 3 -> newPassiveLevel = passive3 + numberUp;
            case 4 -> newPassiveLevel = passive4 + numberUp;
        }
        if (DBJPA.update("user_pet", Arrays.asList(passiveKey, newPassiveLevel), Arrays.asList("user_id", userId, "pet_id", petId))) {
            increasePassive(passiveIndex, numberUp);
            return true;
        }
        return false;
    }

    public boolean updateRune(int runeIndex, int numberUp) {
        int newRuneLevel = 1;
        String runeKey = "rune" + (runeIndex + 1);
        switch (runeIndex) {
            case 0 -> newRuneLevel = rune1 + numberUp;
            case 1 -> newRuneLevel = rune2 + numberUp;
            case 2 -> newRuneLevel = rune3 + numberUp;
        }
        if (DBJPA.update("user_pet", Arrays.asList(runeKey, newRuneLevel), Arrays.asList("user_id", userId, "pet_id", petId))) {
            increaseRune(runeIndex, numberUp);
            return true;
        }
        return false;
    }

    public HeroBattleEntity toHeroBattle(int team) {
        HeroBattleEntity heroBattle = new HeroBattleEntity();
        heroBattle.heroId = petId;
        heroBattle.position = 6;
        heroBattle.team = team;
        heroBattle.tier = tier;
        ResPetEntity resPet = ResMonster.getPet(petId);
        heroBattle.point = resPet.getPoint(passive1, passive2, passive3, passive4);
        if (isMaxPassive()) {
            heroBattle.point.setCurrentValue(Point.FIX_HP, resPet.getScaleHp(rune1));
            heroBattle.point.setCurrentValue(Point.FIX_ATTACK, resPet.getScaleAttack(rune2));
            heroBattle.point.setCurrentValue(Point.FIX_SPEED, resPet.getScaleSpeed(rune3));
        }
        heroBattle.activeSkill = ResHero.getSkill(resPet.getActiveSkill() + level - 1);
        return heroBattle;
    }

    public UserPetTopEntity toPetTop() {
        return new UserPetTopEntity(this);
    }

    public boolean isMaxAllRune() {
        return LogicUtil.getMinInt(rune1, rune2, rune3) >= CfgPet.maxRune;
    }


}
