package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Data
@Entity
@NoArgsConstructor
@Table(name = "freedom_arena_log_buff")
public class FreedomArenaLogBuffEntity {
    @Id
    int buffId;
    long count;


    public FreedomArenaLogBuffEntity(int buffId) {
        this.buffId = buffId;
        this.count = 0;
    }

    public void incre() {
        count++;
    }
}
