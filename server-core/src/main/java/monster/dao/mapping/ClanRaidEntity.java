package monster.dao.mapping;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgClan;
import monster.config.CfgClanRaid;
import monster.config.lang.Lang;
import monster.config.penum.*;
import monster.controller.AHandler;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.battle.dependence.entity.SimulateHero;
import monster.service.monitor.EventMonitor;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.UserTeam;
import monster.task.dbcache.MailCreatorCache;
import monster.task.dbcache.MaterialCache;
import protocol.Pbmethod;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "clan_raid")
@Data
@NoArgsConstructor
public class ClanRaidEntity implements java.io.Serializable {

    @Id
    private int clanId, level;
    long power;
    String dataMonster, dataDamage = "[]";

    public ClanRaidEntity(int clanId, int raidLevel) {
        this.clanId = clanId;
        this.level = raidLevel;
        if (level <= 60) genNewHero(level);
    }

    public long getPower() {
        int size = CfgClanRaid.config.power.size();
        return size <= level - 1 ? CfgClanRaid.config.power.get(size - 1) : CfgClanRaid.config.power.get(level - 1);
    }

    public boolean isAllMonsterDie() {
        HeroInfoEntity[] aMonster = getAMonster();
        for (HeroInfoEntity hero : aMonster) {
            if (hero != null && hero.point.isAlive()) {
                return false;
            }
        }
        return true;
    }

    public boolean sendBonus(String clanName) {
        String message = String.format(Lang.getTitle("content_clan_raid_kill"), clanName, level);
        //        String message = String.format("Hạm đội id: %s\nĐã tiêu diệt boss Hạm Đội cấp độ %s", clanId, level);
        List<DamageEntity> aDamage = getADamage();
        if (aDamage.isEmpty()) {
            Logs.warn(String.format("ClanRaid bonus noDamage clanId=%s level=%s", clanId, level));
            return true;
        }
        List<UserClanEntity> aUserClan = dbListUserClan(aDamage.stream().map(DamageEntity::getUserId).collect(Collectors.toList()));
        if (aUserClan == null) return false; // Database error
        List<UserMailEntity> aMail = new ArrayList<>();
        List<String> aSql2 = new ArrayList<>();
        for (int i = 0; i < aDamage.size(); i++) {
            DamageEntity entity = aDamage.get(i);
            UserClanEntity userClan = aUserClan.stream().filter(user -> user.getUserId() == entity.userId).findFirst().orElse(null);
            if (userClan != null) {
                final int index = i + 1;
                JsonArray arr = GsonUtil.parseJsonArray(userClan.getBossRaid());
                if (arr.contains(new JsonPrimitive(level))) {
                    aMail.add(UserMailEntity.builder().userId(userClan.getUserId())
                            .title(String.format(Lang.getTitle("title_boss_damage_top"), index)).message(Lang.getTitle("content_clan_raid_recevied"))
                            .build());
                    //                    aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index), Lang.getTitle("content_clan_raid_recevied"), ""));
                } else {
                    arr.add(level);
                    aSql2.add(DBJPA.getUpdateQuery("user_clan", Arrays.asList("boss_raid", arr.toString()), Arrays.asList("user_id", userClan.getUserId())));
                    UserEntity user = UserOnline.getDbUser(userClan.getUserId());
                    if (user != null) {
                        Actions.save(user, "clan_raid", "debug", "bossRaid", arr.toString(), "level", level);
                    }
                    String title = String.format(Lang.getTitle("title_boss_damage_top"), index), bonus = "";
                    if (index == 1) {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final1Gold.get(level - 1), CfgClanRaid.config.final1Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final1Gold.get(level - 1), CfgClanRaid.config.final1Coin.get(level - 1))));
                    } else if (index == 2) {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final2Gold.get(level - 1), CfgClanRaid.config.final2Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final2Gold.get(level - 1), CfgClanRaid.config.final2Coin.get(level - 1))));
                    } else if (index == 3) {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final3Gold.get(level - 1), CfgClanRaid.config.final3Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final3Gold.get(level - 1), CfgClanRaid.config.final3Coin.get(level - 1))));
                    } else if (index <= 10) {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final10Gold.get(level - 1), CfgClanRaid.config.final10Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final10Gold.get(level - 1), CfgClanRaid.config.final10Coin.get(level - 1))));
                    } else if (index <= 15) {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final15Gold.get(level - 1), CfgClanRaid.config.final15Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final15Gold.get(level - 1), CfgClanRaid.config.final15Coin.get(level - 1))));
                    } else if (index <= 20) {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final20Gold.get(level - 1), CfgClanRaid.config.final20Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final20Gold.get(level - 1), CfgClanRaid.config.final20Coin.get(level - 1))));
                    } else {
                        bonus = String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final30Gold.get(level - 1), CfgClanRaid.config.final30Coin.get(level - 1));
                        //                        aSql1.add(DBHelper.sqlMail(userClan.getUserId(), String.format(Lang.getTitle("title_boss_damage_top"), index),
                        //                                message, String.format("[1,%s,3,1,6,%s]", CfgClanRaid.config.final30Gold.get(level - 1), CfgClanRaid.config.final30Coin.get(level - 1))));
                    }
                    aMail.add(UserMailEntity.builder().userId(userClan.getUserId())
                            .title(title).message(message).bonus(bonus).origin("mail_clanRaid")
                            .build());
                }
            }
        }
        if (dbSendBonus(aSql2)) {
            MailCreatorCache.sendMail(aMail);
            return true;
        }
        return false;
    }

    public synchronized void attack(AHandler handler, MyUser mUser, UserClanEntity userClan, List<Long> heroIds, ClanEntity clan) {
        UserEntity user = mUser.getUser();
        long remainTime = userClan.getRaidRemainTime();
        int raidFee = userClan.getRaidFee();
        if (remainTime == 0) {
            raidFee = 0;
        }
        if (raidFee > 90) {
            handler.addErrResponse(handler.getLang(Lang.err_number_attack));
            return;
        }
        if (user.getGem() < raidFee) {
            handler.addErrResponse(handler.getLang(Lang.err_not_enough_gem));
            return;
        }
        BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
        if (myTeam == null) {
            handler.addErrResponse(handler.getLang(Lang.err_hero_duplicate));
            return;
        }
        UserMaterialEntity materialEntity = mUser.getResources().getMaterial(MaterialType.GUILD_COIN);
        if (materialEntity == null) {
            handler.addErrResponse();
            return;
        }

        BattleResultEntityNew battleResult = BattleBuilder.builder().setTeam(myTeam, BattleTeam.builder().aHero(getAMonster()).build())
                .setMode(BattleType.MODE_GUILD_BOSS_RAID).setInfo(user.getId()).battle();
        int nextRaidFee = remainTime == 0 ? 0 : raidFee + CfgClan.config.raid.feeStep;
        long raidCountdown = remainTime == 0 ? System.currentTimeMillis() : userClan.raidCountdown;

        int addGold = CfgClanRaid.config.gold.get(level - 1), guildExp = CfgClanRaid.config.guildExp.get(level - 1), guildCoin = CfgClanRaid.config.guildCoin.get(level - 1);
        long totalDamage = battleResult.getTotalDamageTeam1();

        String strDataDamage = addDamage(mUser.getUser(), totalDamage);
        updateMonsterHp(battleResult.getSimulateResult().team2);
        String strDataMonster = new Gson().toJson(aMonster);
        if (battleResult.isAllOpponentDie()) {
            addGold += CfgClanRaid.config.killGold.get(level - 1);
            guildCoin += CfgClanRaid.config.killCoin.get(level - 1);
        }
        if (dbUpdateBattle(mUser, strDataMonster, strDataDamage, nextRaidFee, raidCountdown, addGold, guildCoin, guildExp, raidFee)) {
            MaterialCache.getInstance().addValue(materialEntity, guildCoin);
            user.addGold(addGold);
            user.addGem(-raidFee, mUser);
            clan.addExp(guildExp);
            //            materialEntity.add(guildCoin);

            userClan.setRaidCountdown(raidCountdown);
            userClan.setRaidFee(nextRaidFee);

            dataMonster = strDataMonster;
            dataDamage = strDataDamage;

            cacheTopDamage();
            Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(mUser.getUser(), Lang.getTitle("title_clan_raid") + level);
            builder.addAllABonus(ListUtil.ofLong(Bonus.BONUS_GEM, user.getGem(), -raidFee));
            handler.addResponse(builder.build());

            mUser.getMBattleInput().put(BattleType.MODE_GUILD_BOSS_RAID, BattleInputCache.builder().battleType(BattleType.MODE_GUILD_BOSS_RAID).battleResult(battleResult)
                    .output(List.of(
                            ListUtil.ofLong(Bonus.BONUS_GOLD, user.getGold(), addGold, Bonus.BONUS_MATERIAL, MaterialType.TYPE_USED_ITEM, materialEntity.materialId, materialEntity.getNumber(), guildCoin),
                            ListUtil.ofLong(user.getGold(), user.getGem(), materialEntity.getNumber(), clan.getLevel(), clan.getExp())
                    )).build());

            UserTeam.saveOrUpdateUserTeam(mUser.getUser(), TeamType.BOSS_CLAN, myTeam, battleResult.atkPower);
            Actions.logGold(user, "clan_raid", addGold);
            Actions.logMaterial(user, "clan_raid", materialEntity, guildCoin);
            if (raidFee > 0) Actions.logGem(user, "clan_raid", -raidFee);
            Actions.save(mUser.getUser(), "clan", "clan_raid", "id", clanId, "level", level, "percentHp", percentHp());
            EventType.BOSS_CLAN_ATTACK.addEvent(mUser);
            if (userClan.getRaidFee() > 0) NotifyType.CLAN_RAID.sendRemoveNotify(mUser);
        } else {
            addDamage(mUser.getUser(), -totalDamage);
            handler.addErrResponse();
        }
    }

    void updateMonsterHp(List<SimulateHero> aHero) {
        HeroInfoEntity[] aMonster = getAMonster();
        for (SimulateHero heroBattle : aHero) {
            float percentFloat = heroBattle.outputPoint.percentHpFloat();
            if (percentFloat == 0 && heroBattle.outputPoint.getHP() > 0) percentFloat = 0.01f;

            if (aMonster[heroBattle.pos] != null && aMonster[heroBattle.pos].point.getHP() >= 0) {
                aMonster[heroBattle.pos].point.setStartHpPercent(percentFloat);
                aMonster[heroBattle.pos].point.setPercentHp(heroBattle.outputPoint.percentHpLong());
                aMonster[heroBattle.pos].point.set(Point.REVIVED, heroBattle.outputPoint.getRevived());
            }
        }
    }

    public void genNewHero(int level) {
        HeroInfoEntity[] tmpHero = CfgClanRaid.getAMonster(level);
        this.power = Arrays.stream(tmpHero).filter(hero -> hero != null)
                .mapToLong(hero -> IMath.getPower(hero.point)).sum();
        this.dataMonster = new Gson().toJson(tmpHero);
    }

    public int getMonsterId() {
        HeroInfoEntity[] aMonster = getAMonster();
        for (HeroInfoEntity monster : aMonster) {
            if (monster != null) return monster.heroId;
        }
        return 0;
    }

    public long percentHp() {
        HeroInfoEntity[] aMonster = getAMonster();
        long totalPercent = 0, numberMonster = 0;
        for (HeroInfoEntity monster : aMonster) {
            if (monster != null) {
                totalPercent += monster.point.getPercentHp();
                numberMonster++;
            }
        }
        long percent = totalPercent / numberMonster;
        return percent == 0 && totalPercent > 0 ? 1 : percent;
    }

    private String addDamage(UserEntity user, long damage) {
        List<DamageEntity> aDamage = getADamage();
        DamageEntity damageEntity = aDamage.stream().filter(entity -> entity.userId == user.getId()).findFirst().orElse(null);
        if (damageEntity != null) damageEntity.damage += damage;
        else aDamage.add(new DamageEntity(user, damage));
        return new Gson().toJson(aDamage);
    }

    void cacheTopDamage() {
        List<DamageEntity> aDamage = getADamage();
        aDamage.sort(Comparator.comparingLong(DamageEntity::getDamage).reversed());
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        for (int i = aDamage.size() - 1; i >= 0; i--) {
            UserEntity user = UserOnline.getDbUser(aDamage.get(i).userId);
            if (user != null) builder.addAUser(protoDamage(user, aDamage.get(i).damage));
        }
        topDamage = builder.build();
    }

    //region init
    @Transient
    HeroInfoEntity[] aMonster;
    @Transient
    Pbmethod.PbListUser topDamage;
    @Transient
    List<DamageEntity> aDamage;

    public List<DamageEntity> getADamage() {
        synchronized (this) {
            if (aDamage == null) {
                if (StringHelper.isEmpty(dataDamage)) aDamage = new ArrayList<>();
                else aDamage = new Gson().fromJson(dataDamage, new TypeToken<ArrayList<DamageEntity>>() {
                }.getType());
                cacheTopDamage();
            }
            if (aDamage != null && !aDamage.isEmpty()) {
                for (int i = aDamage.size() - 1; i >= 0; i--) {
                    if (aDamage.get(i).getDamage() == 0) aDamage.remove(i);
                }
            }
        }
        return aDamage;
    }

    public Pbmethod.PbListUser getTopDamage() {
        if (topDamage == null) cacheTopDamage();
        return topDamage;
    }

    public HeroInfoEntity[] getAMonster() {
        if (aMonster == null) {
            aMonster = new Gson().fromJson(dataMonster, HeroInfoEntity[].class);
        }
        return aMonster;
    }
    //endregion

    //region proto
    private Pbmethod.PbUser.Builder protoDamage(UserEntity user, long damage) {
        Pbmethod.PbUser.Builder builder = Pbmethod.PbUser.newBuilder();
        builder.setId(user.getId());
        builder.setName(user.getName());
        builder.setLevel(user.getLevel());
        builder.addAvatar(user.getAvatarType()).addAvatar(user.getAvatar());
        builder.setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(user.getAvatarFrame()).build());
        builder.setInfo(Pbmethod.CommonVector.newBuilder().addALong(damage).build());
        return builder;
    }
    //endregion

    //region Database
    private boolean dbSendBonus(List<String> sql2) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            // session.unwrap(Session.class).update(this); // why update this here ?
            for (String value : sql2) {
                session.createNativeQuery(value).executeUpdate();
            }
            session.createNativeQuery("update clan set raid_level=raid_level+1 where id=" + clanId).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(ex);
            Logs.error(new Gson().toJson(sql2));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private List<UserClanEntity> dbListUserClan(List<Integer> userIds) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String strIds = userIds.stream().map(value -> String.valueOf(value)).collect(Collectors.joining(","));
            return session.createNativeQuery("select * from user_clan where user_id in (" + strIds + ")", UserClanEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    private boolean dbUpdateBattle(MyUser mUser, String strDataMonster, String strDamage,
                                   int raidFee, long raidCountdown, int addGold, int addGuildCoin, int addGuildExp, int addGem) {
        int userId = mUser.getUser().getId();
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update clan set exp=exp+" + addGuildExp + " where id=" + clanId).executeUpdate();
            //            session.createNativeQuery(DBHelper.sqlUpdateMaterial(userId, MaterialType.GUILD_COIN, addGuildCoin)).executeUpdate();
            if (addGem == 0)
                session.createNativeQuery("update user set gold=gold+" + addGold + " where id=" + userId).executeUpdate();
            else
                session.createNativeQuery("update user set gold=gold+" + addGold + ", gem=gem-" + addGem + " where id=" + userId).executeUpdate();
            session.createNativeQuery("update user_clan set raid_fee=" + raidFee + ",raid_countdown=" + raidCountdown + " where user_id=" + userId).executeUpdate();

            String sql = "update clan_raid set data_monster=:dataMonster, data_damage=:dataDamage where clan_id=" + clanId + " and level=" + level;
            Query query = session.createNativeQuery(sql);
            query.setParameter("dataMonster", strDataMonster);
            query.setParameter("dataDamage", strDamage);
            query.executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(ex);
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    //endregion

    @Data
    public class DamageEntity {
        int userId, avatar, avatarType, avatarFrame;
        long damage;
        String name;

        public DamageEntity(UserEntity user, long damage) {
            this.userId = user.getId();
            this.name = user.getName();
            this.avatar = user.getAvatar();
            this.avatarType = user.getAvatarType();
            this.avatarFrame = user.getAvatarFrame();
            this.damage = damage;
        }

        public String strUserId() {
            return String.valueOf(userId);
        }

        public Pbmethod.PbUser.Builder toProto() {
            Pbmethod.PbUser.Builder builder = Pbmethod.PbUser.newBuilder();
            builder.setId(userId).addAvatar(avatarType).addAvatar(avatar);
            builder.setName(name);
            builder.setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(avatarFrame).build());
            builder.getInfoBuilder().addALong(damage);
            return builder;
        }

    }
}
