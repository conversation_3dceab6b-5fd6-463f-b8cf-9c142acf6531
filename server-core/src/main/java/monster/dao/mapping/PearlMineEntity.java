package monster.dao.mapping;

import grep.helper.DateTime;
import lombok.*;
import monster.dao.UserDAO;
import monster.game.fishing.config.ConfigFishing;
import monster.game.fishing.entity.UserFishingEntity;
import monster.service.monitor.UserOnline;
import protocol.Pbmethod;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "pearl_mine")
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PearlMineEntity implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    int clusterId, mineId;
    int districtId;
    int userId, mineType;
    Date startMiningTime;

    public long getPearlQuantity() {
        return userId > 0 ? ConfigFishing.getMiningPearl(mineType, startMiningTime) : 0;
    }

    public long getCountdown() {
        return userId > 0 ? ConfigFishing.getMiningTimeCountdown(startMiningTime) : 0;
    }

    public Pbmethod.CommonVector.Builder toProto(UserFishingEntity userFishing) {
        var builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(mineId).addALong(mineType).addALong(userId);
        String name = "";
        if (userId > 0) {
            UserEntity user = new UserDAO().getUser(userId);
            if (user != null) name = String.format("S%s. %s", (user.getServer() - 3), user.getName());
        }
        builder.addAString(name);
        builder.addALong(getPearlQuantity());
        builder.addALong(getCountdown());

        int enemyStatus = 3;
        long countdownToRob = 0;
        Date now = new Date();
        Date robTime = getRobTime();
        if (userId > 0 && now.before(robTime)) {
            enemyStatus = 1;
            countdownToRob = (robTime.getTime() - now.getTime()) / 1000;
        } else if (userFishing.getBeRobNumber() <= 0) enemyStatus = 2;
        builder.addALong(enemyStatus);
        builder.addALong(countdownToRob);
        return builder;
    }

    public Date getRobTime() {
        return DateTime.getHourOffset(startMiningTime, 1);
    }
}
