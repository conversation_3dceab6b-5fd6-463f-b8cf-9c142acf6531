package monster.dao.mapping;

import grep.database.DBJPA;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;

@Entity
@Table(name = "user_vip_reward")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserVipRewardEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private int userId, vipLevel, vipReward;

    public boolean needReward() {
        return vipLevel != vipReward;
    }

    public boolean updateVip(int vip) {
        return DBJPA.update("user_vip_reward", List.of("vip_level", vip), List.of("id", id));
    }
}
