package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_artifact")
@Data
@NoArgsConstructor
public class UserArtifactEntity implements Serializable {
    @Id
    int userId, artifactId, star;
    int number;
    Date dateCreated;

    @Transient
    int numberUsing;

    public UserArtifactEntity(int userId, int artifactId, int star) {
        this.userId = userId;
        this.artifactId = artifactId;
        this.star = star;
    }

    public void add(int value) {
        number += value;
    }

    public boolean isAvailable() {
        return number - numberUsing > 0;
    }

    public void using() {
        numberUsing++;
    }

    public void release() {
        numberUsing--;
    }

    public String getKey() {
        return artifactId + "_" + star;
    }
}
