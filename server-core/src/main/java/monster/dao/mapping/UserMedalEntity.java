package monster.dao.mapping;

// Generated Jul 29, 2014 3:03:44 PM by Hibernate Tools 3.4.0.CR1

import grep.helper.DateTime;
import grep.log.Logs;
import lombok.Data;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import java.util.Calendar;
import java.util.Date;

/**
 * UserMedal generated by hbm2java
 */
@Entity
@Data
public class UserMedalEntity implements java.io.Serializable {

    @Id
    private int userId;
    private int medalId;
    private Date dateBegin, dateEnd;

    public UserMedalEntity() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, 1);
        this.dateEnd = calendar.getTime();
    }

    public UserMedalEntity(int userId, int medalId, String dateEnd) {
        this.userId = userId;
        this.medalId = medalId;
        this.dateBegin = new Date();
        try {
            this.dateEnd = DateTime.getSDFFullDate().parse(dateEnd);
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

}
