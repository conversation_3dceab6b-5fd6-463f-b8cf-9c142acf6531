package monster.dao.mapping;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.*;
import monster.object.UserInt;
import monster.object.UserNotify;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_data")
@NoArgsConstructor
@Data
public class UserDataEntity implements Serializable {
    @Id
    private int userId;
    private String dataInt, dataNotify, dailyQuest, challenge, heroExpert;
    private int attackPower, dungeonLevel, countSummonHeroic, first_summon_basic;
    @Column(name = "count_hero_e5")
    int countHeroE5;
    int numberHeroic, numberProphet;
    private int heroSlot, usedItemUpHeroSlot, friendNotify;
    private long lastGlobalChat = -1, lastClanChat = -1, lastRecruitChat = -1, lastServerChat;
    private Long eventTimeBraveTrial = 0L, eventTimeAspenDungeon = 0L;
    private int starHeroOpened;
    private int levelShopCry, maxLabyrinth = 1;
    private int potCurrentMap = -1, countReplaceItem = 0;
    private long newUserLog;
    private String summonLimit, summonHeroLimit, heroIdMaxPower, replaceItem;
    @Column(name = "summon_limit2")
    private String summonLimit2;
    @Column(name = "summon_limit3")
    private String summonLimit3;
    private String firstTeamSsr;
    private int chessMasterPoint, indentureLevel, levelMaxIndenture, adventurePoint = 0;
    private boolean isPrivateChatFriendOnly, isFirstTimeAdventure, isNewUserReplayed = false;
    private String bloodResonanceHero;
    // số lần đi giúp đỡ mèo bạn bè trong ngày
    private int numberHelpCat, timeKey;
    @Transient
    UserInt uInt;
    @Transient
    UserNotify uNotify;
    @Transient
    int countHerovoid4, countHerovoid8, countHerovoid12, countHerovoid16;

    public UserDataEntity(int userId) {
        this.userId = userId;
        this.dataInt = "[]";
        this.dataNotify = "[]";
        this.dailyQuest = "[]";
        this.challenge = "[]";
        this.firstTeamSsr = "[]";
        this.summonLimit = NumberUtil.genListDBInt(3, 0); // eventId - heroSelect - numEnsure
        this.heroSlot = CfgHero.config.heroBaseSlot;
        this.adventurePoint = 2000;
        this.isFirstTimeAdventure = true;
        this.isNewUserReplayed = false;
        this.bloodResonanceHero = "[]";
        getUInt().setValue(UserInt.GUARANTEED_SUMMON_HEROIC, CfgSummon.config.insurance - 10);
    }

    public int getHeroSlot() {
        return Math.min(heroSlot, CfgHero.maxHeroSlot);
    }

    public UserInt getUInt() {
        if (uInt == null) uInt = new UserInt(dataInt);
        return uInt;
    }

    public String getBloodResonanceHero() {
        if (StringHelper.isEmpty(bloodResonanceHero)) bloodResonanceHero = "[]";
        return bloodResonanceHero;
    }
    public List<Long> getListBloodResonanceHero() {
        return GsonUtil.strToListLong(getBloodResonanceHero());
    }


    public UserNotify getUNotify() {
        if (uNotify == null) uNotify = new UserNotify(dataNotify, userId);
        return uNotify;
    }
    public int getNumberHelpCat() {
        int newTimeKey = DateTime.getTimeKey();
        if (timeKey != newTimeKey) {
            int numberHelpCatNew = 0;
            if (update("number_help_cat", numberHelpCatNew, "time_key", newTimeKey)) {
                this.numberHelpCat = numberHelpCatNew;
                this.timeKey = newTimeKey;
            }
        }
        return numberHelpCat;
    }
    // SummonLimit Event
    public List<Integer> getInfoSummonLimit(int serverId) {
        // eventId - heroSelect - numEnsure - numHero
        List<Integer> info = GsonUtil.strToListInt(summonLimit);
        while (info.size() < 4) info.add(0);
        if (info.get(0) != CfgSummonLimit.getConfigEvent(serverId).getId()) { // reset data khi next event
            info.set(0, CfgSummonLimit.getConfigEvent(serverId).getId());
            info.set(1, 0);
            info.set(2, 0);
            info.set(3, 0);
            updateInfoSummon(info);
        }
        return info;
    }

    // SummonLimit2 Event
    public List<Integer> getInfoSummonLimit2(int serverId) {
        if (StringHelper.isEmpty(summonLimit2)) summonLimit2 = "[]";
        // eventId - heroSelect - numEnsure - numHero - numEnsure2 - numHero2
        List<Integer> info = GsonUtil.strToListInt(summonLimit2);
        while (info.size() < 6) info.add(0);
        if (info.get(0) != CfgSummonLimit2.getConfigEvent(serverId).getId()) { // reset data khi next event
            info.set(0, CfgSummonLimit2.getConfigEvent(serverId).getId()); // eventId
            info.set(1, 0); //
            info.set(2, 0);
            info.set(3, 0);
            info.set(4, 0);
            info.set(5, 0);
            updateInfoAndHeroSummon2(info, CfgSummonLimit2.getHeroIdSummon(serverId));
        }
        return info;
    }


    public List<Integer> getInfoHeroSummon(int serverId) { // heroId - limit
        //return GsonUtil.strToListInt(summonHeroLimit);
        return CfgSummonLimit2.getHeroIdSummon(serverId);
    }

    // SummonLimit3 Event
//    public List<Integer> getInfoSummonLimit3(int serverId) {
//        if (StringHelper.isEmpty(summonLimit3)) summonLimit3 = "[]";
//        // eventId - heroSelect - numEnsure1 - numHero - numEnsure2
//        List<Integer> info = GsonUtil.strToListInt(summonLimit3);
//        while (info.size() < 5) info.add(0);
//        if (info.get(0) != CfgSummonLimit3.getConfigEvent(serverId).getId()) { // reset data khi next event
//            info.set(0, CfgSummonLimit3.getConfigEvent(serverId).getId());
//            info.set(1, 0);
//            info.set(2, 0);
//            info.set(3, 0);
//            info.set(4, 0);
//            updateInfoAndHeroSummon3(info);
//        }
//        return info;
//    }

    public List<Integer> getInfoHeroSummon3(int serverId) { // heroId - limit
        return CfgSummonLimit3.getHeroIdSummon(serverId);
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_data", updateData, Arrays.asList("user_id", userId));
    }

    public boolean updateInfoSummon(List<Integer> infos) {
        if (update(List.of("summon_limit", StringHelper.toDBString(infos)))) {
            this.summonLimit = infos.toString();
            return true;
        }
        return false;
    }

    public boolean updateInfoSummon2(List<Integer> infos) {
        if (update(List.of("summon_limit2", StringHelper.toDBString(infos)))) {
            this.summonLimit2 = infos.toString();
            return true;
        }
        return false;
    }

    public boolean updateInfoAndHeroSummon2(List<Integer> infos, List<Integer> heroes) {
        if (update(List.of("summon_limit2", StringHelper.toDBString(infos), "summon_hero_limit", StringHelper.toDBString(heroes)))) {
            this.summonLimit2 = infos.toString();
            this.summonHeroLimit = heroes.toString();
            return true;
        }
        return false;
    }

//    public boolean updateInfoAndHeroSummon3(List<Integer> infos) {
//        if (update(List.of("summon_limit3", StringHelper.toDBString(infos)))) {
//            this.summonLimit3 = infos.toString();
//            return true;
//        }
//        return false;
//    }

    public boolean updateDungeonLevel(int level) {
        if (update(Arrays.asList("dungeon_level", level))) {
            dungeonLevel = level;
            return true;
        }
        return false;
    }

    public long getEventTimeAspenDungeon() {
        if (eventTimeAspenDungeon == null || eventTimeAspenDungeon < 1000) {
            eventTimeAspenDungeon = DateTime.getCurrentDate().getTimeInMillis() - DateTime.DAY_MILLI_SECOND * (CfgDungeon.dayOpenEvent + CfgDungeon.dayEndEvent); // for start event now
            update("event_time_aspen_dungeon", eventTimeAspenDungeon);
        }
        return eventTimeAspenDungeon;
    }

    public long getEventTimeBraveTrial() {
        if (eventTimeBraveTrial == null || eventTimeBraveTrial < 1000) {
            eventTimeBraveTrial = DateTime.getCurrentDate().getTimeInMillis(); // System.currentTimeMillis();
            update("event_time_brave_trial", eventTimeBraveTrial);
        }
        return eventTimeBraveTrial;
    }

    public void addCountVoid1(int num) {
        countHerovoid4 += num;
    }

    public void addCountVoid2(int num) {
        countHerovoid8 += num;
    }

    public void addCountVoid3(int num) {
        countHerovoid12 += num;
    }

    public void addCountVoid4(int num) {
        countHerovoid16 += num;
    }

    public boolean checkFirstSummonHeroic() {
        boolean value = countSummonHeroic == 0 ? true : false;
        return value;
    }

    public boolean checkFirstSummonBasic() {
        boolean value = first_summon_basic == 0 ? true : false;
        return value;
    }

    public boolean checkSummonedASecondTime() {
        boolean value = countSummonHeroic == 1 ? true : false;
        return value;
    }

    public boolean updateCountSummonHeroic(int value) {
        if (update(Arrays.asList("count_summon_heroic", value))) {
            countSummonHeroic = value;
            return true;
        }
        return false;
    }

    public boolean updateCountHeroStarE5(int value) {
        if (update(Arrays.asList("count_hero_e5", value))) {
            countHeroE5 = value;
            return true;
        }
        return false;
    }

    public boolean updateCheckLevelShopCry(int level) {
        if (update(Arrays.asList("level_shop_cry", level))) {
            levelShopCry = level;
            return true;
        }
        return false;
    }

    public boolean updateFirstSummonBasic() {
        if (update(Arrays.asList("first_summon_basic", 1))) {
            first_summon_basic = 1;
            return true;
        }
        return false;
    }

    public String getFirstTeamSsr() {
        return StringHelper.isEmpty(firstTeamSsr) ? "[]" : firstTeamSsr;
    }

    public boolean updateFirstTeamSSR(String value) {
        if (update("first_team_ssr", value)) {
            firstTeamSsr = value;
            return true;
        }
        return false;
    }

    public boolean updateAdventurePoint(int newAdventurePoint) {
        if (update("adventure_point", newAdventurePoint)) {
            this.adventurePoint = newAdventurePoint;
            return true;
        }
        return false;
    }

    public boolean updateFirstTimeAdventurePoint() {
        if (update("is_first_time_adventure", false)) {
            this.isFirstTimeAdventure = false;
            return true;
        }
        return false;
    }

    public boolean updateNewUserReplayed() {
        if (update("is_new_user_replayed", true)) {
            this.isNewUserReplayed = true;
            return true;
        }
        return false;
    }

    public boolean updateNumberHeroic(int addValue) {
        if (update("number_heroic", numberHeroic + addValue)) {
            numberHeroic += addValue;
            return true;
        }
        return false;
    }

    public boolean updateNumberProphet(int addValue) {
        if (update("number_prophet", numberProphet + addValue)) {
            numberProphet += addValue;
            return true;
        }
        return false;
    }
    public boolean updateNumberHelpCat(int addValue) {
        if (update("number_help_cat", numberHelpCat + addValue)) {
            numberHelpCat += addValue;
            return true;
        }
        return false;
    }
}