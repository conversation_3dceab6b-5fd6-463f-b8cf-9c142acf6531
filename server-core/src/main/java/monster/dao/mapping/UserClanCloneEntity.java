package monster.dao.mapping;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgClan;
import monster.config.CfgClanFire;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

@Entity
@Table(name = "user_clan_clone")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserClanCloneEntity implements Serializable {
    @Id
    int userId, positionIndex;
    String skills, skills2;

    public String getSkills2() {
        if (StringHelper.isEmpty(skills2)) {
            this.skills2 = "[[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0]]";
        }
        return this.skills2;
    }
}
