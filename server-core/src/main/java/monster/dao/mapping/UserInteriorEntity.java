package monster.dao.mapping;

import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgInterior;
import monster.dao.mapping.main.ResInteriorGalleryEntity;
import monster.dao.mapping.main.ResInteriorItemEntity;
import monster.object.MyUser;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.*;

@Entity
@Table(name = "user_interior")
@Data
@NoArgsConstructor
public class UserInteriorEntity {
    @Id
    int userId;
    int numberCreated;
    int lastReceive;
    String wallpaperData;
    String galleryData;
    int isNew;
    int totalNumberCreated;
    int numberUsedRedCard;
    int numberUsedYellowCard;
    int isWrong;
    Date lastTimeCreate;

    public UserInteriorEntity(MyUser mUser) {
        this.userId = mUser.getUser().getId();
        this.numberCreated = 0;
        this.lastReceive = 0;
        this.wallpaperData = CfgInterior.getDefaultWallpaperData(mUser);
        this.galleryData = CfgInterior.getDefaultGalleryData();
        this.totalNumberCreated = 0;
        this.numberUsedRedCard = 0;
        this.numberUsedYellowCard = 0;
        this.isNew = 1;
        this.isWrong = 1;
    }

    public List<Integer> getDataWallpaper(Map<Integer, UserPetEntity> mPet) {
        List<Integer> data = GsonUtil.strToListInt(wallpaperData);
        Set<Integer> oldPetIds = new HashSet<>();
        for (int i = 0; i < data.size(); i += 3) {
            oldPetIds.add(data.get(i));
        }
        for (UserPetEntity uPet : mPet.values()) {
            if (!oldPetIds.contains(uPet.getPetId()) && uPet.isMaxAllRune()) {
                for (int i = 0; i < CfgInterior.config.getNumberFloor(); i++) {
                    data.add(uPet.getPetId());
                    data.add(i);
                    data.add(1);
                }
            }
        }

        return data;
    }

    public String getNewWallpaperData(Map<Integer, UserPetEntity> mPet, int shipId, int floorIndex, int newWallpaperId) {
        List<Integer> data = getDataWallpaper(mPet);
        for (int i = 0; i < data.size(); i += 3) {
            if (data.get(i) == shipId && data.get(i + 1) == floorIndex) data.set(i + 2, newWallpaperId);
        }

        return StringHelper.toDBString(data);
    }

//    public String getWallpaperDataAfterNewShip(UserPetEntity uPet) {
//        List<Integer> data = getDataWallpaper();
//        for (int i = 0; i < CfgInterior.config.getNumberFloor(); i++) {
//            data.add(uPet.getPetId());
//            data.add(i);
//            data.add(1);
//        }
//
//        return StringHelper.toDBString(data);
//    }

    public List<List<Integer>> getDataGallery() {
        return GsonUtil.strTo2ListInt(galleryData);
    }

    public String getNewGalleryData(List<UserInteriorItemEntity> aUIItem) {
        List<List<Integer>> aOldData = getDataGallery();

        for (int i = 0; i < aOldData.size(); i++) {
            List<Integer> newData = new ArrayList<>(aOldData.get(i));
            int galleryId = newData.get(0);
            ResInteriorGalleryEntity resGallery = CfgInterior.mGallery.get(galleryId);

            //Bỏ gallery id ở vị trí đầu
            newData.remove(0);
            for (UserInteriorItemEntity uIItem : aUIItem) {
                ResInteriorItemEntity resIItem = CfgInterior.mResItem.get(uIItem.getItemKey());
                int itemImage = resIItem.getImageId();
                Set<Integer> itemSet = resGallery.getItemSet();

                if (itemSet.contains(itemImage) && !newData.contains(itemImage)) {
                    newData.add(itemImage);
                }
            }
            //Thêm lại gallery id ở vị trí đầu
            newData.add(0, resGallery.getGalleryId());
            aOldData.set(i, newData);
        }

        return StringHelper.toDBString(aOldData);
    }

//    public boolean isGalleryCompleted(int galleryId) {
//        List<List<Integer>> dataGallery = getDataGallery();
//        ResInteriorGalleryEntity resGallery = CfgInterior.mGallery.get(galleryId);
//        for (List<Integer> data : dataGallery) {
//            if (data.get(0) != galleryId) continue;
//            return data.size() - 1 == resGallery.getItemSet().size();
//        }
//
//        return false;
//    }

    public List<Integer> getAllCompletedGalleryId() {
        List<Integer> aGalleryId = new ArrayList<>();
        List<List<Integer>> dataGallery = getDataGallery();
        for (List<Integer> data : dataGallery) {
            ResInteriorGalleryEntity resGallery = CfgInterior.mGallery.get(data.get(0));
            if (data.size() - 1 == resGallery.getItemSet().size()) {
                aGalleryId.add(resGallery.getGalleryId());
            }

        }

        return aGalleryId;
    }

    public boolean isCreateTooFast(){
//        System.out.println("date: " + new Date(new Date().getTime() + 3000));
//        System.out.println(lastTimeCreate);
        return lastTimeCreate != null && new Date().before(new Date(lastTimeCreate.getTime() + 3000));
    }
}
