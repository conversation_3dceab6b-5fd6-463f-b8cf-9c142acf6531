package monster.dao.mapping;

import grep.helper.GsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgInterior;
import monster.dao.mapping.main.ResInteriorItemEntity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.List;

@Entity
@Table(name = "user_interior_collection")
@Data
@NoArgsConstructor
public class UserInteriorCollectionEntity implements Serializable {
    @Id
    int userId, heroKey;
    String dataCreated;
    int numberComplete;

    public UserInteriorCollectionEntity(int userId, int heroKey) {
        this.userId = userId;
        this.heroKey = heroKey;
        this.dataCreated = "[]";
        this.numberComplete = 0;
    }

    public List<Integer> getNewDataCreated(List<Integer> allNewItemKeyForHero) {
        List<Integer> dataCreatedAsList = GsonUtil.strToListInt(dataCreated);
        for (int newItemKey : allNewItemKeyForHero) {
            ResInteriorItemEntity resItem = CfgInterior.mResItem.get(newItemKey);
            dataCreatedAsList.add(resItem.getId());
            if (dataCreatedAsList.size() == CfgInterior.collectionMapByHeroKey.get(heroKey).getACollection().size())
                dataCreatedAsList.clear();
        }

        return dataCreatedAsList;
    }

    public int getNewNumberComplete(List<Integer> allNewItemKeyForHero) {
        List<Integer> dataCreatedAsList = GsonUtil.strToListInt(dataCreated);
        for (int newItemKey : allNewItemKeyForHero) {
            ResInteriorItemEntity resItem = CfgInterior.mResItem.get(newItemKey);
            dataCreatedAsList.add(resItem.getId());

            if (dataCreatedAsList.size() == CfgInterior.collectionMapByHeroKey.get(heroKey).getACollection().size()){
                return numberComplete + 1;
            }
        }

        return numberComplete;
    }

    public List<Integer> getDataCreated() {
        return GsonUtil.strToListInt(dataCreated);
    }
}
