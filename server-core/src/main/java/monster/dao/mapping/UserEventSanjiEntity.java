package monster.dao.mapping;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgEventSanji;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.*;

@Entity
@Table(name = "user_event_sanji")
@Data
@NoArgsConstructor
public class UserEventSanjiEntity {
    @Id
    private int userId;
    private int serverId;
    private String bonusStatus;
    private long pointVuiVe;
    private int dishLastSend;
    private Date timeFreeSend;
    private String itemData;
    private int rankVuiVe;

    @Transient
    public static final int BONUS_UNQUALIFIED = 0;
    @Transient
    public static final int BONUS_NOT_RECEIVED = 1;
    @Transient
    public static final int BONUS_RECEIVED = 2;

    public UserEventSanjiEntity(int userId, int serverId) {
        this.userId = userId;
        this.serverId = serverId;
        bonusStatus = CfgEventSanji.getDefaultBonusStatus();
        itemData = checkItemData();
        this.timeFreeSend = new Date();
    }

    public boolean addPointVuiVe(int point) {
        if (update(Arrays.asList("point_vui_ve", pointVuiVe + point))) {
            pointVuiVe += point;
            return true;
        }
        return false;
    }

    private String checkItemData() {
        List<Integer> ids = new ArrayList<>();
        for (int i = 0; i < CfgEventSanji.config.itemsChef.size(); i++) {
            CfgEventSanji.ItemChef oItem = CfgEventSanji.config.itemsChef.get(i);
            ids.add(oItem.getId());
            ids.add(oItem.getLimit());
        }
        return ids.toString();
    }

    public boolean updateItemData(int itemId, int limit) {
        List<Integer> arr = GsonUtil.strToListInt(itemData);
        for (int i = 0; i < arr.size(); i += 2) {
            if (arr.get(i) == itemId) {
                arr.set(i + 1, limit);
                if (update(Arrays.asList("item_data", StringHelper.toDBString(arr)))) {
                    itemData = arr.toString();
                    return true;
                } else return false;
            }
        }
        return false;
    }

    public int getLimitItem(int itemId) {
        JsonArray arr = GsonUtil.parseJsonArray(itemData);
        for (int i = 0; i < arr.size(); i += 2) {
            if (arr.get(i).getAsInt() == itemId) {
                return arr.get(i + 1).getAsInt();
            }
        }
        return 0;
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_event_sanji", updateData, Arrays.asList("user_id", userId));
    }

    public int getBonusStatus(long pointLevel) {
        for (int i = 0; i < CfgEventSanji.aPointVuiVe.size(); i++) {
            if (pointLevel == CfgEventSanji.aPointVuiVe.get(i)) return getStatusBonus().get(i);
        }

        return 0;
    }

    public String getNewBonusStatus(long newPoint) {
        List<Integer> status = getStatusBonus();

        for (int i = 0; i < CfgEventSanji.aPointVuiVe.size(); i++) {
            if (status.get(i) == 0 && newPoint >= CfgEventSanji.aPointVuiVe.get(i)) status.set(i, 1);
        }

        return StringHelper.toDBString(status);
    }

    public String getReceivedStatusBonusStatus(long pointLevel) {
        List<Integer> status = getStatusBonus();
        for (int i = 0; i < CfgEventSanji.aPointVuiVe.size(); i++) {
            if (pointLevel == CfgEventSanji.aPointVuiVe.get(i)) status.set(i, 2);
        }

        return StringHelper.toDBString(status);
    }

    public List<Integer> getStatusBonus() {
        return GsonUtil.strToListInt(bonusStatus);
    }
}
