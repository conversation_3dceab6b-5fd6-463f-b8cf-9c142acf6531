package monster.dao.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "user_avatar")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserAvatarEntity implements Serializable {
    @Id
    private int userId, avatarId, typeId;
    private long expiredTime;
}
