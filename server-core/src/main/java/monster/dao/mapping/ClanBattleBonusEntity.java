package monster.dao.mapping;

import grep.helper.JsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.sf.json.JSONArray;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import java.util.Date;

@Entity(name = "clan_battle_bonus")
@Data
@NoArgsConstructor
public class ClanBattleBonusEntity {
    @Id
    private int id;
    private int clanId;
    private String message;
    private String bonus;
    private int number;
    private String receiver = "";
    private Date dateModified = new Date();
    private Date dateCreated = new Date();

    public ClanBattleBonusEntity(int clanId, String message, String bonus, int number) {
        this.clanId = clanId;
        this.message = message;
        this.bonus = bonus;
        this.number = number;
    }

    public int getNumberBonusAvailable() {
        if (receiver.length() == 0) return number;
        return number - receiver.split("\n").length;
    }

    public void addReceiver(String name) {
        receiver += "+ " + name + "\n";
    }

    public protocol.Pbmethod.CommonVector.Builder toProto() {
        protocol.Pbmethod.CommonVector.Builder builder = protocol.Pbmethod.CommonVector.newBuilder();
        builder.addALong(id);
        builder.addALong(number);
        builder.addALong(getNumberBonusAvailable());
        builder.addAllALong(JsonUtil.convertToListLong(JSONArray.fromObject(bonus)));
        builder.addAString(message);
        builder.addAString(receiver);
        return builder;
    }

}
