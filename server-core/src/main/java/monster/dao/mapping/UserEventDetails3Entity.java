package monster.dao.mapping;

import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "user_event_details")
@NoArgsConstructor
@Data
public class UserEventDetails3Entity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private Date createdOn;
    private int userId, eventId;
    private String metadata, metadata2, reward;

    public UserEventDetails3Entity(int userId, int eventId) {
        this.userId = userId;
        this.eventId = eventId;
        this.createdOn = new Date();
        this.metadata = "";
    }

    public long getLongMetadata() {
        if (StringHelper.isEmpty(metadata)) return 0;
        try {
            return Long.parseLong(metadata);
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return 0;
    }

    public long getLongMetadata2() {
        if (StringHelper.isEmpty(metadata2)) return 0;
        try {
            return Long.parseLong(metadata2);
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return 0;
    }
}
