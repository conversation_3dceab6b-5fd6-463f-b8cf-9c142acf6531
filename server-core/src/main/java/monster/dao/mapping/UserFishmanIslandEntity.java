package monster.dao.mapping;

import grep.helper.DateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import monster.config.CfgFishmanIsland;


@Entity
@Table(name = "user_fishman_island")
@Data
@NoArgsConstructor
public class UserFishmanIslandEntity {
    @Id
    int userId;
    int candyRain;
    int mouseMashing;
    int zoroChallenge;
    Date lastTimeCandy;
    Date lastTimeMouse;
    Date lastTimeChallenge;

    public UserFishmanIslandEntity(int userId) {
        this.userId = userId;
        this.candyRain = 0;
        this.mouseMashing = 0;
        this.zoroChallenge = 0;
    }

    public List<Integer> getStatus() {
        return new ArrayList<>(Arrays.asList(candyRain, mouseMashing, zoroChallenge));
    }

    public boolean isCompleted(int type) {
        switch (type) {
            case CfgFishmanIsland.CANDY_RAIN:
                return lastTimeCandy != null && DateTime.getDayDiff(lastTimeCandy, new Date()) >= 1;
            case CfgFishmanIsland.MOUSE_MASHING:
                return lastTimeMouse != null && DateTime.getDayDiff(lastTimeMouse, new Date()) >= 1;
            case CfgFishmanIsland.ZORO_CHALLENGE:
                return lastTimeChallenge != null && DateTime.getDayDiff(lastTimeChallenge, new Date()) >= 1;
        }

        return true;
    }
}
