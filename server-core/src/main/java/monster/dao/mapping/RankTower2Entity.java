package monster.dao.mapping;

import grep.helper.ListUtil;
import grep.helper.StringHelper;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.penum.AvatarType;
import monster.service.monitor.AvatarTimer;

import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "rank_tower2")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RankTower2Entity implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private int serverId, towerLevel;
    private int userId, userLevel;
    private int avatar, avatarType, avatarFrame, figureRepresent;
    String name;

    public List<Long> getListAvatar() {
        return ListUtil.ofLong(avatarType, avatar, avatarFrame);
    }

    public RankTower2Entity(UserEntity user, int towerLevel) {
        this.serverId = user.getServer();
        this.userId = user.getId();
        this.name = user.getName();
        this.userLevel = user.getLevel();
        this.avatar = user.getAvatar();
        this.avatarType = user.getAvatarType();
        this.towerLevel = towerLevel;
        this.avatarFrame = user.getAvatarFrame();
    }

    public void update(UserEntity user) {
        this.serverId = user.getServer();
        this.userId = user.getId();
        this.name = user.getName();
        this.userLevel = user.getLevel();
        this.avatar = user.getAvatar();
        this.avatarType = user.getAvatarType();
        this.avatarFrame = user.getAvatarFrame();
    }

}
