package monster.dao.mapping;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.AllArgsConstructor;
import lombok.Data;
import monster.config.CfgBossServer;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import monster.object.MyUser;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.util.DBHelper;

import jakarta.persistence.*;

import java.util.*;

@Entity
@Table(name = "user_boss_server")
@Data
@AllArgsConstructor
public class UserBossServerEntity {
    @Id
    int userId;
    int serverId, numberTurn, numberBuyDaily;
    int lastRank1, lastRank2, lastRank3, lastRank4, lastRank5;
    String lastSeasonReceive, heroesAttacked;
    Long totalDamage1 = 0L, totalDamage2 = 0L, totalDamage3 = 0L, totalDamage4 = 0L, totalDamage5 = 0L;
    Long maxDamage1 = 0L, maxDamage2 = 0L, maxDamage3 = 0L, maxDamage4 = 0L, maxDamage5 = 0L;
    String team1, team2, team3, team4, team5;
    long lastTotalDamage1, lastTotalDamage2, lastTotalDamage3, lastTotalDamage4, lastTotalDamage5;
    int numberTurnFree;

    Date lastTime;

    public UserBossServerEntity() {
    }

    public UserBossServerEntity(int userId, int serverId) {
        this.userId = userId;
        this.serverId = serverId;
        this.lastTime = new Date(DateTime.getNextMSFromMin(-5));
        this.numberTurn = CfgBossServer.config.getBaseMaxTurn();
        this.lastSeasonReceive = CfgBossServer.str1.replace(" ", "");
        this.heroesAttacked = "";
        this.lastRank1 = -1;
        this.lastRank5 = -1;
        this.lastRank2 = -1;
        this.lastRank3 = -1;
        this.lastRank4 = -1;
        this.numberTurnFree = CfgBossServer.config.getBaseMaxTurn();
    }

    public List<Boolean> isReceived() {
        List<Boolean> aBool = new ArrayList<>();
        for (Integer elm : getListReceive()) {
            aBool.add(elm == 1);
        }
        return aBool;
    }

    public List<Integer> getListReceive() {
        return GsonUtil.strToListInt(lastSeasonReceive);
    }

    public int getLastRank(int bossIndex) {
        switch (bossIndex) {
            case 0:
                if (lastTotalDamage1 <= 0) return -1;
                return Math.max(lastRank1, 0);
            case 1:
                if (lastTotalDamage2 <= 0) return -1;
                return Math.max(lastRank2, 0);
            case 2:
                if (lastTotalDamage3 <= 0) return -1;
                return Math.max(lastRank3, 0);
            case 3:
                if (lastTotalDamage4 <= 0) return -1;
                return Math.max(lastRank4, 0);
            case 4:
                if (lastTotalDamage5 <= 0) return -1;
                return Math.max(lastRank5, 0);
        }
        return -1;
    }

//    public void setReceived(int bossIndex) {
//        List<Integer> newReceive = GsonUtil.strToListInt(lastSeasonReceive);
//        newReceive.set(bossIndex, 1);
//        lastSeasonReceive = newReceive.toString().replace(" ", "");
//    }

    public synchronized void sendReward(AHandler handler, MyUser myUser, BossServerEntity aBoss) {
        List<Long> aBonus = new ArrayList<>();
        int countHeroGerma = (int) myUser.getResources().getHeroes().stream().filter(hero -> hero.getIntVoidLevel() >= 1).count();
        List<Boolean> listReceive = isReceived();
        boolean isReceived = lastSeasonReceive.replace(" ", "").equals(CfgBossServer.str1);
        Actions.save(serverId, userId, "boss_server", "mail_season_start",
                "session", handler.getSession(),
                "receive", lastSeasonReceive.replace(" ", ""));

        if (!isReceived) {
            for (int i = 0; i < CfgBossServer.config.getNumberBoss(); i++) {
                List<Long> tmp = new ArrayList<>(CfgBossServer.getURankBonus(i, getLastRank(i), aBoss.getSelfDie(), countHeroGerma));
                aBonus = Bonus.merge(aBonus, tmp);
            }

            if (!dbUpdateUBoss()) {
                handler.addErrResponse();
                return;
            }

            this.lastSeasonReceive = CfgBossServer.str1;

            List<String> aNameBoss = aBoss.getNameBoss();

            Actions.save(serverId, userId, "boss_server", "mail_season_end",
                    "session", handler.getSession(),
                    "bonus", new Gson().toJson(aBonus),
                    "receive", lastSeasonReceive.replace(" ", ""));
//            String title = aBoss.getSelfDie() == 1 ? "Chưa tiêu diệt được toàn bộ Boss\n" : "Toàn bộ Boss đã bị tiêu diệt\n";
            String content = "";

            for (int i = 0; i < CfgBossServer.config.getNumberBoss(); i++) {
                content += getContentMail(i, aNameBoss);
            }

//            title += getLastRank(0) < 0 ? "" : String.format("+ Hạng %s trong cuộc chiến với " + aNameBoss.get(0) + "\n", (getLastRank(0) == 0 ? "100+" : getLastRank(0)));
//            title += getLastRank(1) < 0 ? "" : String.format("+ Hạng %s trong cuộc chiến với " + aNameBoss.get(1) + "\n", (getLastRank(1) == 0 ? "100+" : getLastRank(1)));
//            title += getLastRank(2) < 0 ? "" : String.format("+ Hạng %s trong cuộc chiến với " + aNameBoss.get(2) + "\n", (getLastRank(2) == 0 ? "100+" : getLastRank(2)));
//            title += getLastRank(3) < 0 ? "" : String.format("+ Hạng %s trong cuộc chiến với " + aNameBoss.get(3) + "\n", (getLastRank(3) == 0 ? "100+" : getLastRank(3)));
//            title += getLastRank(4) < 0 ? "" : String.format("+ Hạng %s trong cuộc chiến với " + aNameBoss.get(4) + "\n", (getLastRank(4) == 0 ? "100+" : getLastRank(4)));

            DBJPA.rawSQL(DBHelper.sqlMail(userId, Lang.getTitle("title_mail_boss_server"), content, aBonus.toString(), "boss_server"));
        }
    }

    private String getContentMail(int bossIndex, List<String> aNameBoss) {
        return getLastRank(bossIndex) < 0 ? "" : String.format(Lang.getTitle("content_mail_boss_server") + aNameBoss.get(bossIndex) + "\n", (getLastRank(bossIndex) == 0 ? "100+" : getLastRank(bossIndex)));
    }

    public long getDamage(int bossIndex) {
        switch (bossIndex) {
            case 0:
                return this.totalDamage1;
            case 1:
                return this.totalDamage2;
            case 2:
                return this.totalDamage3;
            case 3:
                return this.totalDamage4;
            case 4:
                return this.totalDamage5;
            default:
                return 0;
        }
    }

    public long getNewMaxDamage(int bossIndex, long newDamage) {
        return Math.max(Arrays.asList(maxDamage1, maxDamage2, maxDamage3, maxDamage4, maxDamage5).get(bossIndex), newDamage);
    }

    public String getTeam(int bossIndex) {
        return Arrays.asList(team1, team2, team3, team4, team5).get(bossIndex);
    }

    public boolean isBestDamage(int bossIndex, long newDamage) {
        return newDamage > Arrays.asList(maxDamage1, maxDamage2, maxDamage3, maxDamage4, maxDamage5).get(bossIndex);
    }

    public void setDamageAndRank(BossServerEntity aBoss, int bossIndex, long damage, long newMaxDamage, String newTeam) {
        switch (bossIndex) {
            case 0:
                totalDamage1 += damage;
                aBoss.updateUserRank(0, userId, totalDamage1);
                maxDamage1 = newMaxDamage;
                team1 = newTeam;
                break;
            case 1:
                totalDamage2 += damage;
                aBoss.updateUserRank(1, userId, totalDamage2);
                maxDamage2 = newMaxDamage;
                team2 = newTeam;
                break;
            case 2:
                totalDamage3 += damage;
                aBoss.updateUserRank(2, userId, totalDamage3);
                maxDamage3 = newMaxDamage;
                team3 = newTeam;
                break;
            case 3:
                totalDamage4 += damage;
                aBoss.updateUserRank(3, userId, totalDamage4);
                maxDamage4 = newMaxDamage;
                team4 = newTeam;
                break;
            case 4:
                totalDamage5 += damage;
                aBoss.updateUserRank(4, userId, totalDamage5);
                maxDamage5 = newMaxDamage;
                team5 = newTeam;
                break;
        }
    }

    public void setNewDay() {
        numberTurn += CfgBossServer.config.getBaseMaxTurn() - numberTurnFree;
        this.lastTime = new Date();
        this.numberBuyDaily = 0;
        this.numberTurnFree = CfgBossServer.config.getBaseMaxTurn();
    }

    public List<Long> getListHeroesAttacked() {
        List<Long> aLong = new ArrayList<>();
        if (!StringHelper.isEmpty(this.heroesAttacked))
            aLong.addAll(GsonUtil.strToListLong(this.heroesAttacked));
        return aLong;
    }

    //region Database access
    boolean dbUpdateUBoss() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_boss_server set last_season_receive='" + CfgBossServer.str1 + "' where user_id=" + userId).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

//    public String getNewHeroAttacked(List<Long> heroIds) {
//        List<Long> heroesAttacked = new ArrayList<>();
//        if (GsonUtil.strToListLong(this.heroesAttacked) != null)
//            heroesAttacked.addAll(GsonUtil.strToListLong(this.heroesAttacked));
//        heroesAttacked.addAll(heroIds);
//        for (int i = 0; i < heroesAttacked.size(); i++) {
//            if (heroesAttacked.get(i) == 0 || heroesAttacked.get(i) == -1) heroesAttacked.remove(i);
//        }
//        return heroesAttacked.toString();
//    }
    //endregion


}
