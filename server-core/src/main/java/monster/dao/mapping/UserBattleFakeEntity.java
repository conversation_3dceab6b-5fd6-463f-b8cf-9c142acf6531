package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "user_battle_fake")
@Data
@NoArgsConstructor
public class UserBattleFakeEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;
    int userId, groupId, numberAttack, is_done, levelTower, tower, levelTowerTo;
    Date createTime;

    public UserBattleFakeEntity(int userId, int groupId, int numberAttack, int levelTower, int tower, int levelTowerTo) {
        this.userId = userId;
        this.groupId = groupId;
        this.numberAttack = numberAttack;
        this.createTime = new Date();
        this.levelTower = levelTower;
        this.tower = tower;
        this.is_done = 0;
        this.levelTowerTo = levelTowerTo;
    }
}
