package monster.dao.mapping;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Entity
@Table(name = "user_fuse_ss")
@Data
@NoArgsConstructor
public class UserFuseSsEntity {

    @Id
    private int userId;
    private long startTime;
    private String fuseData;

    public UserFuseSsEntity(UserEntity user) {
        this.userId = user.getId();
        this.startTime = System.currentTimeMillis();
        this.fuseData = "[]";
    }

    @Transient
    private Map<Integer, Integer> mFuse;

    public String getFuseData() {
        if (StringHelper.isEmpty(fuseData)) {
            fuseData = "[]";
        }
        return fuseData;
    }

    public void setFuseData(String value) {
        this.fuseData = value;
        mFuse = null;
    }

    public Map<Integer, Integer> getMFuse() {
        if (mFuse == null) {
            mFuse = new HashMap<>();
            JsonArray arr = GsonUtil.parseJsonArray(getFuseData());
            for (int i = 0; i < arr.size(); i += 2) {
                mFuse.put(arr.get(i).getAsInt(), arr.get(i + 1).getAsInt());
            }
        }
        return mFuse;
    }

    public String getTmpFuseData(int heroKey) {
        boolean found = false;
        JsonArray arr = GsonUtil.parseJsonArray(getFuseData());
        for (int i = 0; i < arr.size(); i += 2) {
            if (arr.get(i).getAsInt() == heroKey) {
                found = true;
                arr.set(i + 1, new JsonPrimitive(arr.get(i + 1).getAsInt() + 1));
                break;
            }
        }
        if (!found) {
            arr.add(new JsonPrimitive(heroKey));
            arr.add(new JsonPrimitive(1));
        }
        return arr.toString();
    }

    public long getCountdown() {
        long passTime = (System.currentTimeMillis() - startTime) / 1000;
        passTime = DateTime.DAY_SECOND * 30 - passTime;
        return passTime < 0 ? 0 : passTime;
    }

    public int getNumberFuse(int heroKey) {
        Map<Integer, Integer> mFuse = getMFuse();
        if (!mFuse.containsKey(heroKey)) return 0;
        return mFuse.get(heroKey);
    }

    public void checkRefresh() {
        if (getCountdown() == 0) {
            dbRefresh();
        }
    }

    public boolean dbRefresh() {
        if (DBJPA.update("user_fuse_ss", Arrays.asList("start_time", System.currentTimeMillis(), "fuse_data", "[]"), Arrays.asList("user_id", userId))) {
            this.startTime = System.currentTimeMillis();
            this.fuseData = "[]";
            mFuse = new HashMap<>();
            return true;
        }
        return false;
    }
}
