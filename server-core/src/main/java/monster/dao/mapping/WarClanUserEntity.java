package monster.dao.mapping;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgWarClan;
import monster.config.penum.MaterialType;
import monster.object.BattleTeam;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.IMath;
import monster.service.monitor.UserOnline;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

@Entity
@Table(name = "war_clan_user")
@Data
@NoArgsConstructor
public class WarClanUserEntity implements Serializable {
    @Id
    int serverId, userId, warId;
    private int clanId;
    private String username;
    private String name;
    private String mostLost = "";
    private String atk1 = "";
    private String atk2 = "";
    private int numberAtk;
    private int level, avatar, avatarType, avatarFrame;
    private int status; // 1: join, 0: fail
    private int clanPosition, teamStatus = 0;
    private String dataHero = "";

    public WarClanUserEntity(int warId, int userId, int clanId, int serverId, int clanPosition, String username, String name, int level) {
        this.warId = warId;
        this.userId = userId;
        this.serverId = serverId;
        this.clanId = clanId;
        this.username = username;
        this.name = name;
        this.level = level;
        this.clanPosition = clanPosition;
    }

    @Transient
    BattleTeam team;

    public void setDataHero(String dataHero) {
        this.dataHero = dataHero;
        team = null;
    }

    public BattleTeam getTeam() {
        if (team == null) {
            try {
                team = new Gson().fromJson(dataHero, BattleTeam.class);
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
        return team;
    }

    public long getPower() {
        BattleTeam team = getTeam();
        if (team == null || team.getAHero() == null) return 0;
        return Stream.of(team.getAHero()).filter(Objects::nonNull).mapToLong(hero -> IMath.getPower(hero.point)).sum();
    }

    public Pbmethod.PbWarClanUser.Builder toProto(boolean oppTeam, int index) {
        Pbmethod.PbWarClanUser.Builder builder = Pbmethod.PbWarClanUser.newBuilder();
//        if (oppTeam && Arrays.asList(2, 3, 4).contains(index) && StringHelper.isEmpty(mostLost)) { // ẩn vị trí 2 3 4 khi chưa bị tấn công
//            builder.setUserId(userId).setName("").setLevel(0).setClanPosition(0);
//            builder.setNumberAttack(0).addAvatar(0).addAvatar(0);
//            builder.setStatus(status);
//            builder.setTeamStatus(teamStatus);
//
//            builder.addAllBonusAtk(Bonus.viewMaterial(MaterialType.GUILD_COIN, CfgWarClan.config.getMaxAttackCoin()));
//        } else {

        UserEntity userEntity = UserOnline.getDbUser(userId);
        builder.setUserId(userId).setName(name).setLevel(level).setClanPosition(clanPosition);
        builder.setNumberAttack(numberAtk);
        builder.setStatus(status);
        builder.setTeamStatus(teamStatus);
        if (userEntity != null) {
            avatarType = userEntity.getAvatarType();
            avatar = userEntity.getAvatar();
            avatarFrame = userEntity.getAvatarFrame();
        }
        builder.addAvatar(avatarType).addAvatar(avatar).addAvatar(avatarFrame);

        if (!StringHelper.isEmpty(atk1)) builder.setAtk1(parseAtkInfo(atk1));
        if (!StringHelper.isEmpty(atk2)) builder.setAtk2(parseAtkInfo(atk2));
        if (!StringHelper.isEmpty(mostLost)) builder.setMostAtk(parseAtkInfo(mostLost));
        builder.addAllBonusAtk(Bonus.viewMaterial(MaterialType.GUILD_COIN, CfgWarClan.config.getMaxAttackCoin()));

        BattleTeam team = getTeam();
        if (team != null && team.getAHero() != null && team.getAPet() != null) {
            for (HeroInfoEntity hero : team.getAHero()) {
                builder.addAHero(hero == null ? Pbmethod.PbTeamHeroInfo.newBuilder() : hero.protoTeamHero());
            }
            UserPetEntity[] aPet = team.getAPet();
            if (aPet == null || aPet.length == 0 || aPet[0] == null) builder.addTeamPet(0).addTeamPet(0);
            else builder.addTeamPet(aPet[0].getPetId()).addTeamPet(aPet[0].getTier());
        }

        builder.setPower(getPower());
//        }
        return builder;
    }

    Pbmethod.CommonVector.Builder parseAtkInfo(String value) {
        int index = value.lastIndexOf("|");
        return Pbmethod.CommonVector.newBuilder().addAString(value.substring(0, index)).addALong(Long.parseLong(value.substring(index + 1)));
    }

    public boolean updateTeam(String dataHero) {
        return DBJPA.update("war_clan_user", Arrays.asList("data_hero", dataHero), Arrays.asList("user_id", userId, "war_id", warId));
    }
}
