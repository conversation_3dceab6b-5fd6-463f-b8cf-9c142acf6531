package monster.dao.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgAlbum;
import monster.controller.logic.AlbumService;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "user_album_clone_test")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAlbumCloneTestEntity implements Serializable {
    @Id
    public int userId, imageId, albumId;
}
