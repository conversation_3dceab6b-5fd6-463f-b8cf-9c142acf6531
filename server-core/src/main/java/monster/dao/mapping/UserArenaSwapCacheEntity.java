package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "user_arena_swap_cache")
@Data
@NoArgsConstructor
public class UserArenaSwapCacheEntity implements java.io.Serializable {

    @Id
    private int id;
    private int eventId, userId, hour;
    private int receive, cluster, physicalServer;
    @Column(name = "user_rank")
    private int rank;

    public UserArenaSwapCacheEntity(int eventId, int userId, int hour, int rank) {
        this.eventId = eventId;
        this.userId = userId;
        this.hour = hour;
        this.rank = rank;
        this.receive = 0;
    }
}
