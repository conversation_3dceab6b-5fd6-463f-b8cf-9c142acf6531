package monster.dao.mapping;

import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.game.clan.entity.UserClanEntity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "user_clan_top")
@Data
@NoArgsConstructor
public class UserClanTopEntity implements Serializable {
    @Id
    int userId, top;
    String skills, skills2;

    public UserClanTopEntity(UserClanEntity uClan, int top){
        this.userId = uClan.getUserId();
        this.skills = uClan.getSkills();
        this.skills2 = uClan.getSkills2();
        this.top = top;
    }

    public String getSkills2() {
        if (StringHelper.isEmpty(skills2)) {
            this.skills2 = "[[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0]]";
        }
        return this.skills2;
    }
}
