package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "user_interior_item")
@Data
@NoArgsConstructor
public class UserInteriorItemEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long itemId;
    private int userId;
    private int itemKey;
    private int shipId;
    private int floorIndex;
    private int spaceIndex;

    public UserInteriorItemEntity(int userId, int itemKey) {
        this.userId = userId;
        this.itemKey = itemKey;
        this.shipId = -1;
        this.floorIndex = -1;
        this.spaceIndex = -1;
    }

    /**
     * @return itemId + itemKey + id thuyền + index tầng + index ô
     */
    public List<Long> getInfo() {
        return new ArrayList<>(Arrays.asList(itemId, (long) itemKey, (long) shipId, (long) floorIndex, (long) spaceIndex));
    }

    public boolean isFree() {
        return shipId == -1 || floorIndex == -1 || spaceIndex == -1;
    }

    public boolean isSetInShipAndOnFloor(int shipId, int floorIndex) {
        return this.shipId == shipId && this.floorIndex == floorIndex;
    }

    //region Database access

    //endregion
}
