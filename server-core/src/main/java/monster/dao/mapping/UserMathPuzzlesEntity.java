package monster.dao.mapping;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import lombok.Getter;
import monster.config.CfgMathPuzzles;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Table(name = "user_math_puzzles")
@Entity
@Getter
public class UserMathPuzzlesEntity implements Serializable {
    @Id
    int userId;
    @Id
    long eventId;
    int countAnswerTrue;
    String buyItemHistory;

    public UserMathPuzzlesEntity() {

    }

    public UserMathPuzzlesEntity(int userId, int countAnswerTrue, long eventId) {
        this.userId = userId;
        this.countAnswerTrue = countAnswerTrue;
        this.eventId = eventId;
        this.buyItemHistory = NumberUtil.genListInt(CfgMathPuzzles.config.getShop().size() * 2, 0).toString();
    }

    public List<Integer> getListBuyHistory() {
        List<Integer> result = GsonUtil.strToListInt(buyItemHistory);
        while (result.size() < CfgMathPuzzles.config.getShop().size() * 2) result.add(0);
        return result;
    }

    public void setNumberBought(int id, int amount) {
        int preAmount = getNumberBought(id);
        List<Integer> result = getListBuyHistory();
        result.set(2 * id + 1, preAmount + amount);
        buyItemHistory = result.toString();
        //
        if (DBJPA.update("user_math_puzzles", Arrays.asList("buy_item_history", buyItemHistory), Arrays.asList("user_id", userId, "event_id", eventId))) {
        }
    }

    public int getNumberBought(int id) {
        if (buyItemHistory == null || buyItemHistory == "" || buyItemHistory.length() == 0) return 0;
        return getListBuyHistory().get(2 * id + 1);
    }

    public boolean updateCountAnswerTrue() {
        countAnswerTrue++;
        if (DBJPA.update("user_math_puzzles", Arrays.asList("count_answer_true", countAnswerTrue),
                Arrays.asList("user_id", userId, "event_id", eventId, "buy_item_history", buyItemHistory))) {
            return true;
        }
        return false;
    }

}
