package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Table(name = "battle_fake_avg_new")
@Data
@NoArgsConstructor
public class BattleFakeAvgNewEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;
    long userBattleFakeId;
    int defenderId, numWin, avgRoundEnd, tower, towerLevel;

    public BattleFakeAvgNewEntity(long userBattleFakeId, int defenderId, int tower, int towerLevel) {
        this.userBattleFakeId = userBattleFakeId;
        this.defenderId = defenderId;
        this.tower = tower;
        this.towerLevel = towerLevel;
    }
}
