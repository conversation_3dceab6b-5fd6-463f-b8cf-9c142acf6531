package monster.dao.mapping;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.cache.memcached.MCache;
import monster.config.CfgShopCry;
import monster.config.penum.MarketType;
import monster.config.penum.MaterialType;
import monster.dao.mapping.main.MarketDetailEntity;
import monster.dao.mapping.main.MarketEntity;
import monster.dao.mapping.main.ResCampaignEntity;
import monster.dao.mapping.main.ShopNewDetailEntity;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.common.LabyrinthService;
import monster.service.monitor.FileData;
import monster.service.resource.ResCampaign;
import monster.service.resource.SuperMarket;
import monster.service.user.Actions;
import monster.service.user.Bonus;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "user_market")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserMarketEntity {
    @Id
    int userId;
    //    private String shopAltar, shopGuild;
    private String marketplace, shopCasino, shopHt;
    //    private long marketplaceCountdown, shopCasinoCountdown, shopHtCountdown, shopGuildCountdown;
    private String shopCountdown;

    static final String KEY_CACHE_REFRESH_SHOP_CRY = "KEY_CACHE_REFRESH_SHOP_CRY_";

    public UserMarketEntity(int userId) {
        this.userId = userId;
        this.shopCountdown = StringHelper.toDBString(SuperMarket.getDefaultCountdown());
    }

    public List<Long> getShopCountdown() {
        if (StringHelper.isEmpty(shopCountdown) || shopCountdown.length() < 2) shopCountdown = "[]";
        List<Long> aLong = GsonUtil.strToListLong(shopCountdown);
        while (aLong.size() < MarketType.maxId) aLong.add(0L);
        return aLong;
    }

    public List<MarketDetailEntity> getShopItem(MarketType marketType) {
        return serializeMarketDetailEntity(getShopValues().get(marketType.id - 1));
    }

    public long getCountdown(MarketType marketType) {
        MarketEntity market = SuperMarket.getMarket(marketType.id);
        if (market.getType() == 4) {
            return DateTime.secondsUntilEndDay();
        } else if (market.getType() == 6) {
            return DateTime.secondsUntilEndMonth();
        }
        if (market.getTimeRefresh() > 0) {
            long timeWait = market.getTimeRefresh();
            long lastRefresh = getCountdownValue(marketType);
            long timePass = (System.currentTimeMillis() - lastRefresh) / 1000;
            if (timePass > timeWait) return 0;
            return timeWait - timePass;
        }
        return -1;
    }

    public long getCountdownValue(MarketType marketType) {
        return getShopCountdown().get(marketType.id - 1);
    }

    public boolean needRefresh(MyUser mUser, MarketType marketType) {
        MarketEntity market = SuperMarket.getMarket(marketType.id);
        switch (market.getType()) {
            case MarketType.SHOP_TYPE_LABYRINTH_THANBI -> {
                return Guice.getInstance(LabyrinthService.class).getUserLabyrinth(mUser).getMarketKey() != (int) getCountdownValue(marketType);
            }
            case MarketType.SHOP_TYPE_REFRESH_DAILY -> {
                return DateTime.getTimeKey() != (int) getCountdownValue(marketType);
            }
            case MarketType.SHOP_TYPE_REFRESH_MONTH -> {
                return DateTime.getMonthKey() + market.getVersion() != (int) getCountdownValue(marketType);
            }
            default -> {
                if (market.getTimeRefresh() > 0) {
                    return System.currentTimeMillis() - getCountdownValue(marketType) > market.getTimeRefresh() * 1000;
                }
            }
        }
        return false;
    }

    public boolean refreshShop(MyUser mUser, MarketType marketType, boolean isFree) {
        var marketDetails = SuperMarket.getShopDetails(marketType);
        if (marketType == MarketType.MARKETPLACE) {
            for (int i = 0; i < marketDetails.size(); i++) {
                MarketDetailEntity detail = marketDetails.get(i);
                List<Integer> bonus = GsonUtil.strToListInt(detail.getItem());
                if (bonus.get(0) == Bonus.BONUS_MATERIAL && bonus.get(1) == MaterialType.TYPE_USED_ITEM) {
                    Logs.warn("----------");
                    int hour = 0, value = 0;
                    if (bonus.get(2) == MaterialType.CAMPAIGN_PROMOTION_5H.id) {
                        hour = 5;
                        value = 3000;
                    } else if (bonus.get(2) == MaterialType.CAMPAIGN_PROMOTION_8H.id) {
                        hour = 8;
                        value = 4800;
                    }
                    Logs.warn("---------- " + hour + " " + value);
                    if (hour > 0) {
                        UserCampaignEntity campaign = Services.userService.getCampaign(mUser);
                        if (campaign != null) {
                            ResCampaignEntity resCampaign = ResCampaign.getCampaign(campaign.getLevel(), campaign.getServerId());
                            value = Math.max(value, (int) (resCampaign.getLootPromoStone() * hour * DateTime.HOUR_SECOND / 5));
                        }
                        Logs.warn("aaa " + value);
                        MarketDetailEntity newDetail = GsonUtil.parse(MarketDetailEntity.class, GsonUtil.toJson(detail));
                        newDetail.setItem(StringHelper.toDBString(Bonus.viewMaterial(MaterialType.PROMOTION_STONE, value)));
                        marketDetails.set(i, newDetail);
                    }
                }
            }
        }
        if (marketType.type == MarketType.SHOP_TYPE_LABYRINTH_THANBI) {
            return updateShopRefresh(marketType, new Gson().toJson(marketDetails), Guice.getInstance(LabyrinthService.class).getUserLabyrinth(mUser).getMarketKey());
        }
        return switch (marketType.type) {
            case MarketType.SHOP_TYPE_REFRESH -> updateShopRefresh(marketType, new Gson().toJson(marketDetails), System.currentTimeMillis());
            case MarketType.SHOP_TYPE_REFRESH_DAILY -> updateShopRefresh(marketType, new Gson().toJson(marketDetails), DateTime.getTimeKey());
            case MarketType.SHOP_TYPE_REFRESH_MONTH -> updateShopRefresh(marketType, new Gson().toJson(marketDetails), DateTime.getMonthKey() + marketType.version);
            default -> updateShop(marketType, new Gson().toJson(marketDetails));
        };
    }

    public void setShopValue(MarketType marketType, String value) {
        getShopValues().set(marketType.id - 1, value);
    }

    public String refreshShopCry(int levelShopNew) {
        String value = new Gson().toJson(SuperMarket.getListShopCry(levelShopNew));
        boolean checkRefresh = FileData.writeFile(userId, "market", MarketType.SHOP_CRY.key, value);
        if (checkRefresh) {
            newShopCry = value;
            return value;
        }
        return "";
    }

    public boolean updateShopCry(String value) {
        if (FileData.writeFile(userId, "market", MarketType.SHOP_CRY.key, value)) {
            newShopCry = value;
            return true;
        }
        return false;
    }

    public boolean updateShop(MarketType marketType, String value) {
        if (FileData.writeFile(userId, "market", marketType.key, value)) {
            setShopValue(marketType, value);
            return true;
        }
        return false;
    }


    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_market", updateData, Arrays.asList("user_id", userId));
    }

    public boolean updateShopRefresh(MarketType marketType, String value, long refreshTime) {
        List<Long> countdown = getShopCountdown();
        countdown.set(marketType.id - 1, refreshTime);
        if (DBJPA.update("user_market", List.of("shop_countdown", StringHelper.toDBString(countdown)), List.of("user_id", userId))
                && FileData.writeFile(userId, "market", marketType.key, value)) {
            setShopCountdown(StringHelper.toDBString(countdown));
            setShopValue(marketType, value);
            return true;
        }
        return false;
    }

    public static void updateUserShopCry(MyUser myUser) {
        int maxLevel = CfgShopCry.config.levelShops.size();
        int levelShopCry = myUser.getUData().getLevelShopCry() == 0 ? 1 : myUser.getUData().getLevelShopCry();
        for (int i = levelShopCry; i <= maxLevel; i++) {
            int starOpen = CfgShopCry.mShopCry.get(i).star;
            int soLuongOpen = CfgShopCry.mShopCry.get(i).soLuong;
            if (starOpen > 15) {
                int levelGemar = starOpen - 15;
                int count = (int) myUser.getResources().getHeroes().stream().filter(hero -> hero.getIntVoidLevel() >= levelGemar).count();
                if (count >= soLuongOpen && levelShopCry < maxLevel) {
                    levelShopCry = i;
                }
            } else {
                int count = (int) myUser.getResources().getHeroes().stream().filter(hero -> hero.getStar() == starOpen).count();
                if (count >= soLuongOpen && levelShopCry < maxLevel) {
                    levelShopCry = i;
                }
            }
        }
        if (levelShopCry != myUser.getUData().getLevelShopCry()) {
            myUser.getUData().updateCheckLevelShopCry(levelShopCry);
        }
    }

    public static boolean hasBuyItemSpecial(MyUser mUser, int levelSpecial) {
        CfgShopCry.LevelShopCry shop = CfgShopCry.mShopSpecial.get(levelSpecial);
        if (shop == null) return false;
        if (shop.star > 15) {
            int levelGemar = shop.star - 15;
            int count = (int) mUser.getResources().getHeroes().stream().filter(hero -> hero.getIntVoidLevel() >= levelGemar).count();
            if (count >= shop.soLuong) {
                return true;
            }
        } else {
            int count = (int) mUser.getResources().getHeroes().stream().filter(hero -> hero.getStar() == shop.star).count();
            if (count >= shop.soLuong) {
                return true;
            }
        }
        return false;
    }


    private List<MarketDetailEntity> serializeMarketDetailEntity(String value) {
        return new Gson().fromJson(value, new TypeToken<List<MarketDetailEntity>>() {
        }.getType());
    }

    public List<ShopNewDetailEntity> checkInstanceShopCry(UserEntity user, int time, int levelShopCry) {
        Object cacheValue = MCache.getInstance().get(KEY_CACHE_REFRESH_SHOP_CRY + userId);
        if (cacheValue == null || StringHelper.isEmpty(newShopCry)) {  // Refresh shop
            String value = refreshShopCry(levelShopCry);
            MCache.getInstance().set(KEY_CACHE_REFRESH_SHOP_CRY + userId, 1, time);
            Actions.save(user, Actions.DSHOPCRY, "RESET_SHOP_CRY", "");
            return new Gson().fromJson(value, new TypeToken<List<ShopNewDetailEntity>>() {
            }.getType());
        } else {
            return new Gson().fromJson(newShopCry, new TypeToken<List<ShopNewDetailEntity>>() {
            }.getType());
        }
    }

    public ShopNewDetailEntity getShopCryById(UserEntity user, int id, int time, int levelShopCry) {
        List<ShopNewDetailEntity> lstItem = checkInstanceShopCry(user, time, levelShopCry);
        return lstItem.stream().filter(item -> item.getId() == id).findFirst().orElse(null);
    }

    public List<ShopNewDetailEntity> updateShopNewDetail(List<ShopNewDetailEntity> lstItem, ShopNewDetailEntity
            item) {
        for (int i = 0; i < lstItem.size(); i++) {
            if (lstItem.get(i).getId() == item.getId()) {
                lstItem.set(i, item);
            }
        }
        return lstItem;
    }


    //region File Data
    @Transient
    private String newShopCry;
    @Transient
    private List<String> shopValues = new ArrayList<>();

    public List<String> getShopValues() {
        while (shopValues.size() < MarketType.maxId) shopValues.add("");
        return shopValues;
    }

    public void checkData() {
        List<String> shopValues = getShopValues();
        for (MarketType marketType : MarketType.values()) {
            if (marketType.enable && marketType.fileData) {
                String value = shopValues.get(marketType.id - 1);
                if (StringHelper.isEmpty(value)) {
                    updateShop(marketType, new Gson().toJson(SuperMarket.getShopDetails(marketType)));
                }
            }
        }
    }

    public boolean getFileData() {
        newShopCry = FileData.readFile(userId, "market", MarketType.SHOP_CRY.key);
        List<String> shopValues = getShopValues();
        for (MarketType marketType : MarketType.values()) {
            if (marketType.enable && marketType.fileData) {
                String value = FileData.readFile(userId, "market", marketType.key);
                if (value == null) return false;
                shopValues.set(marketType.id - 1, value);
            }
        }
        return true;
    }
    //endregion
}
