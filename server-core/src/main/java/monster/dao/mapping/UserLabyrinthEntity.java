package monster.dao.mapping;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgLabyrinth;
import monster.config.CfgUser;
import monster.dao.mapping.main.ResRelicEntity;
import monster.object.LabyrinthHero;
import monster.object.LabyrinthMap;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.common.LabyrinthService;
import monster.service.monitor.FileData;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_labyrinth")
@Data
@NoArgsConstructor
public class UserLabyrinthEntity implements Serializable {
    @Id
    private int userId, eventId;
    private int floor, posRow, posSlot, mapMode;
    private int stageLevel, stageMap;
    private int numberPlay, heroRevive;
    private String relic;
    // maps;
    // private String myHeroes; // [[idHero, heroId, star, level, percentHp, percentAnger]]
    private Date dateModified = new Date();
    private int maxFloor, maxRow, maxHardRow;

    @Transient
    List<List<LabyrinthMap>> aMap;
    @Transient
    Map<Long, LabyrinthHero> mHero;
    @Transient
    String myHeroes, maps;
    @Transient
    Pbmethod.PbHero addedSupportHero = null;

    public UserLabyrinthEntity(UserEntity user, int eventId) {
        this.userId = user.getId();
        this.eventId = eventId;
        this.numberPlay = 1;
        this.mapMode = 1;
        fileUpdateMyHeroes("");
        fileUpdateMaps("");
    }

    public int getMarketKey() {
        return eventId * 10 + floor;
    }

    public int getInputRow(int row) {
        return floor == 0 ? row : row + 10;
    }

    public boolean isFinalMap() {
        return floor + 1 == CfgLabyrinth.maxFloor && posRow == getListMap().size() - 2;
    }

    public boolean isSweepAble(MyUser mUser) {
        return mUser.getUData().getMaxLabyrinth() > stageLevel;
    }

    //endregion

    //region Getter Setter
    public List<Integer> getListRelic() {
        if (StringHelper.isEmpty(relic) || relic.length() <= 2) relic = "[]";
        return GsonUtil.strToListInt(relic);
    }

    public int getBuffFountain() {
        int totalBuff = 0;
        List<Integer> relicIds = getListRelic();
        for (int i = 0; i < relicIds.size(); i++) {
            ResRelicEntity resRelic = CfgLabyrinth.getRelic(relicIds.get(i));
            if (resRelic != null) totalBuff += resRelic.getBuffFountain();
        }
        return totalBuff;
    }

    public int getBuffHpAfterBattle() {
        int totalBuff = 0;
        List<Integer> relicIds = getListRelic();
        for (int i = 0; i < relicIds.size(); i++) {
            ResRelicEntity resRelic = CfgLabyrinth.getRelic(relicIds.get(i));
            if (resRelic != null) totalBuff += resRelic.getBuffAfterBattle();
        }
        return totalBuff;
    }

    public Map<Long, LabyrinthHero> getMHero() {
        if (mHero == null) {
            if (StringHelper.isEmpty(myHeroes) || myHeroes.length() <= 2) myHeroes = "[]";
            mHero = new Gson().fromJson(myHeroes, new TypeToken<Map<Long, LabyrinthHero>>() {
            }.getType());
        }
        return mHero;
    }

    public List<List<LabyrinthMap>> getListMap() {
        if (aMap == null) {
            if (StringHelper.isEmpty(maps) || maps.length() <= 2) maps = "[]";
            aMap = new Gson().fromJson(maps, new TypeToken<List<List<LabyrinthMap>>>() {
            }.getType());
        }
        return aMap;
    }

    public LabyrinthMap getMap(int row, int slotIndex) {
        List<List<LabyrinthMap>> aMap = getListMap();
        if (row < aMap.size()) {
            List<LabyrinthMap> map = aMap.get(row);
            if (slotIndex < map.size()) return map.get(slotIndex);
        }
        return null;
    }

    public LabyrinthHero getMyHero(long idHero) {
        return getMHero().get(idHero);
    }
    //endregion

    //region proto
    public Pbmethod.PbLabyrinth toProto(MyUser mUser) {
        Pbmethod.PbLabyrinth.Builder builder = Pbmethod.PbLabyrinth.newBuilder();
        builder.setInfo(Pbmethod.CommonVector.newBuilder()
                .addALong(heroRevive == 0 ? 100 : -1).addALong(numberPlay)
                .addALong(mUser.getUserDailyData().getNumberUsedVipBenefit(CfgUser.VIP_BENEFIT_LABYRINTH)).addALong(CfgUser.getVipLabyrinthBuyTurn(mUser.getUser().getVip()))
                .addALong(isSweepAble(mUser) ? 1 : 0)
        );
        builder.setMapMode(mapMode == CfgLabyrinth.mapModeEasy ? 0 : 1);
        if (floor == 0) builder.setFishingStatus(2);
        else builder.setFishingStatus(0);

        builder.setCountdown(CfgLabyrinth.getCountdown()).setFloor(floor + 1).setMaxFloor(CfgLabyrinth.maxFloor);
        getListRelic().forEach(relicId -> builder.addRelicId(relicId));
        for (LabyrinthHero hero : getMHero().values()) {
            protocol.Pbmethod.PbHero.Builder heroBuilder = hero.toProto();
            if (heroBuilder != null) builder.addAHero(heroBuilder);
        }
        //        fileUpdateMyHeroes(new Gson().toJson(getMHero()));
        List<List<LabyrinthMap>> aMap = getListMap();
        if (aMap.isEmpty()) Guice.getInstance(LabyrinthService.class).genMap(mUser);
        int startIndex = floor == 0 ? 0 : 10; // posRow <= 10 ? 0 : 11;
        int endIndex = floor == 0 ? 10 : 20; // posRow <= 11 ? 11 : aMap.size();
        for (int i = startIndex; i <= endIndex; i++) {
            for (LabyrinthMap labyrinthMap : aMap.get(i)) {
                if (labyrinthMap.generate == true && labyrinthMap.needRegen()) {
                    Guice.getInstance(LabyrinthService.class).genMapData(mUser, labyrinthMap, this);
                }
                builder.addMaps(labyrinthMap.toProto(floor, posRow, posSlot));
            }
        }
        return builder.build();
    }
    //endregion

    //region File Data
    public boolean getFileData() {
        maps = FileData.readFile(userId, "labyrinth", "maps");
        myHeroes = FileData.readFile(userId, "labyrinth", "my_heroes");
        return maps != null && myHeroes != null;
    }

    public boolean fileUpdateMyHeroes(String data) {
        if (FileData.writeFile(userId, "labyrinth", "my_heroes", data)) {
            this.myHeroes = data;
            return true;
        }
        return false;
    }

    public boolean fileUpdateMaps(String data) {
        if (FileData.writeFile(userId, "labyrinth", "maps", data)) {
            this.maps = data;
            return true;
        }
        return false;
    }
    //endregion

    //region Database
    public boolean updateWhenBuyTurn(int numberBuy) {
        int newNumberPlay = numberPlay + numberBuy;
        if (!dbUpdate(List.of("number_play", newNumberPlay)))
            return false;

        this.numberPlay = newNumberPlay;
        return true;
    }

    public boolean dbUpdatePos(int row, int slotIndex) {
        if (dbUpdate("pos_row", row, "pos_slot", slotIndex)) {
            this.posRow = row;
            this.posSlot = slotIndex;
            return true;
        }
        return false;
    }

    public boolean dbUpdate(Object... values) {
        return dbUpdate(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean dbUpdate(List<Object> values) {
        return DBJPA.update("user_labyrinth", values, Arrays.asList("user_id", userId));
    }

    //endregion

}