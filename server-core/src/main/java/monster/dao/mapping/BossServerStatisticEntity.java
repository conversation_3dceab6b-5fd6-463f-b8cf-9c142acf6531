package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgBossServer;

import jakarta.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "boss_server_statistic")
@Data
@NoArgsConstructor
public class BossServerStatisticEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    int serverId;
    Date dateStatistic;
    int numberUser;
    int numberUserAttack;
    int numberUserStatus;
    int numberChallengeStatus;
    long totalPower;
    int beAttacked1, beAttacked2, beAttacked3, beAttacked4, beAttacked5;
    long damage1, damage2, damage3, damage4, damage5;
    String percentHp;
    int isUserKill;

    @Transient
    public Set<Integer> user = new HashSet<>();
    @Transient
    public Set<Integer> userAttacked = new HashSet<>();
    @Transient
    public Set<Integer> userStatus = new HashSet<>();
    @Transient
    public Set<Integer> challengeStatus = new HashSet<>();

    public BossServerStatisticEntity(Date dateStatistic, int serverId) {
        this.dateStatistic = dateStatistic;
        this.serverId = serverId;
        this.numberUser = 0;
        this.numberUserAttack = 0;
        this.numberUserStatus = 0;
        this.numberChallengeStatus = 0;
        this.totalPower = 0;
        this.beAttacked1 = 0;
        this.beAttacked2 = 0;
        this.beAttacked3 = 0;
        this.beAttacked4 = 0;
        this.beAttacked5 = 0;
        this.damage1 = 0;
        this.damage2 = 0;
        this.damage3 = 0;
        this.damage4 = 0;
        this.damage5 = 0;
        this.percentHp = CfgBossServer.str100;
        this.isUserKill = -1;
    }

    public void setDamage(int bossIndex, long damage) {
        switch (bossIndex) {
            case 0:
                this.damage1 += damage;
                break;
            case 1:
                this.damage2 += damage;
                break;
            case 2:
                this.damage3 += damage;
                break;
            case 3:
                this.damage4 += damage;
                break;
            case 4:
                this.damage5 += damage;
                break;
        }
    }

    public long getDamage(int bossIndex) {
        switch (bossIndex) {
            case 0:
                return this.damage1;
            case 1:
                return this.damage2;
            case 2:
                return this.damage3;
            case 3:
                return this.damage4;
            default:
                return this.damage5;
        }
    }
}
