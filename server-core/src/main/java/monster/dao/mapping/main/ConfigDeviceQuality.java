package monster.dao.mapping.main;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Entity
@Table(name = "config_device_quality", catalog = "dson_main")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigDeviceQuality {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public int id;
    public String deviceModel;
    public int qualityLevel;

}
