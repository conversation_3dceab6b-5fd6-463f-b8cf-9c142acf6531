package monster.dao.mapping.main;

import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "dson_main.res_abyss_hero_type")
@NoArgsConstructor
public class ResAbyssHeroTypeEntity {
    @Getter
    @Id
    int id;
    @Getter
    int heroKey;
    @Getter
    String artifact;
    @Getter
    String skills;
    @Getter
    int treasureEffect;
    int treasureIndex, treasureIndex2;
    @Getter
    int specialItemType, grownPoint;
    boolean atk, def, sp, dot, trueDmg, buff, debuff, cc, counterAttack, heal, crit;

    public int getTreasureIndex(int treasure) {
        return treasure > 20 ? treasureIndex2 : treasureIndex;
    }

    public List<String> getListType(){
        List<String> listType = new ArrayList<>();
        if (atk) listType.add("atk");
        if (def) listType.add("def");
        if (sp) listType.add("sp");
        if (dot) listType.add("dot");
        if (trueDmg) listType.add("trueDmg");
        if (buff) listType.add("buff");
        if (debuff) listType.add("debuff");
        if (cc) listType.add("cc");
        if (counterAttack) listType.add("counterAttack");
        if (heal) listType.add("heal");
        if (crit) listType.add("crit");
        return listType;
    }

    @Override
    public String toString() {
        return "id = " + id + " - heroKey = " + heroKey;
    }
}
