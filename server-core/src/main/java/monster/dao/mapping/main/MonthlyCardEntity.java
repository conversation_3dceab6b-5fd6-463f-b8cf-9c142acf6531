package monster.dao.mapping.main;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Data
@Table(name = "monthly_card", catalog = "dson_main")
@AllArgsConstructor
@NoArgsConstructor
public class MonthlyCardEntity {

    @Id
    private int id;
    private int userId;
    private String type;
    private Date fromTime, toTime;

}
