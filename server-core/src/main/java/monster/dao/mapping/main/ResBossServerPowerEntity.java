package monster.dao.mapping.main;

import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgBossServer;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "dson_main.res_boss_server_power")
@Data
@NoArgsConstructor
public class ResBossServerPowerEntity {
    @Id
    int serverId;
    int scale;

    public ResBossServerPowerEntity(int serverId){
        this.serverId = serverId ;
        this.scale = CfgBossServer.config.getBasePower();
    }
}
