package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Data;
import monster.config.CfgCampaign;
import monster.config.CfgDropItem;
import monster.config.CfgUser;
import monster.config.penum.MaterialType;
import monster.config.penum.MonsterType;
import monster.dao.mapping.UserCampaignEntity;
import monster.dao.mapping.UserDropEventEntity;
import monster.dao.mapping.UserPowerEntity;
import monster.game.user.service.UserService;
import monster.object.BattleTeam;
import monster.object.ModePlay;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.IMath;
import monster.service.common.TeamService;
import monster.service.monitor.Telegram;
import monster.service.user.Bonus;
import monster.util.GameDebug;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;

import java.util.*;
import java.util.stream.Collectors;

import static monster.config.CfgDropItem.dbGetUserDropEntity;

@Entity
@Table(name = "res_campaign")
@Data
public class ResCampaignEntity {
    @Id
    int id;
    Integer requireLevel, minPower;
    String lootItem2, stage, lootArtifact, output;
    Integer lootArtifactFrag, lootHeroShard, lootPromoStone, lootBasicQuestScroll, lootSeniorQuestScroll;
    Integer lootMonsterSoul, lootChaosStone, lootMagicDust;
    int haveToPlay, levelShow, starShow;
    int background;
    String winReward;
    String condition, monsterId, monsterLevel;
    long powerCalculate;
    @Transient
    int gold, exp, soul;

    public int getMaxGold(int vip) {
        return maxGold + CfgUser.getMaxAutoTime(vip) * gold * 60 / 5;
    }

    public int getMaxExp(int vip) {
        return maxExp + CfgUser.getMaxAutoTime(vip) * exp * 60 / 5;
    }

    public int getMaxSoul(int vip) {
        return maxSoul + CfgUser.getMaxAutoTime(vip) * soul * 60 / 5;
    }

    public int getMaxPromotionStone(int vip) {
        return maxPromotionStone + CfgUser.getMaxAutoTime(vip) * exp * 60 / 5;
    }

    @Transient
    int maxGold, maxExp, maxSoul, maxPromotionStone;
    @Transient
    List<Double> dropItems = new ArrayList<>();
    @Transient
    List<Double> dropArtifact = new ArrayList<>();
    @Transient
    List<TeamConfig> newTeams;
    @Transient
    ModePlay modePlay;

    public BattleTeam getBattleTeam(MyUser mUser, UserCampaignEntity userCampaign, long atkPower, long numberPassFunction) {
        if (id < CfgCampaign.config.levelApplyReducePower) numberPassFunction = 0;
        int numberStageLevel = Math.max(userCampaign.getFirstLooseLevel() == 0 ? 0 : id - userCampaign.getFirstLooseLevel(), 0);
        UserPowerEntity userPowerEntity = mUser == null ? new UserPowerEntity() : Guice.getInstance(UserService.class).getUserPower(mUser);
        if (userPowerEntity != null && mUser != null) {
            GameDebug.addLog("campaign userId=%s -> atkPower=%s powerCalculate=%s numberPassFunction=%s firstLoosePower=%s firstLooseLevel=%s powerMax=%s -> scale=%s"
                    .formatted(mUser.getUser().getId(), atkPower, powerCalculate, numberPassFunction,
                            userCampaign == null ? 0 : userCampaign.getFirstLoosePower(), userCampaign == null ? 0 : userCampaign.getFirstLooseLevel(), userPowerEntity.getPowerMaxCampaign(),
                            IMath.getScaleMonsterPower(atkPower, powerCalculate, numberPassFunction, userCampaign == null ? 0 : userCampaign.getFirstLoosePower(),
                                    userPowerEntity.getPowerMaxCampaign(), numberStageLevel)));
        }
        float scaleMonsterPower = IMath.getScaleMonsterPower(atkPower, powerCalculate, numberPassFunction, userCampaign == null ? 0 : userCampaign.getFirstLoosePower(),
                userPowerEntity.getPowerMaxCampaign(), numberStageLevel);
        BattleTeam team = Guice.getInstance(TeamService.class).getMonsterTeam(monsterId, monsterLevel, scaleMonsterPower, MonsterType.CAMPAIGN);
        for (HeroInfoEntity aHero : team.getAHero()) {
            if (aHero != null) {
                aHero.setLevel(levelShow);
                aHero.star = starShow;
                aHero.setHeroInfo(new ArrayList<>());
            }
        }
        return team;
    }

    //public ResMonsterEntity[] getAMonster() {
    //    if (aMonster == null) {
    //        aMonster = new ResMonsterEntity[6 * (newTeams == null ? 1 : newTeams.size() + 1)];
    //        List<List<Integer>> monsterIds = GsonUtil.strTo2ListInt(monsterId);
    //        if (!monsterIds.isEmpty()) {
    //            for (int i = 0; i < monsterIds.size(); i++) {
    //                if (monsterIds.get(i) > 0)
    //                    aMonster[i] = ResMonster.getMonster(monsterIds.get(i)).get(MonsterType.CAMPAIGN);
    //            }
    //        }
    //
    //        if (newTeams != null) {
    //            int startIndex = 6;
    //            for (int i = 0; i < newTeams.size(); i++) {
    //                TeamConfig team = newTeams.get(i);
    //                for (int j = 0; j < team.position.size(); j++) {
    //                    aMonster[team.position.get(j) - 1 + startIndex * (i + 1)] = ResMonster.getMonster(team.heroId.get(j)).get(MonsterType.CAMPAIGN);
    //                }
    //            }
    //        }
    //    }
    //    return aMonster;
    //}

    public void init() {
        //        List<List<Integer>> newIds = new ArrayList<>();
        //        var monsterIds = GsonUtil.strTo2ListInt(monsterId);
        //        var monsterPositions = GsonUtil.strTo2ListInt(monsterPosition);
        //        for (int i = 0; i < monsterIds.size(); i++) {
        //            List<Integer> newValue = NumberUtil.genListInt(6, 0);
        //            var monsterId = monsterIds.get(i);
        //            var position = monsterPositions.get(i);
        //            for (int index = 0; index < monsterId.size(); index++) {
        //                newValue.set(position.get(index) - 1, monsterId.get(index));
        //            }
        //            newIds.add(newValue);
        //        }
        //        DBJPA.update("dson_main.res_campaign", List.of("monster_id", StringHelper.toDBString(newIds)), List.of("id", id));

        try {
            if (output != null) {
                JSONObject obj = JSONObject.fromObject(output);
                gold = obj.getInt("aG");
                exp = obj.getInt("exp");
                soul = obj.getInt("soul");
                maxGold = gold * (int) (DateTime.HOUR_SECOND * 2f);
                maxExp = exp * (int) (DateTime.HOUR_SECOND * 2f);
                maxSoul = soul * (int) (DateTime.HOUR_SECOND * 2f);
                maxPromotionStone = lootPromoStone * (int) (DateTime.HOUR_SECOND * 2f);
            }
            if (lootItem2 != null) {
                JSONArray arr = JSONArray.fromObject(String.format("[%s]", lootItem2));
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject obj = arr.getJSONObject(i);
                    dropItems.add(obj.getDouble("id"));
                    dropItems.add(obj.getDouble("rate"));
                }
            }
            if (condition != null) {
                modePlay = new Gson().fromJson(condition, ModePlay.class);
            }
            if (lootArtifact != null) {
                JSONArray arr = JSONArray.fromObject(String.format("[%s]", lootArtifact));
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject obj = arr.getJSONObject(i);
                    dropArtifact.add(obj.getDouble("id"));
                    dropArtifact.add(obj.getDouble("rate"));
                }
            }
            //if (!StringHelper.isEmpty(monsterTeam)) {
            //    newTeams = new Gson().fromJson(monsterTeam, new TypeToken<List<TeamConfig>>() {
            //    }.getType());
            //}
        } catch (Exception ex) {
            Telegram.sendNotify("Error config Campaign id=%s".formatted(id));
            Logs.error(ex);
        }
    }

    public boolean checkLoots(UserCampaignEntity uCampaign) {
        Double ran = new Random().nextDouble() * 100;
        double totalRate = 0;
        for (int i = 0; i < dropItems.size(); i += 2) {
            totalRate += dropItems.get(i + 1);
            if (ran < totalRate) {
                uCampaign.addLootItem(dropItems.get(i).intValue());
                return true;
            }
        }
        for (int i = 0; i < dropArtifact.size(); i += 2) {
            totalRate += dropArtifact.get(i + 1) * 100;
            if (ran < totalRate) {
                uCampaign.addLootArtifact(dropArtifact.get(i).intValue());
                return true;
            }
        }
        if (lootArtifactFrag != null && lootArtifactFrag != 0) {
            totalRate += MaterialType.getArtifactShard(lootArtifactFrag).rate;
            if (ran < totalRate) {
                uCampaign.addLootArtifactShard(lootArtifactFrag);
                return true;
            }
        }
        if (lootHeroShard != null && lootHeroShard != 0) {
            totalRate += MaterialType.getHeroShard(lootHeroShard).rate;
            if (ran < totalRate) {
                uCampaign.addLootHeroShard(lootHeroShard);
                return true;
            }
        }
        //        if (lootPromoStone != null && lootPromoStone != 0) {
        //            totalRate += MaterialType.PROMOTION_STONE.rate;
        //            if (ran < totalRate) {
        //                uCampaign.addLootPromotionStone();
        //                return true;
        //            }
        //        }
        if (lootChaosStone != null && lootChaosStone != 0) {
            totalRate += MaterialType.CHAOS_STONE.rate;
            if (ran < totalRate) {
                uCampaign.addLootChaosStone();
                return true;
            }
        }
        if (lootMagicDust != null && lootMagicDust != 0) {
            totalRate += MaterialType.MAGIC_DUST.rate;
            if (ran < totalRate) {
                uCampaign.addLootMagicDust();
                return true;
            }
        }
        //        if (lootBasicQuestScroll != null && lootBasicQuestScroll != 0) {
        //            totalRate += MaterialType.QUEST_SCROLL.rate;
        //            if (ran < totalRate) {
        //                //                            uCampaign.addLootQuestScroll();
        //                return true;
        //            }
        //        }
        //        if (lootSeniorQuestScroll != null && lootSeniorQuestScroll != 0) {
        //            totalRate += MaterialType.SENIOR_QUEST_SCROLL.rate;
        //            if (ran < totalRate) {
        //                //                            uCampaign.addLootSeniorQuestScroll();
        //                return true;
        //            }
        //        }
        if (lootMonsterSoul != null && lootMonsterSoul != 0) {
            totalRate += MaterialType.MONSTER_SOUL.rate;
            if (ran < totalRate) {
                uCampaign.addLootMonsterSoul();
                return true;
            }
        }

        return false;
    }

    public String addItem(int itemId) {
        JsonArray arr = new JsonArray();
        if (arr.size() < 50) {
            arr.add(itemId);
            return arr.toString();
        }
        return arr.toString();
    }

    public int addLoot(int item) {
        if (item < 200) {
            item++;
        }
        return item;
    }


    public List<Long> BonusByTime(UserCampaignEntity uCampaign) {
        List<Long> aBonus = new ArrayList<>();
        Double ran = new Random().nextDouble() * 100;
        double totalRate = 0;
        String lootItem = "", lootArtifact = "", lootArtifactShard = "", lootHeroShardx = "";
        // cacu item
        for (int i = 0; i < dropItems.size(); i += 2) {
            totalRate += dropItems.get(i + 1);
            if (ran < totalRate) {
                lootItem = addItem(dropItems.get(i).intValue());
                if (lootItem.length() > 2) {
                    Map<Integer, Integer> mItem = getMapItem(GsonUtil.parseJsonArray(lootItem));
                    mItem.forEach((itemId, number) -> aBonus.addAll(Bonus.view(Bonus.BONUS_ITEM, itemId, number)));
                    return aBonus;
                }
            }
        }
        // cacu artifact
        for (int i = 0; i < dropArtifact.size(); i += 2) {
            totalRate += dropArtifact.get(i + 1) * 100;
            if (ran < totalRate) {
                lootArtifact = addItem(dropArtifact.get(i).intValue());
                if (lootArtifact.length() > 2) {
                    Map<Integer, Integer> mArtifact = getMapItem(GsonUtil.parseJsonArray(lootArtifact));
                    mArtifact.forEach((artifactId, number) -> aBonus.addAll(Bonus.view(Bonus.BONUS_ARTIFACT, artifactId, 1, number)));
                    return aBonus;
                }
            }
        }

        // cacu lootArtifactFrag
        if (lootArtifactFrag != null && lootArtifactFrag != 0) {
            totalRate += MaterialType.getArtifactShard(lootArtifactFrag).rate;
            if (ran < totalRate) {
                lootArtifactShard = addItem(lootArtifactFrag);
                if (lootArtifactShard.length() > 2) {
                    Map<Integer, Integer> mArtifactShard = getMapItem(GsonUtil.parseJsonArray(lootArtifactShard));
                    mArtifactShard.forEach((artifactShardId, number) -> aBonus.addAll(Bonus.viewMaterial(MaterialType.getArtifactShard(artifactShardId), number)));
                    return aBonus;
                }
            }
        }

        // cacu lootHeroShard
        if (lootHeroShard != null && lootHeroShard != 0) {
            totalRate += MaterialType.getHeroShard(lootHeroShard).rate;
            if (ran < totalRate) {
                lootHeroShardx = addItem(lootHeroShard);
                if (lootHeroShardx.length() > 2) {
                    Map<Integer, Integer> mHeroShard = getMapItem(GsonUtil.parseJsonArray(lootHeroShardx));
                    mHeroShard.forEach((heroShardId, number) -> aBonus.addAll(Bonus.viewMaterial(MaterialType.getHeroShard(heroShardId), number)));
                    return aBonus;
                }
            }
        }

        // lootPromoStone
        if (lootPromoStone != null && lootPromoStone != 0) {
            totalRate += MaterialType.PROMOTION_STONE.rate;
            if (ran < totalRate) {
                aBonus.addAll(Bonus.viewMaterial(MaterialType.PROMOTION_STONE, lootPromoStone));
                return aBonus;
            }
        }
        // lootChaosStone
        if (lootChaosStone != null && lootChaosStone != 0) {
            totalRate += MaterialType.CHAOS_STONE.rate;
            if (ran < totalRate) {
                aBonus.addAll(Bonus.viewMaterial(MaterialType.CHAOS_STONE, lootChaosStone));
                return aBonus;
            }
        }

        if (lootMagicDust != null && lootMagicDust != 0) {
            totalRate += MaterialType.MAGIC_DUST.rate;
            if (ran < totalRate) {
                aBonus.addAll(Bonus.viewMaterial(MaterialType.MAGIC_DUST, lootMagicDust));
                return aBonus;
            }
        }
        if (lootBasicQuestScroll != null && lootBasicQuestScroll != 0) {
            totalRate += MaterialType.QUEST_SCROLL.rate;
            if (ran < totalRate) {
                aBonus.addAll(Bonus.viewMaterial(MaterialType.QUEST_SCROLL, lootBasicQuestScroll));
                return aBonus;
            }
        }
        if (lootSeniorQuestScroll != null && lootSeniorQuestScroll != 0) {
            totalRate += MaterialType.SENIOR_QUEST_SCROLL.rate;
            if (ran < totalRate) {
                aBonus.addAll(Bonus.viewMaterial(MaterialType.SENIOR_QUEST_SCROLL, lootSeniorQuestScroll));
                return aBonus;
            }
        }
        if (lootMonsterSoul != null && lootMonsterSoul != 0) {
            totalRate += MaterialType.MONSTER_SOUL.rate;
            if (ran < totalRate) {
                aBonus.addAll(Bonus.viewMaterial(MaterialType.MONSTER_SOUL, lootMonsterSoul));
                return aBonus;
            }
        }

        return aBonus;
    }

    public List<Long> bonusEventDrop(UserCampaignEntity uCampaign, int numberLoop) {
        // Event drop
        List<Long> aBonus = new ArrayList<>();
        List<ConfigEventDropEntity> activeEvent = CfgDropItem.aEventDrop.stream()
                .filter(event -> event.getStartDate().getTime() < System.currentTimeMillis() && System.currentTimeMillis() < event.getEndDate().getTime())
                .collect(Collectors.toList());
        if (!activeEvent.isEmpty()) {
            Random rand = new Random();
            activeEvent.forEach(event -> {
                if (event.getItemId() == 0) { // normal config
                    int numberDrop = 0;
                    for (int i = 0; i < numberLoop; i++) {
                        if (rand.nextFloat() * 100 < event.getRate()) numberDrop++;
                    }
                    JsonArray arrEvent = addEventDrop(event.getId(), numberDrop);
                    for (int i = 0; i < arrEvent.size(); i += 2) {
                        aBonus.addAll(CfgDropItem.getEventDrop(arrEvent.get(i).getAsInt(), arrEvent.get(i + 1).getAsInt()));
                    }
                } else { // super config -> follow by min, max rate
                    UserDropEventEntity userDropEntity = dbGetUserDropEntity(uCampaign.getUserId(), String.valueOf(event.getId()));
                    CfgDropItem.DropInfo dropInfo = CfgDropItem.config.getDropInfo(event.getItemId());
                    if (dropInfo != null) {
                        int numberDrop = 0;
                        for (int i = 0; i < numberLoop; i++) {
                            if (rand.nextFloat() < dropInfo.rate) numberDrop++;
                        }
                        if (userDropEntity != null) {
                            int newNumberDrop = userDropEntity.getNumberDrop() + numberDrop;
                            int newNumberCheck = userDropEntity.getNumberCheck() + numberLoop;
                            float minDrop = newNumberCheck * dropInfo.min, maxDrop = newNumberCheck * dropInfo.max;

                            if (newNumberDrop > maxDrop) { // fix to max drop
                                numberDrop = maxDrop > userDropEntity.getNumberDrop() ? (int) (maxDrop - userDropEntity.getNumberDrop()) : 0;
                            } else if (newNumberDrop < minDrop) {
                                numberDrop = minDrop > userDropEntity.getNumberDrop() ? (int) (minDrop - userDropEntity.getNumberDrop()) : 0;
                            }

                            if (numberDrop > 0) {
                                JsonArray arrEvent = addEventDrop(event.getId(), numberDrop);
                                for (int i = 0; i < arrEvent.size(); i += 2) {
                                    aBonus.addAll(CfgDropItem.getEventDrop(arrEvent.get(i).getAsInt(), arrEvent.get(i + 1).getAsInt()));
                                }
                            }
                        }
                    }
                }
            });
        }
        return aBonus;
    }

    public JsonArray addEventDrop(int eventId, int numberDrop) {
        JsonArray arr = new JsonArray();
        for (int i = 0; i < arr.size(); i += 2) {
            if (arr.get(i).getAsInt() == eventId) {
                arr.set(i + 1, new JsonPrimitive(arr.get(i + 1).getAsInt() + numberDrop));
                return arr;
            }
        }
        arr.add(eventId);
        arr.add(numberDrop);
        return arr;
    }

    public Map<Integer, Integer> getMapItem(JsonArray arr) {
        Map<Integer, Integer> map = new HashMap<>();
        for (int i = 0; i < arr.size(); i++) {
            if (!map.containsKey(arr.get(i).getAsInt())) map.put(arr.get(i).getAsInt(), 0);
            map.put(arr.get(i).getAsInt(), map.get(arr.get(i).getAsInt()) + 1);
        }
        return map;
    }

    public List<Long> getWinBonus() {
        //        List<Long> aLong = new ArrayList<>();
        //        if (!StringHelper.isEmpty(winReward)) {
        //            JSONObject obj = JSONObject.fromObject(winReward);
        //            if (obj.containsKey("exp")) aLong.addAll(Bonus.view(Bonus.BONUS_EXP, obj.getInt("exp")));
        //            if (obj.containsKey("gold")) aLong.addAll(Bonus.view(Bonus.BONUS_GOLD, obj.getInt("gold")));
        //            if (obj.containsKey("spirit"))
        //                aLong.addAll(Bonus.viewMaterial(MaterialType.SPIRIT, obj.getInt("spirit")));
        //            if (obj.containsKey("itemId")) aLong.addAll(Bonus.view(Bonus.BONUS_ITEM, obj.getInt("itemId"), 1));
        //            if (obj.containsKey("itemShardId"))
        //                aLong.addAll(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_HERO_SHARD, obj.getInt("itemShardId"), 50));
        //        }
        //        return aLong;
        if (StringHelper.isEmpty(winReward)) return new ArrayList<>();
        return GsonUtil.strToListLong(winReward);
    }

    public int getRequireLevel() {
        return requireLevel == null ? 1000 : requireLevel;
    }

    public class TeamConfig {
        public List<Integer> heroId;
        public List<Integer> position;
    }

    public List<Long> getAllHeroId() {
        //, monsterId, monsterPosition, monsterTeam,
        List<Long> ids = Arrays.asList(0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L);
        //        List<Long> pos = GsonUtil.strToListLong(monsterPosition);
        //        List<Long> idHero1 = GsonUtil.strToListLong(monsterId);
        //        // team 1
        //        for (int i = 0; i < pos.size(); i++) {
        //            ids.set(Math.toIntExact(pos.get(i) - 1), idHero1.get(i));
        //        }
        //        // team 2
        //        List<TeamConfig> team = null;
        //        if (!StringHelper.isEmpty(monsterTeam)) {
        //            team = new Gson().fromJson(monsterTeam, new TypeToken<List<TeamConfig>>() {
        //            }.getType());
        //            for (int i = 0; i < team.get(0).position.size(); i++) {
        //                ids.set(team.get(0).position.get(i) + 5, Long.valueOf(team.get(0).heroId.get(i)));
        //            }
        //        }
        return ids;
    }

    public int getChapter() {
        return Integer.parseInt(stage.split("-")[0]);
    }

    public int getChapterId() {
        return id;
        //return Integer.parseInt(stage.split("-")[0]) * 100 + Integer.parseInt(stage.split("-")[1]);
    }
}
