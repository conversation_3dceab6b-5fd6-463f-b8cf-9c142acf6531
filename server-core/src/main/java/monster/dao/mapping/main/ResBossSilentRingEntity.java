package monster.dao.mapping.main;

import grep.helper.DateTime;
import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@AllArgsConstructor
@Getter
@Setter
@Table(name = "res_boss_silent_ring")
@NoArgsConstructor
public class ResBossSilentRingEntity implements Serializable {
    @Id
    int state;
    int monster1, monster2, boss;
    int level1, level2, levelBoss;
    String listSkill1;
    String listSkill2;
    String hpSkill;
    String rankDmg;
    String reward;
    String rankDmgSeason;
    String seasonReward;
    String timeOpen;
    String timeEnd;

    //
    String timeExpireCollectLastReward;
    @Transient
    String difficult;

    public Date getTimeOpen() {
        return DateTime.convertStrToDate(timeOpen);
//        long friday10h = DateTime.getFullDateOfWeek(4,10);
//        long currentTime = (new Date()).getTime();
//
//        if(currentTime >= friday10h) {
//            return friday10h;
//        }
//        return  DateTime.getFullDateOfWeek(-3,10);
    }

    public Date getTimeEnd() {
        return DateTime.convertStrToDate(timeEnd);
        // return DateTime.getFullDateOfWeek(3,10);
        //return (getTimeOpen() +  6 * DateTime.DAY_MILLI_SECOND + 7 * DateTime.HOUR_MILLI_SECOND);
    }

    public long getTimeExpireCollectLastReward() {
        return DateTime.convertStrToDate(timeExpireCollectLastReward).getTime();
        // return (getTimeOpen() +  7 * DateTime.DAY_MILLI_SECOND -  1 * DateTime.HOUR_MILLI_SECOND );
    }

    public List<Integer> getHpSkill() {
        return GsonUtil.strToListInt(hpSkill);
    }

    public List<Integer> getListSkills1() {
        return GsonUtil.strToListInt(listSkill1);
    }

    public List<Integer> getListSkills2() {
        return GsonUtil.strToListInt(listSkill2);
    }

}
