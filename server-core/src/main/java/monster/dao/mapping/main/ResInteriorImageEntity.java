package monster.dao.mapping.main;

import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import monster.config.CfgInterior;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "dson_main.res_interior_image")
@Data
public class ResInteriorImageEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    int imageId;
    String name;
    String desc;
    int placeType;
    String genQuality;
    String genHero;

    int rulePoint;

    public List<Integer> getQualityGen() {
        return GsonUtil.strToListInt(genQuality);
    }

    public List<Integer> getHeroGen() {
        return GsonUtil.strToListInt(genHero);
    }

//    public Set<Integer> getOldQuality() {
//        List<ResInteriorItemEntity> aResItem = CfgInterior.listResItem;
//        Set<Integer> oldQuality = new HashSet<>();
//        for (ResInteriorItemEntity resItem : aResItem) {
//            oldQuality.add(resItem.getQuality());
//        }
//
//        return oldQuality;
//    }
//
//    public Set<Integer> getOldHero() {
//        List<ResInteriorItemEntity> aResItem = CfgInterior.listResItem;
//        Set<Integer> oldHero = new HashSet<>();
//        for (ResInteriorItemEntity resItem : aResItem) {
//            oldHero.add(resItem.getHero());
//        }
//
//        return oldHero;
//    }
}
