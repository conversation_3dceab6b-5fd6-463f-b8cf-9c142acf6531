package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import grep.helper.GsonUtil;
import grep.log.Logs;
import lombok.Data;
import monster.object.PointDescArtifactEntity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.HashMap;
import java.util.Map;

@Entity
@Table(name = "res_skin")
@Data
public class ResSkinEntity {
    @Id
    int id;
    String name, desc, point, changeSkill;

    @Transient
    PointDescArtifactEntity[] aPoint;
    @Transient
    Map<Integer, Integer> mChangeSkill = new HashMap<>();

    public Integer changeSkillId(int skillId) {
        return mChangeSkill.get(skillId);
    }

    public void init() {
        aPoint = new Gson().fromJson(point, PointDescArtifactEntity[].class);
        try {
            JsonObject obj = GsonUtil.parseJsonObject(changeSkill);
            for (String s : obj.keySet()) {
                mChangeSkill.put(Integer.parseInt(s), obj.get(s).getAsInt());
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }
}
