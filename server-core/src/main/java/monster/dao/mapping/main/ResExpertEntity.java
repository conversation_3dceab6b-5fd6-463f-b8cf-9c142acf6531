package monster.dao.mapping.main;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.helper.GsonUtil;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "res_expert")
@Data
public class ResExpertEntity {
    @Id
    private int id;
    private int heroId;
    private String questData;
    private boolean isDisable;

    public String getDefaultData() {
        JsonArray arr = GsonUtil.parseJsonArray(questData);
        for (int i = 0; i < arr.size(); i++) {
            JsonArray subArr = arr.get(i).getAsJsonArray();
            for (int j = 0; j < subArr.size(); j++) {
                subArr.set(j, new JsonPrimitive(0));
            }
        }
        return arr.toString();
    }
}
