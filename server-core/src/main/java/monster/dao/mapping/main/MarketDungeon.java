package monster.dao.mapping.main;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "market_dungeon", schema = "dson_main")
public class MarketDungeon {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "shop_level")
    private Integer shopLevel;

    @Column(name = "dungeon_level_required")
    private Integer dungeonLevelRequired;

    @Column(name = "item")
    private String item;

    @Column(name = "price")
    private String price;

}