package monster.dao.mapping.main;

import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.penum.MarketType;
import monster.service.resource.SuperMarket;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.List;

@Entity
@Table(name = "market")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MarketDetailEntity implements Serializable {
    @Id
    int id;
    int marketId, show, stock, itemOrder;
    int typeHard, priceOff;
    String price, item;
    float percent;

    public boolean isShow() {
        return show == 1;
    }

//    public void checkRandomItem() {
//        if (item.equals("[3,2,-1,20]")) {
//            item = String.format("[3,2,%s,20]", ResHero.getRandomHeroSameStar(3).id);
//        }
//    }

    public Pbmethod.CommonVector.Builder toProtoUnlimitedNew() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();

        int priceOff = getPriceOff();
        if (priceOff > 0) builder.addAString(String.valueOf(priceOff));

        builder.addALong(id).addALong(100);
        builder.addAllALong(getShowListPriceBonus());
        builder.addALong(-1);
        builder.addAllALong(GsonUtil.strToListLong(item));
        return builder;
    }

    public Pbmethod.CommonVector.Builder toProtoUnlimited() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();

        int priceOff = getPriceOff();
        if (priceOff > 0) builder.addAString(String.valueOf(priceOff));

        builder.addALong(id).addALong(MarketType.ITEM_AVAILABLE);
        builder.addAllALong(getShowListPriceBonus());
        builder.addALong(-1);
        builder.addAllALong(GsonUtil.strToListLong(item));
        return builder;
    }

    public Pbmethod.CommonVector.Builder toProtoStock(int index) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();

        int priceOff = getPriceOff();
        if (priceOff > 0) builder.addAString(String.valueOf(priceOff));

        builder.addALong(index).addALong(stock <= 0 ? MarketType.ITEM_OUT_OF_STOCK : stock);
        builder.addAllALong(getShowListPriceBonus());
        builder.addALong(-1);
        builder.addAllALong(GsonUtil.strToListLong(item));
        return builder;
    }

    public List<Long> getShowListPriceBonus() {
        int priceOff = getPriceOff();
        List<Long> aLong = GsonUtil.strToListLong(price);
        int index = aLong.size() - 1;
        if (priceOff > 0) aLong.set(index, aLong.get(index) * (100 - priceOff) / 100);
        return aLong;
    }

    public List<Long> getListPriceBonus() {
        int priceOff = getPriceOff();
        List<Long> aLong = GsonUtil.strToListLong(price);
        int index = aLong.size() - 1;
        if (priceOff > 0) aLong.set(index, -Math.abs(aLong.get(index) * (100 - priceOff) / 100));
        else aLong.set(index, -Math.abs(aLong.get(index)));
        return aLong;
    }

    public int getPriceOff() {
        MarketDetailEntity marketDetail = SuperMarket.getItem(id);
        return marketDetail == null ? 0 : marketDetail.priceOff;
    }
}
