package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserGemEntity;
import monster.service.resource.ResGem;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Table(name = "res_gem_summon")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResGemSummonEntity implements java.io.Serializable {

    @Id
    public int id;
    public int version;
    public String name, pollGem;

    @Transient
    Map<Integer, GemData> mData = new HashMap<>();
    @Transient
    public String defaultData;

    public GemData getGemData(int idData) {
        return mData.get(idData);
    }

    public void init() {
        List<GemData> gems = new Gson().fromJson(pollGem, new TypeToken<List<GemData>>() {
        }.getType());
        List<Integer> aLong = new ArrayList<>();
        for (GemData gem : gems) {
            aLong.add(gem.id);
            aLong.add(gem.numberSummon);
            mData.put(gem.id, gem);
        }
        defaultData = StringHelper.toDBString(aLong);
    }

    public class GemData {
        public int id, id_res_set_gem, Quality, Clazz, numberSummon;

        public UserGemEntity toUserGemEntity(int userId) {
            var builder = UserGemEntity.builder()
                    .userId(userId).setId(id_res_set_gem).clazz(Clazz != 0 ? Clazz : NumberUtil.getRandom(1, 5)).quality(Quality);

            ResGemQualityEntity quality1 = ResGem.getRandomQuality(Quality);
            builder.pointType1(quality1.pointId).pointValue1(quality1.getPoint());

            ResGemQualityEntity quality2 = ResGem.getRandomQuality(Quality);
            builder.pointType2(quality2.pointId).pointValue2(quality2.getPoint());

            ResGemQualityEntity quality3 = ResGem.getRandomQuality(Quality);
            builder.pointType3(quality3.pointId).pointValue3(quality3.getPoint());

            ResGemQualityEntity quality4 = ResGem.getRandomQuality(Quality);
            builder.pointType4(quality4.pointId).pointValue4(quality4.getPoint());

            return builder.build();
        }
    }
}
