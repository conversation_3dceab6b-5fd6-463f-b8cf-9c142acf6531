package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserInteriorItemEntity;
import monster.object.PointDescHeroEntity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.*;

@Entity
@Table(name = "dson_main.res_interior_collection")
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class ResInteriorCollectionEntity {
    @Id
    int heroKey;
    String heroName;
    String collection;
    int wallpaperId;
    String point6;
    String point12;
    String point18;

    @Transient
    List<Integer> aCollection;

    public ResInteriorCollectionEntity(int heroKey, String heroName, String collection, int wallpaperId, String point6, String point12, String point18) {
        this.heroKey = heroKey;
        this.heroName = heroName;
        this.collection = collection;
        this.wallpaperId = wallpaperId;
        this.point6 = point6;
        this.point12 = point12;
        this.point18 = point18;
    }

    public List<Integer> getACollection() {
        if (aCollection != null && !aCollection.isEmpty()) return aCollection;
        aCollection = GsonUtil.strToListInt(collection);
        return aCollection;
    }

    public PointDescHeroEntity[] getEffectForListItem(List<UserInteriorItemEntity> aUIItem) {
        List<Integer> listIItemKey = new ArrayList<>();
        for (UserInteriorItemEntity uIItem : aUIItem){
            listIItemKey.add(uIItem.getItemKey());
        }

        return getEffectForListItemKey(listIItemKey);
    }

    public PointDescHeroEntity[] getEffectForListItemKey(List<Integer> listIItemKey) {
        int count = getCountToEffect(listIItemKey);
        if (count < 6) return null;
        PointDescHeroEntity[] effect = new PointDescHeroEntity[3];
        List<String> values = Arrays.asList(point6, point12, point18);
        List<Integer> numbers = Arrays.asList(6, 12, 18);
        for (int i = 0; i < values.size(); i++) {
            if (count < numbers.get(i)) break;
            PointDescHeroEntity[] tmp = new Gson().fromJson(values.get(i), new TypeToken<PointDescHeroEntity[]>() {
            }.getType());
            effect[i] = tmp[0];
        }

        return effect;
    }

//    public int getCountToEffect(List<UserInteriorItemEntity> aUIItem) {
//        if (aUIItem == null || aUIItem.isEmpty()) return 0;
//        List<Integer> listIItemKey = new ArrayList<>();
//        for (UserInteriorItemEntity uIItem : aUIItem){
//            listIItemKey.add(uIItem.getItemKey());
//        }
//
//        return getCountToEffect(listIItemKey);
//    }

    public int getCountToEffect(List<Integer> listIItemKey) {
        if (listIItemKey == null || listIItemKey.isEmpty()) return 0;

        Map<Integer, Integer> mapItemCountToEffect = new HashMap<>();
        Map<Integer, Integer> mapItemNumber = new HashMap<>();

        for (int i = 0; i < getACollection().size(); i++) {
            int itemKey = getACollection().get(i);
            if (!mapItemCountToEffect.containsKey(itemKey)) mapItemCountToEffect.put(itemKey, 1);
            else mapItemCountToEffect.put(itemKey, mapItemCountToEffect.get(itemKey) + 1);
        }

        for (int itemKey : listIItemKey) {
            if (!mapItemCountToEffect.containsKey(itemKey)) continue;
            if (!mapItemNumber.containsKey(itemKey)) mapItemNumber.put(itemKey, 1);
            else mapItemNumber.put(itemKey, mapItemNumber.get(itemKey) + 1);
        }

        if (mapItemCountToEffect.isEmpty() || mapItemNumber.isEmpty()) return 0;

        int count = 0;
        for (Map.Entry<Integer, Integer> entry : mapItemCountToEffect.entrySet()) {
            int itemKey = entry.getKey();
            if(entry.getValue() == null || mapItemNumber.get(itemKey) == null) continue;
            count += Math.min(entry.getValue(), mapItemNumber.get(itemKey));
        }


        return count;
    }
}
