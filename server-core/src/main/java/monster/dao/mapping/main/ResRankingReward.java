package monster.dao.mapping.main;

import grep.helper.GsonUtil;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "res_ranking_reward", schema = "dson_main")
public class ResRankingReward {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "type")
    private Integer type;

    @Column(name = "description")
    private String description;

    @Column(name = "user_rank")
    private String userRank;

    @Column(name = "reward")
    private String reward;

    @Transient
    List<List<Integer>> ranks;
    @Transient
    List<List<Long>> rewards;

    public List<List<Integer>> getListRank() {
        if (ranks == null) {
            ranks = GsonUtil.strTo2ListInt(userRank);
        }
        return ranks;
    }

    public List<List<Long>> getListReward() {
        if (rewards == null) {
            rewards = GsonUtil.strTo2ListLong(reward);
        }
        return rewards;
    }

    public List<Long> getBonus(int myRank) {
        for (int i = 0; i < getListRank().size(); i++) {
            if (myRank >= ranks.get(i).getFirst() && (myRank <= ranks.get(i).getLast() || ranks.get(i).getLast() == -1)) {
                return getListReward().get(i);
            }
        }
        return new ArrayList<>();
    }

    public List<Long> getListBonus(int getRank) {
        var ranks = getListRank();
        var rewards = getListReward();
        for (int index = 0; index < ranks.size(); index++) {
            var rank = ranks.get(index);
            List<Long> reward = rewards.get(index);
            if (rank.size() == 1 && rank.get(0) == getRank) return reward;
            else if (rank.size() > 1 && rank.get(0) <= getRank && (getRank <= rank.get(rank.size() - 1) || rank.get(rank.size() - 1) == -1)) return reward;
        }
        return new ArrayList<>();
    }
}