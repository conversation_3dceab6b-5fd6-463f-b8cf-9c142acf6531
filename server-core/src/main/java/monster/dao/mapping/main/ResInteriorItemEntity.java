package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.helper.GsonUtil;
import lombok.*;
import monster.config.CfgInterior;
import monster.dao.mapping.UserHeroEntity;
import monster.object.PointDescHeroEntity;
import monster.service.resource.ResHero;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "dson_main.res_interior_item")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResInteriorItemEntity implements Serializable {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    int id;
    int imageId;
    int quality;
    int hero;
    int clazz;
    int faction;
    String effect;

    public PointDescHeroEntity[] getAEffect() {
        if (aEffect == null || aEffect.length == 0) {
            aEffect = new Gson().fromJson(effect, new TypeToken<PointDescHeroEntity[]>(){
            }.getType());
        }

        return aEffect;
    }

    @Transient
    PointDescHeroEntity[] aEffect;

    public boolean isOnGround(){
        return CfgInterior.mResImage.get(imageId).placeType == 1;
    }
}
