package monster.dao.mapping.main;

import com.google.gson.Gson;
import grep.helper.GsonUtil;
import lombok.Data;
import monster.config.CfgBossInterServer;
import monster.config.CfgBossServer;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.*;

@Entity
@Table(name = "dson_main.res_boss_inter_server")
@Data
public class ResBossInterServerEntity {
    @Id
    int id;
    long power;
    String level, team, bonusRank, bonusDamage;
    int avatarShow;

    public List<Integer> getALevel() {
        return GsonUtil.strToListInt(level);
    }

    public List<Integer> getAHero() {
        return GsonUtil.strToListInt(team);
    }


    public CfgBossInterServer.RankBonus getBonusRank(){
        return new Gson().from<PERSON><PERSON>(bonusRank, CfgBossInterServer.RankBonus.class);
    }

    public CfgBossInterServer.DamageBonus getBonusDamage(){
        return new Gson().from<PERSON>son(bonusDamage, CfgBossInterServer.DamageBonus.class);
    }
}
