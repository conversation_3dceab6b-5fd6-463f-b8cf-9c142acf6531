package monster.dao.mapping.main;

import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Table(name = "warning_material", catalog = "dson_main")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarningMaterialEntity implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public int id;
    public int bonusId, typeId, materialId;
    public long number;
    public String name, actionKey;

    public String getKey() {
        if (StringHelper.isEmpty(actionKey))
            return String.format("%s_%s_%s", bonusId, typeId, materialId);
        return String.format("%s_%s_%s_%s", actionKey, bonusId, typeId, materialId);
    }
}
