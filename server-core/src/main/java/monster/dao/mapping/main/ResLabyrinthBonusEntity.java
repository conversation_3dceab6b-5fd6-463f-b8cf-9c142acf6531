package monster.dao.mapping.main;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "res_labyrinth_bonus")
@NoArgsConstructor
@Data
public class ResLabyrinthBonusEntity {
    @Id
    private int stage;
    String firstPreview, secondPreview;
    String monster, monsterElite, boss, item;
    String hardMonster, hardMonsterElite, hardBoss, hardItem;
}
