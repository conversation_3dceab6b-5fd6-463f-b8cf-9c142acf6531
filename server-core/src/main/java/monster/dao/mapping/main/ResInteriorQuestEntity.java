package monster.dao.mapping.main;

import grep.helper.GsonUtil;
import lombok.Getter;
import monster.config.CfgInterior;
import monster.config.lang.Lang;
import monster.service.battle.common.config.HeroType;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "dson_main.res_interior_gallery")
@Getter
public class ResInteriorQuestEntity {
    @Id
    private int questId;
    private String title;
    private String titleEn;
    private short isEnable;
    private short isPlus;
    private String numbers;
    private String types;
    private String bonus;
    private String wallpapers;

    public String getTitle(Lang lang, int value) {
        String result = lang == null || lang.isVi() ? title : titleEn;
        switch (questId) {
            case CfgInterior.ITEM_QUALITY2:
            case CfgInterior.ITEM_CREATE:
            case CfgInterior.RED_CARD:
            case CfgInterior.YELLOW_CARD:
                List<Long> aNumber = getANumber();
                for (Long integer : aNumber) {
                    if (value == integer) return String.format(result, integer);
                }
                break;
            case CfgInterior.GALLERY:
                List<Long> aType = getAType();
                for (Long aInt : aType) {
                    if (value == aInt) {
                        ResInteriorGalleryEntity gallery = CfgInterior.mGallery.get(value);
                        return String.format(result, gallery.getName());
                    }
                }
                break;
//            case CfgInterior.HERO_FACTION:
//                return String.format(result, HeroType.getFaction(value).nameVi);
        }

        return null;
    }

    public List<Long> getANumber() {
        return GsonUtil.strToListLong(numbers);
    }

    public List<Long> getAType() {
        return GsonUtil.strToListLong(types);
    }

    public List<Long> getAWallpaper() {
        return GsonUtil.strToListLong(wallpapers);
    }

    public List<List<Long>> getBonus() {
        return GsonUtil.strTo2ListLong(bonus);
    }

    public boolean isPlusType() {
        return isPlus == 1;
    }

    public boolean isEnabled() {
        return isEnable == 1;
    }

    public List<Long> getBonus(long value) {
        List<Long> aLong = isPlusType() ? getANumber() : getAType();
        List<List<Long>> aBonus = getBonus();
        if (aLong == null || aLong.isEmpty()) return new ArrayList<>();
        if (aBonus == null || aBonus.isEmpty()) return new ArrayList<>();
        for (int i = 0; i < aLong.size(); i++) {
            if (value == aLong.get(i)) return aBonus.get(i);
        }

        return new ArrayList<>();
    }

    public int getWallpaper(long value) {
        List<Long> aWallpaper = getAWallpaper();
        List<Long> aLong = isPlusType() ? getANumber() : getAType();
        if (aWallpaper == null || aWallpaper.isEmpty()) return -1;
        if (aLong == null || aLong.isEmpty()) return -1;
        for (int i = 0; i < aLong.size(); i++) {
            if (value == aLong.get(i)) return aWallpaper.get(i).intValue();
        }

        return -1;
    }
}
