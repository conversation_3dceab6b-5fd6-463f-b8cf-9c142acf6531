package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.resource.ResHero;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "res_relic")
@Data
public class ResRelicEntity {

    public static final int USED_ALL_ALLY = 0, USED_HERO_ID = 1, USED_FRONT_LINE = 2, USED_BACK_LINE = 3, USED_CLASS = 5, USED_FACTION = 4, USED_ALL_ENEMY = 6;
    public static final int USED_ANY_ALLY = 7;
    //
    public static final int SPECIAL_TYPE_IN_BATTLE = 0, SPECIAL_TYPE_SANJI_RICE = 1, SPECIAL_TYPE_HEAL_AFTER_BATTLE = 2;
    public static final int SPECIAL_TYPE_POINT_X_NUMBER_WIN = 3;
    public static final int SPECIAL_TYPE_FRAGMENT_POWER = 4, SPECIAL_TYPE_X_FRAGMENT_POWER = 5;
    public static final int SPECIAL_TYPE_BUFF_ATK_HP = 6;
    public static final int SPECIAL_TYPE_END_ROUND_EXTRA_DAMAGE = 7, SPECIAL_TYPE_END_ROUND_KILLER = 8;
    public static final int SPECIAL_TYPE_FRONTLINE_ATTACK_TEAMMATE = 9;
    public static final int SPECIAL_TYPE_ENERGY_SEAL_BELOW_15 = 10, SPECIAL_TYPE_ENERGY_SEAL_WHEN_OPP_ACTIVE_SKILL = 11;
    public static final int SPECIAL_TYPE_STEAL_HP = 12;

    @Id
    int id;
    String name, description, skillData;
    int rrank;
    int gameMode; // áp dụng trong mode nào của game
    int usabled; // Dùng cho tướng nào
    String data; // data cho biến usabled
    /**
     * áp dụng trong các trường hợp đặc biệt để làm skill -> phức tạp quá
     * = 0 -> bình thường ra SkillEntity áp dụng trong battle
     * = 1 -> bữa cơm sanji
     */
    int specialType;

    public List<HeroBattleEntity> getSupportHero(HeroBattleEntity[] atkHeroes, HeroBattleEntity[] defHeroes) {
        JsonArray arr = GsonUtil.parseJsonArray(getData());
        List<HeroBattleEntity> aHero = new ArrayList<>();
        for (int i = 0; i < atkHeroes.length; i++) {
            {
                HeroBattleEntity hero = atkHeroes[i];
                if (hero != null && hero.isHero()) {
                    switch (usabled) {
                        case USED_ANY_ALLY:
                            aHero.add(hero);
                            return aHero;
                        case USED_ALL_ALLY:
                            aHero.add(hero);
                            break;
                        case USED_HERO_ID:
                            for (int index = 0; index < arr.size(); index++)
                                if (hero.heroId == arr.get(index).getAsInt())
                                    aHero.add(hero);
                            break;
                        case USED_FRONT_LINE:
                            if (i < 3) aHero.add(hero);
                            break;
                        case USED_BACK_LINE:
                            if (i >= 3) aHero.add(hero);
                            break;
                        case USED_CLASS: {
                            ResHeroEntity resHero = ResHero.getHero(hero.heroId);
                            if (resHero.getHeroClass().value == arr.get(0).getAsInt()) aHero.add(hero);
                            break;
                        }
                        case USED_FACTION: {
                            ResHeroEntity resHero = ResHero.getHero(hero.heroId);
                            if (resHero.getHeroFaction().value == arr.get(0).getAsInt()) aHero.add(hero);
                            break;
                        }
                    }
                }
            }
            {
                HeroBattleEntity hero = defHeroes[i];
                if (hero != null && hero.isHero()) {
                    switch (usabled) {
                        case USED_ALL_ENEMY:
                            aHero.add(hero);
                            break;
                    }
                }
            }
        }
        return aHero.stream().filter(HeroBattleEntity::isHero).collect(Collectors.toList());
    }

    public String getData() {
        return StringHelper.isEmpty(data) || data.length() <= 2 ? "[]" : data;
    }

    public int getBuffFountain() {
        if (specialType == SPECIAL_TYPE_SANJI_RICE) {
            return Integer.parseInt(skillData);
        }
        return 0;
    }

    public int getBuffAfterBattle() {
        if (specialType == SPECIAL_TYPE_HEAL_AFTER_BATTLE) {
            return Integer.parseInt(skillData);
        }
        return 0;
    }

    @Transient
    SkillEntity skill;

    public void init() {
        if (specialType == 0 && !StringHelper.isEmpty(skillData) && skillData.length() > 20) {
            skill = new Gson().fromJson(skillData, SkillEntity.class);
            skill.animation = 99;
        }
    }

}
