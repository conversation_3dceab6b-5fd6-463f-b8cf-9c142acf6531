package monster.dao.mapping.main;

import com.google.gson.JsonArray;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.object.MyUser;
import monster.service.Services;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Transient;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigFuseSsEntity {

    @Id
    private int heroId;
    private String food, conditions;
    private int number;

    @Transient
    private Map<Integer, Integer> mFuse;

    public String getFood() {
        if (StringHelper.isEmpty(food) || !food.startsWith("[")) {
            food = "[";
        }
        return food;
    }

    public Map<Integer, Integer> getMFuse() {
        if (mFuse == null) {
            mFuse = new HashMap();
            JsonArray arr = GsonUtil.parseJsonArray(getFood());
            for (int i = 0; i < arr.size(); i += 2) {
                mFuse.put(arr.get(i).getAsInt(), arr.get(i + 1).getAsInt());
            }
        }
        return mFuse;
    }

    public List<Long> getListFoodConfig() {
        return GsonUtil.strToListLong(getFood());
    }

    public boolean checkConditions(MyUser mUser) {
        return Services.userService.checkConditions(mUser, conditions);
    }

}
