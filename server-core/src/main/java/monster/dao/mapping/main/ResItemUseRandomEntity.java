package monster.dao.mapping.main;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;

@Entity
@Table(name = "res_item_use_random")
@Data
public class ResItemUseRandomEntity {
    @Id
    int id;
    int itemId;
    float rate;
    String bonus, especially;

    public void addRate(float rate) {
        this.rate += rate;
    }

    public List<Long> getBonus() {
        return new Gson().fromJson(bonus, new TypeToken<List<Long>>() {
        }.getType());
    }

    public List<Long> getEspecially() {
        return new Gson().fromJson(especially, new TypeToken<List<Long>>() {
        }.getType());
    }
}
