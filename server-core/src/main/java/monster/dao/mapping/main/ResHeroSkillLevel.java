package monster.dao.mapping.main;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "res_hero_skill_level", schema = "dson_main")
public class ResHeroSkillLevel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "level", nullable = false)
    private Integer level;

    @Column(name = "active_skill")
    private Integer activeSkill;

    @Column(name = "passive1")
    private Integer passive1;

    @Column(name = "passive2")
    private Integer passive2;

    @Column(name = "passive3")
    private Integer passive3;

}