package monster.dao.mapping.main;

// Generated Jul 29, 2014 3:03:44 PM by Hibernate Tools 3.4.0.CR1

import lombok.Data;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * Config generated by hbm2java
 */
@Entity
@Data
@Table(name = "config", catalog = "dson_main")
public class ConfigEntity {

    @Id
    private String k;
    private String v;

    public ConfigEntity() {
    }

    public ConfigEntity(String k, String v) {
        this.k = k;
        this.v = v;
    }

    public int getVInt() {
        return Integer.parseInt(v);
    }


}
