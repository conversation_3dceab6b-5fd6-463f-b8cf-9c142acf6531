package monster.dao.mapping.main;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserCampaignEntity;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.common.SystemService;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigFunctionEntity {

    @Id
    private int id;
    private String name;
    private int isEnable, isNotify;
    public int campaignUnlock, dayOpenServer, userVip, userLevel;

}
