package monster.dao.mapping.main;

import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.game.story.entity.UserStoryEntity;
import monster.game.territory.entity.UserTerritoryEntity;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.common.SystemService;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Transient;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigFunctionEntity {

    @Id
    private int id;
    private String name;
    private int isEnable, isNotify;
    int clientToGo;
    String storyUnlock;
    int dayOpenServer, maxLevel;
    int userVip, userLevel;
    int campaignLevel, territoryTotalLevel, shibuyaLevel;
    @Transient
    int chapter = 0, level = 0;

    public boolean isStoryUnlock(UserStoryEntity userStory) {
        if (StringHelper.isEmpty(storyUnlock) || !storyUnlock.contains("-")) return true;
        if (chapter == 0) {
            String[] info = storyUnlock.split("-");
            chapter = Integer.parseInt(info[0]);
            level = Integer.parseInt(info[1]);
        }
        return userStory.getUnlockChapter() > chapter
                || (userStory.getUnlockChapter() == chapter && userStory.getLevel() >= level);
    }

    public boolean isDayOpenServer(int serverDay) {
        if (dayOpenServer == 0) return true;
        return Guice.getInstance(SystemService.class).getNumberDayServerOpen(serverDay) >= dayOpenServer;
    }

    public boolean isCampaignLevel(MyUser mUser) {
        return campaignLevel == 0 || Services.userService.getCampaign(mUser).getLevel() >= campaignLevel;
    }

    public boolean isShibuyaLevel(MyUser mUser) {
        return shibuyaLevel == 0 || mUser.getUData().getMaxLabyrinth() >= shibuyaLevel;
    }

    public boolean isTotalTerritoryLevel(MyUser mUser) {
        if (this.territoryTotalLevel > 0) {
            var userTerritoryMapByFactionId = mUser.getCache().getUserTerritoryMapByFactionId(null, mUser);
            if (userTerritoryMapByFactionId == null) return false;
            var sumLevel = userTerritoryMapByFactionId.values().stream().mapToInt(UserTerritoryEntity::getLevel).sum();
            return sumLevel >= this.territoryTotalLevel;
        }
        return true;
    }
}
