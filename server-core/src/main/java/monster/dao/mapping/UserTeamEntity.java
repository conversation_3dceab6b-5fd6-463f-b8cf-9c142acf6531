package monster.dao.mapping;

import com.google.gson.Gson;
import lombok.*;
import monster.object.BattleTeam;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;

@Entity
@Table(name = "user_team")
@NoArgsConstructor
@Data
public class UserTeamEntity implements Serializable {
    @Id
    int userId, teamId;
    int serverId;
    long power, lastWinPower;
    String info, data;

    @Transient
    BattleTeam battleTeam;

    public BattleTeam getBattleTeam() {
        if (battleTeam == null) {
            battleTeam = new Gson().fromJson(data, BattleTeam.class);
        }
        return battleTeam;
    }

    public void setData(String data) {
        this.data = data;
        battleTeam = null;
    }

    public UserTeamEntity(int userId, int teamId, int serverId) {
        this.userId = userId;
        this.teamId = teamId;
        this.serverId = serverId;
    }

    public BattleTeam getTeam() {
        return new Gson().fromJson(data, BattleTeam.class);
    }
}
