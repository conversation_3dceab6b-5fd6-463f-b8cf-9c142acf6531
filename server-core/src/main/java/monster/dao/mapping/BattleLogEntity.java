package monster.dao.mapping;

import com.google.gson.Gson;
import grep.helper.DateTime;
import grep.helper.Filer;
import grep.helper.HttpHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgServer;
import monster.config.penum.BattleType;
import monster.server.AppConfig;
import monster.service.battle.dependence.BattleInput;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "battle_log")
@Data
@NoArgsConstructor
public class BattleLogEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    String title;
    int battleType, serverId;
    Date dateCreated;

    public BattleLogEntity(String title, int battleType) {
        this.title = title;
        this.battleType = battleType;
        this.dateCreated = new Date();
        this.serverId = CfgServer.serverId;
    }

    public BattleInput getInput() {
        BattleType battleType = BattleType.get(getBattleType());
        if (this.serverId == 0 || this.serverId == CfgServer.serverId || !battleType.logPath.equals("battlelogs")) { // the old way
            String tmp = DateTime.getDateyyyyMMdd(getDateCreated());
            String data = Filer.readFile(battleType.logPath + "/" + tmp + "/" + getId() + ".input");
            if (data == null || data.length() < 10) return null;
            return new Gson().fromJson(data, BattleInput.class);
        }
        //        String data = HttpHelper.getContent(String.format("http://%s.%s:%s/admin/cfg/battle/%s",
        //                AppConfig.cfg.prefixIp, AppConfig.cfg.localIp, AppConfig.cfg.backdoor, id)); // Filer.getHttpContent(gameUrl + battleId);
        //        System.out.println(String.format("http://%s/admin/cfg/battle/%s", CfgServer.getMappingServer(serverId), id));
        String data = HttpHelper.getContent(String.format("http://%s/admin/cfg/battle/%s", AppConfig.getServerUrl(serverId), id)); // Filer.getHttpContent(gameUrl + battleId);
        return new Gson().fromJson(data, BattleInput.class);
    }

    public byte[] getRawData() {
        BattleType battleType = BattleType.get(getBattleType());
        if (this.serverId == 0 || this.serverId == CfgServer.serverId || !battleType.logPath.equals("battlelogs")) { // the old way
            String tmp = DateTime.getDateyyyyMMdd(getDateCreated());
            byte[] data = Filer.readBinFile(battleType.logPath + "/" + tmp + "/" + getId());
            if (data == null || data.length < 10) return null;
            return data;
        }
        String tmp = HttpHelper.getContent(String.format("http://%s/admin/cfg/battle/%s", AppConfig.getServerUrl(serverId), "b" + id)); // Filer.getHttpContent(gameUrl + battleId);
        String[] explode = tmp.split(",");
        byte[] data = new byte[explode.length];
        for (int i = 0; i < explode.length; i++) data[i] = Byte.parseByte(explode[i]);
        return data;
    }

    public byte[] getOutputRaw() {
        BattleType battleType = BattleType.get(getBattleType());
        if (this.serverId == 0 || this.serverId == CfgServer.serverId || !battleType.logPath.equals("battlelogs")) { // the old way
            String tmp = DateTime.getDateyyyyMMdd(getDateCreated());
            byte[] data = Filer.readBinFile(battleType.logPath + "/" + tmp + "/" + getId() + ".output");
            if (data == null || data.length < 10) return null;
            return data;
        }
        String tmp = HttpHelper.getContent(String.format("http://%s/admin/cfg/battleoutput/%s", AppConfig.getServerUrl(serverId), "b" + id)); // Filer.getHttpContent(gameUrl + battleId);
        String[] explode = tmp.split(",");
        byte[] data = new byte[explode.length];
        for (int i = 0; i < explode.length; i++) data[i] = Byte.parseByte(explode[i]);
        return data;
    }

}
