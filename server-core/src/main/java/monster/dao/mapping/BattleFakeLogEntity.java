package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "battle_fake_log")
@Data
@NoArgsConstructor
public class BattleFakeLogEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;
    String title;
    int battleType;
    Date dateCreated;

    public BattleFakeLogEntity(String title, int battleType) {
        this.title = title;
        this.battleType = battleType;
        this.dateCreated = new Date();
    }

    public String toString() {
        return id + " - " + title + " - " + battleType + " - " + dateCreated;
    }
}
