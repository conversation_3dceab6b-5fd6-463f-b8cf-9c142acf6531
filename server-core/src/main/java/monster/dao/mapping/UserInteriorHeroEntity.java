package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "user_interior_hero")
@Data
@NoArgsConstructor
public class UserInteriorHeroEntity {
    @Id
    int heroId;
    int heroKey;
    int userId; // user id
    int shipId; // thuyền id
    int floorIndex;

    public UserInteriorHeroEntity(int heroId, int heroKey, int userId, int shipId, int floorIndex) {
        this.heroId = heroId;
        this.heroKey = heroKey;
        this.userId = userId;
        this.shipId = shipId;
        this.floorIndex = floorIndex;
    }


//    public List<Long> getDataFloor(){
//        return GsonUtil.strToListLong(dataFloor);
//    }/

//    public CfgInterior.Boat getObjBoat() {
//        if (objBoat == null) objBoat = new Gson().fromJson(data, CfgInterior.Boat.class);
//        return objBoat;
//    }
//
//    public List<List<Integer>> getBoatData() {
//        List<List<Integer>> aLLong = new ArrayList<>();
//        List<CfgInterior.Floor> aFloor = getObjBoat().getDataFloor();
//
//        for (CfgInterior.Floor floor : aFloor) {
//            List<Integer> aLong = new ArrayList<>();
//            aLong.add(floor.getHeroId());
//            floor.getDataSlot().forEach(slot -> aLong.addAll(slot.toListInt()));
//            aLLong.add(aLong);
//        }
//
//        return aLLong;
//    }

    /**
     * @return heroId + id thuyền + index tầng
     */
    public List<Long> getInfo() {
        return new ArrayList<>(Arrays.asList((long) heroId, (long) shipId, (long) floorIndex));
    }
}
