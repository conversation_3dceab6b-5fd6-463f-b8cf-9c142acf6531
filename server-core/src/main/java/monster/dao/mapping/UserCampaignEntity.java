package monster.dao.mapping;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.log.Logs;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgCampaign;
import monster.config.CfgDropItem;
import monster.config.CfgLimitFunction;
import monster.config.CfgUser;
import monster.config.penum.MaterialType;
import monster.dao.mapping.main.ResCampaignEntity;
import monster.object.MyUser;
import monster.server.Constans;
import monster.server.config.Guice;
import monster.service.common.CampaignService;
import monster.game.truongevent.service.TruongProEventService;
import monster.service.monitor.Telegram;
import monster.service.resource.ResCampaign;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.util.*;

@Entity
@Table(name = "user_campaign")
@NoArgsConstructor
@Data
public class UserCampaignEntity {
    @Id
    int userId;
    int serverId;
    int level, levelAuto;
    int levelBug;
    long gold, spirit, exp;
    long firstLoosePower;
    int firstLooseLevel;
    long lastAward, lastLevelUp, lastLoots;
    String teamRequired, team;
    String lootItem, lootArtifact, lootArtifactShard, lootHeroShard;
    int lootPromotionStone, lootMonsterSoul, lootMagicDust, lootChaosStone, lootQuestScroll, lootSeniorQuestScroll, numberBuyBonus, quickBonusFree;
    String eventDrops;
    Date quickBonus, timeBuyPack;
    Date lastTimeReward, lastTimeAttackWin;

    public UserCampaignEntity(UserEntity user) {
        this.userId = user.getId();
        this.serverId = user.getServer();
        this.level = 1;
        this.levelAuto = 1; // ko hieu de lam gi
        this.numberBuyBonus = 0;
        this.lastAward = System.currentTimeMillis();
        this.lastLoots = System.currentTimeMillis();
        this.eventDrops = "[]";
        this.lootItem = "[]";
        this.lootArtifact = "[]";
        this.lootArtifactShard = "[]";
        this.lootHeroShard = "[]";
        this.teamRequired = "[0,-1,-1,-1,-1,-1,-1,-1]";
        this.team = Constans.DEFAULT_TEAM;
        this.lastTimeReward = new Date();
        this.quickBonusFree = 1;
    }

    public void calculateReward(int vip, int serverId) {
        if (levelAuto == 0) return;
        levelAuto = Math.min(levelAuto, CfgLimitFunction.config.limitCampaignLevel);
        ResCampaignEntity campaign = ResCampaign.getCampaign(levelAuto, serverId);
        if (campaign == null)
            Logs.error(String.format("userId=%s Campaign calculateReward level=%s fail", userId, levelAuto));
        {
            long numberAward = (System.currentTimeMillis() - lastAward) / 5000;
            if (numberAward > 0) {
                gold += campaign.getGold() * numberAward;
                spirit += campaign.getSoul() * numberAward;
                lootPromotionStone += campaign.getLootPromoStone() * numberAward;
                exp += campaign.getExp() * numberAward;
                lastAward = lastAward + numberAward * 5000;
                ResCampaignEntity maxCampaign = ResCampaign.getCampaign(level, serverId);
                if (gold > maxCampaign.getMaxGold(vip)) gold = maxCampaign.getMaxGold(vip);
                if (spirit > maxCampaign.getMaxSoul(vip)) spirit = maxCampaign.getMaxSoul(vip);
                if (exp > maxCampaign.getMaxExp(vip)) exp = maxCampaign.getMaxExp(vip);
                if (lootPromotionStone > maxCampaign.getMaxPromotionStone(vip))
                    lootPromotionStone = maxCampaign.getMaxPromotionStone(vip);
                updateAwards();
            }
        }
        {
            boolean hasAward = false;
            int numberAward = (int) ((System.currentTimeMillis() - lastLoots) / DateTime.MIN_MILLI_SECOND / 10);
            int numberLoop = numberAward > 60 ? 60 : numberAward; // 1h 6 lần, 60 -> max 10 tiếng

            for (int i = 0; i < numberLoop; i++) {
                if (!hasAward) hasAward = campaign.checkLoots(this);
                else campaign.checkLoots(this);
            }

            if (numberLoop > 0) {
                if (!hasAward) hasAward = CfgDropItem.checkEventLoots(this, numberLoop);
                else CfgDropItem.checkEventLoots(this, numberLoop);
            }

            if (hasAward) {
                lastLoots = lastLoots + numberAward * DateTime.MIN_MILLI_SECOND * 10;
                updateLoots();
            }
        }
    }

    public List<Long> calculateRewardByTime(MyUser mUser, int hour) {
        boolean in304Event = Guice.getInstance(TruongProEventService.class).inEvent304(mUser.getUser().getServer());
        UserEntity user = mUser.getUser();
        List<Long> aBonus = new ArrayList<>();
        int lvAuto = levelAuto == 0 ? 1 : levelAuto;
        ResCampaignEntity campaign = ResCampaign.getCampaign(lvAuto, user.getServer());
        {
            int vipPercent = CfgUser.getVipAutoCampaign(user.getVip());
            long numberAward = hour * DateTime.HOUR_MILLI_SECOND / 5000;
            long gold = campaign.getGold() * numberAward;
            long spirit = campaign.getSoul() * numberAward;
            long exp = campaign.getExp() * numberAward;
            long promotionStone = campaign.getLootPromoStone() * numberAward;

            gold += gold * vipPercent / 100;
            gold += in304Event ? gold / 2 : 0;
            spirit += spirit * vipPercent / 100;
            spirit += in304Event ? spirit / 2 : 0;

            aBonus.addAll(Bonus.view(Bonus.BONUS_GOLD, gold));
            aBonus.addAll(Bonus.viewMaterial(MaterialType.SPIRIT, spirit));
            aBonus.addAll(Bonus.viewMaterial(MaterialType.PROMOTION_STONE, promotionStone + promotionStone * vipPercent / 100));
            //            aBonus.addAll(Bonus.view(Bonus.BONUS_EXP, (exp + exp * vipPercent / 100)));
            aBonus.addAll(Bonus.view(Bonus.BONUS_EXP, exp));
//            aBonus.addAll(Guice.getInstance(CampaignService.class).eventDropItem(mUser, (int) numberAward));
        }
        {
            int numberLoop = hour * 6; // 1h 6 lần
            for (int i = 0; i < numberLoop; i++) {
                aBonus = Bonus.merge(aBonus, campaign.BonusByTime(this));
            }
            if (numberLoop > 0) {
                aBonus = Bonus.merge(aBonus, campaign.bonusEventDrop(this, numberLoop));
            }
        }
        return aBonus;
    }


    public List<Long> getLoots(int vip) {
        int vipPercent = CfgUser.getVipAutoCampaign(vip);
        List<Long> aLong = new ArrayList<>();
        if (lootItem.length() > 2) {
            Map<Integer, Integer> mItem = getMapItem(GsonUtil.parseJsonArray(lootItem));
            mItem.forEach((itemId, number) -> aLong.addAll(Bonus.view(Bonus.BONUS_ITEM, itemId, number)));
        }
        if (lootArtifact.length() > 2) {
            Map<Integer, Integer> mArtifact = getMapItem(GsonUtil.parseJsonArray(lootArtifact));
            mArtifact.forEach((artifactId, number) -> aLong.addAll(Bonus.view(Bonus.BONUS_ARTIFACT, artifactId, 1, number)));
        }
        if (lootArtifactShard.length() > 2) {
            Map<Integer, Integer> mArtifactShard = getMapItem(GsonUtil.parseJsonArray(lootArtifactShard));
            mArtifactShard.forEach((artifactShardId, number) -> aLong.addAll(Bonus.viewMaterial(MaterialType.getArtifactShard(artifactShardId), number)));
        }
        if (lootHeroShard.length() > 2) {
            Map<Integer, Integer> mHeroShard = getMapItem(GsonUtil.parseJsonArray(lootHeroShard));
            mHeroShard.forEach((heroShardId, number) -> {
                MaterialType materialType = MaterialType.getHeroShard(heroShardId);
                if (materialType == null) {
                    Telegram.sendNotify("Error Campaign loot heroShardId = " + heroShardId);
                } else {
                    aLong.addAll(Bonus.viewMaterial(materialType, number));
                }
            });
        }
        if (lootPromotionStone > 0) {
            aLong.addAll(Bonus.viewMaterial(MaterialType.PROMOTION_STONE, lootPromotionStone + lootPromotionStone * vipPercent / 100));
        }
        if (lootMonsterSoul > 0)
            aLong.addAll(Bonus.viewMaterial(MaterialType.MONSTER_SOUL, lootMonsterSoul));
        if (lootMagicDust > 0)
            aLong.addAll(Bonus.viewMaterial(MaterialType.MAGIC_DUST, lootMagicDust));
        if (lootChaosStone > 0)
            aLong.addAll(Bonus.viewMaterial(MaterialType.CHAOS_STONE, lootChaosStone));
        if (lootQuestScroll > 0)
            aLong.addAll(Bonus.viewMaterial(MaterialType.QUEST_SCROLL, lootQuestScroll));
        if (lootSeniorQuestScroll > 0)
            aLong.addAll(Bonus.viewMaterial(MaterialType.SENIOR_QUEST_SCROLL, lootSeniorQuestScroll));

        // Event drop
        JsonArray arrEvent = GsonUtil.parseJsonArray(eventDrops);
        for (int i = 0; i < arrEvent.size(); i += 2) {
            aLong.addAll(CfgDropItem.getEventDrop(arrEvent.get(i).getAsInt(), arrEvent.get(i + 1).getAsInt()));
        }
        return aLong;
    }

    public Map<Integer, Integer> getMapItem(JsonArray arr) {
        Map<Integer, Integer> map = new HashMap<>();
        for (int i = 0; i < arr.size(); i++) {
            if (!map.containsKey(arr.get(i).getAsInt())) map.put(arr.get(i).getAsInt(), 0);
            map.put(arr.get(i).getAsInt(), map.get(arr.get(i).getAsInt()) + 1);
        }
        return map;
    }

    public boolean addLootItem(int itemId) {
        JsonArray arr = GsonUtil.parseJsonArray(lootItem);
        if (arr.size() < 50) {
            arr.add(itemId);
            lootItem = arr.toString();
            return true;
        }
        return false;
    }


    public boolean addLootArtifact(int artifactId) {
        JsonArray arr = GsonUtil.parseJsonArray(lootArtifact);
        if (arr.size() < 50) {
            arr.add(artifactId);
            lootArtifact = arr.toString();
            return true;
        }
        return false;
    }

    public boolean addLootArtifactShard(int artifactId) {
        JsonArray arr = GsonUtil.parseJsonArray(lootArtifactShard);
        if (arr.size() < 50) {
            arr.add(artifactId);
            lootArtifactShard = arr.toString();
            return true;
        }
        return false;
    }

    public boolean addLootHeroShard(int heroShard) {
        if (heroShard >= 30002 && heroShard <= 30004) {
            heroShard = NumberUtil.randomInList(List.of(30002, 30004));
        }
        JsonArray arr = GsonUtil.parseJsonArray(lootHeroShard);
        if (arr.size() < 50) {
            arr.add(heroShard);
            lootHeroShard = arr.toString();
            return true;
        }
        return false;
    }

    public boolean addLootQuestScroll() {
        if (lootQuestScroll < 50) {
            lootQuestScroll++;
            return true;
        }
        return false;
    }

    public boolean addLootSeniorQuestScroll() {
        if (lootSeniorQuestScroll < 50) {
            lootSeniorQuestScroll++;
            return true;
        }
        return false;
    }

    public boolean addLootPromotionStone() {
        if (lootPromotionStone < 200) {
            lootPromotionStone++;
            return true;
        }
        return false;
    }

    public boolean addLootMonsterSoul() {
        if (lootMonsterSoul < 200) {
            lootMonsterSoul += 3 + System.currentTimeMillis() % 2;
            if (lootMonsterSoul > 200) lootMonsterSoul = 200;
            return true;
        }
        return false;
    }

    public boolean addLootChaosStone() {
        if (lootChaosStone < 200) {
            lootChaosStone++;
            return true;
        }
        return false;
    }

    public boolean addLootMagicDust() {
        if (lootMagicDust < 200) {
            lootMagicDust += 5 + System.currentTimeMillis() % 2;
            if (lootMagicDust > 200) lootMagicDust = 200;
            return true;
        }
        return false;
    }


    /**
     * Thêm số lần đồ sự kiện rơi ra
     */
    public void addEventDrop(int eventId, int numberDrop) {
        JsonArray arr = GsonUtil.parseJsonArray(eventDrops);
        for (int i = 0; i < arr.size(); i += 2) {
            if (arr.get(i).getAsInt() == eventId) {
                arr.set(i + 1, new JsonPrimitive(arr.get(i + 1).getAsInt() + numberDrop));
                eventDrops = arr.toString();
                return;
            }
        }
        arr.add(eventId);
        arr.add(numberDrop);
        eventDrops = arr.toString();
    }

    public Pbmethod.ListCommonVector toProto(int vip, int serverId) {
        ResCampaignEntity resCampaignEntity = ResCampaign.getCampaign(levelAuto, serverId);
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            ResCampaignEntity campaignEntity = ResCampaign.getCampaign(level, serverId);
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            cmm.addALong(getLoots(vip).isEmpty() ? 0 : 1); // loot ?
            cmm.addALong(level).addALong(campaignEntity == null ? 1000 : campaignEntity.getRequireLevel()).addALong(levelAuto);
            cmm.addALong(gold).addALong(spirit).addALong(exp);
            cmm.addALong(resCampaignEntity == null ? 0 : gold * 5 / resCampaignEntity.getGold()).addALong(DateTime.HOUR_SECOND * 10 + CfgUser.getMaxAutoTime(vip) * DateTime.MIN_SECOND);
            cmm.addALong(CfgCampaign.isCampaign2(serverId) ? 1 : 0);
            builder.addAVector(cmm);
        }
        {
            JsonArray arr = GsonUtil.parseJsonArray(team);
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            for (int i = 0; i < arr.size(); i++) cmm.addALong(arr.get(i).getAsInt());
            builder.addAVector(cmm);
        }
        return builder.build();
    }

    public void updateAwards() {
        DBJPA.update("user_campaign", Arrays.asList("gold", gold, "exp", exp, "spirit", spirit, "last_award", lastAward), Arrays.asList("user_id", userId));
    }

    public boolean updateQuickBonus() {
        if (DBJPA.update("user_campaign", Arrays.asList("quick_bonus", DateTime.getFullDate(), "quick_bonus_free", quickBonusFree - 1), Arrays.asList("user_id", userId))) {
            quickBonus = new Date();
            quickBonusFree--;
            return true;
        }
        return false;
    }

    public boolean updateNumberBuyBonus() {
        if (DBJPA.update("user_campaign", Arrays.asList("number_buy_bonus", numberBuyBonus + 1), Arrays.asList("user_id", userId))) {
            numberBuyBonus++;
            return true;
        }
        return false;
    }

    public boolean resetQuickBonus(boolean hasPack) {
        int numBonus = hasPack ? 3 : 1;
        if (DBJPA.update("user_campaign", Arrays.asList("quick_bonus", DateTime.getFullDate(), "number_buy_bonus", 0, "quick_bonus_free", numBonus), Arrays.asList("user_id", userId))) {
            quickBonus = new Date();
            numberBuyBonus = 0;
            quickBonusFree = numBonus;
            return true;
        }
        return false;
    }

    public void updateLoots() {
        DBJPA.update("user_campaign", Arrays.asList("loot_item", lootItem, "loot_artifact", lootArtifact, "loot_artifact_shard", lootArtifactShard, "loot_hero_shard", lootHeroShard,
                "loot_promotion_stone", lootPromotionStone, "loot_monster_soul", lootMonsterSoul, "loot_magic_dust", lootMagicDust, "loot_chaos_stone", lootChaosStone,
                "loot_quest_scroll", lootQuestScroll, "loot_senior_quest_scroll", lootSeniorQuestScroll, "last_loots", lastLoots,
                "event_drops", eventDrops), Arrays.asList("user_id", userId));
    }

    public boolean buyPackQuickBonus() {
        if (DBJPA.update("user_campaign", Arrays.asList("quick_bonus_free", quickBonusFree + 2, "time_buy_pack", DateTime.getFullDate()), Arrays.asList("user_id", userId))) {
            this.timeBuyPack = Calendar.getInstance().getTime();
            this.quickBonusFree += 2;
            return true;
        }
        return false;
    }

    public boolean resetAwards() {
        if (DBJPA.update("user_campaign", Arrays.asList("gold", 0, "exp", 0, "spirit", 0, "last_time_reward", DateTime.getFullDate()), Arrays.asList("user_id", userId))) {
            gold = 0;
            exp = 0;
            spirit = 0;
            lastTimeReward = new Date();
            return true;
        }
        return false;
    }

    public boolean resetLoots() {
        if (DBJPA.update("user_campaign", Arrays.asList("loot_item", "[]", "loot_artifact", "[]", "loot_artifact_shard", "[]", "loot_hero_shard", "[]",
                "loot_promotion_stone", 0, "loot_monster_soul", 0, "loot_magic_dust", 0, "loot_chaos_stone", 0,
                "loot_quest_scroll", 0, "loot_senior_quest_scroll", 0, "event_drops", "[]"), Arrays.asList("user_id", userId))) {
            lootItem = "[]";
            lootArtifact = "[]";
            lootArtifactShard = "[]";
            lootHeroShard = "[]";
            lootPromotionStone = 0;
            lootMonsterSoul = 0;
            lootMagicDust = 0;
            lootChaosStone = 0;
            lootQuestScroll = 0;
            lootSeniorQuestScroll = 0;
            eventDrops = "[]";
            return true;
        }
        return false;
    }

    public boolean update(Object... updateData) {
        return update(Arrays.stream(updateData).toList());
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_campaign", updateData, Arrays.asList("user_id", userId));
    }
}
