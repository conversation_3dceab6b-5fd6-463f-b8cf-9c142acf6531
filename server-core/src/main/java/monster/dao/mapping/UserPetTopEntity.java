package monster.dao.mapping;

import grep.database.DBJPA;
import grep.helper.LogicUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgPet;
import monster.dao.mapping.main.ResPetEntity;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Arrays;

@Entity
@Table(name = "user_pet_top")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserPetTopEntity implements java.io.Serializable {

    @Id
    private int userId, petId, top;
    @Builder.Default
    private int level = 1, tier = 1;
    @Builder.Default
    private int passive1 = 1, passive2 = 0, passive3 = 0, passive4 = 0;
    @Builder.Default
    private int rune1 = 0, rune2 = 0, rune3 = 0;

    public UserPetTopEntity(UserPetEntity uPet) {
        this.userId = uPet.getUserId();
        this.petId = uPet.getPetId();
        this.level = uPet.getLevel();
        this.tier = uPet.getTier();
        this.passive1 = uPet.getPassive1();
        this.passive2 = uPet.getPassive2();
        this.passive3 = uPet.getPassive3();
        this.passive4 = uPet.getPassive4();
        this.rune1 = uPet.getRune1();
        this.rune2 = uPet.getRune2();
        this.rune3 = uPet.getRune3();
    }

    public UserPetTopEntity(UserPetTopEntity petTop, int top) {
        this.userId = petTop.getUserId();
        this.petId = petTop.getPetId();
        this.top = top;
        this.level = petTop.getLevel();
        this.tier = petTop.getTier();
        this.passive1 = petTop.getPassive1();
        this.passive2 = petTop.getPassive2();
        this.passive3 = petTop.getPassive3();
        this.passive4 = petTop.getPassive4();
        this.rune1 = petTop.getRune1();
        this.rune2 = petTop.getRune2();
        this.rune3 = petTop.getRune3();
    }

    public boolean isMaxAllRune() {
        return LogicUtil.getMinInt(rune1, rune2, rune3) >= CfgPet.maxRune;
    }

    public boolean isMaxRuneLevel(int runeIndex) {
        return Arrays.asList(rune1, rune2, rune3).get(runeIndex) >= CfgPet.maxRune;
    }

    public boolean isMaxPassive() {
        return passive1 == CfgPet.maxPassive && passive2 == CfgPet.maxPassive && passive3 == CfgPet.maxPassive && passive4 == CfgPet.maxPassive;
    }

    public int getPassive(int index) {
        switch (index) {
            case 1:
                return passive1;
            case 2:
                return passive2;
            case 3:
                return passive3;
            case 4:
                return passive4;
        }
        return 0;
    }

    public int getRune(int index) {
        return Arrays.asList(rune1, rune2, rune3).get(index);
    }

    public Pbmethod.CommonVector.Builder toProto() {
        return Pbmethod.CommonVector.newBuilder().addALong(petId).addALong(level).addALong(tier)
                .addALong(passive1).addALong(passive2).addALong(passive3).addALong(passive4)
                .addALong(rune1).addALong(rune2).addALong(rune3);
    }


    public HeroBattleEntity toHeroBattle(int team) {
        HeroBattleEntity heroBattle = new HeroBattleEntity();
        heroBattle.heroId = petId;
        heroBattle.position = 6;
        heroBattle.team = team;
        heroBattle.tier = tier;
        ResPetEntity resPet = ResMonster.getPet(petId);
        heroBattle.point = resPet.getPoint(passive1, passive2, passive3, passive4);
        if (isMaxPassive()) {
            heroBattle.point.setCurrentValue(Point.FIX_HP, resPet.getScaleHp(rune1));
            heroBattle.point.setCurrentValue(Point.FIX_ATTACK, resPet.getScaleAttack(rune2));
            heroBattle.point.setCurrentValue(Point.FIX_SPEED, resPet.getScaleSpeed(rune3));
        }
        heroBattle.activeSkill = ResHero.getSkill(resPet.getActiveSkill() + level - 1);
        return heroBattle;
    }

    public long getPower() {
//        long powerPet = level * 3000 + (passive1 + passive2 + passive3 + passive4) * 2286 + rune1 + rune2 * 6 + rune3;
        return IMath.getPetPower(this);
    }
}