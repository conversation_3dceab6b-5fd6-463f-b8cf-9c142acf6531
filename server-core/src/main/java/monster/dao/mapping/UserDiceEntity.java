package monster.dao.mapping;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgDice;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.object.MyUser;
import monster.service.user.Actions;
import monster.task.dbcache.MailCreatorCache;
import monster.util.DBHelper;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serializable;
import java.util.*;

@Entity
@Table(name = "user_dice")
@Data
@NoArgsConstructor
public class UserDiceEntity implements Serializable {
    @Id
    int userId, eventId; //user id
    int serverId;
    int position; // vị trí
    //    int numberDice; // số xx thường
//    int numberLuckyDice; // số xx lucky
    int numberStar; // số sao
    int lastStarPosition; // vị trí nhận sao trước đó
    int specialEffect; // Hiệu ứng đb
    int numberDiceBought; // Số xx thường đã mua
    String level; // Level của toàn map
    Date lastDateFreeDice; //thời gian nhận free gần nhất
    String lastSeasonReceived; // đã nhận các mốc nào
    int lapCount; // số lần đạt max phần thưởng
    int point;

    public UserDiceEntity(int userId, int serverId, int eventId) {
        this.userId = userId;
        this.eventId = eventId;
        this.serverId = serverId;
        this.position = 0;
//        this.numberDice = 5;
//        this.numberLuckyDice = 0;
        this.numberStar = 0;
        this.numberDiceBought = 0;
        this.level = CfgDice.newLevelDice.toString().replace(" ", "");
//        this.level = newLevelDice.toString().replace(" ", "");
        this.lastStarPosition = 0;
        this.lastSeasonReceived = CfgDice.newReceived.toString().replace(" ", "");
        this.lapCount = 1;
        this.point = 0;
    }

    public List<Long> getInfo(MyUser mUser) {
        List<Long> aLong = new ArrayList<>();
//        aLong.add((long) Math.min(lapCount,CfgDice.config.getMaxLap()));
        aLong.add((long) lapCount);
        aLong.add((long) CfgDice.config.getMaxLap());
        aLong.add((long) position);
        aLong.add(mUser.getResources().getMaterial(MaterialType.DICE).getNumber());
        aLong.add(mUser.getResources().getMaterial(MaterialType.DICE_LUCKY).getNumber());
//        aLong.add(numberDice);
//        aLong.add(numberLuckyDice);
        aLong.add((long) numberStar);
        aLong.add((long) specialEffect);
        aLong.add(getNumberBuyLeft());
        return aLong;
    }


    /**
     * @return data của toàn bộ map List(22)<List(3)<Long>> 22 vị trí, 3 level
     */
    public List<List<Long>> getCurData() {
        List<List<Long>> status = new ArrayList<>();
        List<Integer> listLevel = getListLevel();
        for (int i = 1; i <= listLevel.size(); i++) {
//            System.out.println("position: " + i + " - level: " + listLevel.get(i - 1));
//            System.out.println(CfgDice.getStatus(i, listLevel.get(i - 1)));

            for (int level = 1; level <= 3; level++) {
                status.add(CfgDice.getData(i, level));
            }
        }

        return status;
    }

    /**
     * @param newPosition vị trí mới
     * @param newLevel    level mới
     * @return data mới
     */
    public List<Long> getNewData(int newPosition, int newLevel) {
        return CfgDice.getData(newPosition, newLevel);
    }

    /**
     * @param score điểm xúc xắc sau khi đã tính toán
     * @return newPosition vị trí mới
     */
    public int getNewPosition(int score) {
        if (position + score > CfgDice.config.getNumberStep())
            return position + score - CfgDice.config.getNumberStep(); //qua vòng mới
        if (position + score < 1) return position + score + CfgDice.config.getNumberStep(); //lùi về qua vị trí đầu tiên
        return position + score;
    }

    /**
     * @param position vị trí
     * @param numLevel số lượng level thay đổi
     * @return level mới
     */
    public int getNewLevel(int position, int numLevel) {
        return Math.max(1, Math.min(getLevelInt(position) + numLevel, 3)); //chặn level
    }

    /**
     * Get level mới của toàn map
     *
     * @param position vị trí
     * @param newLevel level mới
     * @return level(string) mới của toàn map
     */
    public String getNewLevelStr(int position, int newLevel) {
        List<Integer> aInt = getListLevel();
        aInt.set(position - 1, Math.max(1, Math.min(newLevel, 3)));

        return aInt.toString().replace(" ", "");
    }

    /**
     * @param newPosition vị trí mới
     * @param newLevel    level mới
     */
    public void updateUDice(int newPosition, String newLevel, int numberStar) {
        this.position = newPosition;
        this.level = newLevel;
        this.numberStar = numberStar;
    }

    /**
     * String -> List
     *
     * @return
     */
    public List<Integer> getListLevel() {
        return GsonUtil.strToListInt(level);
    }

    /**
     * Lấy level hiện tại theo vị trí
     *
     * @param position vị trí
     * @return level
     */
    public int getLevelInt(int position) {
        return getListLevel().get(position - 1);
    }

    /**
     * @param newPosition vị trí mới
     * @param isGoBack    có phải đi lùi hay ko
     * @return List<bonusStar> đi qua bao nhiêu sao thì có bấy nhiêu phần tử.
     * Trả về empty nếu đi lùi hoặc ko đi qua vị trí nhận sao. Trả về null khi lỗi ko update đưuọc db
     */
    public List<Long> getBonusStar(int newPosition, boolean isGoBack, UserEntity user) {
        List<Long> aLong = new ArrayList<>();
        Long numBonus = 0L;
        int startPosition = lastStarPosition;

        if (isGoBack) { //Đi lùi
            return aLong;
        }

        //Nếu đi qua vị trí cuối và sang vòng mới thì chạy 2 vòng lặp. Nếu trong quãng đường đó gặp vị trí tặng sao thì add vào list và set vị trí nhận sao.
        if (newPosition < position) {
            for (int i = lastStarPosition + 1; i <= CfgDice.config.getNumberStep(); i++) { //Lặp từ vị trí nhận sao trước đó đến vị trí cuối của map
                if (CfgDice.isStar(i)) {
                    List<Long> aListLong = new ArrayList<>(CfgDice.getData(i, getLevelInt(i)));
                    numBonus += aListLong.get(aListLong.size() - 1);
                    this.lastStarPosition = i;
                }
            }

            //Qua vòng mới nên set vị trí nhận sao trước đó = 0.
            lastStarPosition = 0;

            for (int i = 1; i <= newPosition; i++) { //Lặp từ vị trí đầu đến vị trí mới.
                if (CfgDice.isStar(i)) {
                    List<Long> aListLong = new ArrayList<>(CfgDice.getData(i, getLevelInt(i)));
                    aListLong.remove(1);
                    numBonus += aListLong.get(aListLong.size() - 1);
                    this.lastStarPosition = i;
                }
            }

            if (!DBJPA.update("user_dice", Arrays.asList("last_star_position", lastStarPosition, "number_star", numberStar + numBonus), Arrays.asList("user_id", userId))) {
                this.lastStarPosition = startPosition;
                return null;
            }

            this.numberStar += numBonus;
            aLong.addAll(Arrays.asList(1L, -100L, numBonus, (long) numberStar));

            return aLong;
        }

        //Chưa qua vòng mới. Lặp từ vị trí nhận sao trước đó + 1 đến vị trí mới. Nếu trong quãng đường đó gặp vị trí tặng sao thì add vào list và set vị trí nhận sao.
        numBonus = 0L;
        for (int i = lastStarPosition + 1; i <= newPosition; i++) {
            if (CfgDice.isStar(i)) {
                List<Long> aListLong = new ArrayList<>(CfgDice.getData(i, getLevelInt(i)));
                numBonus += aListLong.get(aListLong.size() - 1);
                this.lastStarPosition = i;
            }
        }

        if (!DBJPA.update("user_dice", Arrays.asList("last_star_position", lastStarPosition, "number_star", numberStar + numBonus), Arrays.asList("user_id", userId))) {
            this.lastStarPosition = startPosition;
            return null;
        }

        this.numberStar += numBonus;
        aLong.addAll(Arrays.asList(1L, -100L, numBonus, (long) numberStar));

        return aLong;
    }

    /**
     * @return số xx thường có thể mua
     */
    public long getNumberBuyLeft() {
        return CfgDice.config.getMaxNumberBuy() - numberDiceBought;
    }

    /**
     * @return có được nhận free xx hay ko???
     */
    public boolean isFreeDice() {
        return getLastDateFreeDice() == null || DateTime.getDayDiff(getLastDateFreeDice(), new Date()) >= 1;
    }

    /**
     * @return
     */
    public List<Integer> getReceived() {
        return GsonUtil.strToListInt(lastSeasonReceived);
    }

    public String getNewReceived(List<Long> aIndex) {
        List<Integer> aInt = getReceived();
        for (int i = 0; i < aIndex.size(); i++) {
            aInt.set(aIndex.get(i).intValue(), 1);
        }

        return aInt.toString().replace(" ", "");
    }

    /**
     * Lấy bonus thưởng khi đạt mốc star
     * Lặp từ index cuối (mốc star cao nhất).
     * Nếu sao của người chơi lớn hơn mốc star(i) và mốc star(i) lớn hơn mốc star đã nhận (lastIndexReward) thì add bonus và update mốc star đã nhận
     *
     * @return received bonus
     */
    public List<List<Long>> getStarReward() {
        List<List<Long>> aLLong = new ArrayList<>();
        List<Long> aLong1 = new ArrayList<>();
        List<Long> aLong2 = new ArrayList<>();
        for (int i = 0; i < getReceived().size(); i++) {
            if (getReceived().get(i) == 0 && numberStar >= CfgDice.config.getReward().getStar().get(i)) {
                aLong1.add((long) i);
                aLong2.addAll(CfgDice.config.getReward().getBonus().get(i));
            }
        }
//        for (int i = CfgDice.config.reward.getStar().size() - 1; i > 0; i--) {
//            if (this.numberStar > CfgDice.config.reward.getStar().get(i) && CfgDice.config.reward.getStar().get(i) > this.lastSeasonReceived) {
//                this.lastSeasonReceived = CfgDice.config.reward.getStar().get(i);
//                aLong.addAll(CfgDice.config.reward.getBonus().get(i));
//                return aLong;
//            }
//        }
        aLLong.add(aLong1);
        aLLong.add(aLong2);
        return aLLong;
    }

    public int getStarRewardStatus(int rewardIndex) {
        if (getReceived().get(rewardIndex) != 0) return 2;
        return numberStar >= CfgDice.config.getReward().getStar().get(rewardIndex) ? 1 : 0;
    }


    public boolean isFinishRound() {
        return numberStar >= CfgDice.config.getReward().getStar().get(CfgDice.config.getReward().getStar().size() - 1);
    }

//    public void sendReward(AHandler handler) {
//        List<List<Long>> starReward = getStarReward();
//        List<Long> aIndex = starReward.get(0);
//        List<Long> aBonus = starReward.get(1);
//        if (!aIndex.isEmpty() && !aBonus.isEmpty()) {
//            String newReceived = isFinishRound() ? CfgDice.newReceived.toString().replace(" ", "") : getNewReceived(aIndex);
//            int newNumberStar = isFinishRound() ? this.numberStar - CfgDice.config.getReward().getStar().get(CfgDice.config.getReward().getStar().size() - 1) : this.numberStar;
//            int newLapCount = isFinishRound() ? this.lapCount + 1 : this.lapCount;
//
//            if (!aBonus.isEmpty()) {
//                if (!DBJPA.update("user_dice", Arrays.asList("last_season_received", newReceived, "number_star", newNumberStar, "lap_count", newLapCount), Arrays.asList("user_id", userId))) {
//                    handler.addErrResponse();
//                    return;
//                }
//
//                this.lastSeasonReceived = newReceived;
//                this.numberStar = newNumberStar;
//                this.lapCount = newLapCount;
//
//                Actions.save(handler.getUser(), "dice", "mail_season", "bonus", new Gson().toJson(aBonus));
//                String title = Lang.getTitle("title_boss_server_reward");
////                DBJPA.rawSQL(DBHelper.sqlMail(userId, title, aBonus.toString()));
//                MailCreatorCache.sendMail(UserMailEntity.builder().userId(userId)
//                        .title(title).bonus(aBonus.toString()).origin("dice")
//                        .build());
//            }
//        }
//    }

    /**
     * @return
     */
    public boolean isAllMaxLevel() {
        for (int i = 0; i < getListLevel().size(); i++) {
            if (CfgDice.isTrap(i + 1)) continue;
            if (getListLevel().get(i) < CfgDice.getMaxLevel()) return false;
        }

        return true;
    }

    public boolean isAllLevel1() {
        for (int i = 0; i < getListLevel().size(); i++) {
            if (CfgDice.isTrap(i + 1)) continue;
            if (getListLevel().get(i) > 1) return false;
        }

        return true;
    }
}
