package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "user_battle_group")
@Data
@NoArgsConstructor
public class UserBattleGroupEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;
    int groupId, numberAttack;
    boolean isDone;
    Date createTime;

    public UserBattleGroupEntity(int groupId, int numberAttack) {
        this.groupId = groupId;
        this.numberAttack = numberAttack;
        this.isDone = false;
        this.createTime = new Date();
    }
}
