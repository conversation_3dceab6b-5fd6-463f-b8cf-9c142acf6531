package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@NoArgsConstructor
@Table(name = "config_hero_release")
public class ConfigHeroReleaseEntity {
    @Id
    int heroId;
    int summonShenron, summonPorunga, wishPorunga, extremeRelease;
    int snakeWay, peakOfTime, release;
    int dayOpenServer;
    String serverIds;

    public boolean isRelease() {
        return release == 1;
    }
}
