package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Entity
@Table(name = "battle_fake_group")
@Data
@NoArgsConstructor
public class BattleFakeGroupEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    String users, desc;

    public BattleFakeGroupEntity(String users, String desc){
        this.users = users;
        this.desc = desc;
    }
}
