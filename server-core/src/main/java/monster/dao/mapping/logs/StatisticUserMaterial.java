package monster.dao.mapping.logs;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Data
@Entity
@Table(catalog = "dson_log")
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class StatisticUserMaterial {
    @Id
    long logId;
    long gold, gem, ruby;
    String material;
    String heroStar5Faction;
}
