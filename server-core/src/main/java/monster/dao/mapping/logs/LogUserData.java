package monster.dao.mapping.logs;


import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@NoArgsConstructor
public class LogUserData implements Serializable {
    @Id
    int userId;
    int level;
    int campaign;
    int ruby, vip;
    long gem, gold;
    String name;
    int server;
    long power;
    Date dateCreated;
}
