package monster.dao.mapping.logs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Entity
@Table(name = "dson_log.hero_test_log_control_detail")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HeroTestLogControlDetail implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long logId;
    private long battleId;
    private int win, round;
    private long control;

}
