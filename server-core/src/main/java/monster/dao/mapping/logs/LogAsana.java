package monster.dao.mapping.logs;

import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "log_asana", schema = "dson_log", catalog = "dson_log")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogAsana {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "assignee", length = 512)
    private String assignee;

    @Column(name = "name", length = 512)
    private String name;

    @Lob
    @Column(name = "notes", nullable = false)
    private String notes;

    @Column(name = "notify", nullable = false)
    @Builder.Default
    private Boolean notify = false;

    @Column(name = "date_created", nullable = false)
    @Builder.Default
    private Date dateCreated = new Date();

}