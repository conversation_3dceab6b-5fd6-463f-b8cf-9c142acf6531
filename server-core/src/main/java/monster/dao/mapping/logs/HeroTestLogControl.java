package monster.dao.mapping.logs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Table(name = "dson_log.hero_test_log_control")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HeroTestLogControl implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private String modeKey, team;
    int numberBattle, userId;
    int bestControl, minControl, averageControl;

}
