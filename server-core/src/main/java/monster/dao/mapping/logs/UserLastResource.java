package monster.dao.mapping.logs;

import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Builder;
import lombok.Data;
import monster.cache.redis.JCache;
import monster.dao.mapping.UserEntity;

@Builder
@Data
public class UserLastResource implements java.io.Serializable {

    public static final int CHECK_HERO = 1, CHECK_RUBY_GEM = 2;
    public static final int UPDATE = 3;

    public int userId;
    public int numberHero;
    public long ruby, gold, gem;

    public boolean needUpdate(UserEntity user, int numberHero) {
        return this.numberHero != numberHero || user.getRuby() != ruby || user.getGold() != gold || user.getGem() != gem;
    }

    public static UserLastResource getObject(UserEntity user) {
        String value = JCache.getInstance().getValue(getKey(user.getId()));
        if (StringHelper.isEmpty(value)) {
            UserLastResource lastResource = UserLastResource.builder().gem(user.getGem()).gold(user.getGold()).ruby(user.getRuby())
                    .numberHero(0).userId(user.getId()).build();
            JCache.getInstance().setValue(getKey(user.getId()), GsonUtil.toJson(lastResource), JCache.EXPIRE_1D * 7);
            return lastResource;
        }
        return GsonUtil.parse(UserLastResource.class, value);
    }

    public void updateCache() {
        JCache.getInstance().setValue(getKey(userId), GsonUtil.toJson(this), JCache.EXPIRE_1D * 7);
    }

    public static String getKey(int userId) {
        return "test1_last_resource_" + userId;
    }

    @Override
    public String toString(){
        return "userId = " + userId + ", numberHero = " + numberHero + ", ruby = " + ruby + ", gold = " + gold + ", gem = " + gem;
    }
}
