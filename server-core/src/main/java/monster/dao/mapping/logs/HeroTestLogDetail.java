package monster.dao.mapping.logs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Entity
@Table(name = "dson_log.hero_test_log_detail")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HeroTestLogDetail implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private long logId;
    private long battleId, totalDamage;
    private int win, round;
    private String heroNormalDamage, heroDotDamage, heroBestDamage;


}
