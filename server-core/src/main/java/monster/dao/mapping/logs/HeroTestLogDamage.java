package monster.dao.mapping.logs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Entity
@Table(name = "dson_log.hero_test_log_dmg")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HeroTestLogDamage implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private String modeKey, team;
    int numberBattle, userId;
    long bestTotalDamage, minTotalDamage, averageTotalDamage;

}
