package monster.dao.mapping.logs;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Data
@Entity
@Table(catalog = "dson_log")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StatisticUserPoint {
    @Id
    public long logId;
    public int skillPoint1, skillPoint2;
    public int petId, petLv;
    public int petPassive1, petPassive2, petPassive3, petPassive4;
    int rune1, rune2, rune3;
}
