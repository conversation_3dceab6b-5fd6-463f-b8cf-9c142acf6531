package monster.dao.mapping;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import monster.object.ChatObject;
import protocol.Pbmethod;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "user_admin_chat")
@Builder
@Data
@AllArgsConstructor
public class UserAdminChatEntity implements java.io.Serializable {

    @Id
    private int id;
    private int userId, serverId;
    private String message = "";
    private boolean isAdmin, isRead;
    private Date dateCreated = new Date();

    public UserAdminChatEntity() {
        isAdmin = System.currentTimeMillis() % 2 == 1 ? true : false;
    }

    public protocol.Pbmethod.ChatMsg.Builder toProto(UserEntity user) {
        protocol.Pbmethod.ChatMsg.Builder tmp = protocol.Pbmethod.ChatMsg.newBuilder();
        tmp.setMsgId(id);
        tmp.setReqTime(dateCreated.getTime());
        tmp.setType(ChatObject.MSG);
        tmp.setMessage(message);
//        if (isAdmin()) {
        protocol.Pbmethod.PbUser.Builder pbUser = protocol.Pbmethod.PbUser.newBuilder();
        pbUser.setName("<Color=red>Admin</Color>").setId(0).setLevel(-1)
                .addAvatar(0).addAvatar(161)
                .setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(47).build());
        tmp.setUser(pbUser);
//        } else {
//            protocol.Pbmethod.PbUser.Builder pbUser = protocol.Pbmethod.PbUser.newBuilder();
//            pbUser.setName(user.getName()).setId(userId).addAvatar(user.getAvatarType()).addAvatar(user.getAvatar()).setLevel(user.getLevel());
//            ClanEntity clan = ClanMonitor.getClan(user.getClan());
//            if (clan != null)
//                pbUser.setClanInfo(protocol.Pbmethod.CommonVector.newBuilder().addALong(clan.getId()).addALong(0).addALong(clan.getMember())
//                        .addAString(clan.getName()).build());
//            tmp.setUser(pbUser);
//        }
        return tmp;
    }
}
