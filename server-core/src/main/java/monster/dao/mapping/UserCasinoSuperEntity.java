package monster.dao.mapping;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.DateTime;
import lombok.Data;
import monster.config.CfgCasino;
import net.sf.json.JSONArray;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.*;

@Entity
@Table(name = "user_casino_super")
@Data
public class UserCasinoSuperEntity implements Serializable {
    @Id
    int userId;
    long casinoRefresh;
    String casinoBonus, casinoEnable;
    long timeFreeRefresh;

    public UserCasinoSuperEntity() {
    }

    public UserCasinoSuperEntity(int userId) {
        this.userId = userId;
    }

    public boolean checkRefresh() {
        boolean hasRefresh = false;
        long curDay = new Long(DateTime.getDateyyyyMMdd(new Date()));
        if (casinoRefresh < curDay || casinoRefresh > curDay) { // force refresh
            forceRefreshCasino();
            hasRefresh = true;
        }
        if (hasRefresh) {
            update();
        }

        return hasRefresh;
    }

    public boolean checkFreeRefresh() {
        boolean checkFreeRefresh = false;
        if ((System.currentTimeMillis() - timeFreeRefresh) >= (CfgCasino.config.timeFreeRefresh * 3600000)) {
            checkFreeRefresh = true;
        }
        return checkFreeRefresh;
    }

    //region Casino
    public List<Long> getListCasinoBonus() {
        List<Long> aLong = new ArrayList<>();
        JSONArray arr = JSONArray.fromObject(casinoBonus);
        for (int i = 0; i < arr.size(); i++) {
            JSONArray tmp = arr.getJSONArray(i);
            for (int j = 0; j < tmp.size(); j++) aLong.add(tmp.getLong(j));
        }
        return aLong;
    }

    public List<Long> getListCasinoEnable() {
        return new Gson().fromJson(casinoEnable, new TypeToken<List<Long>>() {
        }.getType());
    }

    public long casinoCountdown() {
//        long timeRemain = DateTime.DAY_SECOND - (System.currentTimeMillis() - casinoRefresh) / 1000;
//        return timeRemain < 0 ? 0 : timeRemain;
        return DateTime.secondsUntilEndDay();
    }

    public void forceRefreshCasino() {
        this.casinoEnable = "[1,1,1,1,1,1,1,1]";
        casinoRefresh = Long.parseLong(DateTime.getDateyyyyMMdd(new Date()));
        this.casinoBonus = "[]";
    }

    public void refreshCasino() {
        this.casinoEnable = "[1,1,1,1,1,1,1,1]";
    }

    //endregion

    public boolean updateCasinoEnable() {
        return DBJPA.update("user_casino_super", Arrays.asList("casino_enable", casinoEnable), Arrays.asList("user_id", userId));
    }

    public boolean update() {
        return DBJPA.update(this);
    }
}
