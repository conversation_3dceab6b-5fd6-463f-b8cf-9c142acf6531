package monster.dao.mapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "user_interior_wishlist")
@Data
@NoArgsConstructor
public class UserInteriorWishlistEntity {
    @Id
    int userId;
    String wishlist;

    public UserInteriorWishlistEntity(int userId) {
        this.userId = userId;
        wishlist = "[]";
    }
}
