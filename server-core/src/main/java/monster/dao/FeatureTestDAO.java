package monster.dao;

import monster.dao.mapping.logs.FeatureTest;

import java.util.List;

@SuppressWarnings("unchecked")
public class FeatureTestDAO extends AbstractDAO {

    public List<FeatureTest> getListFeatureTest() {
        return doQuery(em -> em.createQuery("select c from FeatureTest c where c.testStatus<2", FeatureTest.class).getResultList());
    }

    public void cleanInterruptTask(long id) {
        doUpdate(em -> em.createQuery("delete from FeatureTestLog where logId=:logId").setParameter("logId", id).executeUpdate());
    }
}
