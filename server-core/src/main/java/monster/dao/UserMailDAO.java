package monster.dao;

import grep.helper.GUtil;
import grep.log.Logs;
import jakarta.persistence.EntityManager;

import java.util.List;

public class UserMailDAO extends AbstractDAO {

    public boolean hasMail(int userId) {
        EntityManager session = null;
        try {
            session = getEntityManager();
            String sql = "select 1 from user_mail where user_id=%s and receive=0 and (available_time is null or available_time<now()) limit 1";
            List list = session.createNativeQuery(String.format(sql, userId)).getResultList();
            return !list.isEmpty();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

}
