package monster.util;

import monster.config.lang.Lang;
import monster.object.MyUser;

public class TextGameFormat {

    public static String getHeroStar(MyUser mUser, int star) {
        if (star >= 6 && star <= 9) {
            return String.format("%s %s(%s)", star, mUser.getLang().get(Lang.text_star), String.format("<color='#a714e6'>%s</color>", "★".repeat(star - 5)));
        } else if (star == 10) {
            return String.format("%s %s(%s)", star, mUser.getLang().get(Lang.text_star), String.format("<color='#e95b07'>%s</color>", "★".repeat(star - 9)));
        } else if (star > 10) {
            return String.format("%s %s", star, mUser.getLang().get(Lang.text_star));
        }
        return String.format("%s %s(%s)", star, mUser.getLang().get(Lang.text_star), String.format("<color='#ff8f0d'>%s</color>", "★".repeat(star)));
    }

}
