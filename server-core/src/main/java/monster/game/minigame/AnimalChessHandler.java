package monster.game.minigame;

import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.dao.mapping.UserMaterialEntity;
import monster.game.truongevent.service.TruongProEventService;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.monitor.EventMonitor;
import monster.service.user.BonusBuilder;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;
import java.util.Map;

public class AnimalChessHandler extends AHandler {

    final String key_cache = "animal_chess";
    final String key_cache_start = "animal_chess_start";

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List.of(ANIMAL_CHESS_START, ANIMAL_CHESS_END_GAME).forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case ANIMAL_CHESS_START -> start();
                case ANIMAL_CHESS_END_GAME -> endgame(parseCommonInput().getALongList());
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void start() {
        UserMaterialEntity userMaterial = mUser.getResources().getMaterial(MaterialType.EVENT_ANIMAL_CHESS);
        if (userMaterial.getNumber() <= 0) {
            addErrResponse(Lang.err_not_enough_material);
            return;
        }
        Object obj = mUser.getCache().get(key_cache);
        if (obj != null && System.currentTimeMillis() - (long) obj < 3000) { // trong 3s gửi nhiều lần service
            addErrResponse("Chờ chút bạn nhé");
            return;
        }
        ServiceResult<List<Long>> serviceResult = Guice.getInstance(MaterialService.class).useMaterial(mUser, MaterialType.EVENT_ANIMAL_CHESS.id, 1, "animal_chess");
        if (serviceResult.success) {
            mUser.getCache().set(key_cache, System.currentTimeMillis());
            mUser.getCache().set(key_cache_start, Integer.valueOf(1));
            serviceResult.writeResponse(this);
            EventType.ANIMAL_CHESS_PLAY.addEvent(mUser);
        } else serviceResult.writeResponse(this);
    }

    void endgame(List<Long> values) {
        if (mUser.getCache().get(key_cache_start) == null) {
            addErrResponse("Bạn chưa bắt đầu game");
            return;
        }
        List<Long> eventValues = new ArrayList<>();
        List<EventType> eventTypes = List.of(
                EventType.ANIMAL_CHESS_CHUOT, EventType.ANIMAL_CHESS_MEO, EventType.ANIMAL_CHESS_CHO,
                EventType.ANIMAL_CHESS_CAO, EventType.ANIMAL_CHESS_SOI, EventType.ANIMAL_CHESS_HO, EventType.ANIMAL_CHESS_SUTU,
                EventType.ANIMAL_CHESS_VOI
        );
        int totalPoint = 0;
        for (int i = 0; i < values.size(); i++) {
            int index = values.get(i).intValue() - 1;
            totalPoint += values.get(i).intValue() * 10;
            eventValues.add((long) eventTypes.get(index).id);
            eventValues.add(1L);
        }
        if (totalPoint >= 360) totalPoint = 360;

        BonusBuilder bonusBuilder = BonusBuilder.newInstance();
        bonusBuilder.addMaterial(MaterialType.EVENT_ANIMAL_CHESS_MEAT, totalPoint);
        BitSet bitSet = Guice.getInstance(TruongProEventService.class).monthlyAnimalChessStatus(mUser.getUser());
        if (bitSet != null && bitSet.get(0)) {
            bonusBuilder.addMaterial(MaterialType.EVENT_ANIMAL_CHESS_MEAT, 50);
        }
        bonusBuilder.process(mUser, "animal_chess").writeResponse(this);
        EventMonitor.getInstance().addDropItem(user.getId(), eventValues);
        mUser.getCache().del(key_cache_start);
    }

}
