package monster.game.indenture;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.MaterialType;
import monster.config.penum.NotifyType;
import monster.controller.AHandler;
import monster.dao.HeroDAO;
import monster.dao.mapping.UserHeroEntity;
import monster.game.indenture.config.ConfigIndenture;
import monster.game.indenture.dao.IndentureDAO;
import monster.game.indenture.entity.IndentureInfo;
import monster.game.indenture.entity.ResIndentureSlot;
import monster.game.indenture.entity.UserIndentureEntity;
import monster.game.indenture.service.IndentureService;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.resource.ResIndentureLevelUp;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.BonusBuilder;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class IndentureHandler extends AHandler {

    //    public List<Long> saveStart = new ArrayList<>();
    IndentureDAO indentureDAO = Guice.getInstance(IndentureDAO.class);
    IndentureService indentureService = Guice.getInstance(IndentureService.class);
    HeroDAO heroDAO = Guice.getInstance(HeroDAO.class);
    IndentureInfo indentureInfo;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(INDENTURE_INFO, INDENTURE_OPEN, INDENTURE_INSTALLATION_HERO, INDENTURE_REMOVE_HERO, INDENTURE_SKIP_TIME, INDENTURE_LEVEL_UP, INDENTURE_INFO_SLOT_LOCKED);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        if (!FunctionType.INDENTURE.isEnable(mUser, this)) return;

        indentureInfo = mUser.getResources().getIndentureInfo();
        if (indentureInfo == null || indentureInfo.getSlots().isEmpty()) {
            addErrResponse();
            return;
        }
        if (indentureInfo.getTop6Ids().size() < 6) {
            addErrResponse("Thu thập đủ 6 tướng để tham gia tính năng này");
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case INDENTURE_INFO -> indentureInfo();
                case INDENTURE_OPEN -> indentureOpen(getInputInt() - 1);
                case INDENTURE_INSTALLATION_HERO -> indentureInstallation();
                case INDENTURE_REMOVE_HERO -> removeHero(getInputInt() - 1);
                case INDENTURE_SKIP_TIME -> skipTimeSlot();
                case INDENTURE_LEVEL_UP -> levelUp();
                case INDENTURE_INFO_SLOT_LOCKED -> infoLocked(getInputInt() - 1);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    /**
     * { vLong:
     * level tướng thấp nhất,
     * maxLevel,
     * canUpLevel,
     * [bonus] }
     * <p>
     * {vLong: id,isOpen, timeOpen, heroId} xn
     **/
    private void indentureInfo() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        List<UserHeroEntity> getTop6 = indentureService.top6HeroIndenture(mUser);
        if (getTop6.isEmpty()) {
            addErrResponse(getLang(Lang.err_system_down) + "(1)");
            return;
        }
        var listHeroE5 = indentureService.getAboveE5Hero(mUser);
        Pbmethod.CommonVector.Builder cm = Pbmethod.CommonVector.newBuilder();
        cm.addALong(indentureInfo.getLevel());
        cm.addALong(indentureInfo.getShowLevelMax());
        cm.addALong(indentureInfo.canLevelUp() ? 1 : 0);
        cm.addALong(ConfigIndenture.config.timeOpen);
        cm.addALong(listHeroE5.size());
        cm.addAllALong(listHeroE5);
        cm.addAllALong(ConfigIndenture.config.feeSkipTime);

        builder.addAVector(cm);
        { // vector 2
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            for (UserHeroEntity userHeroEntity : getTop6) {
                cmm.addALong(userHeroEntity.getId());
            }
            builder.addAVector(cmm);
        }
        /**
         * isOpen: 1: đã mở khóa
         * isOpen: 0: chưa mở khóa
         * */
        var slots = indentureInfo.getSlots();
        for (int i = 0; i < slots.size(); i++) {
            builder.addAVector(slots.get(i).toProto(i));
        }
        addResponse(builder.build());
    }

    private void indentureOpen(int index) {
        UserIndentureEntity entityToUpdate = indentureInfo.getSlots().get(index);
        ResIndentureSlot cfgSlot = ConfigIndenture.indentureSlots.get(index);
        //        if (cfgSlot.getTypeUnlock() != 0 && listHeroUser.get(index - 1).getIsOpen() == 0) {
        //            addErrResponse("Bạn cần phải mở ô khóa trước đó");
        //            return;
        //        }
        int vip = user.getVip(); // vip
        int levelCampaign = Services.userService.getCampaign(mUser).getLevel(); // level campaign

        List<Long> aBonus;
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        // Type unlock = 1
        if (cfgSlot.getTypeUnlock() == 1) {
            if (levelCampaign - 1 < cfgSlot.getDataUnlock()) {
                JsonArray arrConvertPrice = GsonUtil.parseFromListLong(Bonus.viewGem(cfgSlot.getCost()));
                if (!mUser.checkPrice(this, arrConvertPrice)) {
                    addErrResponse(getLang(Lang.err_not_enough_gem));
                    return;
                }
                List<Long> bonus = Bonus.receiveListItem(mUser, arrConvertPrice, "open_indenture_with_bonus");
                if (!dbUpdateIsOpenAndStatus(entityToUpdate.getId(), 1)) {
                    addErrResponse();
                    return;
                }
                entityToUpdate.setIsOpen(1);
                builder.addAllALong(bonus);
            }
            if (levelCampaign - 1 >= cfgSlot.getDataUnlock()) {
                aBonus = new ArrayList<>();
                JsonArray arrConvertPrice = GsonUtil.parseFromListLong(aBonus);
                List<Long> bonus = Bonus.receiveListItem(mUser, arrConvertPrice, "open_indenture_with_level_campaign");
                if (!dbUpdateIsOpenAndStatus(entityToUpdate.getId(), 1)) {
                    addErrResponse();
                    return;
                }
                entityToUpdate.setIsOpen(1);
                builder.addAllALong(bonus);
            }
        }
        // Type unlock = 2
        if (cfgSlot.getTypeUnlock() == 2) {
            if (vip >= cfgSlot.getDataUnlock()) {
                if (!dbUpdateIsOpenAndStatus(entityToUpdate.getId(), 1)) {
                    addErrResponse();
                    return;
                }
                entityToUpdate.setIsOpen(1);
            } else {
                addErrResponse("Bạn cần đạt vip yêu cầu mới mở khóa được slot này!");
                return;
            }
        }
        addResponse(builder.build());
        indentureService.checkNotify(mUser, null);
    }

    /**
     * Khi được lắp khế ước:
     * - Lưu lại được level cũ của hero đó, lưu lại trạng thái share_status của hero đó - 2
     * - Cần biết hero nào được lắp vào -> level hero = level của hero có level min
     */
    private void indentureInstallation() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int index = aLong.get(0).intValue() - 1;
        if (aLong.size() < 2) {
            addErrResponse("Chưa chọn tướng");
            return;
        }
        UserIndentureEntity entityToUpdate = indentureInfo.getSlots().get(index);
        if (entityToUpdate.getCountdownToOpen() > 0) {
            addErrResponse("Chưa hết thời gian mở khoá");
            return;
        }
        for (int i = 1; i < aLong.size(); i++) {
            if (mUser.getResources().getHero(aLong.get(i)).getLevel() > 1) {
                addErrResponse("Chỉ lắp được tướng cấp 1");
                return;
            }
        }
        for (int i = 1; i < aLong.size(); i++) {
            long idHero = aLong.get(i);
            if (mUser.getResources().getHero(idHero) != null) {
                var userHeroEntity = mUser.getResources().getHero(idHero);
                if (i > 1) entityToUpdate = indentureService.getAvailableSlot(mUser); // slot đầu tiên là user chọn
                if (entityToUpdate == null) break;

                int levelOld = userHeroEntity.getLevel();
                int shareStatus = ConfigIndenture.STATUS_SHARE_RECEIVE_INDENTURE;
                if (!updateInstallation(entityToUpdate.getId(), idHero, levelOld)) {
                    addErrResponse();
                    return;
                }
                entityToUpdate.setId(entityToUpdate.getId());
                entityToUpdate.setHeroId(idHero);
                entityToUpdate.setLevelOld(levelOld);
                // save DB
                if (indentureDAO.dbUpdateLevelNewHero(1, idHero)) {
                    userHeroEntity.setIndentureLevel(indentureInfo.getLevel());
                    userHeroEntity.setShareStatus(ConfigIndenture.STATUS_SHARE_RECEIVE_INDENTURE);
                    userHeroEntity.setLockIndenture(1);
                }
                userHeroEntity.calculatePointHero(mUser);
                Actions.save(user, "indenture", "indenture_installation", "id", idHero, "shareStatus", shareStatus, "levelOld", levelOld);
            }
        }
        addResponse(null);
    }

    /**
     * Input: {vLong: id}
     * - Cần trả về level old, share status = 0
     * - Vì tháo tướng nên cần trả về thời gian count down và bonus
     */
    private void removeHero(int index) {
        UserIndentureEntity entityToUpdate = indentureInfo.getSlots().get(index);
        var serviceResult = indentureService.removeHero(mUser, entityToUpdate);
        if (serviceResult.success) addResponse(null);
        else serviceResult.writeResponse(this);
    }

    private void skipTimeSlot() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int id = aLong.get(0).intValue() - 1;
        UserIndentureEntity entityToUpdate = indentureInfo.getSlots().get(id);
        if (entityToUpdate.getCountdownToOpen() == 0) {
            addErrResponse("Ô chứa tướng đã sẵn sàng");
            return;
        }
        JsonArray arrConvertPrice = GsonUtil.parseFromListLong(ConfigIndenture.config.feeSkipTime);
        if (!mUser.checkPrice(this, arrConvertPrice)) {
            addErrResponse(getLang(Lang.err_not_enough_gem));
            return;
        }
        List<Long> aBonus = Bonus.receiveListItem(mUser, arrConvertPrice, "skip_time_slot");
        if (!dbUpdateIsOpen(entityToUpdate.getId())) {
            addErrResponse();
            return;
        }
        entityToUpdate.setDateRemove(null);
        builder.addAllALong(aBonus);
        addResponse(builder.build());
    }

    private void levelUp() {
        if (indentureInfo.canLevelUp()) levelUpLogic();
        else addErrResponse("Chưa đủ điều kiện nâng cấp");
    }

    private void levelUpLogic() {
        int levelIndenture = mUser.getUData().getIndentureLevel() + 1;
        if (levelIndenture <= ResIndentureLevelUp.resIndentureLevelUp.size()) {
            List<Long> feeLevelUp = BonusBuilder.newInstance()
                    .add(Bonus.viewGold(-ResIndentureLevelUp.resIndentureLevelUp.get(levelIndenture).getItemAmount1()))
                    .add(Bonus.viewMaterial(MaterialType.SPIRIT, -ResIndentureLevelUp.resIndentureLevelUp.get(levelIndenture).getItemAmount2()))
                    .add(Bonus.viewMaterial(MaterialType.PROMOTION_STONE, -ResIndentureLevelUp.resIndentureLevelUp.get(levelIndenture).getItemAmount3()))
                    .build();
            String msg = Bonus.checkMoney(mUser, feeLevelUp);
            if (msg != null) {
                addErrResponse(msg);
                return;
            }
            List<Long> aBonus = Bonus.receiveListItem(mUser, "indenture_up", feeLevelUp);
            mUser.getUData().update("indenture_level", levelIndenture);
            mUser.getUData().setIndentureLevel(levelIndenture);
            // indentureInfo.calculateIndentureLevel(mUser); // bỏ đi để update trong heroHandler
            addResponse(getCommonVector(aBonus));
            Actions.save(user, "indenture", "level_up", "level", levelIndenture);
        } else addErrResponse(Lang.max_level);
    }

    private void infoLocked(int index) {
        ResIndentureSlot cfgSlot = ConfigIndenture.indentureSlots.get(index);

        int levelCampaign = Services.userService.getCampaign(mUser).getLevel(); // level campaign
        int vip = user.getVip();
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        if (cfgSlot.getTypeUnlock() == 1) {
            if (levelCampaign - 1 < cfgSlot.getDataUnlock()) {
                List<Long> bonusOpenAutomatic = Bonus.viewGem(cfgSlot.getCost());
                int levelAFK = cfgSlot.getDataUnlock();
                String desc = "Đạt mốc AFK: " + levelAFK + " (Hiện tại: " + levelCampaign + ")";
                builder.addAllALong(bonusOpenAutomatic);
                builder.addAString(desc);
            } else if (levelCampaign - 1 >= cfgSlot.getDataUnlock()) {
                List<Long> bonusOpenAutomatic = new ArrayList<>();
                int levelAFK = cfgSlot.getDataUnlock();
                String desc = "Đạt mốc AFK: " + levelAFK + " (Hiện tại: " + levelCampaign + ")";
                builder.addAllALong(bonusOpenAutomatic);
                builder.addAString(desc);
            }

        }
        if (cfgSlot.getTypeUnlock() == 2) {
            int levelVip = cfgSlot.getDataUnlock();
            String desc = "Đạt VIP: " + levelVip + " (Hiện tại: " + vip + ")";
            builder.addAString(desc);
        }
        addResponse(builder.build());
    }

    private boolean dbUpdateLevelUp(int levelReceiveIndenture, List<Long> listId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_hero set level=:levelReceiveIndenture where id in :listId");
            query.setParameter("levelReceiveIndenture", levelReceiveIndenture);
            query.setParameter("listId", listId);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateIsOpen(int id) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_indenture set date_remove=null where id=:id");
            query.setParameter("id", id);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateIsOpenAndStatus(int id, long isOpen) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_indenture set is_open=:isOpen, status_slot = 1 where id=:id");
            query.setParameter("isOpen", isOpen);
            query.setParameter("id", id);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean updateInstallation(int id, long heroId, int levelOld) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_indenture set hero_id=:heroId, level_old=:levelOld where id=:id");
            query.setParameter("id", id);
            query.setParameter("levelOld", levelOld);
            query.setParameter("heroId", heroId);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }
}