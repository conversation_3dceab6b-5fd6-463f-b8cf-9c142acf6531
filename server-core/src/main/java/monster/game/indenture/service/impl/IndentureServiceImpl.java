package monster.game.indenture.service.impl;

import com.google.inject.Inject;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.NotifyType;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserHeroEntity;
import monster.game.indenture.config.ConfigIndenture;
import monster.game.indenture.dao.IndentureDAO;
import monster.game.indenture.entity.IndentureInfo;
import monster.game.indenture.entity.ResIndentureSlot;
import monster.game.indenture.entity.UserIndentureEntity;
import monster.game.indenture.service.IndentureService;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.service.Services;
import monster.service.common.NotifyService;
import monster.service.user.Actions;
import protocol.Pbmethod;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.function.Predicate.not;

public class IndentureServiceImpl implements IndentureService {
    @Inject
    IndentureDAO indentureDAO;
    @Inject
    NotifyService notifyService;

    private List<UserIndentureEntity> getListSlots(MyUser mUser) {
        UserEntity user = mUser.getUser();
        List<UserIndentureEntity> listHeroUser = indentureDAO.getSlotInfoUser(user.getId());
        int numberToSave = ConfigIndenture.indentureSlots.size() - listHeroUser.size();
        if (numberToSave > 0) {
            List<UserIndentureEntity> newSlot = new ArrayList<>();
            for (int i = 0; i < numberToSave; i++) {
                ResIndentureSlot cfgSlot = ConfigIndenture.indentureSlots.get(listHeroUser.size() + i);
                UserIndentureEntity userIndenture = new UserIndentureEntity();
                userIndenture.setUserId(user.getId());
                userIndenture.setHeroId(0);
                userIndenture.setIsOpen(cfgSlot.getTypeUnlock() == 0 ? 1 : 0);
                userIndenture.setLevelOld(0);
                newSlot.add(userIndenture);
            }
            if (indentureDAO.save(newSlot.toArray(UserIndentureEntity[]::new))) {
                listHeroUser.addAll(newSlot);
            }
        }
        return listHeroUser;
    }

    @Override
    public List<UserIndentureEntity> getSlotHasHero(MyUser mUser) {
        var slots = mUser.getResources().getIndentureInfo().getSlots();
        return slots.stream().filter(slot -> slot.getHeroId() > 0).collect(Collectors.toList());
    }

    @Override
    public List<UserHeroEntity> top6HeroIndenture(MyUser mUser) {
        List<UserHeroEntity> userHeroes = mUser.getResources().getHeroes()
                .stream().filter(not(UserHeroEntity::isShareLevel)).collect(Collectors.toList());

        userHeroes.sort(Comparator.comparing(UserHeroEntity::getRealLevel).reversed()
                .thenComparing(Comparator.comparing(UserHeroEntity::getRank).reversed())
                .thenComparing(Comparator.comparing(UserHeroEntity::getPower).reversed())
        );
        List<UserHeroEntity> top6 = userHeroes.subList(0, Math.min(6, userHeroes.size()));
        return top6;//.stream().map(UserHeroEntity::getId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getIdHeroIndenture(MyUser mUser) {
        // Get list id hero receive indenture
        List<Long> userIndenture = indentureDAO.getIdHeroIndenture(mUser.getUser().getId());
        return userIndenture;
    }

    @Override
    public int getLevelIndenture(MyUser mUser) {
        //Get level indenture share - level min
        return top6HeroIndenture(mUser).getLast().getLevel();
    }

    @Override
    public UserIndentureEntity getAvailableSlot(MyUser mUser) {
        List<UserIndentureEntity> slots = mUser.getResources().getIndentureInfo().getSlots();
        for (UserIndentureEntity slot : slots) {
            System.out.println(slot.getHeroId() + " " + slot.getCountdownToOpen() + " " + slot.isClose());
            if (slot.getHeroId() == 0 && slot.getCountdownToOpen() == 0 && !slot.isClose()) return slot;
        }
        return null;
    }

    @Override
    public UserIndentureEntity getSlotByHeroId(MyUser mUser, long idHero) {
        return mUser.getResources().getIndentureInfo().getSlots().stream().filter(slot -> slot.getHeroId() == idHero).findFirst().orElse(null);
    }

    @Override
    public ServiceResult removeHero(MyUser mUser, UserIndentureEntity entityToUpdate) {
        long heroId = entityToUpdate.getHeroId();
        int levelBegin = entityToUpdate.getLevelOld();
        if (!indentureDAO.dbUpdateLevelNewHero(0, entityToUpdate.getHeroId())) {
            return ServiceResult.error(Lang.err_system_down);
        }
        var hero = mUser.getResources().getHero(entityToUpdate.getHeroId());
        hero.setLockIndenture(0);
        hero.setIndentureLevel(0);
        hero.setShareStatus(0);
        hero.calculatePointHero(mUser);

        if (!indentureDAO.dbUpdateRemoveHero(entityToUpdate.getId())) {
            return ServiceResult.error(Lang.err_system_down);
        }
        entityToUpdate.removeHero();
        Actions.save(mUser.getUser(), "indenture", "remove", "id", heroId, "levelBegin", levelBegin);
        return ServiceResult.success();
    }

    @Override
    public List<Long> getListUpdateHero(MyUser mUser, List<Long> idHeroes) {
        return null;
    }

    @Override
    public void firstInitHeroes(MyUser mUser, List<UserHeroEntity> heroes) {
        IndentureInfo indentureInfo = mUser.getResources().getIndentureInfo();
        indentureInfo.setSlots(getListSlots(mUser));
        List<Long> shareStatus2 = indentureInfo.getSlots().stream().filter(slot -> slot.getHeroId() > 0)
                .map(slot -> slot.getHeroId()).collect(Collectors.toList());
        List<UserHeroEntity> userHeroes = heroes.stream().filter(hero -> !shareStatus2.contains(hero.getId()))
                .collect(Collectors.toList());
        userHeroes.sort(Comparator.comparing(UserHeroEntity::getLevel).reversed()
                .thenComparing(Comparator.comparing(UserHeroEntity::getRank).reversed())
                .thenComparing(Comparator.comparing(UserHeroEntity::getPower).reversed())
        );
        List<UserHeroEntity> top6 = userHeroes.isEmpty() ? new ArrayList<>() : userHeroes.subList(0, Math.min(6, userHeroes.size()));
        List<Long> top6Id = top6.stream().map(UserHeroEntity::getId).collect(Collectors.toList());

        int numberUniqueE5 = heroes.stream().filter(hero -> hero.getStar() >= 15).map(UserHeroEntity::getHeroId).distinct().collect(Collectors.toList()).size();
        indentureInfo.setNumberUniqueE5(numberUniqueE5);
        indentureInfo.setTop6Level(top6.isEmpty() ? 0 : top6.getLast().getLevel());
        indentureInfo.setTop6Ids(top6Id);
        indentureInfo.calculateIndentureLevel(mUser);
        for (UserHeroEntity hero : heroes) {
            if (shareStatus2.contains(hero.getId())) {
                hero.setShareStatus(2);
                hero.setIndentureLevel(indentureInfo.getLevel());
            } else if (top6Id.contains(hero.getId())) {
                hero.setShareStatus(1);
                hero.setIndentureLevel(indentureInfo.getLevel());
                Logs.warn(hero.getId() + " " + hero.getIndentureLevel() + " " + hero.getLevel());
            }
        }
    }

    @Override
    public void checkNotify(MyUser mUser, Pbmethod.CommonVector.Builder cmm) {
        int levelCampaign = Services.userService.getCampaign(mUser).getLevel(); // level campaign
        int vip = mUser.getUser().getVip();
        boolean notifyAfk = false, notifyVip = false;
        IndentureInfo indentureInfo = mUser.getResources().getIndentureInfo();
        if (indentureInfo.getSlots() != null) {
            for (int i = 0; i < indentureInfo.getSlots().size(); i++) {
                UserIndentureEntity slot = indentureInfo.getSlots().get(i);
                if (slot.isClose()) {
                    ResIndentureSlot cfgSlot = ConfigIndenture.indentureSlots.get(i);
                    if (cfgSlot.getTypeUnlock() == 1 && levelCampaign >= cfgSlot.getDataUnlock()) notifyAfk = true;
                    if (cfgSlot.getTypeUnlock() == 2 && vip >= cfgSlot.getDataUnlock()) notifyVip = true;
                }
            }
        }
        if (cmm == null) {
            notifyService.sendNotify(mUser, NotifyType.INDENTURE_NOTIFY_SLOT_AFK, notifyAfk);
            notifyService.sendNotify(mUser, NotifyType.INDENTURE_NOTIFY_SLOT_VIP, notifyVip);
        } else {
            if (notifyAfk) cmm.addALong(NotifyType.INDENTURE_NOTIFY_SLOT_AFK.value);
            if (notifyVip) cmm.addALong(NotifyType.INDENTURE_NOTIFY_SLOT_VIP.value);
        }
    }

    @Override
    public List<Long> getAboveE5Hero(MyUser mUser) {
        List<UserHeroEntity> userHeroes = mUser.getResources().getHeroes().stream().filter(hero -> hero.getStar() >= 15).collect(Collectors.toList());
        userHeroes.sort(Comparator.comparing(UserHeroEntity::getPower));
        Set<Integer> set = new HashSet<>();
        List<Long> resultId = new ArrayList<>();
        for (UserHeroEntity userHero : userHeroes) {
            if (!set.contains(userHero.getHeroId())) {
                set.add(userHero.getHeroId());
                resultId.add(userHero.getId());
            }
        }
        return resultId;
    }
}
