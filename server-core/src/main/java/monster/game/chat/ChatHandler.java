package monster.game.chat;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgAccount;
import monster.config.CfgChat;
import monster.config.lang.Lang;
import monster.config.penum.ChatType;
import monster.config.penum.NotifyType;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.dao.mapping.BattleLogEntity;
import monster.dao.mapping.UserAdminChatEntity;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mongo.mapping.UserAdminChatPojo;
import monster.object.ChatObject;
import monster.object.UserInt;
import monster.protocol.CommonProto;
import monster.service.Services;
import monster.service.monitor.ChatMonitor;
import monster.service.monitor.ClanMonitor;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.util.Utils;
import protocol.Pbmethod;

import java.util.*;

/**
 * Chat global + chat clan
 */
public class ChatHandler extends AHandler {

    public static Map<Integer, ChatMonitor> globalChat = new HashMap<>();

    private static synchronized ChatMonitor getGlobalChat(int serverId) {
        // serverId = (serverId - 1) / 3; -> gộp 3 sv làm 1
        serverId = serverId <= 3 ? -1 : 0; // chat all server
        if (!globalChat.containsKey(serverId)) globalChat.put(serverId, new ChatMonitor("serverChat:" + serverId));
        return globalChat.get(serverId);
    }

    public static synchronized Long getLastChat(int serverId) {
        serverId = serverId <= 3 ? -1 : 0; // chat all server
        if (globalChat.containsKey(serverId)) return globalChat.get(serverId).getLastChat();
        return -1L;
    }

    public static Map<Integer, ChatMonitor> mClanRecruit = new HashMap<>();

    private static synchronized ChatMonitor getClanRecruit(int serverId) {
        if (!mClanRecruit.containsKey(serverId))
            mClanRecruit.put(serverId, new ChatMonitor("clanRecruit:" + serverId).setType(ChatType.CLAN_RECRUIT));
        return mClanRecruit.get(serverId);
    }

    public static synchronized Long getLastRecruit(int serverId) {
        if (mClanRecruit.containsKey(serverId)) return mClanRecruit.get(serverId).getLastChat();
        return -1L;
    }

    @Override
    public AHandler newInstance() {
        return new ChatHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(GLOBAL_CHAT, GLOBAL_CHAT_LIST, CLAN_CHAT, CLAN_CHAT_LIST, CHAT_HIDDEN_VIP,
                CLAN_RECRUIT, CLAN_RECRUIT_LIST, SHARE_BATTLE, ADMIN_CHAT, ADMIN_CHAT_LIST);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        if (Arrays.asList(GLOBAL_CHAT, CLAN_CHAT).contains(actionId)) {
            { // Kiểm tra user có bị block chat ko
                String blockMsg = CfgChat.getBlockChatMsg(user.getMainId());
                if (!StringHelper.isEmpty(blockMsg)) {
                    addPopupResponse(blockMsg);
                    return;
                }
            }
        }
        try {
            switch (actionId) {
                case GLOBAL_CHAT -> globalChat();
                case CLAN_CHAT -> clanChat();
                case GLOBAL_CHAT_LIST -> globalChatList(CommonProto.parseCommonVector(requestData).getALongList().get(0));
                case CLAN_CHAT_LIST -> clanChatList(CommonProto.parseCommonVector(requestData).getALongList().get(0));
                case CHAT_HIDDEN_VIP -> hiddenVip();
                case CLAN_RECRUIT -> clanRecruit();
                case CLAN_RECRUIT_LIST -> clanRecruitList(CommonProto.parseCommonVector(requestData).getALongList().get(0));
                case SHARE_BATTLE -> shareBattle();
                case ADMIN_CHAT -> adminChat();
                case ADMIN_CHAT_LIST -> adminChatList(ADMIN_CHAT_LIST);
                //                    adminChatListMongo(ADMIN_CHAT_LIST);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    private void adminChat() {
        if (!StringHelper.isEmpty(mUser.getBlockChat())) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getBlockType() == Status.BLOCK_CHAT.value) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getLevel() < 20) {
            addPopupResponse(getLang(Lang.chat_required_level));
            return;
        }

        Long chatTime = (Long) mUser.getCache().get("adminChat");
        if (chatTime != null && System.currentTimeMillis() - chatTime < 5 * 1000) {
            addErrResponse(getLang(Lang.chat_too_quick));
            return;
        }

        String lastMsg = (String) mUser.getCache().get("adminChatMsg");
        if (lastMsg == null) lastMsg = "";
        lastMsg = lastMsg.replaceAll(" ", "");

        Pbmethod.CommonVector cmm = CommonProto.parseCommonVector(requestData);
        long lastReqTime = cmm.getALong(0);
        String chatMsg = cmm.getAString(0);

        chatMsg = chatMsg.length() >= 160 ? chatMsg.substring(0, 160) : chatMsg;
        //        chatMsg = chatMsg.replaceAll("\\[[^]]*\\]", "");
        //        chatMsg = chatMsg.replaceAll("<[^>]*>", "");
        if (chatMsg.equals("reset") && CfgAccount.isAdmin(user.getUsername())) {
            Utils.resetServer();
            return;
        }

        if (!CfgChat.isValidChat(chatMsg, lastMsg)) {
            addPopupResponse(getLang(Lang.chat_msg_invalid));
            return;
        }

        if (DBJPA.save(UserAdminChatEntity.builder().userId(user.getId()).serverId(user.getServer())
                .message(chatMsg).dateCreated(new Date()).isRead(false).isAdmin(false)
                .build())) {
            adminChatList(ADMIN_CHAT);
            //        getGlobalChat(user.getServer()).addChat(new ChatObject(user, vip, CfgChat.replaceInvalidWord(chatMsg)));
            //            addResponse(GLOBAL_CHAT, protoListChat(getChatHistory(getGlobalChat(user.getServer()).getAChat(), lastReqTime)));
        } else addErrResponse();
    }

    private void adminChatListMongo(int service) {
        if (true) return;
        monster.dao.mongo.ChatDAO dao = new monster.dao.mongo.ChatDAO();
        Long lastId = (Long) mUser.getCache().get("adminChat");
        if (lastId == null) lastId = 0L;
        UserAdminChatPojo adminChat = dao.getAdminChat(user);
        addResponse(service, adminChat.toProto(user, lastId));
        mUser.getCache().set("adminChat", adminChat.getLastId());
        dao.updateRead(user);
    }

    private void adminChatList(int service) {
        Long lastTime = (Long) mUser.getCache().get("adminChat");
        if (lastTime == null) lastTime = 0L;

        List<UserAdminChatEntity> chats = Services.chatService.getAdminChat(user);
        while (!chats.isEmpty() && lastTime >= chats.get(0).getDateCreated().getTime())
            chats.remove(0);

        // update lại last read để có notify
        if (!chats.isEmpty()) {
            lastTime = chats.get(chats.size() - 1).getDateCreated().getTime();
            if (lastTime != mUser.getUData().getLastRecruitChat()) {
                mUser.getUData().update("last_recruit_chat", lastTime);
                mUser.getUData().setLastRecruitChat(lastTime);
            }
        }

        Pbmethod.ChatHistory.Builder builder = Pbmethod.ChatHistory.newBuilder();
        chats.forEach(chat -> builder.addAChat(chat.toProto(user)));
        addResponse(service, builder.build());
        mUser.getCache().set("adminChat", lastTime);
        //        DBJPA.rawSQL("UPDATE user_admin_chat SET is_read=1 WHERE user_id=" + user.getId() + " AND is_read=0 AND is_admin=1");
    }

    private void shareBattle() {
        if (user.getLevel() < 30) {
            addErrResponse(getLang(Lang.user_function_level_required, 30));
            return;
        }
        int feeGem = 5;

        Pbmethod.CommonVector cmm = CommonProto.parseCommonVector(requestData);
        int battleId = (int) cmm.getALong(0);
        int where = cmm.getALongList().size() > 1 ? (int) cmm.getALong(1) : 0;
        if (where > 2) where = 0;
        String title = cmm.getAString(0);

        if (!CfgChat.isValidChat(title, "")) {
            addPopupResponse(getLang(Lang.chat_msg_invalid));
            return;
        }

        UserInt userInt = mUser.getUData().getUInt();
        if (userInt.getValue(UserInt.DATE_KEY) != DateTime.getTimeKey()) {
            userInt.setValue(UserInt.DATE_KEY, 0);
            userInt.setValue(UserInt.SHARE_BATTLE_0, 0);
            userInt.setValue(UserInt.SHARE_BATTLE_1, 0);
            userInt.setValue(UserInt.SHARE_BATTLE_2, 0);
            userInt.update(user.getId());
        }
        int numberShare = userInt.getValue(where + 20);
        if (numberShare >= 3) {
            addErrResponse("Tối đa 3 lần mỗi ngày");
            return;
        }

        BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
        if (battleLog == null) addErrResponse(getLang(Lang.battle_not_found));
        else if (user.getGem() < feeGem) {
            addErrResponse(getLang(Lang.err_not_enough_gem));
        } else {
            if (where == 1 && user.getClan() == 0) {
                addErrResponse(getLang(Lang.join_clan_to_share_chat));
                return;
            }
            boolean hiddenVip = mUser.getUData().getUInt().getValue(UserInt.HIDDEN_VIP) == 1;
            int vip = hiddenVip ? 0 : user.getVip();

            List<Long> aBonus = Bonus.receiveListItem(mUser, "share_battle", Bonus.viewGem(-feeGem));
            if (where == 0) {
                getGlobalChat(user.getServer()).addChat(new ChatObject(user, vip, battleLog.getId(), String.format("%s\n%s", battleLog.getTitle(), title)));
            } else if (where == 1) {
                ClanMonitor.getClan(user.getClan()).addChat(new ChatObject(user, vip, battleLog.getId(), String.format("%s\n%s", battleLog.getTitle(), title)));
            } else if (where == 2) {
                ChatServerHandler.getServerChat(user.getServer()).addChat(new ChatObject(user, vip, battleLog.getId(), String.format("%s\n%s", battleLog.getTitle(), title)));
            }
            addResponse(getCommonVector(aBonus));
            userInt.setValue(where + 20, numberShare + 1);
            userInt.update(user.getId());
        }
    }

    private void clanRecruit() {
        boolean hiddenVip = mUser.getUData().getUInt().getValue(UserInt.HIDDEN_VIP) == 1;
        int vip = hiddenVip ? 0 : user.getVip();

        if (!StringHelper.isEmpty(mUser.getBlockChat())) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getBlockType() == Status.BLOCK_CHAT.value) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getLevel() < 20) {
            addPopupResponse(getLang(Lang.chat_required_level));
            return;
        }

        if (user.getClanPosition() != 3 && user.getClanPosition() != 2) {
            addErrResponse(getLang(Lang.clan_leader_coleader_required));
            return;
        }

        Long chatTime = (Long) mUser.getCache().get("recruitChat");
        if (chatTime != null && System.currentTimeMillis() - chatTime < 5 * 1000) {
            addErrResponse(getLang(Lang.chat_too_quick));
            return;
        }

        String lastMsg = (String) mUser.getCache().get("recruitChatMsg");
        if (lastMsg == null) lastMsg = "";
        lastMsg = lastMsg.replaceAll(" ", "");

        Pbmethod.CommonVector cmm = CommonProto.parseCommonVector(requestData);
        long lastReqTime = cmm.getALong(0);
        String chatMsg = cmm.getAString(0);
        chatMsg = chatMsg.length() >= 160 ? chatMsg.substring(0, 160) : chatMsg;
        //        chatMsg = chatMsg.replaceAll("\\[[^]]*\\]", "");
        //        chatMsg = chatMsg.replaceAll("<[^>]*>", "");
        if (chatMsg.equals("reset") && CfgAccount.isAdmin(user.getUsername())) {
            Utils.resetServer();
            return;
        }

        if (!CfgChat.isValidChat(chatMsg, lastMsg)) {
            addPopupResponse(getLang(Lang.chat_msg_invalid));
            return;
        }

        Actions.save(user, Actions.GUSER, Actions.DCHAT, "type", "recruit");
        getClanRecruit(user.getServer()).addChat(new ChatObject(user, vip, chatMsg, ChatObject.CLAN_RECRUIT));

        addResponse(CLAN_RECRUIT, protoListChat(getChatHistory(getClanRecruit(user.getServer()).getAChat(), lastReqTime)));
        mUser.getCache().set("recruitChat", Long.valueOf(System.currentTimeMillis()));
        mUser.getCache().set("recruitChatMsg", "");
    }

    private void clanRecruitList(long lastReqTime) {
        List<ChatObject> aChat = getChatHistory(getClanRecruit(user.getServer()).getAChat(), lastReqTime);
        addResponse(CLAN_RECRUIT_LIST, protoListChat(aChat));
    }

    private void hiddenVip() {
        int hidden = (int) CommonProto.parseCommonVector(requestData).getALong(0);

        if (hidden != 0 && hidden != 1) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }

        UserInt uInt = mUser.getUData().getUInt();
        uInt.setValue(UserInt.HIDDEN_VIP, hidden);
        if (uInt.update(user.getId()))
            addResponse(getCommonVector(hidden));
        else addErrResponse();
    }

    private void globalChat() {
        boolean hiddenVip = mUser.getUData().getUInt().getValue(UserInt.HIDDEN_VIP) == 1;
        int vip = hiddenVip ? 0 : user.getVip();

        if (!StringHelper.isEmpty(mUser.getBlockChat())) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getBlockType() == Status.BLOCK_CHAT.value) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getLevel() < CfgChat.config.userLevelForChatGlobal) {
            addPopupResponse(String.format(getLang(Lang.chat_required_level), CfgChat.config.userLevelForChatGlobal));
            return;
        }

        Long chatTime = (Long) mUser.getCache().get("globalChat");
        if (chatTime != null && System.currentTimeMillis() - chatTime < 5 * 1000) {
            addErrResponse(getLang(Lang.chat_too_quick));
            return;
        }

        String lastMsg = (String) mUser.getCache().get("globalChatMsg");
        if (lastMsg == null) lastMsg = "";
        lastMsg = lastMsg.replaceAll(" ", "");

        Pbmethod.CommonVector cmm = CommonProto.parseCommonVector(requestData);
        long lastReqTime = cmm.getALong(0);
        int msgType = (int) cmm.getALong(1);
        if (msgType == ChatObject.MSG) {
            String chatMsg = cmm.getAString(0);
            chatMsg = chatMsg.length() >= 160 ? chatMsg.substring(0, 160) : chatMsg;
            if (chatMsg.length() > 50) {
                addPopupResponse(mUser.getLang().isVi() ? "Nội dung chat trên kênh thế giới chỉ được tối đa 50 ký tự" : "Chat content on the world channel is limited to a maximum of 50 characters");
                return;
            }
            //            chatMsg = chatMsg.replaceAll("\\[[^]]*\\]", "");
            //            chatMsg = chatMsg.replaceAll("<[^>]*>", "");
            if (chatMsg.equals("reset") && CfgAccount.isAdmin(user.getUsername())) {
                Utils.resetServer();
                return;
            }

            if (!CfgChat.isValidChat(chatMsg, lastMsg)) {
                addPopupResponse(getLang(Lang.chat_msg_invalid));
                return;
            }

            Actions.save(user, Actions.GUSER, Actions.DCHAT, "type", "global");
            getGlobalChat(user.getServer()).addChat(new ChatObject(user, vip, CfgChat.replaceInvalidWord(chatMsg)));
        } else if (msgType == ChatObject.SHOW_HERO) {
            int heroId = (int) cmm.getALong(2);
            UserHeroEntity uHero = mUser.getResources().getHero(heroId);
            if (uHero == null) {
                addErrResponse(getLang(Lang.err_params));
                return;
            }
            UserHeroEntity tmpHero = uHero.cloneEmptyHero();
            getGlobalChat(user.getServer()).addChat(new ChatObject(user, vip, tmpHero.protoTeamHeroInfo()));
        }
        addResponse(GLOBAL_CHAT, protoListChat(getChatHistory(getGlobalChat(user.getServer()).getAChat(), lastReqTime)));
        mUser.getCache().set("globalChat", Long.valueOf(System.currentTimeMillis()));
        mUser.getCache().set("globalChatMsg", "");
        saveGlobalChat();
    }

    private void globalChatList(long lastReqTime) {
        List<ChatObject> aChat = getChatHistory(getGlobalChat(user.getServer()).getAChat(), lastReqTime);
        addResponse(GLOBAL_CHAT_LIST, protoListChat(aChat));
        if (!aChat.isEmpty()) saveGlobalChat();
    }

    private void clanChat() {
        boolean hiddenVip = mUser.getUData().getUInt().getValue(UserInt.HIDDEN_VIP) == 1;
        int vip = hiddenVip ? 0 : user.getVip();

        if (user.getClan() == 0) {
            addErrResponse(getLang(Lang.clan_no_clan));
            return;
        }
        // Add chat;
        Pbmethod.CommonVector cmm = CommonProto.parseCommonVector(requestData);
        long reqTime = cmm.getALongList().get(0);
        int msgType = cmm.getALongList().get(1).intValue();

        if (msgType == ChatObject.MSG) {
            String chatMsg = cmm.getAString(0);
            //            chatMsg = chatMsg.replaceAll("\\[[^]]*\\]", "");
            //            chatMsg = chatMsg.replaceAll("<[^>]*>", "");
            chatMsg = chatMsg.length() >= 160 ? chatMsg.substring(0, 160) : chatMsg;
            if (!CfgChat.isValidChat(chatMsg, "")) {
                addPopupResponse(getLang(Lang.chat_msg_invalid));
                return;
            }
            ClanMonitor.getClan(user.getClan()).addChat(user, msgType, chatMsg, vip);
        } else if (msgType == ChatObject.SHOW_HERO) {
            int heroId = (int) cmm.getALong(2);
            UserHeroEntity uHero = mUser.getResources().getHero(heroId);
            if (uHero == null) {
                addErrResponse(getLang(Lang.err_params));
                return;
            }
            UserHeroEntity tmpHero = uHero.cloneEmptyHero();
            ClanMonitor.getClan(user.getClan()).addChat(user, tmpHero.protoTeamHeroInfo(), vip);
        }
        addResponse(CLAN_CHAT, protoListChat(getChatHistory(ClanMonitor.getClan(user.getClan()).getAChat(), reqTime)));
        saveClanChat();
    }

    private void clanChatList(long lastReqTime) {
        if (user.getClan() == 0) {
            addErrResponse(getLang(Lang.clan_no_clan));
            return;
        }
        List<ChatObject> aChat = getChatHistory(ClanMonitor.getClan(user.getClan()).getAChat(), lastReqTime);
        addResponse(CLAN_CHAT_LIST, protoListChat(aChat));
        if (!aChat.isEmpty()) saveClanChat();
    }
    //endregion

    //region Logic
    private void saveGlobalChat() {
        mUser.getUData().setLastGlobalChat(System.currentTimeMillis());
        mUser.getUData().update("last_global_chat", System.currentTimeMillis());
    }

    private void saveClanChat() {
        mUser.getUData().setLastClanChat(System.currentTimeMillis());
        mUser.getUData().update("last_clan_chat", System.currentTimeMillis());
    }

    private List<ChatObject> getChatHistory(List<ChatObject> aChat, long lastReqTime) {
        List<ChatObject> tmp = new ArrayList<ChatObject>();
        int count = 0;
        for (int i = aChat.size() - 1; i >= 0; i--) {
            if (aChat.get(i).getTime() > lastReqTime) {
                tmp.add(0, aChat.get(i));
                count++;
            }
            if (count >= ChatMonitor.maxReturn) {
                break;
            }
        }
        return tmp;
    }

    private Pbmethod.ChatHistory protoListChat(List<ChatObject> aChat) {
        Pbmethod.ChatHistory.Builder builder = Pbmethod.ChatHistory.newBuilder();
        builder.setNumber(0);
        aChat.forEach(chat -> builder.addAChat(chat.toProto()));
        return builder.build();
    }
    //endregion
}
