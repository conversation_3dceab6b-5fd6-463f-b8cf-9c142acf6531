package monster.game.chat.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "game_report", schema = "dson")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GameReport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "report_id")
    private Integer reportId;

    @Column(name = "report_data")
    private String reportData;

}