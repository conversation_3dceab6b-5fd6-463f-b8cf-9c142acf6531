package monster.game.chat.entity;

import grep.helper.DateTime;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import monster.service.resource.ResLuckyMoney;
import protocol.Pbmethod;

import java.util.Calendar;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "user_lucky_money", schema = "dson")
@AllArgsConstructor
@NoArgsConstructor
public class UserLuckyMoney {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "server_id")
    private Integer serverId;

    @Column(name = "clan_id")
    private Integer clanId;

    @Column(name = "res_lucky_id")
    private Integer resLuckyId;

    @Column(name = "lucky_type")
    private Integer luckyType; // 1: clan | 2: sever | 3: world
    int numberSplit, numberReceive;
    String giveOutDescription;
    private Date timeCreated, timeExpired;
    private Date timeGiveOut, timeGiveOutExpired;
    private Date timeLuckyMoneyEnd;

    public boolean isNumberFull() {
        return numberReceive >= numberSplit;
    }

    public boolean isTimeExpired() {
        return System.currentTimeMillis() > timeExpired.getTime();
    }

    public boolean isTimeGiveOutExpired() {
        return System.currentTimeMillis() > timeGiveOutExpired.getTime();
    }

    public Pbmethod.CommonVector toProto() {
        protocol.Pbmethod.CommonVector.Builder getData = protocol.Pbmethod.CommonVector.newBuilder();
        long secondsRemaining = timeExpired.getTime() - timeCreated.getTime();
        Calendar calendar = DateTime.getCalendar(timeCreated, Calendar.SECOND, (int) (secondsRemaining / 1000));
        long timeRemaining = Math.max(0, (calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000);
        if(timeRemaining > 0){
            getData.addALong(id);
            getData.addALong(luckyType);
            getData.addALong(ResLuckyMoney.resLuckyMoneyEntity.get(resLuckyId).getMinSplit());
            getData.addALong(ResLuckyMoney.resLuckyMoneyEntity.get(resLuckyId).getMaxSplit());
            getData.addALong(timeRemaining);
            getData.addALong(ResLuckyMoney.resLuckyMoneyEntity.get(resLuckyId).getGem());
            getData.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(resLuckyId).getName());
            getData.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(resLuckyId).getDescription());
        }
        return getData.build();

    }

}