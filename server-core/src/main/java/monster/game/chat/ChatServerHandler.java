package monster.game.chat;

import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgAccount;
import monster.config.CfgChat;
import monster.config.lang.Lang;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.dao.mapping.UserHeroEntity;
import monster.object.ChatObject;
import monster.object.UserInt;
import monster.protocol.CommonProto;
import monster.service.monitor.ChatMonitor;
import monster.service.user.Actions;
import monster.util.Utils;
import protocol.Pbmethod;

import java.util.*;

/**
 * Xử lý chat server
 */
public class ChatServerHandler extends AHandler {

    public static Map<Integer, ChatMonitor> serverChat = new HashMap<>();

    public static synchronized ChatMonitor getServerChat(int serverId) {
        if (!serverChat.containsKey(serverId)) serverChat.put(serverId, new ChatMonitor("privateChat:" + serverId));
        return serverChat.get(serverId);
    }

    public static synchronized Long getLastChat(int serverId) {
        if (serverChat.containsKey(serverId)) return serverChat.get(serverId).getLastChat();
        return -1L;
    }

    @Override
    public AHandler newInstance() {
        return new ChatServerHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(SERVER_CHAT, SERVER_CHAT_LIST);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        if (Arrays.asList(SERVER_CHAT).contains(actionId)) {
            { // Kiểm tra user có bị block chat ko
                String blockMsg = CfgChat.getBlockChatMsg(user.getMainId());
                if (!StringHelper.isEmpty(blockMsg)) {
                    addPopupResponse(blockMsg);
                    return;
                }
            }
        }
        try {
            switch (actionId) {
                case SERVER_CHAT:
                    serverChat();
                    break;
                case SERVER_CHAT_LIST:
                    serverChatList(CommonProto.parseCommonVector(requestData).getALongList().get(0));
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    private void serverChat() {
        boolean hiddenVip = mUser.getUData().getUInt().getValue(UserInt.HIDDEN_VIP) == 1;
        int vip = hiddenVip ? 0 : user.getVip();

        if (!StringHelper.isEmpty(mUser.getBlockChat())) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getBlockType() == Status.BLOCK_CHAT.value) {
            addPopupResponse(getLang(Lang.chat_block));
            return;
        }

        if (user.getLevel() < 20) {
            addPopupResponse(String.format(getLang(Lang.chat_required_level), 20));
            return;
        }

        Long chatTime = (Long) mUser.getCache().get("serverChat");
        if (chatTime != null && System.currentTimeMillis() - chatTime < 5 * 1000) {
            addErrResponse(getLang(Lang.chat_too_quick));
            return;
        }

        String lastMsg = (String) mUser.getCache().get("serverChatMsg");
        if (lastMsg == null) lastMsg = "";
        lastMsg = lastMsg.replaceAll(" ", "");

        Pbmethod.CommonVector cmm = CommonProto.parseCommonVector(requestData);
        long lastReqTime = cmm.getALong(0);
        int msgType = (int) cmm.getALong(1);
        if (msgType == ChatObject.MSG) {
            String chatMsg = cmm.getAString(0);
            chatMsg = chatMsg.length() >= 160 ? chatMsg.substring(0, 160) : chatMsg;
            chatMsg = chatMsg.replaceAll("\\[[^]]*\\]", "");
            chatMsg = chatMsg.replaceAll("<[^>]*>", "");
            if (chatMsg.equals("reset") && CfgAccount.isAdmin(user.getUsername())) {
                Utils.resetServer();
                return;
            }

            if (!CfgChat.isValidChat(chatMsg, lastMsg)) {
                addPopupResponse(getLang(Lang.chat_msg_invalid));
                return;
            }

            Actions.save(user, Actions.GUSER, Actions.DCHAT, "type", "server");
            getServerChat(user.getServer()).addChat(new ChatObject(user, vip, CfgChat.replaceInvalidWord(chatMsg)));
        } else if (msgType == ChatObject.SHOW_HERO) {
            long idHero = cmm.getALong(2);
            UserHeroEntity uHero = mUser.getResources().getHero(idHero);
            if (uHero == null) {
                addErrResponse(getLang(Lang.err_params));
                return;
            }
            UserHeroEntity tmpHero = uHero.cloneEmptyHero();
            getServerChat(user.getServer()).addChat(new ChatObject(user, vip, tmpHero.protoTeamHeroInfo()));
        }
        addResponse(SERVER_CHAT, protoListChat(getChatHistory(getServerChat(user.getServer()).getAChat(), lastReqTime)));
        mUser.getCache().set("serverChat", Long.valueOf(System.currentTimeMillis()));
        mUser.getCache().set("serverChatMsg", "");
        saveServerChat();
    }

    private void serverChatList(long lastReqTime) {
        List<ChatObject> aChat = getChatHistory(getServerChat(user.getServer()).getAChat(), lastReqTime);
        addResponse(SERVER_CHAT_LIST, protoListChat(aChat));
        if (!aChat.isEmpty()) saveServerChat();
    }
    //endregion

    //region Logic
    private void saveServerChat() {
        mUser.getUData().setLastServerChat(System.currentTimeMillis());
        mUser.getUData().update("last_server_chat", System.currentTimeMillis());
    }

    private List<ChatObject> getChatHistory(List<ChatObject> aChat, long lastReqTime) {
        List<ChatObject> tmp = new ArrayList<ChatObject>();
        int count = 0;
        for (int i = aChat.size() - 1; i >= 0; i--) {
            if (aChat.get(i).getTime() > lastReqTime) {
                tmp.add(0, aChat.get(i));
                count++;
            }
            if (count >= 30) {
                break;
            }
        }
        return tmp;
    }

    private Pbmethod.ChatHistory protoListChat(List<ChatObject> aChat) {
        Pbmethod.ChatHistory.Builder builder = Pbmethod.ChatHistory.newBuilder();
        builder.setNumber(0);
        aChat.forEach(chat -> builder.addAChat(chat.toProto()));
        return builder.build();
    }
    //endregion
}
