package monster.game.chat;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.NotifyType;
import monster.controller.AHandler;
import monster.dao.mapping.UserEntity;
import monster.game.chat.config.ConfigLuckyMoney;
import monster.game.chat.dao.LuckyMoneyDAO;
import monster.game.chat.entity.UserLuckyMoney;
import monster.game.chat.entity.UserLuckyMoneyHistory;
import monster.game.chat.service.LuckyMoneyService;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.monitor.Telegram;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResLuckyMoney;
import monster.service.user.Actions;
import protocol.Pbmethod;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Lucky money
 */
public class LuckyMoneyHandler extends AHandler {
    LuckyMoneyService luckyMoneyService = Guice.getInstance(LuckyMoneyService.class);
    LuckyMoneyDAO luckyMoneyDAO = Guice.getInstance(LuckyMoneyDAO.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = List.of(LUCKY_MONEY_LIST_USER, LUCKY_MONEY_LIST_SYSTEM, LUCKY_MONEY_RECEIVE,
                LUCKY_MONEY_DETAIL, LUCKY_MONEY_DIARY, LUCKY_MONEY_GIVE_OUT);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        if (!FunctionType.LUCKY_MONEY.isEnable(mUser, this)) return;

        checkTimeMonitor("s");

        try {
            switch (actionId) {
                case LUCKY_MONEY_LIST_USER -> listUserLuckyMoney(); // done
                case LUCKY_MONEY_GIVE_OUT -> giveOut(); // done
                case LUCKY_MONEY_LIST_SYSTEM -> listSystemLuckyMoney(); // done
                case LUCKY_MONEY_DETAIL -> detail(); // done
                case LUCKY_MONEY_DIARY -> diary(); // done
                case LUCKY_MONEY_RECEIVE -> receive(); // done
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    // List lì xì mà user đang có nhưng chưa phát
    private void listUserLuckyMoney() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        List<UserLuckyMoney> userLuckyMonies = luckyMoneyService.getUserLuckyMoneyById(mUser);
        for (UserLuckyMoney luckyMoney : userLuckyMonies) {
            long secondsRemaining = luckyMoney.getTimeExpired().getTime() - luckyMoney.getTimeCreated().getTime();
            Calendar calendar = DateTime.getCalendar(luckyMoney.getTimeCreated(), Calendar.SECOND, (int) (secondsRemaining / 1000));
            long timeRemaining = Math.max(0, (calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000);
            if (timeRemaining == 0) {
                //                calculateValueLuckyMoney(luckyMoney.getId(),
                //                        ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getMaxSplit(),
                //                        ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getDescription(),
                //                        ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getGem(),
                //                        ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getId(), luckyMoney, true);
            } else builder.addAVector(luckyMoney.toProto());
        }
        addResponse(builder.build());
        if (userLuckyMonies.isEmpty()) NotifyType.LUCKY_MONEY_NOTIFY_GIVE_OUT.sendRemove(mUser);
    }

    /**
     * Chia nhỏ quả thành n phần và insert thẳng vào bảng : user_lucky_money_history -> user_id trống là chưa nhận
     * Chuyển trạng thái row của bảng user_lucky_money thành đang trao (set time_give_out && time_give_out_expired)
     * Cập nhật user_lucky_money.give_out_description
     */
    private void giveOut() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        List<String> aString = CommonProto.parseCommonVector(getRequestData()).getAStringList();
        long id = aLong.get(0).intValue();
        int numberSplit = aLong.get(1).intValue();
        String description = aString.get(0);
        UserLuckyMoney userLuckyMoney = luckyMoneyService.getUserLuckyMoney(id);
        if (userLuckyMoney == null || userLuckyMoney.getUserId() != user.getId()) {
            addErrResponse("Lì xì không hợp lệ");
            return;
        }
        if (userLuckyMoney.isTimeExpired()) {
            addErrResponse("Lì xì đã hết hạn");
            return;
        }
        if (userLuckyMoney.getTimeGiveOut() != null) {
            addErrResponse("Lì xì đã phát");
            return;
        }
        if (mUser.getUser().getClan() == 0 && userLuckyMoney.getLuckyType() == 1) {
            addErrResponse("Bạn cần tham gia gia tộc!");
            return;
        }
        int luckyId = userLuckyMoney.getResLuckyId();
        if (numberSplit > ResLuckyMoney.resLuckyMoneyEntity.get(luckyId).getMaxSplit() || numberSplit < ResLuckyMoney.resLuckyMoneyEntity.get(luckyId).getMinSplit()) {
            addErrResponse("Số lượng phát lì xì không hợp lệ");
            return;
        }
        long gemFromIdRes = ResLuckyMoney.resLuckyMoneyEntity.get(luckyId).getGem();
        // Phát lì xì
        calculateValueLuckyMoney(id, numberSplit, description, gemFromIdRes, luckyId, userLuckyMoney, false);
        addResponse(null);
    }

    /**
     * Lấy danh sách user_lucky_money theo type + đang trao + chưa hết hạn
     */
    private void listSystemLuckyMoney() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        List<UserLuckyMoney> systemLuckyMoney = luckyMoneyService.getSystemLuckyMoney(mUser, GsonUtil.toListInt(aLong));
        List<Long> luckyMoneyHistory = luckyMoneyDAO.getListReceivedLuckyPackage(user.getId(),
                        systemLuckyMoney.stream().map(UserLuckyMoney::getId).collect(Collectors.toList()))
                .stream().map(UserLuckyMoneyHistory::getLuckyId).collect(Collectors.toList());
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (UserLuckyMoney luckyMoney : systemLuckyMoney) {
            Pbmethod.CommonVector.Builder cm = Pbmethod.CommonVector.newBuilder();
            long count = luckyMoneyHistory.contains(luckyMoney.getId()) ? 1 : 0;
            cm.addALong(luckyMoney.getId());
            cm.addALong(luckyMoney.getLuckyType());
            if (luckyMoney.getTimeLuckyMoneyEnd() != null) {
                long timeTheEnd = luckyMoney.getTimeLuckyMoneyEnd().getTime() / 1000;
                long timeNow = System.currentTimeMillis() / 1000;
                long timeRemaining = timeNow - timeTheEnd;
                if (timeRemaining < ConfigLuckyMoney.config.getTimeReturnLuckyMoney()) {
                    infoUser(builder, luckyMoney, cm, count);
                }
            } else infoUser(builder, luckyMoney, cm, count);
        }
        addResponse(builder.build());
    }

    private void infoUser(Pbmethod.ListCommonVector.Builder builder, UserLuckyMoney luckyMoney, Pbmethod.CommonVector.Builder cm, long count) {
        cm.addALong(count > 0 ? 1 : 0); // 0: chưa nhận;  1: đã nhận
        cm.addALong(luckyMoney.isNumberFull() ? 2 : 1); // 2: hết hạn; 1: đang trao
        cm.addALong(luckyMoney.getTimeExpired().getTime());

        long secondsRemaining = luckyMoney.getTimeGiveOutExpired().getTime() - luckyMoney.getTimeGiveOut().getTime();
        Calendar calendar = DateTime.getCalendar(luckyMoney.getTimeGiveOut(), Calendar.SECOND, (int) (secondsRemaining / 1000));
        long timeRemainReceive = Math.max(0, (calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000);
        cm.addALong(timeRemainReceive);
        cm.addALong(ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getGem());
        cm.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getName());
        if (luckyMoney.getGiveOutDescription() == null || luckyMoney.getGiveOutDescription().isEmpty()) {
            cm.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getDescription());
        } else {
            cm.addAString(luckyMoney.getGiveOutDescription());
        }
        cm.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(luckyMoney.getResLuckyId()).getDescription());
        builder.addAVector(cm);
    }

    private void detail() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        long id = aLong.get(0);
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        // thông tin tổng quát
        UserLuckyMoney userLuckyMoney = luckyMoneyService.getUserLuckyMoney(id);
        if (userLuckyMoney == null) {
            addErrResponse();
            return;
        }
        Pbmethod.CommonVector.Builder cm = Pbmethod.CommonVector.newBuilder();
        cm.addALong(userLuckyMoney.getId());
        cm.addALong(userLuckyMoney.getLuckyType());
        cm.addALong(userLuckyMoney.getNumberReceive());
        cm.addALong(userLuckyMoney.getNumberSplit());
        cm.addALong(userLuckyMoney.getTimeGiveOutExpired().getTime());
        cm.addALong(ResLuckyMoney.resLuckyMoneyEntity.get(userLuckyMoney.getResLuckyId()).getGem());
        cm.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(userLuckyMoney.getResLuckyId()).getName());
        if (!userLuckyMoney.getGiveOutDescription().isEmpty()) {
            cm.addAString(userLuckyMoney.getGiveOutDescription());
        } else {
            cm.addAString(ResLuckyMoney.resLuckyMoneyEntity.get(userLuckyMoney.getResLuckyId()).getDescription());
        }
        builder.addAVector(cm);

        // Thông tin user phát lì xì
        Pbmethod.CommonVector.Builder userGiveLuckyMoney = Pbmethod.CommonVector.newBuilder();
        UserEntity userGiveOut = UserOnline.getDbUser(userLuckyMoney.getUserId());
        if (userGiveOut != null) {
            userGiveLuckyMoney.addALong(userGiveOut.getId());
            userGiveLuckyMoney.addALong(userGiveOut.getLevel());
            userGiveLuckyMoney.addALong(userGiveOut.getAvatar());
            userGiveLuckyMoney.addAString(userGiveOut.getName());
            builder.addAVector(userGiveLuckyMoney);
        }

        // thông tin user nhận lì xì
        List<UserLuckyMoneyHistory> userLuckyMoneyHistory = luckyMoneyService.getLuckyId(id);
        for (UserLuckyMoneyHistory luckyMoneyHistory : userLuckyMoneyHistory) {
            if (luckyMoneyHistory.getUserId() != 0) {
                Pbmethod.CommonVector.Builder userReceiveLuckyMoney = Pbmethod.CommonVector.newBuilder();
                userReceiveLuckyMoney.addALong(luckyMoneyHistory.getStatus());
                userReceiveLuckyMoney.addALong(luckyMoneyHistory.getGem());
                UserEntity userReceive = UserOnline.getDbUser(luckyMoneyHistory.getUserId());
                if (userReceive != null) {
                    userReceiveLuckyMoney.addAString(userReceive.getName());
                }
                builder.addAVector(userReceiveLuckyMoney);
            }
        }
        addResponse(builder.build());
    }

    /**
     * Lấy data từ bảng user_lucky_money_history theo user_id rồi trả cho user
     * thông tin giveOut thì lấy ở bảng user_lucky_money
     */
    private void diary() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();

        // get number receive
        List<UserLuckyMoneyHistory> luckyMoneyHistory = luckyMoneyService.getListUserReceivedLuckyMoneyHistory(mUser);
        long totalGem = luckyMoneyHistory.stream().mapToLong(UserLuckyMoneyHistory::getGem).sum();
        Pbmethod.CommonVector.Builder receive = Pbmethod.CommonVector.newBuilder();
        receive.addALong(luckyMoneyHistory.size());
        receive.addALong(totalGem);
        builder.addAVector(receive);

        // get number give out
        List<UserLuckyMoney> listUserLuckyMoney = luckyMoneyService.getAllLuckyMoneyOfUser(mUser);
        long totalGemGiveOut = 0;
        for (UserLuckyMoney userGiveOut : listUserLuckyMoney) {
            totalGemGiveOut += ResLuckyMoney.resLuckyMoneyEntity.get(userGiveOut.getResLuckyId()).getGem();
        }

        Pbmethod.CommonVector.Builder giveOut = Pbmethod.CommonVector.newBuilder();
        giveOut.addALong(listUserLuckyMoney.size());
        giveOut.addALong(totalGemGiveOut);
        builder.addAVector(giveOut);

        // list history
        Pbmethod.CommonVector.Builder listHistory = Pbmethod.CommonVector.newBuilder();
        for (UserLuckyMoneyHistory moneyHistory : luckyMoneyHistory) {
            if (moneyHistory.getUserId() == mUser.getUser().getId()) {
                Date dateConvert = moneyHistory.getTimeReceive();
                listHistory.addALong(dateConvert.getTime());
                listHistory.addALong(1);
                listHistory.addALong(moneyHistory.getGem());
            }
        }
        long totalGemGiveOutHistory = 0;
        for (UserLuckyMoney userGiveOut : listUserLuckyMoney) {
            if (userGiveOut.getUserId() == mUser.getUser().getId()) {
                Date instant = userGiveOut.getTimeGiveOut();
                listHistory.addALong(instant.getTime());
                listHistory.addALong(2);
                totalGemGiveOutHistory = ResLuckyMoney.resLuckyMoneyEntity.get(userGiveOut.getResLuckyId()).getGem();
                listHistory.addALong(totalGemGiveOutHistory);
            }
        }

        builder.addAVector(listHistory);
        addResponse(builder.build());
    }

    /**
     * input: gửi lên id của bang user_lucky_money
     * Lấy ngẫu nhiên phần thưởng đã chia ở bảng user_money_history có lucky_id tương ứng
     * Set lại user_id theo user_money_history ngẫu nhieên trên và + tiền cho user
     */
    private void receive() {
        List<Long> aLong = CommonProto.parseCommonVector(getRequestData()).getALongList();
        long luckyId = aLong.get(0);
        luckyMoneyService.receive(mUser, this, luckyId);
    }

    private void calculateValueLuckyMoney(long id, int numberSplit, String description, long gemFromIdRes, int luckyId, UserLuckyMoney userLuckyMoney, boolean auto) {
        List<Long> valueGems = getValueGems(numberSplit, gemFromIdRes, id, userLuckyMoney.getUserId());

        // Lưu lịch sử chia lucky money
        List<UserLuckyMoneyHistory> luckyMoneyHistoriesAdd = new ArrayList<>();
        for (Long dataGems : valueGems) {
            UserLuckyMoneyHistory userLuckyMoneyHistory = new UserLuckyMoneyHistory();
            userLuckyMoneyHistory.setResLuckyId(userLuckyMoney.getResLuckyId());
            userLuckyMoneyHistory.setGem(dataGems);
            if (dataGems.equals(Collections.max(valueGems))) {
                userLuckyMoneyHistory.setStatus(1);
            } else if (dataGems.equals(Collections.min(valueGems))) {
                userLuckyMoneyHistory.setStatus(0);
            } else {
                userLuckyMoneyHistory.setStatus(2);
            }
            userLuckyMoneyHistory.setUserId(0);
            userLuckyMoneyHistory.setLuckyId(Math.toIntExact(userLuckyMoney.getId()));
            userLuckyMoneyHistory.setTimeCreated(Instant.now());
            luckyMoneyHistoriesAdd.add(userLuckyMoneyHistory);
        }

        DBJPA.save(luckyMoneyHistoriesAdd.toArray());

        // Cập nhật thông tin lucky money
        Date expiryDate = new Date(System.currentTimeMillis() + (ResLuckyMoney.resLuckyMoneyEntity.get(luckyId).getTimeGiveOutExpire() * 86400L * 1000));
        if (dbUpdateLuckyMoneyGiveOut(id, numberSplit, description, (ResLuckyMoney.resLuckyMoneyEntity.get(luckyId).getTimeGiveOutExpire() * 86400))) {
            userLuckyMoney.setClanId(mUser.getUser().getClan());
            userLuckyMoney.setNumberSplit(numberSplit);
            userLuckyMoney.setGiveOutDescription(description);
            userLuckyMoney.setTimeGiveOut(new Date());
            userLuckyMoney.setTimeGiveOutExpired(expiryDate);
        }

        Actions.save(user, "lucky_money", "give_out" + (auto ? "_auto" : ""), "id", userLuckyMoney.getId(), "numberSplit", numberSplit, "expiryDate", DateTime.getFullDate(expiryDate),
                "clan",
                user.getClan());
    }

    public static List<Long> getValueGems(int numberSplit, long gemFromIdRes, long luckyId, int userId) {

        long averageValue = gemFromIdRes / numberSplit;
        long minValue = Math.max(1, (long) (averageValue * 0.2));
        long maxValue = Math.max(minValue + 1, (long) (averageValue * 1.8));

        List<Long> valueGems = new ArrayList<>();
        Random random = new Random();
        long sumWithdraw = 0;

        for (int i = 0; i < numberSplit; i++) {
            long remaining = gemFromIdRes - sumWithdraw;
            long maxWithdraw = Math.min(maxValue, remaining);
            long minWithdraw = Math.min(minValue, remaining);
            long moneyWithdraw;

            if (maxWithdraw <= 1) {
                moneyWithdraw = 1;
            } else {
                moneyWithdraw = random.nextLong(maxWithdraw - minWithdraw + 1) + minWithdraw;
            }
            if (moneyWithdraw <= 0) {
                Telegram.sendError("Lỗi xì xì phát ra có số âm của user: " + userId + " luckyId: " + luckyId);
            }
            valueGems.add(moneyWithdraw);
            sumWithdraw += moneyWithdraw;

        }

        // Kiểm tra tổng có đúng bằng gemFromIdRes không
        long diff = gemFromIdRes - sumWithdraw;
        if (diff != 0) {
            int lastIndex = valueGems.size() - 1;
            valueGems.set(lastIndex, valueGems.get(lastIndex) + diff);
        }

        return valueGems;
    }


    public boolean dbUpdateLuckyMoneyGiveOut(long id, long numberSplit, String description, long secondsAdd) {
        LocalDateTime localDateTime = LocalDateTime.now().plusSeconds(secondsAdd);
        Date timeGiveOutExpired = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_lucky_money set clan_id=:clanId, number_split=:numberSplit, give_out_description=:description, time_give_out=CURRENT_TIMESTAMP, time_give_out_expired=:timeGiveOutExpired where id=:id");
            query.setParameter("numberSplit", numberSplit);
            query.setParameter("description", description);
            query.setParameter("timeGiveOutExpired", timeGiveOutExpired);
            query.setParameter("id", id);
            query.setParameter("clanId", mUser.getUser().getClan());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }


}
