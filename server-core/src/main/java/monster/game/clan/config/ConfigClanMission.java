package monster.game.clan.config;

import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import monster.config.CfgServer;
import monster.game.clan.entity.ResClanMission;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ConfigClanMission {

    public static Map<Integer, ResClanMission> mMission;
    public static long maxPoint;
    public static List<Long> pointBonus;
    public static int pointPerMission = 10;
    public static Date fromTime;
    public static long timeEvent = 7 * DateTime.DAY_MILLI_SECOND;

    public static int getEventId() {
        if (CfgServer.isTestServer()) return 1;
        return (int) ((System.currentTimeMillis() - fromTime.getTime()) / timeEvent) + 1;
    }

    public static int countdownToNextEvent() {
        return (int) (fromTime.getTime() + getEventId() * timeEvent - System.currentTimeMillis()) / 1000;
    }

    public static void setConfigMain(String value) {
        JsonObject obj = GsonUtil.parseJsonObject(value);
        maxPoint = obj.get("maxPoint").getAsInt();
        pointBonus = GsonUtil.strToListLong(obj.get("bonus").getAsJsonArray().toString());

        List<ResClanMission> missions = DBJPA.getList(CfgServer.DB_MAIN + "res_clan_mission", ResClanMission.class);
        mMission = missions.stream().collect(Collectors.toMap(ResClanMission::getType, Function.identity()));

        try {
            fromTime = DateTime.getSDFFullDate().parse(obj.get("fromTime").getAsString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
