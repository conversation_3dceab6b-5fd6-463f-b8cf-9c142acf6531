package monster.game.clan.config;

import grep.helper.NumberUtil;
import monster.game.clan.entity.ClanBoss;
import monster.game.clan.entity.UserClanEntity;

import java.util.List;

public class ConfigClanBoss {

    public static DataConfig config;
    public static int numberBuyAttack = 1;

    public static int userBoxYellow(ClanBoss clanBoss, UserClanEntity userClan) {
        int numberUnReceived = clanBoss.getBoxYellow() - userClan.getBossBoxYellow();
        if (numberUnReceived < 0) return 0;
        return Math.min(numberUnReceived, config.numberOpenYellowBox - userClan.getBossBoxYellow());
    }

    public static int userBoxRed(ClanBoss clanBoss, UserClanEntity userClan) {
        int numberUnReceived = clanBoss.getBoxRed() - userClan.getBossBoxRed();
        if (numberUnReceived < 0) return 0;
        return Math.min(numberUnReceived, config.numberOpenRedBox - userClan.getBossBoxRed());
    }

    public static int getNumberRedBox(long totalDamage) {
        int numberNormalBox = getNumberNormalBox(totalDamage);
        int count = 0;
        for (Integer value : List.of(50, 100)) {
            if (numberNormalBox >= value && NumberUtil.isHitRandom(config.rateRed)) {
                count++;
            }
        }
        return count;
    }

    public static int getNumberYellowBox(long totalDamage) {
        int numberNormalBox = getNumberNormalBox(totalDamage);
        int count = 0;
        for (Integer value : List.of(20, 40, 60, 80)) {
            if (numberNormalBox >= value && NumberUtil.isHitRandom(config.rateYellow)) {
                count++;
            }
        }
        return count;
    }

    public static int getNumberNormalBox(long totalDamage) {
        for (int i = config.levelDamage.size() - 1; i >= 0; i--) {
            if (totalDamage >= config.levelDamage.get(i)) return i + 1;
        }
        return 0;
    }

    public static int getFeeBuyAttack(UserClanEntity userClan) {
        if (userClan.getBossAttack() >= config.numberFreeAtk + numberBuyAttack) return -1;
        if (userClan.getBossAttack() >= config.numberFreeAtk) return config.feeBuyAtk;
        return 0;
    }

    public static int getFreeAttack(UserClanEntity userClan) {
        return config.numberFreeAtk - userClan.getBossAttack() < 0 ? 0 : config.numberFreeAtk - userClan.getBossAttack();
    }

    public static void init(String value) {
        System.out.println("config = " + config.bossId);
    }

    public class DataConfig {
        public int numberOpenRedBox, numberOpenYellowBox;
        public int numberFreeAtk, feeBuyAtk;
        public List<Integer> bossId;
        public List<Long> levelDamage;
        public int rateRed, rateYellow;
    }

}
