package monster.game.chess.service;

import monster.game.chess.entity.UserChessMasterEntity;
import monster.object.Position;
import monster.object.MyUser;
import monster.object.ServiceResult;
import protocol.Pbmethod;

import java.util.List;

public interface ChessService {

    UserChessMasterEntity getUserChess(MyUser mUser);
    Integer getEventId(MyUser mUser);
    boolean startMatch(MyUser mUser);
    ServiceResult<Pbmethod.ListCommonVector> makeAMove(MyUser mUser, Position position, boolean useSkill);
    ServiceResult<Pbmethod.CommonVector> revive(MyUser mUser);
    ServiceResult<List<Long>> endGame(MyUser mUser);
    ServiceResult<Pbmethod.ListCommonVector> upgradeStatus(MyUser mUser);
    ServiceResult<List<Long>> upgrade(MyUser mUser, int id);
}
