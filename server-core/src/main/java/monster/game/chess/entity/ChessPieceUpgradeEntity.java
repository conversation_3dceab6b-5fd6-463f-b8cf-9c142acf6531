package monster.game.chess.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;

@Getter
@Entity
@Table(name = "chess_piece_spawn", catalog = "dson_main")
@NoArgsConstructor
public class ChessPieceUpgradeEntity implements Serializable {
    @Id
    int chessId, level;
    int fee, number;
    String title, desc;
}
