package monster.game.chess.entity;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.game.chess.config.ConfigChess;
import monster.game.chess.pieces.ChessMatch;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_chess_master", catalog = "dson")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserChessMasterEntity implements Serializable {
    @Id
    int userId, eventId;
    int dateKey;
    int point, highestScore;
    int gameStatus;
    int numberTurn, numberRevive, numberSkill;
    int myChessColumn, myChessRow, skillExplode;
    String chessBoard, upgradePiece;
    int freePlay;

    @Transient
    ChessMatch chessMatch;

    public void setPoint(int newPoint) {
        point = newPoint;
    }

    public Pbmethod.CommonVector toProto() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(gameStatus);
        builder.addALong(numberTurn).addALong(point).addALong(highestScore);
        builder.addALong(numberSkill).addALong(numberRevive);
        builder.addALong(skillExplode); // có skill nổ hay không
        builder.addALong(freePlay).addALong(getNumberHint());
        return builder.build();
    }

    public ChessMatch getChessMatch() {
        if (chessMatch == null) {
            chessMatch = ChessMatch.parseFromString(this, chessBoard);
        }
        return chessMatch;
    }

    public List<Integer> getListUpgradeStatus() {
        if (StringHelper.isEmpty(upgradePiece)) upgradePiece = "[]";
        List<Integer> values = GsonUtil.strToListInt(upgradePiece);
        while (values.size() < ConfigChess.upgradeIndex.size()) values.add(0);
        return values;
    }

    public int getNumberHint() {
        int id = 1000;
        int index = ConfigChess.upgradeIndex.indexOf(id);
        int curLevel = getListUpgradeStatus().get(index);
        return ListUtil.getElement(ConfigChess.mPieceUpgrade.get(id), curLevel).getNumber();
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_chess_master", updateData, Arrays.asList("user_id", userId, "event_id", eventId));
    }
}