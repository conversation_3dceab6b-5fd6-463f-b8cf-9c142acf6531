package monster.game.chess.pieces;

import monster.object.Position;

import java.util.ArrayList;
import java.util.List;

/**
 * Tốt
 */
public class Pawn extends ChessPiece {
    public static final int DEFAULT_ID = 1;

    public Pawn(Board board, Color color, ChessMatch chessMatch) {
        super(board, color, chessMatch);
        this.chessMatch = chessMatch;
    }

    @Override
    public List<Position> possibleMoves() {
        List<Position> validPosition = new ArrayList<>();
        var checkPosition = List.of(position.cloneWith(0, 1), position.cloneWith(0, -1),
                position.cloneWith(1, 0), position.cloneWith(-1, 0));
        for (Position pos : checkPosition) {
            if (getBoard().positionExists(pos)) {
                var targetPiece = getBoard().piece(pos);
                if (targetPiece == null) validPosition.add(pos);
            }
        }
        return validPosition;
    }

    @Override
    public boolean couldKillPlayer(Position playerPos) {
        int rowDiff = Math.abs(playerPos.getRow() - position.getRow());
        int colDiff = Math.abs(playerPos.getColumn() - position.getColumn());

        // Check if the king and player are in the same row or column
        if (rowDiff == 1 && colDiff == 1) {
            return true;
        }

        return false;
    }

    @Override
    public String toString() {
        return "P " + position;
    }

}
