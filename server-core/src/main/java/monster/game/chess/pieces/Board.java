package monster.game.chess.pieces;

import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.log.Logs;
import lombok.Getter;
import monster.object.Position;

import java.util.ArrayList;
import java.util.List;

@Getter
public class Board {

    private int rows;
    private int columns;
    private ChessPiece[][] pieces;

    public Board(int rows, int columns) {
        if (rows < 1 || columns < 1) {

        }
        this.rows = rows;
        this.columns = columns;
        pieces = new ChessPiece[rows][columns];
    }

    public int getRows() {
        return rows;
    }

    public int getColumns() {
        return columns;
    }

    public ChessPiece piece(int row, int column) {
        if (!positionExists(row, column)) {

        }
        return pieces[row][column];
    }

    public ChessPiece piece(Position position) {
        if (!positionExists(position)) {

        }
        return (ChessPiece) pieces[position.getRow()][position.getColumn()];
    }

    public void placePiece(ChessPiece piece, Position position) {
        if (thereIsAPiece(position)) {
            Logs.warn("chess placePiece " + piece.toString() + " " + position.toString());
        }
        pieces[position.getRow()][position.getColumn()] = piece;
        piece.position = position;
    }

    public ChessPiece removePiece(Position position) {
        if (!positionExists(position)) {

        }
        if (piece(position) == null) {
            return null;
        }
        ChessPiece aux = piece(position);
        //        aux.position = null;
        pieces[position.getRow()][position.getColumn()] = null;
        return aux;
    }

    private boolean positionExists(int row, int column) {
        return row >= 0 && row < rows && column >= 0 && column < columns;
    }

    public boolean positionExists(Position position) {
        return positionExists(position.getRow(), position.getColumn());
    }

    public boolean thereIsAPiece(Position position) {
        if (!positionExists(position)) {

        }
        return piece(position) != null;
    }

    public void placePieceRandom(ChessPiece piece) {
        int row = NumberUtil.getRandom(rows), col = NumberUtil.getRandom(columns);
        while (piece(row, col) != null) {
            row = NumberUtil.getRandom(rows);
            col = NumberUtil.getRandom(columns);
        }
        placePiece(piece, new Position(row, col));
    }

    public List<ChessPiece> getComputerPiece() {
        List<ChessPiece> computerPiece = new ArrayList<>();
        for (int row = 0; row < pieces.length; row++) {
            for (int col = 0; col < pieces[row].length; col++) {
                if (pieces[row][col] != null && (pieces[row][col]).getColor() == Color.BLACK) {
                    computerPiece.add(pieces[row][col]);
                }
            }
        }
        return computerPiece;
    }

    public ChessPiece getPlayerPiece() {
        for (int row = 0; row < pieces.length; row++) {
            for (int col = 0; col < pieces[row].length; col++) {
                if (pieces[row][col] != null && (pieces[row][col]).getColor() == Color.WHITE) {
                    return pieces[row][col];
                }
            }
        }
        return null;
    }

    @Override
    public String toString() {
        int[][] data = new int[rows][columns];
        for (int row = 0; row < pieces.length; row++) {
            for (int col = 0; col < pieces[row].length; col++) {
                data[row][col] = pieces[row][col] == null ? 0 : pieces[row][col].id;
            }
        }
        return GsonUtil.toJson(data);
    }
}