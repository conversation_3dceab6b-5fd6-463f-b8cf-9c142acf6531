package monster.game.chess.pieces;

import monster.object.Position;

import java.util.ArrayList;
import java.util.List;

/**
 * Tượng
 */
public class BiShop extends ChessPiece {
    public static final int DEFAULT_ID = 3;

    public BiShop(Board board, Color color, ChessMatch chessMatch) {
        super(board, color, chessMatch);
        this.chessMatch = chessMatch;
    }

    @Override
    public String toString() {
        return "B " + position;
    }

    @Override
    public List<Position> possibleMoves() {
        List<Position> validPosition = new ArrayList<>();

        Position p = new Position(0, 0);

        // nw
        p.setValues(position.getRow() - 1, position.getColumn() - 1);
        while (getBoard().positionExists(p) && !getBoard().thereIsAPiece(p)) {
            validPosition.add(p.clone());
            p.setValues(p.getRow() - 1, p.getColumn() - 1);
        }
        if (getBoard().positionExists(p) && isThereOpponentPiece(p)) {
            validPosition.add(p.clone());
        }

        // ne
        p.setValues(position.getRow() - 1, position.getColumn() + 1);
        while (getBoard().positionExists(p) && !getBoard().thereIsAPiece(p)) {
            validPosition.add(p.clone());
            p.setValues(p.getRow() - 1, p.getColumn() + 1);
        }
        if (getBoard().positionExists(p) && isThereOpponentPiece(p)) {
            validPosition.add(p.clone());
        }

        // se
        p.setValues(position.getRow() + 1, position.getColumn() + 1);
        while (getBoard().positionExists(p) && !getBoard().thereIsAPiece(p)) {
            validPosition.add(p.clone());
            p.setValues(p.getRow() + 1, p.getColumn() + 1);
        }
        if (getBoard().positionExists(p) && isThereOpponentPiece(p)) {
            validPosition.add(p.clone());
        }

        // sw
        p.setValues(position.getRow() + 1, position.getColumn() - 1);
        while (getBoard().positionExists(p) && !getBoard().thereIsAPiece(p)) {
            validPosition.add(p.clone());
            p.setValues(p.getRow() + 1, p.getColumn() - 1);
        }
        if (getBoard().positionExists(p) && isThereOpponentPiece(p)) {
            validPosition.add(p.clone());
        }

        return validPosition;
    }

    @Override
    public boolean couldKillPlayer(Position playerPos) {
        int dRow = Math.abs(playerPos.getRow() - position.getRow());
        int dColumn = Math.abs(playerPos.getColumn() - position.getColumn());

        // Check if the bishop and player are on the same diagonal
        if (dRow != dColumn) {
            return false;
        }

        // Iterate over the diagonal line from the bishop to the player
        int row = position.getRow();
        int column = position.getColumn();
        int stepX = playerPos.getRow() > position.getRow() ? 1 : -1;
        int stepY = playerPos.getColumn() > position.getColumn() ? 1 : -1;

        row += stepX;
        column += stepY;
        while (row != playerPos.getRow() || column != playerPos.getColumn()) {
            // Check if there is a piece on the square that can block the attack
            if (getBoard().thereIsAPiece(new Position(row, column))) {
                return false;
            }
            row += stepX;
            column += stepY;
        }

        return true;
    }
}
