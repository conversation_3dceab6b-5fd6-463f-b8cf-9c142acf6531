package monster.game.dice.service.impl;

import monster.dao.mapping.ConfigEvent;
import monster.game.dice.service.DiceService;
import monster.game.truongevent.config.TruongConfig;
import monster.game.truongevent.service.TruongProEventService;
import monster.object.MyUser;

import javax.inject.Inject;

public class DiceServiceImpl implements DiceService {
    @Inject
    TruongProEventService truongProEventService;

    @Override
    public ConfigEvent getConfigEvent(MyUser mUser) {
        return truongProEventService.getEventByTemplate(TruongConfig.EVENT_TEMPLATE_DICE, mUser.getUser().getServer());
    }

    @Override
    public Integer getEventId(MyUser mUser) {
        ConfigEvent configEvent = getConfigEvent(mUser);
        return configEvent == null ? null : configEvent.getId();
    }
}
