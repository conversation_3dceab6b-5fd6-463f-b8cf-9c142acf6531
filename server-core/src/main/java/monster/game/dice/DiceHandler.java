package monster.game.dice;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.CfgDice;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.FunctionType;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.dao.mapping.UserDiceEntity;
import monster.dao.mapping.UserMailEntity;
import monster.protocol.CommonProto;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.task.dbcache.MailCreatorCache;
import protocol.Pbmethod;

import java.text.ParseException;
import java.util.*;

public class DiceHandler extends AHandler {
    public String[] args;
    public UserDiceEntity uDice;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(DICE_GO, DICE_STATUS, DICE_OPEN_CARD, DICE_RECEIVE_REWARD, DICE_VIEW_STAR_REWARD);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public AHandler newInstance() {
        return new DiceHandler();
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");


        if (!FunctionType.DICE.isEnable(mUser, this)) {
            addErrResponse(getLang(Lang.user_function_closed));
            return;
        }

        uDice = mUser.getCache().getUDice(this, mUser);
        if (uDice == null) {
            addErrResponse();
            return;
        }

        try {
            switch (actionId) {
                case DICE_STATUS:
                    status();
                    break;
                case DICE_GO:
                    go();
                    break;
                case DICE_OPEN_CARD:
                    openCard();
                    break;
                case DICE_RECEIVE_REWARD:
                    receiveReward();
                    break;
                case DICE_VIEW_STAR_REWARD:
                    viewStarReward();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handler service
    void status() throws ParseException {
        protocol.Pbmethod.ListCommonVector.Builder builder = protocol.Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(protocol.Pbmethod.CommonVector.newBuilder().addAllALong(uDice.getInfo(mUser))
                .addALong(uDice.isFreeDice() ? 1 : 0).addALong(uDice.getPoint()).addALong(CfgDice.config.getMaxPoint()));
        builder.addAVector(getCommonVector(CfgDice.config.getFeeBuyDice()));
        builder.addAVector(getCommonVector(CfgDice.config.getPointReward()));
        builder.addAVector(getCommonVector(GsonUtil.strToListLong(uDice.getLevel())));
        uDice.getCurData().forEach(aLong -> builder.addAVector(getCommonVector(aLong)));
        addResponse(builder.build());
    }

    void go() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        boolean isLucky = aLong.size() != 0; //Nếu không có input thì là xx thường
        int score, pointDice;
        List<Long> aBonus = new ArrayList<>();

        //Check max vòng
        if (uDice.getLapCount() > CfgDice.config.getMaxLap()) { //uDice.getLapCount() > CfgDice.config.getMaxLap()
            addErrResponse(getLang(Lang.dice_max_round));
            return;
        }

        //Nếu là xx thường thì random pointDice - điểm xx
        pointDice = isLucky ? aLong.get(0).intValue() : getRandomByRate(CfgDice.config.getRate());
        //        pointDice = isLucky ? aLong.get(0).intValue() : new Random().nextInt(6) + 1;
        boolean isGoBack = uDice.getSpecialEffect() == 5 || (uDice.getSpecialEffect() == 4 && pointDice % 2 == 1);

        //Tính lại score theo skill
        score = scoreAfterEffect(pointDice);

        //VỊ trí mới
        boolean isNewRound = uDice.getPosition() + score > CfgDice.config.getNumberStep();
        int newPosition = uDice.getNewPosition(score);

        //Level chưa thay đổi
        String newLevel = uDice.getLevel();

        //Nếu ko đi lùi và ô đích đến ko phải ô trap thì lấy level mới đã tăng
        if (!isGoBack && !CfgDice.isTrap(newPosition))
            newLevel = uDice.getNewLevelStr(newPosition, uDice.getNewLevel(newPosition, 1));

        //Lấy bonusStar
        List<Long> bonusStar = uDice.getBonusStar(newPosition, isGoBack, user);

        //Nếu lỗi khi update db
        if (bonusStar == null) {
            addErrResponse();
            return;
        }

        //Lấy data mới của ô đích
        List<Long> newData = uDice.getNewData(newPosition, uDice.getNewLevel(newPosition, 1));
        //Lấy data cũ của ô đích
        List<Long> oldData = uDice.getNewData(newPosition, uDice.getNewLevel(newPosition, 0));

        //Check số xúc xắc
        JsonArray arrConvertPrice = isLucky ? GsonUtil.parseFromListLong(Bonus.viewMaterial(MaterialType.DICE_LUCKY, 1)) :
                GsonUtil.parseFromListLong(Bonus.viewMaterial(MaterialType.DICE, 1));
        if (!mUser.checkPrice(this, arrConvertPrice)) {
            addErrResponse(getLang(Lang.dice_not_enough_dice));
            return;
        }

        //Trừ xúc xắc
        aBonus = Bonus.receiveListItem(mUser, arrConvertPrice, "dice_go");
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        //Update db, trả lại xx nếu update fail
        if (!dbUpdateUDice(newPosition, uDice.getNumberStar(), uDice.getLastStarPosition(), newLevel)) {
            arrConvertPrice = isLucky ? GsonUtil.parseFromListLong(Bonus.viewMaterial(MaterialType.DICE_LUCKY, 1)) :
                    GsonUtil.parseFromListLong(Bonus.viewMaterial(MaterialType.DICE, 1));
            Bonus.receiveListItem(mUser, arrConvertPrice, "dice_go_fail");
            addErrResponse();
            return;
        }

        //Log
        Actions.save(user, "dice", "go", "id", user.getId(), "bonus", aBonus);

        //Update cache
        uDice.updateUDice(newPosition, newLevel, uDice.getNumberStar());

        //Trao quà
        boolean isFinishRound = uDice.isFinishRound();
//        uDice.sendReward(this);

        long numberDice = mUser.getResources().getMaterial(MaterialType.DICE).getNumber();
        long numberDiceLucky = mUser.getResources().getMaterial(MaterialType.DICE_LUCKY).getNumber();
        int newPoint = uDice.getPoint();
        List<Long> rewardBonus = new ArrayList<>();
//        if (CfgDice.isPoint(newPosition)) {
//            newPoint += CfgDice.config.getStepPoint();
//            if (newPoint >= CfgDice.config.getMaxPoint()) {
//                rewardBonus = Bonus.receiveListItem(mUser, "dice_reward", CfgDice.config.getPointReward());
//                newPoint -= CfgDice.config.getMaxPoint();
//            }
//        }

        //Client
        protocol.Pbmethod.ListCommonVector.Builder builder = protocol.Pbmethod.ListCommonVector.newBuilder();
        //Trả về điểm, vị trí mới, số xx, xx lucky còn lại
        Pbmethod.CommonVector.Builder commonVector = Pbmethod.CommonVector.newBuilder();
        commonVector.addALong(pointDice).addALong(score).addALong(newPosition).addALong(numberDice)
                .addALong(numberDiceLucky).addALong(isFinishRound ? 1 : 0).addALong(uDice.getLapCount()).addALong(uDice.getNumberStar()).addALong(newPoint);
        if (!rewardBonus.isEmpty()) commonVector.addAllALong(rewardBonus);
        builder.addAVector(commonVector);

        Actions.save(user, "dice", "go", "id", user.getId(), "number_dice", numberDice, "number_dice_lucky", numberDiceLucky, "position", newPosition, "num_star", uDice.getNumberStar(), "point", newPoint, "reward", rewardBonus);
        if (isNewRound) EventType.DICE_FINISH_ROUND.addEvent(mUser, 1);
        if (isGoBack || CfgDice.isPoint(newPosition) || CfgDice.isEmpty(newPosition)) { // Nếu đi lùi hoặc ô đích là ô tăng point thì chỉ trả đến đây
            //Update db
            if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0, "point", newPoint), Arrays.asList("user_id", user.getId()))) {
                addErrResponse();
                return;
            }

            //Update cache
            uDice.setSpecialEffect(0);
            uDice.setPoint(newPoint);

            addResponse(builder.build());
            return;
        }

        //Trả về trạng thái của ô đích đến
        builder.addAVector(protocol.Pbmethod.CommonVector.newBuilder().addAllALong(newData));

        //Trả về sao được tặng nếu có
        if (bonusStar.get(bonusStar.size() - 2) != 0) builder.addAVector(getCommonVector(bonusStar));

        if (CfgDice.isStar(newPosition)) { //Ô đích là sao
            //Update db
            if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                addErrResponse();
                return;
            }

            //Update cache
            uDice.setSpecialEffect(0);

            addResponse(builder.build());
            return;
        }

        if (CfgDice.isMaterial(newPosition)) { //Ô đích là tài nguyên (không phải sao)
            if (uDice.getSpecialEffect() == 8) { //Effect không nhận tài nguyên ở lần tới
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);

                addResponse(builder.build());
                return;
            }

            aBonus.clear();
            aBonus = CfgDice.toBonusView(oldData);
            if (aBonus.isEmpty()) {
                addErrResponse();
                return;
            }

            //Update db
            if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                addErrResponse();
                return;
            }

            //Update cache
            uDice.setSpecialEffect(0);

            int materialId = aBonus.get(aBonus.size() - 2).intValue();
            int numberBonus = aBonus.get(aBonus.size() - 1).intValue();
            aBonus = Bonus.receiveListItem(mUser, "dice_go_material", aBonus);

            if (aBonus.isEmpty()) {
                addErrResponse();
                return;
            }

            int curNumber = aBonus.get(aBonus.size() - 2).intValue();

            //Client
            builder.addAVector(protocol.Pbmethod.CommonVector.newBuilder().addALong(1).addALong(materialId).addALong(numberBonus).addALong(curNumber));
            addResponse(builder.build());

            //Log
            Actions.save(user, "dice", "go", "id", user.getId(), "material_bonus", aBonus, "cur_number", curNumber);
            return;
        }

        //ô đích là trap
        switch (newData.get(1).intValue()) {
            case 5: // trap thêm xx
                aBonus.clear();
                //Cộng xx thưuòng
                aBonus = Bonus.receiveListItem(mUser, "dice_open_card", Bonus.viewMaterial(MaterialType.DICE, 1));
                if (aBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }

                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);

                //Client
                builder.addAVector(protocol.Pbmethod.CommonVector.newBuilder().addALong(5).addALong(1).addALong(aBonus.get(aBonus.size() - 2)));

                //Log
                Actions.save(user, "dice", "go", "id", user.getId(), "bonus", aBonus);
                addResponse(builder.build());
                return;

            case 6: //trap thêm xx lucky
                aBonus.clear();
                //Cộng xx lucky
                aBonus = Bonus.receiveListItem(mUser, "dice_open_card", Bonus.viewMaterial(MaterialType.DICE_LUCKY, 1));
                if (aBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }

                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);

                //Client
                builder.addAVector(protocol.Pbmethod.CommonVector.newBuilder().addALong(6).addALong(1).addALong(aBonus.get(aBonus.size() - 2)));

                //Log
                Actions.save(user, "dice", "go", "id", user.getId(), "bonus", aBonus);
                addResponse(builder.build());
                return;
            case 3: //trap tarot
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 3), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(3);

                //Client
                builder.addAVector(getCommonVector(3L));
                addResponse(builder.build());
                return;

            case 4: //trap choáng
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 4), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(4);

                //Client
                builder.addAVector(getCommonVector(4L));
                addResponse(builder.build());
                return;
            default:
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);
        }

        //Client
        addResponse(builder.build());
    }

    void openCard() {
        Random rd = new Random();
        List<Long> listEffect = new ArrayList<>(Arrays.asList(1L, 2L, 5L, 6L, 7L, 8L, 9L));
        if (uDice.isAllMaxLevel()) listEffect.remove(6L);
        if (uDice.isAllLevel1()) listEffect.remove(9L);
        int effect = listEffect.get(rd.nextInt(listEffect.size())).intValue();

        switch (effect) {
            case 1: //Tài nguyên
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);

                //Random tài nguyên
                int position = rd.nextInt(CfgDice.config.getNumberStep()) + 1;
                while (!CfgDice.isMaterial(position)) {
                    position = rd.nextInt(CfgDice.config.getNumberStep()) + 1;
                }
                List<Long> bonusView = CfgDice.toBonusView(CfgDice.getData(position, uDice.getLevelInt(position)));
                if (bonusView.isEmpty()) {
                    addErrResponse();
                    return;
                }

                int materialId = bonusView.get(2).intValue();
                int numberBonus = bonusView.get(3).intValue();
                List<Long> aBonus = Bonus.receiveListItem(mUser, "dice_open_card_mat", bonusView);

                if (aBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }

                int curNumber = aBonus.get(2).intValue();


                //Client
                addResponse(protocol.Pbmethod.CommonVector.newBuilder().addALong(1).addALong(materialId).addALong(numberBonus).addALong(curNumber).build());

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "material", aBonus, "cur_number", curNumber);
                break;

            case 2: //x2
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 2), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(2);

                //Client
                addResponse(getCommonVector(2L));

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "special_effect", 2);
                break;

            case 5:  //đi lùi
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 5), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(5);

                //Client
                addResponse(getCommonVector(5L));

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "special_effect", 5);
                break;

            case 6: //Tăng 1 level của ô bất kì
                //Lấy list position của các ô không max level
                List<Integer> validPosition = new ArrayList<>();
                for (int i = 1; i <= CfgDice.config.getNumberStep(); i++) {
                    if (!CfgDice.isTrap(i) && !CfgDice.isPoint(i) && !CfgDice.isEmpty(i) && uDice.getLevelInt(i) < CfgDice.getMaxLevel())
                        validPosition.add(i);
                }
                if (validPosition.isEmpty()) position = CfgDice.getPositionMaterial().get(0);
                else position = validPosition.get(rd.nextInt(validPosition.size()));

                //Level mới
                String newLevel = uDice.getNewLevelStr(position, uDice.getLevelInt(position) + 1);

                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("level", newLevel, "special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);
                uDice.setLevel(newLevel);

                //Client
                addResponse(protocol.Pbmethod.CommonVector.newBuilder().addALong(6).addALong(position).addALong(1).addALong(uDice.getLevelInt(position)).build());

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "special_effect", 6);
                break;

            case 7: //Trở về xuất phát
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("position", 0, "special_effect", 0, "last_star_position", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);
                uDice.setPosition(0);
                uDice.setLastStarPosition(0);

                //Client
                addResponse(getCommonVector(7));

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "special_effect", 7);
                break;

            case 8: //Không được nhận tài nguyên ở ô tiếp theo
                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("special_effect", 8), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(8);

                //Client
                addResponse(getCommonVector(8));

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "special_effect", 8);
                break;

            case 9: //Giảm 1 level của ô bất kì
                //Lấy list position của các ô có level > 1
                validPosition = new ArrayList<>();
                for (int i = 1; i <= CfgDice.config.getNumberStep(); i++) {
                    if (!CfgDice.isTrap(i) && !CfgDice.isPoint(i) && !CfgDice.isEmpty(i) && uDice.getLevelInt(i) > 1)
                        validPosition.add(i);
                }

                position = validPosition.get(rd.nextInt(validPosition.size()));

                //Level mới
                newLevel = uDice.getNewLevelStr(position, uDice.getLevelInt(position) - 1);

                //Update db
                if (!DBJPA.update("user_dice", Arrays.asList("level", newLevel, "special_effect", 0), Arrays.asList("user_id", user.getId()))) {
                    addErrResponse();
                    return;
                }

                //Update cache
                uDice.setSpecialEffect(0);
                uDice.setLevel(newLevel);

                //Client
                addResponse(protocol.Pbmethod.CommonVector.newBuilder().addALong(9).addALong(position).addALong(-1).addALong(uDice.getLevelInt(position)).build());

                //Log
                Actions.save(user, "dice", "open_card", "id", user.getId(), "special_effect", 9);
                break;
        }
    }

    void receiveReward() {
        List<List<Long>> starReward = uDice.getStarReward();
        List<Long> aIndex = starReward.get(0);
        List<Long> aBonus = starReward.get(1);
        if (!aIndex.isEmpty() && !aBonus.isEmpty()) {
            String newReceived = uDice.getNewReceived(aIndex);
            if (!aBonus.isEmpty()) {
                if (!DBJPA.update("user_dice", Arrays.asList("last_season_received", newReceived), Arrays.asList("user_id", uDice.getUserId()))) {
                    addErrResponse();
                    return;
                }

                uDice.setLastSeasonReceived(newReceived);
                aBonus = Bonus.receiveListItem(mUser, "dice_season_reward", aBonus);
                if (aBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }

                Actions.save(user, "dice", "season_reward", "received", uDice.getLastSeasonReceived());
            }
        }

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector(aBonus));
        for (int i = 0; i < CfgDice.config.getReward().getStar().size(); i++)
            builder.addAVector(getCommonVector(CfgDice.config.getReward().getStar().get(i), uDice.getStarRewardStatus(i), CfgDice.config.getReward().getBonus().get(i)));
        addResponse(builder.build());
    }

    void freeDice() {
//        //Declare
//        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        //Check null
//        if (uDice == null) {
//            addErrResponse();
//            return;
//        }
//
//        //Check xem có được nhận free không?
//        if (!uDice.isFreeDice()) {
//            addErrResponse(getLang(Lang.bonus_already_received));
//            return;
//        }
//
//        //Update db
//        if (!DBJPA.update("user_dice", Arrays.asList("last_date_free_dice", df.format(new Date())), Arrays.asList("user_id", user.getId()))) {
//            addErrResponse();
//            return;
//        }
//
//        uDice.setLastDateFreeDice(new Date());
//
//        //Bonus
//        List<Long> aBonus = Bonus.receiveListItem(mUser, "free_dice", Bonus.viewMaterial(MaterialType.DICE, CfgDice.config.getFreeDaily()));
//
//        //Check tăng xx
//        if (aBonus.isEmpty()) {
//            addErrResponse();
//            return;
//        }
//
//        //Client
//        addResponse(getCommonVector(aBonus));
//
//        //Log
//        Actions.save(user, "dice", "free_dice", "id", user.getId(), "bonus", aBonus);
    }

    void viewStarReward() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < CfgDice.config.getReward().getStar().size(); i++) {
            Pbmethod.CommonVector.Builder commonVector = Pbmethod.CommonVector.newBuilder();
            commonVector.addALong(CfgDice.config.getReward().getStar().get(i))
                    .addALong(uDice.getStarRewardStatus(i))
                    .addAllALong(CfgDice.config.getReward().getBonus().get(i));
            builder.addAVector(commonVector);
        }
        addResponse(builder.build());
    }
    //endregion

    //region Logic
    private int scoreAfterEffect(int score) {
        switch (uDice.getSpecialEffect()) {
            case 5: //Đi lùi
                return -score;
            case 2: //X2
                return score * 2;
            case 4: //Choáng
                return score % 2 == 1 ? -score : score;
            default:
                return score;
        }
    }

    private int getRandomByRate(List<Integer> aRate) {
        Random rd = new Random();
        int rate = rd.nextInt(100) + 1;
        int point = 1, sum = aRate.get(0);
        for (int i = 1; i < aRate.size(); i++) {
            if (sum >= 100) return 6;
            if (rate <= sum) return point;
            point++;
            sum += aRate.get(i);
        }

        return point;
    }
    //endregion

    //region Database access
    boolean dbUpdateUDice(int newPosition, int numberStar, int lastStarPosition, String newLevelStr) {
        return DBJPA.update("user_dice", Arrays.asList("position", newPosition, "last_star_position", lastStarPosition, "number_star", numberStar, "level", newLevelStr), Arrays.asList("user_id", user.getId()));

    }
    //endregion
}
