package monster.game.royalpalace.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Data
@Entity
@Table(name = "res_faction_skill_upgrade_royal_palace", schema = "dson_main")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResFactionSkillUpgradeEntity {
    @Id
    private int id;
    private int faction, levelTier;
    private String statsBonus, skillPassive, feeUpgrade;

    @Column(name = "fee_hero_5star_upgrade")
    private int feeHero5StarUpgrade;
    private int levelUnlock;
}
