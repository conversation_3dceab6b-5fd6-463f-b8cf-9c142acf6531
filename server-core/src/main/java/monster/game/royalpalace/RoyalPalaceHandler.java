package monster.game.royalpalace;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.controller.logic.UserHeroService;
import monster.dao.mapping.UserHeroEntity;
import monster.game.royalpalace.entity.UserRoyalPalaceEntity;
import monster.game.royalpalace.service.UserRoyalPalaceService;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResFactionSkillUpgrade;
import monster.service.resource.ResFactionUpgrade;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class RoyalPalaceHandler extends AHandler {
    UserRoyalPalaceService userRoyalPalaceService = Guice.getInstance(UserRoyalPalaceService.class);
    UserHeroService userHeroService = Guice.getInstance(UserHeroService.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(USER_ROYAL_PALACE_STATUS, USER_ROYAL_PALACE_UPGRADE_LEVEL, USER_ROYAL_PALACE_STATUS_SKILL_FACTION, USER_ROYAL_PALACE_SKILL_LEVEL_UPGRADE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case USER_ROYAL_PALACE_STATUS -> status();
                case USER_ROYAL_PALACE_UPGRADE_LEVEL -> upgradeLevel();
                case USER_ROYAL_PALACE_STATUS_SKILL_FACTION -> statusSkillFaction();
                case USER_ROYAL_PALACE_SKILL_LEVEL_UPGRADE -> skillLevelUpgrade();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void status() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (UserRoyalPalaceEntity userRoyalPalace : userRoyalPalaceService.getUserRoyalPalace(mUser)) {
            builder.addAVector(userRoyalPalace.toProto());
        }
        addResponse(builder.build());
    }

    private void upgradeLevel() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int faction = aLong.get(0).intValue();
        UserRoyalPalaceEntity userRoyalPalace = userRoyalPalaceService.getUserRoyalPalace(mUser).stream().filter(user -> user.getFaction() == faction).findFirst().orElse(null);
        if (userRoyalPalace == null) return;
        List<Long> feeUpgradeLevelFaction = GsonUtil.strToListLong(ResFactionUpgrade.feeFactionUpgrade(userRoyalPalace.getLevelFaction() + 1, faction));
        if (feeUpgradeLevelFaction == null) return;
        long itemId = feeUpgradeLevelFaction.get(2);
        long number = feeUpgradeLevelFaction.get(3);

        List<Long> aBonus = Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_USED_ITEM, itemId, -number);
        JsonArray checkFee = GsonUtil.parseFromListLong(aBonus);
        // check phí nâng cấp
        if (!mUser.checkPrice(this, checkFee)) {
            return;
        }
        // trừ nguyên liệu
        List<Long> bonus = Bonus.receiveListItem(mUser, "royal_palace_upgrade_level", aBonus);
        // update data cho user
        int newLevel = userRoyalPalace.getLevelFaction() + 1;
        if (!updateLevelFaction(faction, newLevel)) {
            addErrResponse();
            return;
        }
        userRoyalPalace.setLevelFaction(newLevel);
        builder.addALong(newLevel);
        builder.addAllALong(bonus);

        Actions.save(user, "royal_palace", "upgrade_level", "user_id", userRoyalPalace.getUserId(), "faction", faction, "new_level", newLevel);
        mUser.getResources().getHeroes().forEach(hero -> hero.calculatePointHero(mUser, Point.ROYAL_PALACE_LEVEL_FACTION_INDEX));
        switch (faction) {
            case 1 -> EventType.ROYAL_PALACE_LEVEL_LUA.addEvent(mUser, newLevel);
            case 2 -> EventType.ROYAL_PALACE_LEVEL_GIO.addEvent(mUser, newLevel);
            case 3 -> EventType.ROYAL_PALACE_LEVEL_NUOC.addEvent(mUser, newLevel);
            case 4 -> EventType.ROYAL_PALACE_LEVEL_ANHSANG.addEvent(mUser, newLevel);
            case 5 -> EventType.ROYAL_PALACE_LEVEL_BONGTOI.addEvent(mUser, newLevel);
        }
        addResponse(builder.build());
    }


    private void statusSkillFaction() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int faction = aLong.get(0).intValue();
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        UserRoyalPalaceEntity userRoyalPalace = userRoyalPalaceService.getUserRoyalPalace(mUser).stream().filter(user -> user.getFaction() == faction).findFirst().orElse(null);
        if (userRoyalPalace == null) return;
        Pbmethod.CommonVector.Builder getData = Pbmethod.CommonVector.newBuilder();
        getData.addAllALong(GsonUtil.strToListLong(userRoyalPalace.getSkill()));
        getData.addALong(userRoyalPalace.getLevelSkill());
        builder.addAVector(getData);
        addResponse(builder.build());
    }


    private void skillLevelUpgrade() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int faction = aLong.get(0).intValue();
        List<Long> listHeroSelectRemove = new ArrayList<>();
        for (int i = 1; i < aLong.size(); i++) {
            long heroId = aLong.get(i);
            listHeroSelectRemove.add(heroId);
        }
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        UserRoyalPalaceEntity userRoyalPalace = userRoyalPalaceService.getUserRoyalPalace(mUser).stream().filter(user -> user.getFaction() == faction).findFirst().orElse(null);
        if (userRoyalPalace == null) return;
        int levelSkillOfFaction = userRoyalPalace.getLevelSkill();

        if (levelSkillOfFaction >= 9) {
            addErrResponse("Bạn đã đạt level tối đa!");
            return;
        }

        List<Long> feeUpgradeLevelSkill = ResFactionSkillUpgrade.getFeeUpgrade(faction, levelSkillOfFaction);
        int feeHero5Star = ResFactionSkillUpgrade.getFeeHero5Star(faction, levelSkillOfFaction);

        int conditionLevelUnlock = ResFactionSkillUpgrade.getLevelUnlock(faction, levelSkillOfFaction);

        if (feeHero5Star == -1 || conditionLevelUnlock == -1 || feeUpgradeLevelSkill == null ){
            return;
        }
        long itemId = feeUpgradeLevelSkill.get(2);
        long number = feeUpgradeLevelSkill.get(3);

        List<Long> aBonus = Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_USED_ITEM, itemId, -number);
        JsonArray checkFee = GsonUtil.parseFromListLong(aBonus);

        // check phí nâng cấp
        if (!mUser.checkPrice(this, checkFee)) {
            return;
        }

        if (userRoyalPalace.getLevelFaction() < conditionLevelUnlock) {
            addErrResponse("Bạn cần đạt level tối thiểu");
            return;
        }
        if (listHeroSelectRemove.size() < feeHero5Star) {
            addErrResponse("Bạn không đủ tài nguyên tướng!");
            return;
        }
        // trừ nguyên liệu
        List<Long> bonus = Bonus.receiveListItem(mUser, "royal_palace_upgrade_level_skill", aBonus);

        List<Long> skillOld = GsonUtil.strToListLong(userRoyalPalace.getSkill());
        // Nguyên liệu tướng 5 sao
        for (Long heroId : listHeroSelectRemove) {
            UserHeroEntity uHero = mUser.getResources().getHero(heroId);
            if (uHero == null) return;

            if (!deleteHeroUser(uHero)) {
                addErrResponse();
                return;
            }
            mUser.getResources().removeHero(heroId);

            Actions.save(user, "royal_palace", "delete_hero", "user_id", mUser.getUser().getId(), "heroId", heroId);
        }

        // update level skill
        int levelSkillNew = userRoyalPalace.getLevelSkill() + 1;

        // update skill
        List<Long> skillNew = new ArrayList<>(skillOld);
        int index = (levelSkillNew - 1) % 3; // Chỉ số tương ứng (0, 1, 2)
        skillNew.set(index, skillNew.get(index) + 1);

        Gson gson = new Gson();
        String skillNow = gson.toJson(skillNew);

        if (!updateLevelSkill(faction, levelSkillNew, skillNow)) {
            addErrResponse();
            return;
        }
        userRoyalPalace.setLevelSkill(levelSkillNew);
        userRoyalPalace.setSkill(skillNow);

        // return client
        builder.addAllALong(skillNew);
        builder.addALong(levelSkillNew);
        builder.addAllALong(bonus);

        Actions.save(user, "royal_palace", "upgrade_level_skill", "user_id", userRoyalPalace.getUserId(), "faction", faction, "new_level_skill", levelSkillNew, "skill_new", skillNow);

        calculateHeroSameFaction(faction);

        addResponse(builder.build());

    }

    public void calculateHeroSameFaction(int faction) {
        List<UserHeroEntity> aHero = mUser.getResources().getHeroes();
        for (UserHeroEntity userHero : aHero) {
            if (userHero.getFaction().value == faction) {
                userHero.calculatePointHero(mUser, Point.ROYAL_PALACE_LEVEL_SKILL_INDEX);
            }
        }
    }

    private boolean updateLevelFaction(int faction, int levelNew) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_royal_palace set level_faction=:levelNew where user_id=:userId and faction=:faction");
            query.setParameter("userId", user.getId());
            query.setParameter("faction", faction);
            query.setParameter("levelNew", levelNew);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean updateLevelSkill(int faction, int levelSkillNew, String skill) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_royal_palace set level_skill=:levelSkillNew, skill=:skill where user_id=:userId and faction=:faction");
            query.setParameter("userId", user.getId());
            query.setParameter("faction", faction);
            query.setParameter("skill", skill);
            query.setParameter("levelSkillNew", levelSkillNew);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean deleteHeroUser(UserHeroEntity userHero) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("delete from user_hero where user_id=" + user.getId() + " and id=" + userHero.getId()).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }


}
