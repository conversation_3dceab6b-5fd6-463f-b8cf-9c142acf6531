package monster.game.pot;//package monster.game.pot;
//
//import grep.helper.StringHelper;
//import monster.config.CfgPot;
//import monster.controller.AHandler;
//import protocol.Pbmethod;
//
//import jakarta.persistence.Transient;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//public class PotRailObject extends PotAbstractObject {
//    public CoordinateObject station;
//    @Transient
//    public List<CoordinateObject> railWay;
//
//    public PotRailObject(int id, String type, CoordinateObject size, CoordinateObject location, CoordinateObject station) {
//        super(id, type, size, location);
//        this.station = station;
//    }
//
//    @Override
//    public PotAbstractObject getInteract(int triggerType, List<CoordinateObject> railWay, CoordinateObject destination, CoordinateObject locationNext) {
//        return this;
//    }
//
//    @Override
//    public Pbmethod.ListCommonVector.Builder dbUpdateObjects(A<PERSON><PERSON><PERSON> handler, UserPotMapEntity userPotMap, List<Long> way, CoordinateObject lastPosition, protocol.Pbmethod.ListCommonVector.Builder collapseGroundBuilder) {
//        return null;
//    }
//
//    public Pbmethod.ListCommonVector.Builder doInteractAndGetBuilder(AHandler handler, UserPotMapEntity userPotMap, List<Long> way, CoordinateObject lastPosition
//            , CoordinateObject railDestination, protocol.Pbmethod.ListCommonVector.Builder collapseGroundBuilder) {
////        System.out.println("staion: " + StringHelper.toDBString(railDestination));
//        if (collapseGroundBuilder.getAVector(0).getALong(0) == FAIL){
////            System.out.println("----collapse false------");
//            return collapseGroundBuilder;
//        }
//        protocol.Pbmethod.ListCommonVector.Builder builder = protocol.Pbmethod.ListCommonVector.newBuilder();
//        List<PotAbstractObject> aLinkObj = new ArrayList<>();
//        List<CoordinateObject> railWay = getRailWay(userPotMap);
//
////        System.out.println("railWay: " + railWay);
////        System.out.println("getRailStep(railDestination): " + getRailStep(railDestination));
//
//        for (CoordinateObject location : railWay) {
//            PotAbstractObject linkObj = userPotMap.getObjectByLocation(location);
//            if (linkObj != null) aLinkObj.add(linkObj);
//        }
//
//        Map<Integer, PotAbstractObject> allObjectChangeThisTimeMappedById = userPotMap.getNewObjectWhenInteractUpdate(aLinkObj, TRIGGER_MOVE_BY_RAIL, railWay, getRailStep(railDestination), null,false);
//        Map<Integer, PotAbstractObject> newAllChangedObjectMappedById = userPotMap.getNewAllChangedObjectMappedById(allObjectChangeThisTimeMappedById);
//
//        String allObjectChangeThisTimeAsJson = CfgPot.toJson(allObjectChangeThisTimeMappedById);
//        String newAllChangedObjectAsJson = CfgPot.toJson(newAllChangedObjectMappedById);
//
//        int newX = way.get(way.size() - 2).intValue();
//        int newY = way.get(way.size() - 1).intValue();
//        CoordinateObject newPosition = new CoordinateObject(newX, newY);
//
//        if (!dbUpdateInteract(userPotMap, newAllChangedObjectAsJson, StringHelper.toDBString(newPosition))) {
////            System.out.println("----collapse false------");
//            builder.addAVector(handler.getCommonVector(FAIL, lastPosition.getX(), lastPosition.getY()));
//            return builder;
//        }
//
//        //Cache
//
//        userPotMap.setCurrentPosition(StringHelper.toDBString(newPosition));
//        userPotMap.setChangedObjects(newAllChangedObjectAsJson);
//        userPotMap.getAllObjectMappedById().putAll(allObjectChangeThisTimeMappedById);
//        userPotMap.setAllChangedObjectMappedById(newAllChangedObjectMappedById);
//
//        builder.addAVector(handler.getCommonVector(SUCCESS));
//
//        builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(RAIL).addALong(this.id).addALong(railDestination.getX())
//                .addALong(railDestination.getY()).addAString(allObjectChangeThisTimeAsJson));
//        return builder;
//    }
//
//    @Override
//    public PotAbstractObject getMoveByRail(List<CoordinateObject> railWay, CoordinateObject ralStep) {
//        return null;
//    }
//
//    @Override
//    public PotAbstractObject getFireByTurret() {
//        return null;
//    }
//
//    @Override
//    public PotAbstractObject getMoveBySwitch(CoordinateObject locationNext) {
//        return null;
//    }
//
//    @Override
//    public String toJson() {
//        return StringHelper.toDBString(this);
//    }
//
//    @Override
//    public boolean update(UserPotMapEntity userPotMap, List<Long> way, Pbmethod.ListCommonVector.Builder collapseGroundBuilder) {
//        return false;
//    }
//
//    public List<CoordinateObject> getRailWay(UserPotMapEntity userPotMap) {
//        if (railWay != null) return railWay;
//        if (location.equals(station)) return null;
//
//        railWay = new ArrayList<>();
//
//        CoordinateObject step;
//
//        if (location.x == station.x && location.y < station.y)
//            step = new CoordinateObject(0, 1);
//        else if (location.x == station.x && location.y > station.y)
//            step = new CoordinateObject(0, -1);
//        else if (location.y == station.y && location.x < station.x)
//            step = new CoordinateObject(1, 0);
//        else step = new CoordinateObject(-1, 0);
//
//        CoordinateObject newLocation = location.getMove(step);
//
//        int count = 0;
//        while (!userPotMap.isWall(newLocation) && !newLocation.equals(station) && count < 1000) {
//            railWay.add(newLocation);
//            newLocation = newLocation.getMove(step);
//            count++;
//        }
////
////        //Theo phương y
////        if (location.x == station.x) {
////            if (location.y < station.y) {
////                for (int i = location.y + 1; i < station.y - size.y; i++) {
////                    railWay.add(new CoordinateObject(location.x, i));
////                }
////                return railWay;
////            }
////
////            for (int i = location.y - 1 - size.y; i > station.y; i--) {
////                railWay.add(new CoordinateObject(location.x, i));
////            }
////            return railWay;
////        }
////
////        //Ray ko được theo đường chéo
////        if (location.y != station.y) return null;
////
////        //Theo phương x
////        if (location.x < station.x) {
////            for (int i = location.x + 1; i < station.x - size.x; i++) {
////                railWay.add(new CoordinateObject(i, location.y));
////            }
////            return railWay;
////        }
////        for (int i = location.x - 1 - size.x; i > station.x; i--) {
////            railWay.add(new CoordinateObject(i, location.y));
////        }
//
//        return railWay;
//    }
//
//    public CoordinateObject getRailStep(CoordinateObject railDestination) {
//        if (location.x == station.x)
//            return location.equals(railDestination) ? new CoordinateObject(0, -1) : new CoordinateObject(0, 1);
//
//        if (location.y != station.y) return null;
//
//        return location.equals(railDestination) ? new CoordinateObject(-1, 0) : new CoordinateObject(1, 0);
//    }
//}
