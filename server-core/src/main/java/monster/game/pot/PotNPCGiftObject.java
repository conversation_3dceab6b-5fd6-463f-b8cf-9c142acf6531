package monster.game.pot;

import grep.helper.StringHelper;
import monster.config.CfgPot;

import java.util.List;

public class PotNPCGiftObject extends PotAbstractObject {
    public int quality, npcId;
    public List<Long> rewardBonus;

    public PotNPCGiftObject(int id, int stateId, String type, CoordinateObject size, CoordinateObject location, List<ActionCallBack> lstActionCallBack, int quality, int npcId, List<Long> rewardBonus) {
        super(id, stateId, type, size, location, lstActionCallBack);
        this.quality = quality;
        this.npcId = npcId;
        this.rewardBonus = rewardBonus;
    }

    @Override
    public PotAbstractObject getNewObjectWhenInteractUpdate(int stateId, int currentLevel) {
        return new PotNPCGiftObject(id, stateId, type, size, location, lstActionCallBack, quality, npcId, rewardBonus);
    }

    @Override
    public PotAbstractObject getNewObjectWhenInteractUpdateLocation(CoordinateObject location) {
        return new PotNPCGiftObject(id, stateId, type, size, location, lstActionCallBack, quality, npcId, rewardBonus);
    }

    @Override
    public PotAbstractObject getMoveByRail(List<CoordinateObject> railWay, CoordinateObject ralStep) {
        return this;
    }

    @Override
    public PotAbstractObject getFireByTurret() {
        return this;
    }

    @Override
    public PotAbstractObject getMoveBySwitch(CoordinateObject locationNext) {
        return this;
    }

    @Override
    public boolean isAvailable() {
        return !List.of(CfgPot.STATE_OPENED, CfgPot.STATE_DESTROYED).contains(stateId);
    }

    @Override
    public String toJson() {
        return StringHelper.toDBString(this);
    }

    @Override
    public boolean update(UserPotMapEntity userPotMap) {
        return false;
    }
}
