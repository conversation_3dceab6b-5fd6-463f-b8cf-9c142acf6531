package monster.game.pot;

import grep.helper.GsonUtil;
import lombok.Getter;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;

@Entity
@Table(name = "dson_main.res_interior_wallpaper")
@Getter
public class ResPotBonusEntity {
    @Id
    private int bonusId;
    private String bonus;

    public List<Long> getABonus(){
        return GsonUtil.strToListLong(bonus);
    }
}
