package monster.game.pot;

import grep.helper.StringHelper;
import monster.config.CfgPot;

import java.util.List;

public class PotChallengeMonsterObject extends PotAbstractObject {
    public int facing, heroViewId, rank;

    public PotChallengeMonsterObject(int id, int stateId, String type, CoordinateObject size, CoordinateObject location, List<ActionCallBack> lstActionCallBack,
                                     int heroViewId, int facing, int rank) {
        super(id, stateId, type, size, location, lstActionCallBack);
        this.facing = facing;
        this.heroViewId = heroViewId;
        this.rank = rank;
    }

    @Override
    public PotAbstractObject getNewObjectWhenInteractUpdate(int stateId, int currentLevel) {
        return new PotChallengeMonsterObject(id, stateId, type, size, location, lstActionCallBack, heroViewId, facing, rank);
    }

    @Override
    public PotAbstractObject getNewObjectWhenInteractUpdateLocation(CoordinateObject location) {
        return new PotChallengeMonsterObject(id, stateId, type, size, location, lstActionCallBack, heroViewId, facing, rank);
    }

    @Override
    public PotAbstractObject getMoveByRail(List<CoordinateObject> railWay, CoordinateObject ralStep) {
        return null;
    }

    @Override
    public PotAbstractObject getFireByTurret() {
        return null;
    }

    @Override
    public PotAbstractObject getMoveBySwitch(CoordinateObject locationNext) {
        return null;
    }

    @Override
    public boolean isAvailable() {
        return stateId != CfgPot.STATE_DESTROYED;
    }

    @Override
    public String toJson() {
        return StringHelper.toDBString(this);
    }

    @Override
    public boolean update(UserPotMapEntity userPotMap) {
        return false;
    }
}
