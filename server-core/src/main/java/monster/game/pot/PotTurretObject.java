package monster.game.pot;//package monster.game.pot;
//
//import grep.helper.StringHelper;
//import monster.config.CfgPot;
//import monster.controller.AHandler;
//import protocol.Pbmethod;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//public class PotTurretObject extends PotAbstractObject {
//    public boolean isActive;
//    public CoordinateObject step;
//    List<CoordinateObject> fireWay;
//
//    public PotTurretObject(int id, String type, CoordinateObject size, CoordinateObject location, boolean isActive, CoordinateObject step) {
//        super(id, type, size, location);
//        this.isActive = isActive;
//        this.step = step;
//    }
//
//    @Override
//    public PotAbstractObject getInteract(int triggerType, List<CoordinateObject> railWay, CoordinateObject railStep, CoordinateObject locationNext) {
//        switch (triggerType) {
//            case TRIGGER_NORMAL:
//                return this;
//            case TRIGGER_MOVE_BY_RAIL:
//                return getMoveByRail(railWay, railStep);
//            case TRIGGER_FIRE_BY_TURRET:
//                return getFireByTurret();
//            case TRIGGER_MOVE_BY_SWITCH:
//                return getMoveBySwitch(locationNext);
//
//        }
//        return this;
//    }
//
//    @Override
//    public Pbmethod.ListCommonVector.Builder dbUpdateObjects
//            (AHandler handler, UserPotMapEntity userPotMap, List<Long> way, CoordinateObject lastPosition, protocol.Pbmethod.ListCommonVector.Builder collapseGroundBuilder) {
//        if (collapseGroundBuilder.getAVector(0).getALong(0) == FAIL) return collapseGroundBuilder;
//        protocol.Pbmethod.ListCommonVector.Builder builder = protocol.Pbmethod.ListCommonVector.newBuilder();
//        List<PotAbstractObject> aLinkObj = new ArrayList<>();
//        List<CoordinateObject> fireWay = getFireWay(userPotMap);
//        if (fireWay == null) {
//            builder.addAVector(handler.getCommonVector(0, lastPosition.getX(), lastPosition.getY()));
//            return builder;
//        }
//
//        for (CoordinateObject location : fireWay) {
//            PotAbstractObject linkObj = userPotMap.getObjectByLocation(location);
//            if (linkObj != null){
//                aLinkObj.add(linkObj);
//            }
//        }
//
//        Map<Integer, PotAbstractObject> allObjectChangeThisTimeMappedById = userPotMap.getNewObjectWhenInteractUpdate(aLinkObj, TRIGGER_FIRE_BY_TURRET);
//        Map<Integer, PotAbstractObject> newAllChangedObjectMappedById = userPotMap.getNewAllChangedObjectMappedById(allObjectChangeThisTimeMappedById);
//
//        String allObjectChangeThisTimeAsJson = CfgPot.toJson(allObjectChangeThisTimeMappedById);
//        String newAllChangedObjectAsJson = CfgPot.toJson(newAllChangedObjectMappedById);
//        int newX = way.get(way.size() - 2).intValue();
//        int newY = way.get(way.size() - 1).intValue();
//        CoordinateObject newPosition = new CoordinateObject(newX, newY);
//
//        if (!dbUpdateInteract(userPotMap, allObjectChangeThisTimeAsJson, StringHelper.toDBString(newPosition))) {
//            builder.addAVector(handler.getCommonVector(FAIL, lastPosition.getX(), lastPosition.getY()));
//            return builder;
//        }
//
//        //Cache
//        userPotMap.setCurrentPosition(StringHelper.toDBString(newPosition));
//        userPotMap.setChangedObjects(newAllChangedObjectAsJson);
//        userPotMap.getAllObjectMappedById().putAll(allObjectChangeThisTimeMappedById);
//        userPotMap.setAllChangedObjectMappedById(newAllChangedObjectMappedById);
//
//        builder.addAVector(handler.getCommonVector(SUCCESS));
//        for (int i = 1; i < collapseGroundBuilder.getAVectorCount(); i++) {
//            builder.addAVector(collapseGroundBuilder.getAVector(i));
//        }
//        builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(TURRET).addALong(this.id).addAString(allObjectChangeThisTimeAsJson));
//        return builder;
//    }
//
//    @Override
//    public PotAbstractObject getMoveByRail(List<CoordinateObject> railWay, CoordinateObject railStep) {
//        List<CoordinateObject> aLocation = getALocation();
//
//        for (CoordinateObject singleLocation : aLocation) {
//            CoordinateObject singleNewLocation = singleLocation.getMove(railStep);
//            if ((singleLocation.equals(railWay.get(0)) && !singleNewLocation.equals(railWay.get(1)))) return this;
//            if ((singleLocation.equals(railWay.get(railWay.size() - 1)) && !singleNewLocation.equals(railWay.get(railWay.size() - 2))))
//                return this;
//        }
//
//        return new PotTurretObject(id, type, size, location.getMove(railStep), isActive, step);
//    }
//
//    @Override
//    public PotAbstractObject getFireByTurret() {
//        if (isActive) return this;
//        return new PotTurretObject(id, type, size, location, true, step);
////        return null;
//    }
//
//    @Override
//    public PotAbstractObject getMoveBySwitch(CoordinateObject locationNext) {
//        return null;
//    }
//
//    public List<CoordinateObject> getFireWay(UserPotMapEntity userPotMap) {
//        if (fireWay != null && !fireWay.isEmpty()) return fireWay;
//
//        fireWay = new ArrayList<>();
//
//        CoordinateObject newLocation = location.getMove(step);
//
//        while (!userPotMap.isWall(newLocation) && !isMe(newLocation)) {
//            fireWay.add(newLocation);
//            newLocation = newLocation.getMove(step);
//        }
//
//        return fireWay;
//    }
//
//    public PotAbstractObject getTargetObject(List<PotAbstractObject> aLinkObj) {
//        for (PotAbstractObject linkObj : aLinkObj) {
//            switch (linkObj.type) {
//                case "monster":
//                case "turret":
//                    return linkObj;
//            }
//        }
//
//        return null;
//    }
//
//    @Override
//    public String toJson() {
//        return StringHelper.toDBString(this);
//    }
//
//    @Override
//    public boolean update(UserPotMapEntity userPotMap, List<Long> way, Pbmethod.ListCommonVector.Builder collapseGroundBuilder) {
//        return false;
//    }
//}
