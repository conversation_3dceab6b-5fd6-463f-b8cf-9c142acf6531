package monster.game.pot;

import grep.helper.StringHelper;
import monster.config.CfgPot;

import java.util.List;

public class PotHealingBeanObject extends PotAbstractObject {
    public PotHealingBeanObject(int id, int stateId, String type, CoordinateObject size, CoordinateObject location,
                                List<ActionCallBack> lstActionCallBack) {
        super(id, stateId, type, size, location, lstActionCallBack);
    }

    @Override
    public PotAbstractObject getNewObjectWhenInteractUpdate(int stateId, int currentLevel) {
        return new PotHealingBeanObject(id, stateId, type, size, location, lstActionCallBack);
    }

    @Override
    public PotAbstractObject getNewObjectWhenInteractUpdateLocation(CoordinateObject location) {
        return new PotHealingBeanObject(id, stateId, type, size, location, lstActionCallBack);
    }

    @Override
    public PotAbstractObject getMoveByRail(List<CoordinateObject> railWay, CoordinateObject ralStep) {
        return null;
    }

    @Override
    public PotAbstractObject getFireByTurret() {
        return null;
    }

    @Override
    public PotAbstractObject getMoveBySwitch(CoordinateObject locationNext) {
        return null;
    }

    @Override
    public String toJson() {
        return StringHelper.toDBString(this);
    }

    @Override
    public boolean update(UserPotMapEntity userPotMap) {
        return false;
    }

    @Override
    public boolean isAvailable(){
        return stateId == CfgPot.STATE_VISIBLE;
    }
}
