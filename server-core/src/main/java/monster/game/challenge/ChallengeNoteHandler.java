package monster.game.challenge;

import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.NotifyType;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.game.challenge.config.ConfigChallengeNote;
import monster.game.challenge.dao.ChallengeNoteDAO;
import monster.game.challenge.entity.ChallengeNote;
import monster.game.challenge.entity.UserChallengeNote;
import monster.game.challenge.service.ChallengeNoteService;
import monster.game.truongevent.config.TruongConfig;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.common.NotifyService;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import java.util.*;

/**
 * <PERSON><PERSON><PERSON><PERSON> vụ sổ tay
 */
public class ChallengeNoteHandler extends AHandler {

    ChallengeNoteService noteService = Guice.getInstance(ChallengeNoteService.class);
    UserChallengeNote note;
    ChallengeNoteDAO challengeNoteDAO = Guice.getInstance(ChallengeNoteDAO.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(NOTE_RECEIVE, NOTE_LIST, NOTE_RECEIVE_SPECIAL, NOTE_STATUS_PROGRESS, NOTE_BEING_COMPLETE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");

        note = noteService.getUserChallenge(mUser);
        if (note == null) {
            addErrResponse();
            return;
        }
        try {
            switch (actionId) {
                case NOTE_LIST -> noteList();
                case NOTE_RECEIVE -> receive();
                case NOTE_RECEIVE_SPECIAL -> receiveSpecial();
                case NOTE_STATUS_PROGRESS -> statusProgress();
                case NOTE_BEING_COMPLETE -> noteBeingComplete();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void noteList() {
        boolean hasNotify = false;
        boolean hasStoryLevel = false;
        UserChallengeNote userChallengeNote = noteService.getUserChallenge(mUser);
        if (userChallengeNote.getGroupId() == ConfigChallengeNote.challengeNoteGroup.size() + 1 && userChallengeNote.getStatusGroup() == 0){
            addErrResponse("Bạn đã đạt cấp độ tối đa!");
            JCache.getInstance().setValue(TruongConfig.KEY_EVENT_CHALLENGE_NOTE.formatted(mUser.getUser().getId()), String.valueOf(0), JCache.EXPIRE_2MTH);
            return;
        }
        List<ChallengeNote> configChallenges = ConfigChallengeNote.mChallengeByGroup.get(userChallengeNote.getGroupId());
        Map<String, Integer> mapData = userChallengeNote.getMapData();
        Map<String, Integer> mapReceive = userChallengeNote.getMapReceive();
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();

        //statusBonusPage = 0: chưa hoàn thành page
        //statusBonusPage = 1: đã hoàn thành page
        Pbmethod.CommonVector.Builder statusView = Pbmethod.CommonVector.newBuilder();

        List<Long> aBonusPage = GsonUtil.strToListLong(ConfigChallengeNote.challengeNoteGroup.get(userChallengeNote.getGroupId()).getQuestReward());
        statusView.addALong(userChallengeNote.getGroupId()).addALong(userChallengeNote.getStatusGroup()).addAllALong(aBonusPage);
        builder.addAVector(statusView);
        for (ChallengeNote configChallengeNote : configChallenges) {
            int missionId = configChallengeNote.getId();
            int receiveIndex = mapReceive.getOrDefault(String.valueOf(missionId), 0);
            int questType = configChallengeNote.getQuestTypeId(receiveIndex);
            EventType eventType = EventType.get(questType);
            long requiredNumber = configChallengeNote.getRequired(receiveIndex);
            long dataValue = noteService.getDataValue(mUser, userChallengeNote, eventType, mapData.getOrDefault(String.valueOf(missionId), 0));
            int status = receiveIndex >= configChallengeNote.getListReward().size()
                    ? Status.MISSION_RECEIVED.value
                    : getStatus(dataValue, configChallengeNote.getRequired(receiveIndex));
            if (status == Status.MISSION_FINISH.value || userChallengeNote.getStatusGroup() == 1) hasNotify = true;
            Pbmethod.CommonVector.Builder pbQuest = Pbmethod.CommonVector.newBuilder();
            pbQuest.addALong(missionId);
            pbQuest.addALong(status); // status
            pbQuest.addALong(eventType.clientToGo); // clientToGo
            if (status == Status.MISSION_NONE.value && eventType == EventType.STORY_LEVEL) {
                hasStoryLevel = true;
            }
            if (eventType == EventType.STORY_LEVEL) {
                pbQuest.addALong(dataValue < requiredNumber ? 0 : 1).addALong(1);
                pbQuest.addAllALong(configChallengeNote.getReward(receiveIndex));
                pbQuest.addAString(eventType.getDescription(mUser.getLang(), "%s-%s".formatted(requiredNumber / 100, requiredNumber % 100)));
            } else {
                pbQuest.addALong(dataValue).addALong(requiredNumber);
                pbQuest.addAllALong(configChallengeNote.getReward(receiveIndex));
                pbQuest.addAString(eventType.getDescription(mUser.getLang(), requiredNumber));
            }
            builder.addAVector(pbQuest);
        }
        addResponse(builder.build());
        Guice.getInstance(NotifyService.class).sendNotify(mUser, NotifyType.CHALLENGE_NOTE_BONUS, hasNotify);
        Guice.getInstance(NotifyService.class).sendNotify(mUser, NotifyType.CHALLENGE_STORY_LEVEL, hasStoryLevel);

    }

    void receive() {
        Pbmethod.CommonVector.Builder statusView = Pbmethod.CommonVector.newBuilder();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int challengeId = aLong.get(0).intValue();
        UserChallengeNote userChallengeNote = noteService.getUserChallenge(mUser);
        List<ChallengeNote> configChallenges = ConfigChallengeNote.mChallengeByGroup.get(userChallengeNote.getGroupId());
        Map<String, Integer> mapData = userChallengeNote.getMapData();
        Map<String, Integer> mapReceive = userChallengeNote.getMapReceive();
        ChallengeNote configChallenge = configChallenges.stream().filter(challenge -> challenge.getId() == challengeId).findFirst().orElse(null);
        int missionId = configChallenge.getId();
        int receiveIndex = mapReceive.getOrDefault(String.valueOf(missionId), 0);
        EventType eventType = EventType.get(configChallenge.getQuestTypeId(receiveIndex));
        long dataValue = noteService.getDataValue(mUser, userChallengeNote, eventType, mapData.getOrDefault(String.valueOf(missionId), 0));
        int status = receiveIndex >= configChallenge.getListReward().size()
                ? Status.MISSION_RECEIVED.value
                : getStatus(dataValue, configChallenge.getRequired(receiveIndex));
        if (status == Status.MISSION_NONE.value) {
            addErrResponse(getLang(Lang.quest_unfinished));
            return;
        } else if (status == Status.MISSION_RECEIVED.value) {
            addErrResponse(getLang(Lang.bonus_already_received));
            return;
        }
        List<Long> aBonus = configChallenge.getReward(receiveIndex);
        mapReceive.put(String.valueOf(missionId), mapReceive.getOrDefault(String.valueOf(missionId), 0) + 1);
        if (challengeNoteDAO.updateReceive(userChallengeNote.getId(), StringHelper.toDBString(mapReceive))) {
            userChallengeNote.setChallengeReceive(StringHelper.toDBString(mapReceive));
        }

        if (userChallengeNote.getMapReceive().size() == configChallenges.size()) {
            int statusGroupNew = 1;
            if (!challengeNoteDAO.dbUpdateStatusGroup(userChallengeNote.getId(), statusGroupNew)) {
                addErrResponse();
                return;
            }
            userChallengeNote.setStatusGroup(statusGroupNew);
            Actions.save(user, "challenge_note", "update_group", "id", userChallengeNote.getId(), "statusGroupNew", userChallengeNote.getStatusGroup());
        }
        List<Long> bonus = Bonus.receiveListItem(mUser, "challenge_note", aBonus);
        if (bonus.isEmpty()) {
            addErrResponse();
            return;
        }
        statusView.addAllALong(bonus);
        addResponse(statusView.build());

    }

    // Kịch bản: Nhận thưởng quà trang sẽ được làm nhiệm vụ trang kế tiếp
    private void receiveSpecial() {
        UserChallengeNote userChallengeNote = noteService.getUserChallenge(mUser);
        if (userChallengeNote.getGroupId() == ConfigChallengeNote.challengeNoteGroup.size() + 1 && userChallengeNote.getStatusGroup() == 0){
            addErrResponse("Bạn đã đạt cấp độ tối đa!");
            JCache.getInstance().setValue(TruongConfig.KEY_EVENT_CHALLENGE_NOTE.formatted(mUser.getUser().getId()), String.valueOf(0), JCache.EXPIRE_2MTH);
            return;
        }
        if (userChallengeNote.getStatusGroup() == 1) {
            List<Long> aBonus = GsonUtil.strToListLong(ConfigChallengeNote.challengeNoteGroup.get(userChallengeNote.getGroupId()).getQuestReward());
            List<Long> rewards = Bonus.receiveListItem(mUser, "challenge_note_receive_special", aBonus);

            if (userChallengeNote.getGroupId() <= ConfigChallengeNote.challengeNoteGroup.size()) {
                String challengeReceiveNew = "{}";
                int statusGroup = 0;
                int groupIdNew = userChallengeNote.getGroupId() + 1;
                if (!challengeNoteDAO.dbUpdateGroupNew(userChallengeNote.getId(), challengeReceiveNew, statusGroup, groupIdNew)) {
                    addErrResponse();
                    return;
                }
                userChallengeNote.setMapMissionId(null);
                userChallengeNote.setStatusGroup(statusGroup);
                userChallengeNote.setGroupId(groupIdNew);
                userChallengeNote.setChallengeReceive(challengeReceiveNew);

                addResponse(Pbmethod.CommonVector.newBuilder().addAllALong(rewards).build());
                Actions.save(user, "challenge_note", "group_new", "id", userChallengeNote.getId(),
                        "challengeReceiveNew", userChallengeNote.getChallengeReceive(), "groupIdNew", userChallengeNote.getGroupId(), "statusGroup", userChallengeNote.getStatusGroup(),
                        "challengeDataNew", userChallengeNote.getChallengeData());
            }
        }
    }


    private void statusProgress() {
        Pbmethod.CommonVector.Builder pbQuest = Pbmethod.CommonVector.newBuilder();
        Map<Integer, Integer> countById = new HashMap<>();
        for (Map.Entry<Integer, List<ChallengeNote>> entry : ConfigChallengeNote.mChallengeByGroup.entrySet()) {
            Integer id = entry.getKey();
            List<ChallengeNote> notes = entry.getValue();
            int count = notes.size();
            countById.put(id, count);
        }
        int cumulativeCount = 0;
        for (Map.Entry<Integer, Integer> entry : countById.entrySet()) {
            cumulativeCount += entry.getValue();
            pbQuest.addALong(cumulativeCount);
            List<Long> aBonus = GsonUtil.strToListLong(ConfigChallengeNote.challengeNoteGroup.get(entry.getKey()).getQuestReward());
            pbQuest.addAllALong(aBonus);
            pbQuest.addALong(-1);
        }
        addResponse(pbQuest.build());
    }

    private void noteBeingComplete() {
        boolean hasNotify = false;
        // missionId -> missionPriority -> lấy priority nhỏ nhất
        Map<Integer, Long> mapStatus = new HashMap<>();
        Pbmethod.CommonVector.Builder statusView = Pbmethod.CommonVector.newBuilder();
        UserChallengeNote userChallengeNote = noteService.getUserChallenge(mUser);
        int groupId = userChallengeNote.getGroupId();
        if (userChallengeNote.getGroupId() == 23) groupId = userChallengeNote.getGroupId() - 1;
        List<ChallengeNote> configChallenges = ConfigChallengeNote.mChallengeByGroup.get(groupId);
        Map<String, Integer> mapData = userChallengeNote.getMapData();
        Map<String, Integer> mapReceive = userChallengeNote.getMapReceive();
        for (ChallengeNote configChallengeNote : configChallenges) {
            int missionId = configChallengeNote.getId();
            int receiveIndex = mapReceive.getOrDefault(String.valueOf(missionId), 0);
            int questType = configChallengeNote.getQuestTypeId(receiveIndex);
            EventType eventType = EventType.get(questType);
            long dataValue = noteService.getDataValue(mUser, userChallengeNote, eventType, mapData.getOrDefault(String.valueOf(missionId), 0));
            int status = receiveIndex >= configChallengeNote.getListReward().size()
                    ? Status.MISSION_RECEIVED.value
                    : getStatus(dataValue, configChallengeNote.getRequired(receiveIndex));
            if (status == Status.MISSION_FINISH.value) {
                mapStatus.put(missionId, 1000000L);
                hasNotify = true;
            } else if (status == Status.MISSION_NONE.value) {
                mapStatus.put(missionId, configChallengeNote.getRequired(receiveIndex) - dataValue);
            } else if (status == Status.MISSION_RECEIVED.value) {
                mapStatus.put(missionId, 0L);
            }
        }
        List<Long> aBonus = GsonUtil.strToListLong(ConfigChallengeNote.challengeNoteGroup.get(groupId).getQuestReward());
        if (mapStatus.values().stream().allMatch(value -> value == 0L)) {
            statusView.addALong(-1);
            statusView.addALong(-1);
            statusView.addAllALong(aBonus);
            statusView.addAString("Hoàn thành tất cả!");
        }

        Map<Integer, Long> sortedMap = new LinkedHashMap<>();
        mapStatus.entrySet()
                .stream()
                .sorted(Map.Entry.<Integer, Long>comparingByValue(Comparator.reverseOrder()))
                .forEachOrdered(entry -> sortedMap.put(entry.getKey(), entry.getValue()));
        Map<Integer, ChallengeNote> allChallenge = ConfigChallengeNote.mChallenge;
        int missionId = sortedMap.entrySet().stream()
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(null);
        for (Map.Entry<Integer, ChallengeNote> entry : allChallenge.entrySet()) {
            if (entry.getKey() == missionId) {
                int receiveIndex = mapReceive.getOrDefault(String.valueOf(missionId), 0);
                int questType = allChallenge.get(missionId).getQuestTypeId(receiveIndex);
                EventType eventType = EventType.get(questType);
                long dataValue = noteService.getDataValue(mUser, userChallengeNote, eventType, mapData.getOrDefault(String.valueOf(missionId), 0));

                if (eventType == EventType.STORY_LEVEL) {
                    long requiredNumber = allChallenge.get(missionId).getRequired(receiveIndex);
                    statusView.addALong(dataValue < requiredNumber ? 0 : 1).addALong(1);
                    statusView.addAllALong(allChallenge.get(missionId).getReward(receiveIndex));
                    statusView.addAString(eventType.getDescription(mUser.getLang(), "%s-%s".formatted(requiredNumber / 100, requiredNumber % 100)));
                } else {
                    statusView.addALong(dataValue).addALong(allChallenge.get(missionId).getRequired(receiveIndex));
                    statusView.addAllALong(allChallenge.get(missionId).getReward(receiveIndex));
                    statusView.addAString(eventType.getDescription(mUser.getLang(), allChallenge.get(missionId).getRequired(receiveIndex)));
                }
            }
        }
        Guice.getInstance(NotifyService.class).sendNotify(mUser, NotifyType.CHALLENGE_NOTE_BONUS, hasNotify);
        addResponse(statusView.build());
    }

    int getStatus(long curNumber, long requiredNumber) {
        if (curNumber < requiredNumber) return Status.MISSION_NONE.value;
        return Status.MISSION_FINISH.value;
    }

}
