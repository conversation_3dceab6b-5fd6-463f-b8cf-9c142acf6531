package monster.game.challenge;

import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.game.challenge.config.ConfigChallenge2;
import monster.game.challenge.dao.ChallengeDAO;
import monster.game.challenge.entity.ChallengeDetail;
import monster.game.challenge.entity.GroupChallengeType;
import monster.game.challenge.service.ChallengeService;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Tổng hợp nhiệm vụ + thử thách thành 1 tính năng chung
 */
public class Challenge2Handler extends AHandler {

    ChallengeService challengeService = Guice.getInstance(ChallengeService.class);
    MaterialService materialService = Guice.getInstance(MaterialService.class);
    ChallengeDAO challengeDAO = Guice.getInstance(ChallengeDAO.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(CHALLENGE2_STATUS, CHALLENGE2_RECEIVE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case CHALLENGE2_STATUS -> status2(getInputInt());
                case CHALLENGE2_RECEIVE -> receive2(getInputALong());
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    void receive2(List<Long> values) {
        int groupId = values.get(0).intValue();
        int challengeId = values.get(1).intValue();

        if (true) {
            Guice.getInstance(ChallengeService.class).receiveAllReward(mUser, groupId)
                    .writeResponse(this);
            return;
        }

        var userChallenge = challengeService.getUserChallenge(mUser, groupId);
        var configChallenges = ConfigChallenge2.mChallengeByGroup.get(groupId);
        Map<String, Integer> mapData = userChallenge.getMapData();
        Map<String, Integer> mapReceive = userChallenge.getMapReceive();
        if (challengeId >= 1) {
            ChallengeDetail configChallenge = configChallenges.stream().filter(challenge -> challenge.getId() == challengeId).findFirst().orElse(null);
            int missionId = configChallenge.getId();
            int receiveIndex = mapReceive.getOrDefault(String.valueOf(missionId), 0);
            EventType eventType = EventType.get(configChallenge.getQuestTypeId(receiveIndex));
            long dataValue = challengeService.getDataValue(mUser, userChallenge, eventType, mapData.getOrDefault(String.valueOf(missionId), 0));
            int status = receiveIndex >= configChallenge.getListReward().size()
                    ? Status.MISSION_RECEIVED.value
                    : getStatus(dataValue, configChallenge.getRequired(receiveIndex));
            if (status == Status.MISSION_NONE.value) {
                addErrResponse(getLang(Lang.quest_unfinished));
                return;
            } else if (status == Status.MISSION_RECEIVED.value) {
                addErrResponse(getLang(Lang.bonus_already_received));
                return;
            }
            mapReceive.put(String.valueOf(missionId), mapReceive.getOrDefault(String.valueOf(missionId), 0) + 1);
            if (challengeDAO.updateReceive(userChallenge.getId(), StringHelper.toDBString(mapReceive))) {
                userChallenge.setChallengeReceive(StringHelper.toDBString(mapReceive));
                materialService.addBonus(mUser, configChallenge.getReward(receiveIndex), "challenge2").writeResponse(this);
            }
        }
    }

    void status2(int groupId) {
        addResponse(challengeService.status(mUser, groupId).build());
    }
    //endregion

    //region Logic
    int getStatus(long curNumber, long requiredNumber) {
        if (curNumber < requiredNumber) return Status.MISSION_NONE.value;
        return Status.MISSION_FINISH.value;
    }
    //endregion

    //region Database
    //endregion

}
