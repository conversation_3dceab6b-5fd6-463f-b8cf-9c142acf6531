package monster.game.challenge;

import grep.log.Logs;
import monster.config.CfgArenaCrystal;
import monster.config.CfgChallenge;
import monster.config.lang.Lang;
import monster.config.penum.ChallengeType;
import monster.config.penum.ItemType;
import monster.config.penum.NotifyType;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.dao.mapping.UserArenaCrystalEntity;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.UserItemEntity;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.common.NotifyService;
import monster.service.resource.ResItem;
import monster.service.user.Bonus;
import net.sf.json.JSONArray;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Mail and Friend
 */
public class ChallengeHandler extends AHandler {

    @Override
    public AHandler newInstance() {
        return new ChallengeHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(CHALLENGE_STATUS, CHALLENGE_RECEIVE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case CHALLENGE_STATUS -> status();
                case CHALLENGE_RECEIVE -> receive();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    void status() {
        int hero6star = 0, hero7star = 0, hero8star = 0, hero9star = 0, hero10star = 0, hero13star = 0, hero15star = 0, levelEnhance = 0, tierUpgrade = 0;
        for (UserHeroEntity hero : mUser.getResources().heroes) {
            hero6star += hero.getStar() >= 6 ? 1 : 0;
            hero7star += hero.getStar() >= 7 ? 1 : 0;
            hero8star += hero.getStar() >= 8 ? 1 : 0;
            hero9star += hero.getStar() >= 9 ? 1 : 0;
            hero10star += hero.getStar() >= 10 ? 1 : 0;
            hero13star += hero.getStar() >= 13 ? 1 : 0;
            hero15star += hero.getStar() >= 15 ? 1 : 0;
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_6STAR) < hero6star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_6STAR, hero6star);
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_7STAR) < hero7star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_7STAR, hero7star);
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_8STAR) < hero8star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_8STAR, hero8star);
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_9STAR) < hero9star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_9STAR, hero9star);
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_10STAR) < hero10star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_10STAR, hero10star);
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_13STAR) < hero13star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_13STAR, hero13star);
        }
        if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_HERO_15STAR) < hero15star) {
            CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_HERO_15STAR, hero15star);
        }

        { // Trang bị
            List<UserItemEntity> uItem = mUser.getResources().items;
            int countGreen = uItem.stream().filter(item -> ResItem.getItem(item.getItemId()).getColorType() == ItemType.COLOR_GREEN).mapToInt(item -> item.getNumber()).sum();
            int countRed = uItem.stream().filter(item -> ResItem.getItem(item.getItemId()).getColorType() == ItemType.COLOR_RED).mapToInt(item -> item.getNumber()).sum();
            int countOrange = uItem.stream().filter(item -> ResItem.getItem(item.getItemId()).getColorType() == ItemType.COLOR_ORANGE).mapToInt(item -> item.getNumber()).sum();
            if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_SET_EQUIPMENT_GREEN) < countGreen) {
                CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_SET_EQUIPMENT_GREEN, countGreen);
            }
            if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_SET_EQUIPMENT_RED) < countRed) {
                CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_SET_EQUIPMENT_RED, countRed);
            }
            if (CfgChallenge.GetChallenge(mUser, ChallengeType.GET_SET_EQUIPMENT_ORANGE) < countOrange) {
                CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.GET_SET_EQUIPMENT_ORANGE, countOrange);
            }
        }

        { // điểm thách đấu
            UserArenaCrystalEntity arenaCrystal = CfgArenaCrystal.getArenaCrystal(user.getId());
            if (arenaCrystal != null && CfgChallenge.GetChallenge(mUser, ChallengeType.ARENA_POINT_CRYSTAL_CROWN) < arenaCrystal.getPoint()) {
                CfgChallenge.updateIfBetterChallenge(mUser, ChallengeType.ARENA_POINT_CRYSTAL_CROWN, arenaCrystal.getPoint());
            }
        }

        JSONArray arrChallenge = CfgChallenge.getChallenge(mUser);
        if (arrChallenge != null) {
            int hasNotify = 0;
            Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
            for (int i = 0; i < CfgChallenge.config.challenges.size(); i++) {
                CfgChallenge.ChallengeObject challenge = CfgChallenge.config.challenges.get(i);
                ChallengeType challengeType = ChallengeType.get(challenge.id);
                if (challengeType.enable) {
                    int index = challenge.id - 1;
                    int value = arrChallenge.getInt(index * 2), milestone = arrChallenge.getInt(index * 2 + 1);
                    Pbmethod.CommonVector.Builder pbQuest = Pbmethod.CommonVector.newBuilder();
                    pbQuest.addALong(challenge.id);
                    if (milestone >= challenge.number.size()) {
                        pbQuest.addALong(Status.MISSION_RECEIVED.value).addALong(challengeType.clientToGo); // clientToGo
                        int maxNumber = challenge.number.get(challenge.number.size() - 1);
                        pbQuest.addALong(value).addALong(maxNumber);
                        pbQuest.addAllALong(challenge.bonus.get(challenge.number.size() - 1));
                        pbQuest.addAString(challenge.getTitle(mUser.getLang(), maxNumber));
                    } else {
                        pbQuest.addALong(getStatus(value, challenge.number.get(milestone))).addALong(challengeType.clientToGo); // clientToGo
                        int maxNumber = challenge.number.get(milestone);
                        pbQuest.addALong(value).addALong(maxNumber);
                        pbQuest.addAllALong(challenge.bonus.get(milestone));
                        pbQuest.addAString(challenge.getTitle(mUser.getLang(), maxNumber));
                        if (value >= maxNumber) {
                            hasNotify = 1;
                        }
                    }
                    builder.addAVector(pbQuest);
                }
            }
            addResponse(builder.build());
            Guice.getInstance(NotifyService.class).setNotify(mUser, NotifyType.CHALLENGE, hasNotify);
            //            if (hasNotify != mUser.getUData().getChallengeNotify()) {
            //                mUser.getUData().setChallengeNotify(hasNotify);
            //                mUser.getUData().update("challenge_notify", hasNotify);
            //            }
        }
    }

    void updateNotify() {
        JSONArray arrChallenge = CfgChallenge.getChallenge(mUser);
        if (arrChallenge != null) {
            int hasNotify = 0;
            for (int i = 0; i < CfgChallenge.config.challenges.size(); i++) {
                CfgChallenge.ChallengeObject challenge = CfgChallenge.config.challenges.get(i);
                ChallengeType challengeType = ChallengeType.get(challenge.id);
                if (challengeType != null && challengeType.enable) {
                    int index = challenge.id - 1;
                    int value = arrChallenge.getInt(index * 2), milestone = arrChallenge.getInt(index * 2 + 1);
                    if (milestone >= challenge.number.size()) {
                    } else {
                        int maxNumber = challenge.number.get(milestone);
                        if (value >= maxNumber) hasNotify = 1;
                    }
                }
            }
            Guice.getInstance(NotifyService.class).setNotify(mUser, NotifyType.CHALLENGE, hasNotify);
            //            if (hasNotify != mUser.getUData().getChallengeNotify()) {
            //                mUser.getUData().setChallengeNotify(hasNotify);
            //                mUser.getUData().update("challenge_notify", hasNotify);
            //            }
        }
    }

    void receive() {
        JSONArray arrChallenge = CfgChallenge.getChallenge(mUser);
        if (arrChallenge != null) {
            int challengeId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
            if (challengeId >= 1) {
                CfgChallenge.ChallengeObject challenge = CfgChallenge.getChallenge(challengeId);
                int indexLevel = (challengeId - 1) * 2 + 1;
                int curValue = arrChallenge.getInt(indexLevel - 1), curLevel = arrChallenge.getInt(indexLevel);

                if (challenge.number.size() <= curLevel) {
                    addErrResponse(getLang(Lang.bonus_already_received));
                    return;
                }

                int status = getStatus(curValue, challenge.number.get(curLevel));

                if (status == Status.MISSION_NONE.value) {
                    addErrResponse(getLang(Lang.quest_unfinished));
                    return;
                } else if (status == Status.MISSION_RECEIVED.value) {
                    addErrResponse(getLang(Lang.bonus_already_received));
                    return;
                }
                arrChallenge.set(indexLevel, arrChallenge.getInt(indexLevel) + 1);
                if (CfgChallenge.dbUpdate(mUser, arrChallenge.toString())) {
                    List<Long> aLong = Bonus.receiveListItem(mUser, "challenge_" + challengeId + "_" + curLevel, challenge.bonus.get(curLevel));
                    addResponse(CommonProto.getCommonVectorProto(aLong, null));
                    updateNotify();
                }
            }
        }
    }

    //endregion

    //region Logic
    int getStatus(int curNumber, int requiredNumber) {
        if (curNumber < requiredNumber) return Status.MISSION_NONE.value;
        return Status.MISSION_FINISH.value;
    }
    //endregion

    //region Database
    //endregion

}
