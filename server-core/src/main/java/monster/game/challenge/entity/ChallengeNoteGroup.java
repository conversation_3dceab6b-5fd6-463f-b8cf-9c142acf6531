package monster.game.challenge.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "challenge_note_group", schema = "dson_main")
public class ChallengeNoteGroup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;
    @Column(name = "group_id")
    private Integer groupId;
    @Column(name = "quest_reward")
    private String questReward;
}
