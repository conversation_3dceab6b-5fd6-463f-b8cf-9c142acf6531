package monster.game.challenge.entity;

import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "challenge_detail", schema = "dson_main")
public class ChallengeDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "group_id")
    private Integer groupId;

    @Column(name = "name")
    private String name;

    @Column(name = "quest_type")
    private String questType;

    @Column(name = "quest_required")
    private String questRequired;

    @Column(name = "quest_reward")
    private String questReward;
    int finalQuest;

    public int getQuestTypeId(int index) {
        Integer value = ListUtil.getElement(GsonUtil.strToListInt(questType), index);
        return value == null ? 0 : value;
    }

    public List<List<Long>> getListReward() {
        return GsonUtil.strTo2ListLong(questReward);
    }

    public List<Long> getListRequired() {
        return GsonUtil.strToListLong(questRequired);
    }

    public long getRequired(int index) {
        return ListUtil.getElement(GsonUtil.strToListLong(questRequired), index);
    }

    public List<Long> getReward(int index) {
        return ListUtil.getElement(GsonUtil.strTo2ListLong(questReward), index);
    }

    public boolean isFinalQuest() {
        return finalQuest == 1;
    }
}