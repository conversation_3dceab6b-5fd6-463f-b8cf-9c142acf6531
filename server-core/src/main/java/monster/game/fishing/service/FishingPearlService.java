package monster.game.fishing.service;

import monster.dao.mapping.PearlMineEntity;
import monster.dao.mapping.UserEntity;
import monster.game.fishing.entity.UserFishingEntity;
import monster.game.fishing.entity.UserFishingPearlLogEntity;
import monster.object.MyUser;
import monster.object.ServiceResult;
import protocol.Pbmethod;

import java.util.List;

public interface FishingPearlService {

    List<PearlMineEntity> getListPearlMine(MyUser mUser, int district);

    List<PearlMineEntity> getListPearlMineOfUser(UserEntity userEntity);

    PearlMineEntity getPearlMine(MyUser mUser, int districtId, int mineId);

    ServiceResult<Pbmethod.PbListBattleResult> mining(MyUser mUser, UserFishingEntity userFishing, int districtId, int mineId, List<Long> heroIds);

    ServiceResult<Pbmethod.PbListBattleResult> rob(MyUser mUser, UserFishingEntity atkUserFishing, UserFishingEntity defUserFishing, int districtId, int mineId, List<Long> heroIds);

    List<UserFishingPearlLogEntity> getListPearlLogWhenAttack(MyUser mUser);

    List<UserFishingPearlLogEntity> getListPearlLogWhenDefend(MyUser mUser);
}
