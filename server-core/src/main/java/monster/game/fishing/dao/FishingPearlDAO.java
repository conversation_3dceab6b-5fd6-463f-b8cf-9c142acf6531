package monster.game.fishing.dao;

import monster.dao.AbstractDAO;
import monster.dao.mapping.PearlMineEntity;
import monster.game.fishing.entity.UserFishingPearlLogEntity;

import java.util.List;

public class FishingPearlDAO extends AbstractDAO {

    public List<PearlMineEntity> getListPearlMine(int clusterId, int districtId) {
        return doQuery(em -> em.createQuery("select c from PearlMineEntity c where c.clusterId=:clusterId and c.districtId=:districtId")
                .setParameter("clusterId", clusterId).setParameter("districtId", districtId).getResultList());
    }

    public List<PearlMineEntity> getListPearlMineOfUser(int clusterId, int userId) {
        return doQuery(em -> em.createQuery("select c from PearlMineEntity c where c.clusterId=:clusterId and c.userId=:userId")
                .setParameter("clusterId", clusterId).setParameter("userId", userId).getResultList());
    }

    public PearlMineEntity getPearlMine(int clusterId, int districtId, int mineId) {
        return doQuery(em -> em.createQuery("select c from PearlMineEntity c where c.clusterId=:clusterId and c.districtId=:districtId and c.mineId=:mineId")
                .setParameter("clusterId", clusterId)
                .setParameter("mineId", mineId)
                .setParameter("districtId", districtId)
                .getResultList().stream().findFirst().orElse(null));
    }

    public boolean mining(PearlMineEntity pearlMine, int userId) {
        return doUpdate(em -> em.createQuery("update PearlMineEntity c set c.userId=:userId, c.startMiningTime=now() where c.clusterId=:clusterId and c.districtId=:districtId and c.mineId=:mineId")
                .setParameter("clusterId", pearlMine.getClusterId()).setParameter("mineId", pearlMine.getMineId()).setParameter("districtId", pearlMine.getDistrictId())
                .setParameter("userId", userId).executeUpdate() >= 1);
    }

    public boolean miningExpired(PearlMineEntity pearlMine) {
        return doUpdate(em -> em.createQuery("update PearlMineEntity c set c.userId=0 where c.clusterId=:clusterId and c.districtId=:districtId and c.mineId=:mineId")
                .setParameter("clusterId", pearlMine.getClusterId()).setParameter("districtId", pearlMine.getDistrictId()).setParameter("mineId", pearlMine.getMineId()).executeUpdate() >= 1);
    }

    public boolean robSuccess(PearlMineEntity pearlMine, int userId, int defUserId) {
        return doUpdate(em -> {
            em.createQuery("update UserFishingEntity c set c.robNumber=c.robNumber-1 where c.userId=:userId").setParameter("userId", userId).executeUpdate();
            em.createQuery("update UserFishingEntity c set c.beRobNumber=c.beRobNumber-1 where c.userId=:userId").setParameter("userId", defUserId).executeUpdate();
            return em.createQuery("update PearlMineEntity c set c.userId=:userId, c.startMiningTime=now() where c.clusterId=:clusterId and c.districtId=:districtId and c.mineId=:mineId")
                    .setParameter("clusterId", pearlMine.getClusterId()).setParameter("mineId", pearlMine.getMineId()).setParameter("districtId", pearlMine.getDistrictId())
                    .setParameter("userId", userId).executeUpdate() >= 1;
        });
    }

    public List<UserFishingPearlLogEntity> getListPearlLogByAttackerId(int attackerId) {
        return doQuery(em -> em.createQuery("select c from UserFishingPearlLogEntity c where c.attackerId=:attackerId order by c.dateCreated desc")
                .setParameter("attackerId", attackerId).setMaxResults(20).getResultList());
    }

    public List<UserFishingPearlLogEntity> getListPearlLogByDefenderId(int defenderId) {
        return doQuery(em -> em.createQuery("select c from UserFishingPearlLogEntity c where c.defenderId=:defenderId order by c.dateCreated desc")
                .setParameter("defenderId", defenderId).setMaxResults(20).getResultList());
    }
}
