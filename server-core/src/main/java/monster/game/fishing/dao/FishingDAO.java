package monster.game.fishing.dao;

import monster.dao.AbstractDAO;
import monster.game.fishing.entity.*;

import java.util.List;

public class FishingDAO extends AbstractDAO {

    public List<UserFishingRecordEntity> getListUserFishingRecord(int userId) {
        return doQuery(em -> em.createQuery("select c from UserFishingRecordEntity c where c.userId=:userId").setParameter("userId", userId).getResultList());
    }

    public boolean catchFish(int userId, int expLevel, int numberCatchFish, UserFishingFishEntity uff, UserFishingRecordEntity record) {
        return doUpdate(em -> {
            em.createQuery("update UserFishingEntity set expLevel=:expLevel,numberCatchFish=:numberCatchFish where userId=:userId")
                    .setParameter("userId", userId).setParameter("expLevel", expLevel).setParameter("numberCatchFish", numberCatchFish).executeUpdate();
            if (record != null) saveOrUpdate(record);
            return save(uff);
        });
    }

    public boolean sellFish(int userId, List<Long> idFishes, long totalCoinSellFish) {
        return doUpdate(em -> {
            em.createQuery("update UserFishingEntity set totalCoinSellFish=:totalCoinSellFish where userId=:userId")
                    .setParameter("userId", userId).setParameter("totalCoinSellFish", totalCoinSellFish).executeUpdate();
            return em.createQuery("delete UserFishingFishEntity where id in :id").setParameter("id", idFishes).executeUpdate() > 0;
        });
    }

    public boolean fishToAqua(int userId, int aquaId, List<Long> idFishes, String point) {
        return doUpdate(em -> {
            em.createQuery("update UserFishingFishEntity set aquaId=0 where userId=:userId and aquaId=:aquaId")
                    .setParameter("userId", userId).setParameter("aquaId", aquaId).executeUpdate();
            em.createQuery("update UserFishingAquariumEntity set aquaPoint=:point where id=:id")
                    .setParameter("point", point).setParameter("id", aquaId).executeUpdate();
            return idFishes.isEmpty() ? true : em.createQuery("update UserFishingFishEntity set aquaId=:aquaId where userId=:userId and id in :idFishes")
                    .setParameter("userId", userId).setParameter("aquaId", aquaId).setParameter("idFishes", idFishes).executeUpdate() > 0;
        });
    }

    public UserFishingLabEntity getUserFishingLab(int userId) {
        return doQuery(em -> em.createQuery("select c from UserFishingLabEntity c where c.userId=:userId").setParameter("userId", userId)
                .getResultList().stream().findFirst().orElse(null));
    }

    public UserFishingEntity getUserFishing(int userId) {
        return doQuery(em -> em.createQuery("select c from UserFishingEntity c where c.userId=:userId").setParameter("userId", userId)
                .getResultList().stream().findFirst().orElse(null));
    }

    public List<UserFishingFishEntity> getListUserFishingFish(int userId) {
        return doQuery(em -> em.createQuery("select c from UserFishingFishEntity c where c.userId=:userId").setParameter("userId", userId).getResultList());
    }

    public List<UserFishingAquariumEntity> getListUserFishingAquarium(int userId) {
        return doQuery(em -> em.createQuery("select c from UserFishingAquariumEntity c where c.userId=:userId").setParameter("userId", userId).getResultList());
    }

    public boolean changeIsland(int userId, int islandId) {
        return doUpdate(em -> em.createQuery("update UserFishingEntity c set c.islandId=:islandId where c.userId=:userId")
                .setParameter("userId", userId).setParameter("islandId", islandId).executeUpdate() > 0);
    }

    public boolean addFish(int userId, int fishId, int number) {
        return doUpdate(em -> em.createQuery("update UserFishingFishEntity c set c.number=c.number+:number where c.userId=:userId and c.fishId=:fishId")
                .setParameter("userId", userId).setParameter("fishId", fishId).setParameter("number", number).executeUpdate() > 0);
    }

    public boolean updateEnergy(int userId, int energy, long lastTimeRecover) {
        return doUpdate(em -> em.createQuery("update UserFishingEntity c set c.energy=:energy, c.energyLastRecover=:lastTimeRecover where c.userId=:userId")
                .setParameter("userId", userId).setParameter("energy", energy).setParameter("lastTimeRecover", lastTimeRecover).executeUpdate() > 0);
    }

    public boolean updateRobNumber(int userId, int robNumber, int beRobNumber, int robKey) {
        return doUpdate(em -> em.createQuery("update UserFishingEntity c set c.robNumber=:robNumber, c.beRobNumber=:beRobNumber, c.robKey=:robKey where c.userId=:userId")
                .setParameter("userId", userId).setParameter("robNumber", robNumber).setParameter("beRobNumber", beRobNumber).setParameter("robKey", robKey).executeUpdate() > 0);
    }

    public boolean unlockSlotAqua(int tankId) {
        return doUpdate(em -> em.createQuery("update UserFishingAquariumEntity c set c.numberSlot=c.numberSlot+1 where c.id=:id")
                .setParameter("id", tankId).executeUpdate() > 0);
    }
}
