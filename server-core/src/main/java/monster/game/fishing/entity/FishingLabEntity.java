package monster.game.fishing.entity;

import grep.helper.GsonUtil;
import lombok.Data;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;
import java.util.Map;

@Entity
@Data
@Table(name = "fishing_lab", catalog = "dson_main")
public class FishingLabEntity {
    @Id
    int albumId;
    int islandId;
    String fishId, bonus;

    public boolean isCompleted(Map<Integer, UserFishingRecordEntity> records) {
        return GsonUtil.strToListInt(fishId).stream().allMatch(records::containsKey);
    }

    public List<Integer> getListFish() {
        return GsonUtil.strToListInt(fishId);
    }

    public List<Long> getListBonus() {
        return GsonUtil.strToListLong(bonus);
    }
}
