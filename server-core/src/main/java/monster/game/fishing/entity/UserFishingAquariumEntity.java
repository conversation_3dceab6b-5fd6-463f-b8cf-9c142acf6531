package monster.game.fishing.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Data
@Table(name = "user_fishing_aquarium", catalog = "dson")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFishingAquariumEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    int userId;
    int numberSlot;
    String aquaPoint;

    @Transient
    List<UserFishingFishEntity> fishes = new ArrayList<>();
}
