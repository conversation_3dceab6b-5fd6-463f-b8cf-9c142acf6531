package monster.game.fishing.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Data
@Table(name = "user_fishing_pearl_log", catalog = "dson")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserFishingPearlLogEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;
    int attackerId, defenderId;
    int pearlNumber;
    long battleId;
    boolean isWin;
    Date dateCreated;
}
