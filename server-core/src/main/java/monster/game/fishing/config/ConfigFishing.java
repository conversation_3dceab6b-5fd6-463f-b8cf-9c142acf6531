package monster.game.fishing.config;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.NumberUtil;
import monster.config.CfgServer;
import monster.game.fishing.entity.ResPearlMineBotEntity;
import monster.game.fishing.entity.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@SuppressWarnings({"rawtypes", "unchecked"})
public class ConfigFishing {
    public static final int GEAR_HOOK = 0, GEAR_LINE = 1, GEAR_ROD = 2, GEAR_FLOAT = 3;
    public static FishingShipEntity[] ships;
    public static List<FishingLevelEntity> levels;
    private static Map<Integer, FishingFishEntity> mFish;
    private static Map<Integer, List<FishingFishEntity>> mIslandFish;
    public static List<FishingIslandEntity> islands;
    public static List<FishingRodEntity> rods;
    public static List<FishingLabEntity> labs;
    public static List<FishingBaitEntity> baits;
    public static List<FishingAreaEntity> areas;
    public static List<FishingTimePerfectEntity> timePerfects;
    public static int maxEnergy = 200, recoverTime = 60, feeEnergy = 10;
    public static String rateFishingType, rateRecord;
    public static List<Long> chests = List.of(1L, 1L, 1L, 2L, 2L, 2L, 2L, 2L, 2L, 2L);
    public static Aquarium cfgAquarium;
    public static Pearl cfgPearl;
    public static List<ResPearlMineBotEntity> listResPearlMineBot = null;
    public static List<ResPearlMineClusterEntity> listResPearlMineCluster = null;
    public static JsonArray jsonArrayChest;
    public static int defaultSlot = 500, feeBuySlot = 100, maxSlot = 1000;
    public static int idleTime = 10; // seconds

    public static FishingTimePerfectEntity getTimePerfect(int timePerfect) {
        for (int i = timePerfects.size() - 1; i >= 0; i--) {
            if (timePerfect >= timePerfects.get(i).getTimePerfect())
                return timePerfects.get(i);
        }
        return timePerfects.get(0);
    }

    public static FishingBaitEntity getBait(int materialId) {
        if (materialId == 0) return null;
        return baits.stream().filter(bait -> bait.getId() == materialId).findFirst().orElse(null);
    }

    public static FishingLevelEntity getLevelInfo(int level) {
        if (level - 1 >= levels.size()) return null;
        return levels.get(level - 1);
    }

    public static FishingFishEntity getFish(int fishKey) {
        return mFish.get(fishKey);
    }

    public static int getTypeFish(FishingBaitEntity baitEntity, FishingAreaEntity areaEntity, FishingTimePerfectEntity timePerfectEntity) {
        int[] rate = GsonUtil.parse(int[].class, rateFishingType);
        if (baitEntity != null && baitEntity.getRate().length() > 2) {
            rate = GsonUtil.parse(int[].class, baitEntity.getRate());
        }
        if (areaEntity != null) {
            List<Integer> rateAdd = GsonUtil.strToListInt(areaEntity.getRateAdd());
            int size = Math.min(rateAdd.size(), rate.length);
            for (int i = 0; i < size; i++) {
                rate[i] += rateAdd.get(i);
            }
        }
        if (timePerfectEntity != null) {
            List<Integer> rateAdd = GsonUtil.strToListInt(timePerfectEntity.getRateAdd());
            int size = Math.min(rateAdd.size(), rate.length);
            for (int i = 0; i < size; i++) {
                rate[i] += rateAdd.get(i);
            }
        }
        return NumberUtil.getRandomIndex(rate);
    }

    public static int getIndexRecord(FishingBaitEntity baitEntity, FishingAreaEntity areaEntity) {
        int[] rate = GsonUtil.parse(int[].class, rateRecord);
        if (baitEntity != null && baitEntity.getRecordRate().length() > 2) {
            rate = GsonUtil.parse(int[].class, baitEntity.getRecordRate());
        }
        if (areaEntity != null) {
            List<Integer> recordAdd = GsonUtil.strToListInt(areaEntity.getRecordRateAdd());
            int size = Math.min(recordAdd.size(), rate.length);
            for (int i = 0; i < size; i++) {
                rate[i] += recordAdd.get(i);
            }
        }
        return NumberUtil.getRandomIndex(rate);
    }

    public static FishingFishEntity getRandomFishByIsland(int islandId, int fishType) {
        List<FishingFishEntity> fishByType = mIslandFish.get(islandId).stream().filter(fish -> fish.getType() == fishType).collect(Collectors.toList());
        return NumberUtil.getRandomInList(fishByType);
    }

    // hook line rod float
    public static int getGearFeeUpgrade(int rodType, int level) {
        if (level == rods.size()) return -1;
        var rod = ListUtil.getElement(rods, level);
        return List.of(rod.getHookCost(), rod.getLineCost(), rod.getRodCost(), rod.getFloatCost()).get(rodType);
    }

    // hook line rod float
    public static int getGearPoint(int rodType, int level) {
        var rod = ListUtil.getElement(rods, level);
        return List.of(rod.getHookPoint(), rod.getLinePoint(), rod.getRodPoint(), rod.getFloatPoint()).get(rodType);
    }

    public static List<Long> getChestBonus(int islandId, int chestType) {
        JsonObject obj = jsonArrayChest.get(islandId - 1).getAsJsonObject();
        return GsonUtil.strToListLong(chestType == 1 ? obj.get("chest1").getAsJsonArray().toString() : obj.get("chest2").getAsJsonArray().toString());
    }

    public static List<Long> getShuffleChest() {
        var randomChests = new ArrayList<>(chests);
        Collections.shuffle(randomChests);
        return randomChests;
    }

    public static ResPearlMineBotEntity getResPearlMineBot(int type) {
        if (listResPearlMineBot == null)
            listResPearlMineBot = DBJPA.getList("dson_main.res_pearl_mine_bot", ResPearlMineBotEntity.class);
        return listResPearlMineBot.stream().filter(resPearlMineBot -> resPearlMineBot.getType() == type).findFirst().orElse(null);
    }

    public static int getCluster(int server) {
        if (CfgServer.isTestServer()) return 0;

        if (listResPearlMineCluster == null)
            listResPearlMineCluster = DBJPA.getList("dson_main.res_pearl_mine_cluster", ResPearlMineClusterEntity.class);
        ResPearlMineClusterEntity resPearlMineCluster = listResPearlMineCluster.stream().filter(tmp -> tmp.inCluster(server)).findFirst().orElse(null);
        return resPearlMineCluster == null ? 1 : resPearlMineCluster.getCluster();
    }

    public static long getMiningTimeCountdown(Date startMiningTime) {
        long miningTime = Math.min(cfgPearl.maxTimeMining * DateTime.HOUR_SECOND, (System.currentTimeMillis() - startMiningTime.getTime()) / 1000);
        return cfgPearl.maxTimeMining * DateTime.HOUR_SECOND - miningTime;
    }

    public static long getMiningPearl(int mineType, Date startMiningTime) {
        long miningMinutes = Math.min(cfgPearl.maxTimeMining * DateTime.HOUR_MIN, (System.currentTimeMillis() - startMiningTime.getTime()) / DateTime.MIN_MILLI_SECOND);
        return miningMinutes * cfgPearl.slotSpeed.get(mineType - 1) / DateTime.HOUR_MIN;
    }

    // {"feeEnergy": 10, "maxEnergy": 200, "recoverTime": 120}
    public static void setConfigEnergy(String value) {
        JsonObject obj = GsonUtil.parseJsonObject(value);
        feeEnergy = obj.get("feeEnergy").getAsInt();
        maxEnergy = obj.get("maxEnergy").getAsInt();
        recoverTime = obj.get("recoverTime").getAsInt();
    }

    public static void setConfigShip(String value) {
        ships = GsonUtil.parse(FishingShipEntity[].class, GsonUtil.parseJsonObject(value).get("ship").toString());
        levels = DBJPA.getList("dson_main.fishing_level", FishingLevelEntity.class);
        rods = DBJPA.getList("dson_main.fishing_rod", FishingRodEntity.class);
        islands = DBJPA.getList("dson_main.fishing_island", FishingIslandEntity.class);
        labs = DBJPA.getList("dson_main.fishing_lab", FishingLabEntity.class);
        baits = DBJPA.getList("dson_main.fishing_bait", FishingBaitEntity.class);
        areas = DBJPA.getList("dson_main.fishing_area", FishingAreaEntity.class);
        timePerfects = DBJPA.getList("dson_main.fishing_time_perfect", FishingTimePerfectEntity.class);

        List<FishingFishEntity> fishes = DBJPA.getQueryList("select * from dson_main.fishing_fish", FishingFishEntity.class);
        mFish = fishes.stream().collect(Collectors.toMap(FishingFishEntity::getId, Function.identity()));
        mIslandFish = new HashMap<>();
        islands.forEach(island -> mIslandFish.put(island.id, new ArrayList<>()));
        fishes.forEach(fish -> mIslandFish.get(fish.getIsland()).add(fish));
    }

    public static void setConfigMain(String value) {
        maxEnergy = GsonUtil.parseJsonObject(value).get("maxEnergy").getAsInt();
        recoverTime = GsonUtil.parseJsonObject(value).get("recoverTime").getAsInt();
        rateFishingType = GsonUtil.parseJsonObject(value).get("rateType").getAsJsonArray().toString();
        rateRecord = GsonUtil.parseJsonObject(value).get("rateRecord").getAsJsonArray().toString();
    }

    public static void setConfigPearl(String value) {
        cfgPearl = GsonUtil.parse(Pearl.class, value);
    }

    public static void setConfigAquarium(String value) {
        cfgAquarium = GsonUtil.parse(Aquarium.class, value);
    }

    // {"chestBonus": [{"chest1": [3, 1, 1, 1, 3, 1, 2, 2], "chest2": [3, 1, 1, 2, 3, 1, 2, 3]},
    // {"chest1": [3, 1, 1, 1, 3, 1, 2, 2], "chest2": [3, 1, 1, 2, 3, 1, 2, 3]},
    // {"chest1": [3, 1, 1, 1, 3, 1, 2, 2], "chest2": [3, 1, 1, 2, 3, 1, 2, 3]},
    // {"chest1": [3, 1, 1, 1, 3, 1, 2, 2], "chest2": [3, 1, 1, 2, 3, 1, 2, 3]},
    // {"chest1": [3, 1, 1, 1, 3, 1, 2, 2], "chest2": [3, 1, 1, 2, 3, 1, 2, 3]},
    // {"chest1": [3, 1, 1, 1, 3, 1, 2, 2], "chest2": [3, 1, 1, 2, 3, 1, 2, 3]}]}
    public static void setConfigChest(String value) {
        jsonArrayChest = GsonUtil.parseJsonObject(value).get("chestBonus").getAsJsonArray();
    }

    public class Aquarium {
        public int defaultSlot;
        public List<Long> priceUnlockSlot;
        public List<Long> unlockAquariumPrice;
        public List<Integer> unlockAquariumLevel;
    }

    public class Pearl {
        public int numberDistrict, numberSlot;
        public int robNumber, beRobNumber, maxTimeMining;
        public float minTimeMining;
        public List<Integer> slotType, slotSpeed; // sản lươợng theo gi
    }
}
