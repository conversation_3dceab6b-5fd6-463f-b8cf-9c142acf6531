package monster.game.fishing.config;

import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.config.lang.Lang;
import monster.util.LockedCallback;

public class FishingDistributedLocked {

    public static void lockedFishingMine(int mineId, LockedCallback callback) {
        if (acquireLockMine(mineId)) {
            try {
                callback.onLocked("");
            } catch (Exception ex) {
                Logs.error(ex);
            }
            releaseLockMine(mineId);
        } else {
            try {
                callback.onLocked("Mỏ đang tranh chấp, hãy thử lại sau");
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
    }

    static boolean acquireLockMine(int mineId) {
        return JCache.getInstance().distributedLocked.acquireLock("fishing_mine_" + mineId);
    }

    static boolean releaseLockMine(int mineId) {
        return JCache.getInstance().distributedLocked.releaseLock("fishing_mine_" + mineId);
    }

}
