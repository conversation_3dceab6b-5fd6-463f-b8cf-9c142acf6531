package monster.game.miningevent.entity;

import lombok.*;
import monster.game.miningevent.config.ConfigMiningEvent;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "mining_event_price")
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MiningEventPriceEntity implements Serializable {
    @Id
    int eventId,clusterId, resId;
    long number;
    int price;

    public int getPriceDiff(){
        return (price - ConfigMiningEvent.config.getDefaultPrice()) * 100 / ConfigMiningEvent.config.getDefaultPrice();
    }
}
