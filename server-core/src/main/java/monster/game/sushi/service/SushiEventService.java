package monster.game.sushi.service;

import monster.dao.mapping.ConfigEvent;
import monster.game.sushi.entity.ClanEventSushiEntity;
import monster.game.sushi.entity.UserEventSushiEntity;
import monster.object.MyUser;

import java.util.List;

public interface SushiEventService {
    ClanEventSushiEntity getInfoPartySushi(MyUser myUser);
    UserEventSushiEntity getInfoClanUser(MyUser myUser);
    List<ClanEventSushiEntity> topUserPoint();
    List<UserEventSushiEntity> topUserPointOfClan(MyUser myUser);
    ConfigEvent getConfigEvent(MyUser mUser);
    Integer getEventId(MyUser mUser);
}
