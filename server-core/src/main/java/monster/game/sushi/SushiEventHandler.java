package monster.game.sushi;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.dao.mapping.ClanEntity;
import monster.dao.mapping.UserEntity;
import monster.game.sushi.config.ConfigEventSushi;
import monster.game.sushi.dao.SushiEventDAO;
import monster.game.sushi.entity.ClanEventSushiEntity;
import monster.game.sushi.entity.UserEventSushiEntity;
import monster.game.sushi.service.SushiEventService;
import monster.object.ServiceResult;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.monitor.ClanMonitor;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.BonusBuilder;
import monster.util.ValidateParam;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SushiEventHandler extends AHandler {

    SushiEventService sushiEventService = Guice.getInstance(SushiEventService.class);
    SushiEventDAO sushiEventDAO = Guice.getInstance(SushiEventDAO.class);
    MaterialService materialService = Guice.getInstance(MaterialService.class);
    UserEventSushiEntity sushiEventUser;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        Arrays.asList(EVENT_SUSHI_INFO_USER_PARTY, EVENT_SUSHI_INFO_CLAN_USER, EVENT_SUSHI_INFO_HISTORY_DEDICATION_SERVER,
                EVENT_SUSHI_CONTRIBUTE_FOOD, EVENT_SUSHI_CONTRIBUTE_EXALTED, EVENT_SUSHI_LEVEL_REWARD_HOT_POT, EVENT_SUSHI_BONUS_HOT_POT).forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);


        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        this.sushiEventUser = sushiEventService.getInfoClanUser(mUser);
        if (sushiEventUser == null) {
            addErrResponse();
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case EVENT_SUSHI_INFO_USER_PARTY -> infoClanEventSushi(); // DONE
                case EVENT_SUSHI_INFO_CLAN_USER -> infoClanUserEventSushi(); // DONE
                case EVENT_SUSHI_INFO_HISTORY_DEDICATION_SERVER -> infoHistoryInServer(); // Done
                case EVENT_SUSHI_CONTRIBUTE_FOOD -> infoUserContributeFood(); // Done
                case EVENT_SUSHI_CONTRIBUTE_EXALTED -> infoUserContributeExalted(); // Done
                case EVENT_SUSHI_LEVEL_REWARD_HOT_POT -> levelRewardHotPot(); // Done
                case EVENT_SUSHI_BONUS_HOT_POT -> bonusHotPot(); //Done
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    // Hiển thị thông tin chung của sự kiện
    private void infoClanEventSushi() {
        ClanEventSushiEntity clanEventSushiEntity = sushiEventService.getInfoPartySushi(mUser);
        Pbmethod.CommonVector.Builder comVector1 = Pbmethod.CommonVector.newBuilder();

        comVector1.addAString(mUser.getUser().getClanName());
        comVector1.addALong(clanEventSushiEntity.getLevel());
        comVector1.addALong(clanEventSushiEntity.getPointLevelNow());
        comVector1.addALong(ConfigEventSushi.config.levelParties.get(clanEventSushiEntity.getLevel()).getPointLevelMax());
        comVector1.addALong(clanEventSushiEntity.getExpVibrant());

        // Hiển thị thông tin của góp sôi nổi
        comVector1.addALong(clanEventSushiEntity.getLevelVibrant());
        comVector1.addALong(clanEventSushiEntity.getPointLevelVibrantNow());
        comVector1.addALong(ConfigEventSushi.config.contributeExalted.get(clanEventSushiEntity.getLevelVibrant()).getNumberLimit());
        addResponse(comVector1.build());

    }

    // Hiển thị thông tin clan của user và bảng xếp hạng liên server
    private void infoClanUserEventSushi() {
        ClanEventSushiEntity getInfoClanSushiEvent = sushiEventDAO.getInfoClanSushiEvent(user.getClan());
        var aClanSushi = sushiEventService.topUserPoint();
        long userClanPointDedication = getInfoClanSushiEvent.getPointDedication();
        int clanRank = getUserRankInSushiEventClan();
        Pbmethod.ClanList.Builder listClan = Pbmethod.ClanList.newBuilder();
        ClanEntity clan = ClanMonitor.getClan(user.getClan());
        listClan.addClan(clan.protoClanInfo().addInfo(String.valueOf(userClanPointDedication)).addInfo(String.valueOf(clanRank)));
        {
            for (ClanEventSushiEntity clanEventSushiEntity : aClanSushi) {
                clan = ClanMonitor.getClan(clanEventSushiEntity.getClanId());
                if (clan != null)
                    listClan.addClan(clan.protoClanInfo().addInfo(String.valueOf(getInfoClanSushiEvent.getPointDedication())));
            }
        }
        System.out.println("listClan: " + listClan.toString());
        addResponse(listClan.build());
    }

    private int getUserRankInSushiEventClan() {
        ClanEventSushiEntity userClan = sushiEventService.getInfoPartySushi(mUser);
        long userClanPointDedication = userClan.getPointDedication();
        List<ClanEventSushiEntity> rankedClans = sushiEventDAO.topClanOfUser(userClanPointDedication);
        int clanRank = 1;
        for (ClanEventSushiEntity clan : rankedClans) {
            if (clan.getPointDedication() > userClanPointDedication) {
                clanRank++;
            } else {
                break;
            }
        }

        return clanRank;
    }

    // Hiển thị lịch sử cống hiến trong server
    private void infoHistoryInServer() {
        ClanEventSushiEntity clanEventSushiEntity = sushiEventService.getInfoPartySushi(mUser);
        System.out.println(clanEventSushiEntity.getClanId());
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        List<UserEventSushiEntity> userList = sushiEventDAO.topUserPoint(clanEventSushiEntity.getClanId());
        userList.forEach(sushiUser -> builder.addAUser(getPbUser(sushiUser)));
        addResponse(builder.build());
    }

    private Pbmethod.PbUser getPbUser(UserEventSushiEntity sushiUser) {
        UserEntity userEntity = UserOnline.getDbUser(sushiUser.getUserId());
        if (userEntity == null) {
            addErrResponse();
            return null;
        }
        Pbmethod.PbUser.Builder pbUser = userEntity.protoTinyUser();
        Pbmethod.CommonVector.Builder commonVector = Pbmethod.CommonVector.newBuilder();
        commonVector.addALong(sushiUser.getPointDedication());
        pbUser.setInfo(commonVector);
        return pbUser.build();
    }

    void infoUserContributeFood() {
        List<Long> listLongInput = CommonProto.parseCommonVector(requestData).getALongList();
        ClanEventSushiEntity clanEventSushiEntity = sushiEventService.getInfoPartySushi(mUser);
        int materialId = listLongInput.get(0).intValue();
        int numberGopVao = listLongInput.get(1).intValue();

        var riceRoll = mUser.getResources().getMaterial(MaterialType.EVENT_SUSHI_RICE_ROLL);
        var salmon = mUser.getResources().getMaterial(MaterialType.EVENT_SUSHI_SALMON);
        var trapSynthetic = mUser.getResources().getMaterial(MaterialType.EVENT_SUSHI_TRAP_SYNTHETIC);

        int maxRiceRoll = (int) riceRoll.getNumber();
        int maxSalmon = (int) salmon.getNumber();
        int maxTrapSynthetic = (int) trapSynthetic.getNumber();

        if (!ValidateParam.number(this, numberGopVao)) return;
        if (numberGopVao > maxRiceRoll || numberGopVao > maxSalmon || numberGopVao > maxTrapSynthetic) {
            addErrResponse();
            return;
        }

        MaterialType materialType = MaterialType.getUsedItem(materialId);
        int rateClanCoin = switch (materialType) {
            case EVENT_SUSHI_RICE_ROLL -> 20;
            case EVENT_SUSHI_SALMON -> 60;
            default -> 200;
        };
        int ratePointDedication = switch (materialType) {
            case EVENT_SUSHI_RICE_ROLL -> 10;
            case EVENT_SUSHI_SALMON -> 30;
            default -> 100;
        };

        int newPointLevelNow = (int) (clanEventSushiEntity.getPointLevelNow() + (ratePointDedication + (ratePointDedication * clanEventSushiEntity.getExpVibrant()) * numberGopVao));
        /* Điểm cấp tiệc=(Điểm cống hiến món ăn + (Điểm cống hiến món ăn * %s buff điểm sôi nổi)* Số lượng món ăn */
        int newLevel = clanEventSushiEntity.getLevel();
        long newPointLevelMax = ConfigEventSushi.config.levelParties.get(newLevel).getPointLevelMax();
        long newPointDedication = (long) numberGopVao * ratePointDedication;

        while (newPointLevelNow >= newPointLevelMax) {
            newLevel++;
            newPointLevelNow -= (int) newPointLevelMax;
            newPointLevelMax = ConfigEventSushi.config.levelParties.get(newLevel).getPointLevelMax();
        }

        // Lưu DB
        if (!dbUpdateUserSushi(newLevel, newPointLevelNow, newPointDedication)) {
            addErrResponse();
            return;
        }

        // Lưu logs
        Actions.save(user, "event_sushi", "contribute_food", "level", newLevel, "point_level_now", newPointLevelNow, "point_dedication", newPointDedication);

        // Lưu vào object (cache)
        clanEventSushiEntity.setLevel(newLevel);
        clanEventSushiEntity.setPointLevelNow(newPointLevelNow);
        clanEventSushiEntity.setPointDedication(newPointDedication);

        ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, BonusBuilder.newInstance()
                .addMaterial(materialType, -numberGopVao)
                .addMaterial(MaterialType.EVENT_SUSHI_CLAN_COINS, (long) numberGopVao * rateClanCoin)
                .addMaterial(MaterialType.EVENT_SUSHI_POINT_DEDICATION, (long) numberGopVao * ratePointDedication)
                .build(), "event_sushi_contribute_food");
        if (serviceResult.success) {
            addResponse(Pbmethod.CommonVector.newBuilder()
                    .addALong(newLevel).addALong(newPointLevelNow).addALong(newPointLevelMax).addALong(newPointDedication)
                    .addAllALong(List.of(3L, 1L, 100000L, 11L, (long) numberGopVao)) // chỗ này để trả về cho user
                    .addAllALong(serviceResult.data)
                    .build());
        } else serviceResult.writeResponse(this);
    }

    private void infoUserContributeExalted() {
        ClanEventSushiEntity getInfoClanSushiEvent = sushiEventDAO.getInfoClanSushiEvent(user.getClan());
        List<Long> listLongInput = CommonProto.parseCommonVector(requestData).getALongList();
        int materialId = listLongInput.get(0).intValue();
        int numberGopVao = listLongInput.get(1).intValue();

        var specialSoySauce = mUser.getResources().getMaterial(MaterialType.EVENT_SUSHI_SPECIAL_SOY_SAUCE);

        int maxSpicyChilli = (int) specialSoySauce.getNumber();

        if (!ValidateParam.number(this, numberGopVao)) return;

        if (numberGopVao > maxSpicyChilli) {
            addErrResponse("Số lượng nhập vào vượt quá số lượng hiện có");
            return;
        }

        MaterialType materialType = MaterialType.getUsedItem(materialId);

        int rateClanCoin;
        int rateQuantityItem;
        if (materialType == MaterialType.EVENT_SUSHI_SPECIAL_SOY_SAUCE) {
            rateClanCoin = 5;
            rateQuantityItem = 1;
        } else {
            rateClanCoin = 0;
            rateQuantityItem = 0;
        }

        int newPointLevelNow = getInfoClanSushiEvent.getPointLevelVibrantNow() + (numberGopVao * rateQuantityItem);
        int newLevel = getInfoClanSushiEvent.getLevelVibrant();
        int newPointLevelMax = ConfigEventSushi.config.contributeExalted.get(newLevel).getNumberLimit();

        while (newPointLevelNow >= newPointLevelMax) {
            newLevel++;
            newPointLevelNow -= newPointLevelMax;
            newPointLevelMax = ConfigEventSushi.config.contributeExalted.get(newLevel).getNumberLimit();
        }

        // Lưu DB
        if (!dbUpdateContributeExalted(newLevel, newPointLevelNow)) {
            addErrResponse();
            return;
        }
        //Logs
        Actions.save(user, "event_sushi", "contribute_exalted", "level", newLevel, "point_level_now", newPointLevelNow);

        // Save cache
        getInfoClanSushiEvent.setLevelVibrant(newLevel);
        getInfoClanSushiEvent.setPointLevelVibrantNow(newPointLevelNow);

        ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, BonusBuilder.newInstance()
                .addMaterial(materialType, -numberGopVao)
                .addMaterial(MaterialType.EVENT_SUSHI_CLAN_COINS, (long) numberGopVao * rateClanCoin)
                .build(), "event_sushi_contribute_exalted");
        if (serviceResult.success) {
            addResponse(Pbmethod.CommonVector.newBuilder()
                    .addALong(newLevel).addALong(newPointLevelNow).addALong(newPointLevelMax)
                    .addAllALong(List.of(3L, 1L, 100000L, 11L, (long) numberGopVao)) // chỗ này để trả về cho user
                    .addAllALong(serviceResult.data)
                    .build());
        } else serviceResult.writeResponse(this);

    }

    void levelRewardHotPot() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        if (sushiEventUser == null) return;
        for (int i = 0; i < ConfigEventSushi.config.levelParties.size(); i++) {
            int level = i + 1;
            int levelStatus = sushiEventUser.getStatus(level);
            List<Long> bonus = ConfigEventSushi.config.levelParties.get(i).getBonus();
            Pbmethod.CommonVector.Builder cmmVectorBuilder = Pbmethod.CommonVector.newBuilder();
            cmmVectorBuilder.addALong(level).addALong(levelStatus).addAllALong(bonus);
            builder.addAVector(cmmVectorBuilder);
        }
        addResponse(builder.build());
    }


    public void bonusHotPot() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int level = aLong.get(0).intValue();
        int currentStatus = sushiEventUser.getStatus(level);
        switch (currentStatus) {
            case ConfigEventSushi.SUSHI_EVENT_NOT_COMPLETED -> addErrResponse(getLang(Lang.event_sushi_unqualified));
            case ConfigEventSushi.SUSHI_EVENT_RECEIVED -> addErrResponse(getLang(Lang.event_sushi_accomplished));
            case ConfigEventSushi.SUSHI_EVENT_NOT_RECEIVED -> {
                List<Long> bonus = ConfigEventSushi.config.levelParties.get(level - 1).getBonus();
                bonus = Bonus.receiveListItem(mUser, "sushi_event_receive", bonus);
                int newStatus = ConfigEventSushi.SUSHI_EVENT_RECEIVED;
                Map<Integer, Integer> newStatusMapByLevel = new HashMap<>();
                newStatusMapByLevel.put(level, newStatus);
                String newUserSushiStatus = sushiEventUser.getNewStatus(newStatusMapByLevel);
                if (!dbUpdateUserSushiStatus(sushiEventUser, newUserSushiStatus)) return;
                Pbmethod.CommonVector.Builder cmmVectorBuilder = Pbmethod.CommonVector.newBuilder();
                cmmVectorBuilder.addALong(newStatus);
                cmmVectorBuilder.addAllALong(bonus);
                addResponse(cmmVectorBuilder.build());
                Actions.save(user, "event_sushi", "receive", "level", level, "status", newStatus, "bonus", bonus);

            }
        }
    }

    private boolean dbUpdateUserSushiStatus(UserEventSushiEntity sushiEventUser, String newUserSushiStatus) {
        if (!dbUpdateSushi(newUserSushiStatus)) {
            addErrResponse();
            return false;
        }
        sushiEventUser.setStatus(newUserSushiStatus);
        return true;
    }

    private boolean dbUpdateSushi(String newStatus) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_event_sushi set status=:newStatus where user_id=:userId and event_id=:eventId");
            query.setParameter("newStatus", newStatus);
            query.setParameter("userId", user.getId());
            query.setParameter("eventId", sushiEventUser.getEventId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateUserSushi(int newLevel, int newPointLevelNow, long newPointDedication) {
        ClanEntity clan = ClanMonitor.getClan(user.getClan());
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.clan_event_sushi set level=:newLevel, point_level_now=:newPointLevelNow, point_dedication=:newPointDedication where clan_id =:clanId and event_id=:eventId");
            query.setParameter("newLevel", newLevel);
            query.setParameter("newPointLevelNow", newPointLevelNow);
            query.setParameter("newPointDedication", newPointDedication);
            query.setParameter("clanId", clan.getId());
            query.setParameter("eventId", sushiEventUser.getEventId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateContributeExalted(int newLevelVibrant, int newPointLeveVibrantNow) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.clan_event_sushi set level_vibrant=:newLevelVibrant, point_level_vibrant_now=:newPointLeveVibrantNow where userId =:userId");
            query.setParameter("newLevelVibrant", newLevelVibrant);
            query.setParameter("newPointLeveVibrantNow", newPointLeveVibrantNow);
            query.setParameter("userId", user.getId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }
}