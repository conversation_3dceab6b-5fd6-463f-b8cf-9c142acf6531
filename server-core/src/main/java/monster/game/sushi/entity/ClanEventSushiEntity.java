package monster.game.sushi.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "clan_event_sushi")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ClanEventSushiEntity implements Serializable {
    @Id
    int clanId;
    @Id
    int eventId;
    int level, pointLevelNow;
    int expVibrant;
    long pointDedication;
    int pointLevelVibrantNow, levelVibrant;

    public ClanEventSushiEntity(int clan, int eventId) {
        this.clanId = clan;
        this.eventId = eventId;
    }

    public ClanEventSushiEntity(int clan) {
        this.clanId = clan;
    }
}
