package monster.game.luckyspin;

import com.google.gson.JsonArray;
import grep.helper.*;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.dao.mapping.UserMaterialEntity;
import monster.game.luckyspin.entity.ResLuckySpinEntity;
import monster.game.luckyspin.entity.ResLuckySpinItemEntity;
import monster.game.luckyspin.entity.UserLuckySpinEntity;
import monster.service.resource.ResLuckySpin;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class Lucky<PERSON>pinHandler extends AHandler {
    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        Arrays.asList(LUCKY_SPIN_STATUS, LUCKY_SPIN_WISH, LUCKY_SPIN_ROLL)
                .forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");

        debug("---------aLongInput: " + getInputALong());

        if (!FunctionType.SWORDSMANSHIP.isEnable()) {
            addErrResponse(getLang(Lang.user_function_closed));
            return;
        }

        try {
            switch (actionId) {
                case LUCKY_SPIN_STATUS -> luckySpinStatus();
                case LUCKY_SPIN_WISH -> wish();
                case LUCKY_SPIN_ROLL -> roll();
            }
        } catch (Exception ex) {
            Logs.error(user.getUsername() + " -> " + GUtil.exToString(ex));
        }
    }

    //region Handle Services
    void luckySpinStatus() {
        int spinId = getInputInt();
        ResLuckySpinEntity resLuckySpin = ResLuckySpin.getResLuckySpinById(spinId);
        if (resLuckySpin == null) {
            addErrResponse(getLang(Lang.user_function_closed));
            return;
        }

        UserLuckySpinEntity userLuckySpin = mUser.getCache().getUserLuckySpin(this, mUser, spinId);
        if (userLuckySpin == null) return;

        Pbmethod.ListCommonVector.Builder listCommonVector = Pbmethod.ListCommonVector.newBuilder();
        {
            Pbmethod.CommonVector.Builder commonVector = Pbmethod.CommonVector.newBuilder();
            commonVector.addALong(userLuckySpin.getLuckyPoint()).addALong(resLuckySpin.getLimitLuckyPoint())
                    .addALong(userLuckySpin.getWishedIndex()).addALong(resLuckySpin.isOpen() ? 1 : 0).addALong(resLuckySpin.getCountdown());
            listCommonVector.addAVector(commonVector);
        }
        resLuckySpin.getListLuckySpinItem().forEach(luckySpinItem -> listCommonVector.addAVector(luckySpinItem.toProto()));

        addResponse(listCommonVector.build());
    }

    void wish() {
        List<Long> aLongInput = getInputALong();
        int spinId = aLongInput.get(0).intValue(), rewardIndex = aLongInput.get(1).intValue();
        ResLuckySpinEntity resLuckySpin = ResLuckySpin.getResLuckySpinById(spinId);
        if (resLuckySpin == null || !resLuckySpin.isOpen()) {
            addErrResponse(getLang(Lang.user_function_closed));
            return;
        }

        ResLuckySpinItemEntity resLuckySpinItem = resLuckySpin.getListLuckySpinItem().get(rewardIndex);
        if (resLuckySpinItem == null || !resLuckySpinItem.isEpic()) {
            addErrParams();
            return;
        }


        UserLuckySpinEntity userLuckySpin = mUser.getCache().getUserLuckySpin(this, mUser, spinId);
        if (userLuckySpin == null) return;

        if (!userLuckySpin.updateWishedIndex(rewardIndex)) {
            addErrResponse();
            return;
        }

        addResponse(getCommonVector(rewardIndex));
    }

    void roll() {
        List<Long> aLongInput = getInputALong();
        debug("--------------aLongInput: " + aLongInput);
        int spinId = aLongInput.get(0).intValue(), number = aLongInput.get(1).intValue();
        if (number <= 0) {
            addErrParams();
            return;
        }

        ResLuckySpinEntity resLuckySpin = ResLuckySpin.getResLuckySpinById(spinId);
        if (resLuckySpin == null || !resLuckySpin.isOpen()) {
            addErrResponse(getLang(Lang.user_function_closed));
            return;
        }

        UserLuckySpinEntity userLuckySpin = mUser.getCache().getUserLuckySpin(this, mUser, spinId);
        if (userLuckySpin == null) return;

        List<Long> aBonusFee = new ArrayList<>();
        List<Long> aBonus = new ArrayList<>();
        List<Long> listRewardIndex = new ArrayList<>();
        List<Float> listRate = resLuckySpin.getListLuckySpinItem().stream().map(ResLuckySpinItemEntity::getPercent).collect(Collectors.toList());
        UserMaterialEntity userMaterial = mUser.getResources().getMaterial(MaterialType.TYPE_USED_ITEM, resLuckySpin.getMaterialId());
        if (userMaterial != null && number > userMaterial.getNumber()) number = (int) userMaterial.getNumber();

        for (int i = 0; i < number; i++) {
            JsonArray arrConvertPrice = GsonUtil.parseFromListLong(Bonus.viewMaterialById(resLuckySpin.getMaterialId(), 1));
            if (!mUser.checkPrice(this, arrConvertPrice))
                return;

            List<Long> tmpBonusFee = Bonus.receiveListItem(mUser, arrConvertPrice, "lucky_spin_fee");
            if (tmpBonusFee.isEmpty()) {
                addErrResponse();
                return;
            }
            aBonusFee.addAll(tmpBonusFee);

            boolean isWish = userLuckySpin.getLuckyPoint() >= resLuckySpin.getLimitLuckyPoint();
            ResLuckySpinItemEntity randomItem = isWish ? resLuckySpin.getListLuckySpinItem().get(userLuckySpin.getWishedIndex()) :
                    LogicUtil.getRandomByRateFloat(resLuckySpin.getListLuckySpinItem(), listRate);
            if (randomItem == null) {
                addErrResponse();
                return;
            }

            int newLuckyPoint = randomItem.isEpic() ? 0 : userLuckySpin.getLuckyPoint() + 10;
            if (!userLuckySpin.updateLuckyPoint(newLuckyPoint)) {
                addErrResponse();
                return;
            }
            listRewardIndex.add((long) randomItem.getRewardIndex());
            aBonus.addAll(randomItem.getReward());
        }

        aBonus = Bonus.receiveListItem(mUser, "lucky_spin_reward", aBonus);
        aBonus.addAll(aBonusFee);
        addResponse(Pbmethod.CommonVector.newBuilder().addALong(userLuckySpin.getLuckyPoint()).addAllALong(listRewardIndex).addAllALong(aBonus).build());
    }
    //endregion
}
