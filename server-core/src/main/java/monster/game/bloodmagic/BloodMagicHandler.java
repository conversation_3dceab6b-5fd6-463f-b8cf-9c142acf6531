package monster.game.bloodmagic;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.NotifyType;
import monster.controller.AHandler;
import monster.dao.mapping.UserHeroEntity;
import monster.game.bloodmagic.config.ConfigBloodMagic;
import monster.game.bloodmagic.entity.UserBloodMagicEntity;
import monster.game.bloodmagic.service.BloodMagicService;
import monster.game.truongevent.service.TruongProEventService;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.common.NotifyService;
import monster.service.resource.ResBloodFusion;
import monster.service.user.Actions;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.BitSet;
import java.util.List;
import java.util.Map;

public class BloodMagicHandler extends AHandler {
    BloodMagicService bloodMagicService = Guice.getInstance(BloodMagicService.class);
    UserBloodMagicEntity userBloodMagic;
    TruongProEventService truongProEventService = Guice.getInstance(TruongProEventService.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(BLOOD_MAGIC_STATUS_BLOOD_MAGIC, BLOOD_MAGIC_UNLOCK_SKILL_IN_GROUP,
                BLOOD_MAGIC_UNLOCK_GROUP_FLOOR, BLOOD_MAGIC_STATUS_STATUS_POINT_SKILL
        );
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        if (!FunctionType.BLOOD_MAGIC.isEnable(mUser, this)) return;
        this.userBloodMagic = bloodMagicService.getUserBloodMagic(mUser);
        if (userBloodMagic == null) {
            addErrResponse();
            return;
        }
        if (user.getLevel() < ResBloodFusion.aBloodFusion.get(0).getLevelUnlock()) {
            addErrResponse("Bạn cần đạt đến level: " + ResBloodFusion.aBloodFusion.get(0).getLevelUnlock() + " để tham gia tính năng");
            return;
        }

        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case BLOOD_MAGIC_STATUS_BLOOD_MAGIC -> status();
                case BLOOD_MAGIC_UNLOCK_SKILL_IN_GROUP -> upSkillInGroup();
                case BLOOD_MAGIC_UNLOCK_GROUP_FLOOR -> upGroupByFloor();
                case BLOOD_MAGIC_STATUS_STATUS_POINT_SKILL -> toPointSkillProto();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void status() {
        UserBloodMagicEntity userBloodMagic = bloodMagicService.getUserBloodMagic(mUser);
        BitSet bitSet = truongProEventService.monthlyBloodMagicStatus(mUser);
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();

        int floor = userBloodMagic.getFloor();
        if (floor <= 0 || floor > ResBloodFusion.aBloodFusion.size()) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        List<List<Long>> listExpByFloor = GsonUtil.strTo2ListLong(ResBloodFusion.aBloodFusion.get(floor - 1).getExp());
        List<Long> listUpgradeByGroup = GsonUtil.strToListLong(ResBloodFusion.aBloodFusion.get(floor - 1).getUpgrade());
        int groupByFloor = userBloodMagic.getGroupByFloor();
        long fee = listUpgradeByGroup.get(groupByFloor - 1);
        if (groupByFloor <= 0 || groupByFloor > listExpByFloor.size()) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        boolean monthlyBloodMagic = bitSet != null && bitSet.get(0);
        int numberLevelCampaign = Services.userService.getCampaign(mUser).getLevel() / 10;
        double numberMonthly = (double) ConfigBloodMagic.config.getNumberMonthlyBloodMagic() / 100;
        int percent = (int) (numberLevelCampaign * numberMonthly);
        int monthlyBloodMagicStatus = monthlyBloodMagic ? percent : 0;
        List<Long> listExpByGroup = listExpByFloor.get(groupByFloor - 1);
        int skillNowInGroup = userBloodMagic.getSkillNowInGroup();
        long statusSkillInGroup = skillNowInGroup == 5 ? fee : listExpByGroup.get(skillNowInGroup);
        int levelCampaign = Services.userService.getCampaign(mUser).getLevel();
        builder.addALong(floor).addALong(groupByFloor).addALong(skillNowInGroup)
                .addALong(userBloodMagic.getEnergyManager().getValue())
                .addALong(statusSkillInGroup).addALong((long) levelCampaign / 10)
                .addALong(monthlyBloodMagicStatus)
                .addALong(userBloodMagic.getEnergyManager().countdownToNextRecover());
        addResponse(builder.build());
        bloodMagicService.checkNotify(mUser);
    }

    private void upSkillInGroup() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        UserBloodMagicEntity userBloodMagic = bloodMagicService.getUserBloodMagic(mUser);
        if (userBloodMagic == null) {
            addErrResponse();
            return;
        }
        long pointNow = userBloodMagic.getEnergyManager().getValue();
        int skillNowInGroup = userBloodMagic.getSkillNowInGroup();
        int floor = userBloodMagic.getFloor();
        if (floor <= 0 || floor > ResBloodFusion.aBloodFusion.size()) {
            addErrResponse();
            return;
        }
        List<List<Long>> listExpByFloor = GsonUtil.strTo2ListLong(ResBloodFusion.aBloodFusion.get(floor - 1).getExp());
        List<Long> listExpByGroup = listExpByFloor.get(userBloodMagic.getGroupByFloor() - 1);
        long expMaxSkillNow = listExpByGroup.get(skillNowInGroup);
        if (expMaxSkillNow > pointNow) {
            Guice.getInstance(NotifyService.class).remove(mUser, NotifyType.BLOOD_MAGIC_UNLOCK_SKILL_IN_GROUP);
            addErrResponse("Cần thêm Chú Lực để đột phá (%s)".formatted(userBloodMagic.getEnergyManager().getValue()));
            return;
        } else {
            int newSkillNowInGroup = skillNowInGroup + 1;
            if (!dbUpdateStatus(newSkillNowInGroup, userBloodMagic.getFloor())) {
                addErrResponse();
                return;
            }
            bloodMagicService.addExp(mUser, userBloodMagic, -expMaxSkillNow);
            userBloodMagic.setSkillNowInGroup(newSkillNowInGroup);
            // save logs
            Actions.save(user, "blood_magic", "up_skill_in_group", "skill_in_group_new", newSkillNowInGroup, "floor_of_skill", userBloodMagic.getFloor());
            calculateSlotHero();
        }
        addResponse(builder.build());
    }

    private void upGroupByFloor() {
        int floor = userBloodMagic.getFloor();
        int groupId = userBloodMagic.getGroupByFloor();
        int skillInGroup = userBloodMagic.getSkillNowInGroup();
        List<Long> listUpgradeByGroup = GsonUtil.strToListLong(ResBloodFusion.aBloodFusion.get(floor - 1).getUpgrade());
        List<List<Long>> listExpByFloor = GsonUtil.strTo2ListLong(ResBloodFusion.aBloodFusion.get(floor - 1).getExp());
        List<Long> listSkillByGroup = listExpByFloor.get(userBloodMagic.getGroupByFloor() - 1);

        if (user.getLevel() < ResBloodFusion.aBloodFusion.get(floor).getLevelUnlock()) {
            addErrResponse("Bạn cần đạt đến level: " + ResBloodFusion.aBloodFusion.get(userBloodMagic.getFloor()).getLevelUnlock() + " để có thể nâng cấp tầng!");
            return;
        }

        long expNow = userBloodMagic.getEnergyManager().getValue();
        long fee = listUpgradeByGroup.get(groupId - 1);
        int skillGroupNew = 0;
        if (expNow < fee) {
            Guice.getInstance(NotifyService.class).remove(mUser, NotifyType.BLOOD_MAGIC_UNLOCK_NEXT_GROUP);
            addErrResponse("Cần thêm Chú Lực để phá cảnh");
            return;
        } else {
            if (groupId < listUpgradeByGroup.size() && skillInGroup == listSkillByGroup.size()) {
                bloodMagicService.addExp(mUser, userBloodMagic, -(fee));
                int groupByFloorNew = userBloodMagic.getGroupByFloor() + 1;
                if (!dbUpdateData(groupByFloorNew, skillGroupNew)) {
                    addErrResponse();
                    return;
                }
                userBloodMagic.setGroupByFloor(groupByFloorNew);
                userBloodMagic.setStatus(1);
                userBloodMagic.setSkillNowInGroup(0);
                // save logs
                Actions.save(user, "blood_magic", "up_group_by_floor", "floor", userBloodMagic.getFloor(), "group_by_floor_new", userBloodMagic.getGroupByFloor(), "skill_now_in_group", userBloodMagic.getSkillNowInGroup());
                calculateSlotHero();
            } else if (groupId == listUpgradeByGroup.size() && skillInGroup == listSkillByGroup.size()) {
                bloodMagicService.addExp(mUser, userBloodMagic, -(fee));
                // cần update cả floor nữa
                int floorNew = floor + 1;
                int groupByFloorNew = 1;
                int skillNowInGroup = 0;
                if (!dbUpdateFloor(floorNew, groupByFloorNew, skillNowInGroup)) {
                    addErrResponse();
                    return;
                }
                userBloodMagic.setFloor(floorNew);
                userBloodMagic.setGroupByFloor(groupByFloorNew);
                userBloodMagic.setStatus(1);
                userBloodMagic.setSkillNowInGroup(skillNowInGroup);
                // save logs
                Actions.save(user, "blood_magic", "up_group_by_floor", "floor_new", userBloodMagic.getFloor(), "group_by_floor_new", userBloodMagic.getGroupByFloor(), "skill_now_in_group", userBloodMagic.getSkillNowInGroup());
                calculateSlotHero();
            }
        }
        addResponse(null);
    }

    void calculateSlotHero() {
        List<UserHeroEntity> userHero = mUser.getResources().getHeroes();
        for (UserHeroEntity hero : userHero) {
            if (hero.getShareStatus() == 2 || hero.getShareStatus() == 1) {
                UserHeroEntity heroUser = mUser.getResources().getHero(hero.getId());
                if (heroUser != null) heroUser.calculatePointHero(mUser);
            }
        }
    }

    void toPointSkillProto() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        BitSet bitSet = truongProEventService.monthlyBloodMagicStatus(mUser);
        boolean monthlyBloodMagic = bitSet != null && bitSet.get(0);
        int numberLevelCampaign = Services.userService.getCampaign(mUser).getLevel() / 10;
        double numberMonthly = (double) ConfigBloodMagic.config.getNumberMonthlyBloodMagic() / 100;
        int percent = (int) (numberLevelCampaign * numberMonthly);
        int monthlyBloodMagicStatus = monthlyBloodMagic ? percent : 0;
        builder.addALong(userBloodMagic.getEnergyManager().getValue())
                .addALong(userBloodMagic.getEnergyManager().countdownToNextRecover())
                .addALong(monthlyBloodMagicStatus);
        addResponse(builder.build());
    }

    private boolean dbUpdateData(int groupSkillFloor, int skillNowInGroup) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_blood_magic set group_by_floor=:groupSkillFloor,skill_now_in_group=:skillNowInGroup where user_id=:userId");
            query.setParameter("userId", user.getId());
            query.setParameter("groupSkillFloor", groupSkillFloor);
            query.setParameter("skillNowInGroup", skillNowInGroup);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(ex);
        } finally {
            closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateStatus(int newSkillNowInGroup, int floor) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_blood_magic set skill_now_in_group=:newSkillNowInGroup where user_id=:userId and floor=:floor");
            query.setParameter("newSkillNowInGroup", newSkillNowInGroup);
            query.setParameter("userId", user.getId());
            query.setParameter("floor", floor);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateFloor(int floorNew, int groupByFloorNew, int skillNowInGroup) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_blood_magic set floor=:floorNew,group_by_floor=:groupByFloorNew, skill_now_in_group=:skillNowInGroup where user_id=:userId");
            query.setParameter("floorNew", floorNew);
            query.setParameter("userId", user.getId());
            query.setParameter("groupByFloorNew", groupByFloorNew);
            query.setParameter("skillNowInGroup", skillNowInGroup);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }
}
