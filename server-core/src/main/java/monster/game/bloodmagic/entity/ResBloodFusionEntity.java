package monster.game.bloodmagic.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "res_blood_fusion")
@Data
public class ResBloodFusionEntity {
    @Id
    private int id;
    private int levelUnlock;
    private String nameKey;
    private String name;
    private String data;
    private String exp;
    private String upgrade;
}
