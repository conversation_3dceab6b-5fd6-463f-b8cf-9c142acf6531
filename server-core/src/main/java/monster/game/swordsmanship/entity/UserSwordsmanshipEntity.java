package monster.game.swordsmanship.entity;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Entity
@Table(name = "user_swordsmanship")
@NoArgsConstructor
@Data
public class UserSwordsmanshipEntity {
    @Id
    int userId;
    String shards;
    Date lastTimeRecruit;
    @Transient
    boolean checkFragmentGift = false;

    public UserSwordsmanshipEntity(int userId) {
        this.userId = userId;
    }

    public List<Integer> getShards() {
        if (shards == null || shards.isEmpty()) return new ArrayList<>();
        return GsonUtil.strToListInt(shards);
    }

    public int getNumberShard(int bookKey) {
        List<Integer> result = getShards();
        for (int i = 0; i < result.size(); i += 2) {
            if (result.get(i) == bookKey) return result.get(i + 1);
        }
        return 0;
    }

    public String getNewShards(int bookKey, int num) {
        List<Integer> result = getShards();
        for (int i = 0; i < result.size(); i += 2) {
            if (result.get(i) == bookKey) {
                result.set(i + 1, num);
                return StringHelper.toDBString(result);
            }
        }
        // neu chua co id shard nay thi se chay vao phan duoi nay
        result.add(bookKey);
        result.add(num);
        return StringHelper.toDBString(result);
    }

    public String getNewShards(List<List<Integer>> listShardInfo) {
        List<Integer> result = getShards();
        for (List<Integer> shardInfo : listShardInfo) {
            int bookKey = shardInfo.get(1);
            int numberAdd = shardInfo.get(2);

            boolean isShardContain = false;
            for (int i = 0; i < result.size(); i += 2) {
                if (result.get(i) != bookKey) continue;

                result.set(i + 1, result.get(i + 1) + numberAdd);
                isShardContain = true;
            }

            if (!isShardContain) {
                result.add(bookKey);
                result.add(numberAdd);
            }
        }
        return StringHelper.toDBString(result);
    }

    public boolean updateShards(String newShards) {
        if (DBJPA.update("dson.user_swordsmanship", Arrays.asList("shards", newShards), Arrays.asList("user_id", userId))) {
            shards = newShards;
            return true;
        }

        return false;
    }

    public boolean updateLastTimeRecruit() {
        if (DBJPA.update("dson.user_swordsmanship", Arrays.asList("last_time_recruit", DateTime.getFullDate()), Arrays.asList("user_id", userId))) {
            lastTimeRecruit = new Date();
            return true;
        }

        return false;
    }
}
