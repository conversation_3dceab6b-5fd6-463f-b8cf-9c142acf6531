package monster.game.swordsmanship.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@NoArgsConstructor
@Getter
@Entity
@Table(name = "res_swordsmanship_book")
public class ResSwordsmanshipBookEntity {
    @Id
    int id;
    String name,clazz,upgradeSkill;
    int generic, rank,passive1, passive2, passive3,shard;
    float basicAttackMin, basicAttackMax;
    float basicHpMin, basicHpMax;
    float basicDefMin, basicDefMax;
    float basicSpdMin, basicSpdMax;
    float intelligentMin, intelligentMax;
}
