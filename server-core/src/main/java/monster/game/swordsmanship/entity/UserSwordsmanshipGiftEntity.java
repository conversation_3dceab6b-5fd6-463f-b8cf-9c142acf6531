package monster.game.swordsmanship.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Entity
@Data
@Table(name = "user_pet2_gift")
@NoArgsConstructor
public class UserSwordsmanshipGiftEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    int id;
    int userId, petId, number, receive;
    Date dateCreated;
}
