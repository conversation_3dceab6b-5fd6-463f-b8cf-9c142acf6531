package monster.game.swordsmanship.service.impl;

import com.google.inject.Inject;
import grep.helper.DateTime;
import grep.log.Logs;
import monster.config.CfgSwordsmanship;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.MaterialType;
import monster.config.penum.NotifyType;
import monster.dao.mapping.UserMaterialEntity;
import monster.game.swordsmanship.entity.ResSwordsmanshipBookEntity;
import monster.game.swordsmanship.entity.UserSwordsmanshipBookEntity;
import monster.game.swordsmanship.entity.UserSwordsmanshipEntity;
import monster.game.swordsmanship.service.SwordsmanshipService;
import monster.object.MyUser;
import monster.object.UserNotify;
import monster.server.config.Guice;
import monster.service.common.NotifyService;
import protocol.Pbmethod;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class SwordsmanshipServiceImpl implements SwordsmanshipService {
    @Inject
    NotifyService notifyService;

    @Override
    public void checkNotify(MyUser mUser) {
        try {
            AtomicBoolean isNotify = new AtomicBoolean(false);
            UserSwordsmanshipEntity userSwordsmanship = mUser.getCache().getUserSwordsmanship(null, mUser);
            if (userSwordsmanship != null) {
                Date lastTimeRecruit = userSwordsmanship.getLastTimeRecruit();
                if (lastTimeRecruit == null || DateTime.getMinuteDiff(lastTimeRecruit, new Date()) >= 8 * 60)
                    isNotify.set(true);

                int itemId = CfgSwordsmanship.config.getRecruitFee().get(0);
                int totalFee = CfgSwordsmanship.config.getRecruitFee().get(1);
                UserMaterialEntity itemFee = mUser.getResources().getMaterial(MaterialType.TYPE_USED_ITEM, itemId);
                if (itemFee.getNumber() >= totalFee) isNotify.set(true);

                CfgSwordsmanship.resBookMapById.values().forEach(resSwordsmanshipBook -> {
                    int numberShard = userSwordsmanship.getNumberShard(resSwordsmanshipBook.getId());
                    int shardRequire = resSwordsmanshipBook.getShard();
                    if (numberShard >= shardRequire) isNotify.set(true);
                });
            }

            notifyService.sendNotify(mUser, NotifyType.SWORDSMANSHIP, isNotify.get());
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private boolean enoughStar(Map<Integer, List<Integer>> mBookStarId, UserSwordsmanshipBookEntity bookEntity, int star, int numberRequired) {
        if (numberRequired == 0) return true;
        if (mBookStarId.containsKey(star)) {
            List<Integer> list = mBookStarId.get(star);
            int number = list.contains(bookEntity.getId()) ? list.size() - 1 : list.size();
            return number >= numberRequired;
        }
        return false;
    }
}
