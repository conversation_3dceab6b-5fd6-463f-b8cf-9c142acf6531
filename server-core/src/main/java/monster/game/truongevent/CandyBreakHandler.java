package monster.game.truongevent;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.game.truongevent.entity.ResCandyBreakMapEntity;
import monster.game.truongevent.entity.UserCandyBreakEntity;
import monster.game.truongevent.service.CandyBreakService;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.resource.ResCandyBreak;
import monster.service.user.Actions;
import monster.service.user.BonusBuilder;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class CandyBreakHandler extends AHandler {
    CandyBreakService candyBreakService = Guice.getInstance(CandyBreakService.class);
    MaterialService materialService = Guice.getInstance(MaterialService.class);
    UserCandyBreakEntity userCandyBreak;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        Arrays.asList(EVENT_CANDY_BREAK_INFO, EVENT_CANDY_BREAK_XEP, EVENT_CANDY_BREAK_XEP_NHANH, EVENT_CANDY_BREAK_END_MAP).forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        userCandyBreak = candyBreakService.getInfoCandyBreak(mUser);
        if (userCandyBreak == null) {
            addErrResponse();
            return;
        }

        try {
            switch (actionId) {
                case EVENT_CANDY_BREAK_INFO -> info(); // Done
                case EVENT_CANDY_BREAK_XEP -> xep(getInputALong()); // Done
                case EVENT_CANDY_BREAK_XEP_NHANH -> xoaNhanh();
                case EVENT_CANDY_BREAK_END_MAP -> endMap();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void info() {
        UserCandyBreakEntity userCandyBreak = candyBreakService.getInfoCandyBreak(mUser);
        ResCandyBreakMapEntity resCandyBreakMap = ResCandyBreak.resCandyBreakMapById.get(userCandyBreak.getMapId());
        Pbmethod.ListCommonVector.Builder listCommonVector = Pbmethod.ListCommonVector.newBuilder();
        Pbmethod.CommonVector.Builder comVector = Pbmethod.CommonVector.newBuilder();
        comVector.addALong(userCandyBreak.getShapeBefore()).addALong(userCandyBreak.getShapeAfter()).
                addALong(userCandyBreak.getStatus()).addALong(userCandyBreak.getExclusiveRight()).
                addALong(userCandyBreak.getExclusiveRight()).addAString(resCandyBreakMap.getName()).
                addALong(userCandyBreak.getMapId());
        listCommonVector.addAVector(comVector);
        Pbmethod.CommonVector.Builder commonVector2 = Pbmethod.CommonVector.newBuilder();
        JSONObject dataArray = JSONObject.fromObject(userCandyBreak.getSharpMap());
        JSONArray candyArray = dataArray.getJSONArray("lstCandy");
        for (int i = 0; i < candyArray.size(); i++) {
            JSONObject candyData = candyArray.getJSONObject(i);
            int id = candyData.getInt("id");
            int column = candyData.getInt("y");
            int row = candyData.getInt("x");
            commonVector2.addALong(row).addALong(column).addALong(id);
        }
        listCommonVector.addAVector(commonVector2);

        //Bonus.viewMaterial(MaterialType.EVENT_CANDY_YELLOW_STAR,20):  [3, 1, 100000, 20]
//        listCommonVector.addAVector(getCommonVector(Bonus.viewMaterial(MaterialType.EVENT_CANDY_YELLOW_STAR, 20)));
        addResponse(listCommonVector.build());
    }

    private void xep(List<Long> listData) {
        UserCandyBreakEntity candyBreakEntity = candyBreakService.getInfoCandyBreak(mUser);
        int addStar = 0;
        int count = 0;
        long row, column;
        if (listData.isEmpty()) {
            addErrResponse("Không tìm thấy vị trí phù hợp");
            return;
        }

        Pbmethod.ListCommonVector.Builder listCommonVector = Pbmethod.ListCommonVector.newBuilder();
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        Pbmethod.CommonVector.Builder commonVector2 = Pbmethod.CommonVector.newBuilder();

        int shapeBeforeNew = candyBreakEntity.getShapeAfter();
        int shapeAfterNew = candyBreakService.generateRandomNumber();
        // Logs
        Actions.save(user, "candy_break", "xep_hinh", "shapeBeforeNew", shapeAfterNew, "shapeBeforeNew", shapeBeforeNew);
        // Save DB
        if (!dbUpdate(candyBreakEntity, shapeAfterNew, shapeBeforeNew)) {
            addErrResponse();
            return;
        }
        // Save cache
        candyBreakEntity.setShapeAfter(shapeAfterNew);
        for (int i = 0; i < listData.size(); i += 2) {
            row = listData.get(i).intValue();
            column = listData.get(i + 1).intValue();
            // Bonus
            if (candyBreakEntity.getIdByXY((int) row, (int) column) != 0) {
                if (mUser.getResources().getMaterial(MaterialType.EVENT_CANDY_CANDY).getNumber() > 0) {
                    count++;
                    String newShapeMap = candyBreakEntity.getNewSharpMap((int) row, (int) column, 0);
                    if (!dbUpdateShapeMap(newShapeMap)) {
                        addErrResponse();
                        return;
                    }
                    candyBreakEntity.setSharpMap(newShapeMap);

                    Actions.save(user, "candy_break", "xep_hinh", "newShapeMap", newShapeMap);

                } else {
                    addErrResponse();
                    return;
                }
            }
        }
        addStar += count;
        // Trừ đi tài nguyên
        ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, BonusBuilder.newInstance()
                .addMaterial(MaterialType.EVENT_CANDY_CANDY, -1)
                .addMaterial(MaterialType.EVENT_CANDY_YELLOW_STAR, addStar)
                .build(), "event_candy_break");
        if (serviceResult.success) {
            builder.addALong(candyBreakEntity.getShapeAfter());
            builder.addAllALong(serviceResult.data);
            for (int i = 0; i < listData.size(); i += 2) {
                row = listData.get(i).intValue();
                column = listData.get(i + 1).intValue();
                commonVector2.addALong(row).addALong(column).addALong(0);
            }
            listCommonVector.addAVector(builder);
            listCommonVector.addAVector(commonVector2);

        } else {
            serviceResult.writeResponse(this);
        }
        addResponse(listCommonVector.build());

    }

    private void xoaNhanh() {
        UserCandyBreakEntity candyBreakEntity = candyBreakService.getInfoCandyBreak(mUser);
        Gson gson = new Gson();
        JsonObject sharpMapObject = gson.fromJson(candyBreakEntity.getSharpMap(), JsonObject.class);
        JsonArray lstMapArray = sharpMapObject.getAsJsonArray("lstCandy");
        int mapSize = lstMapArray.size();
        int subCandy = 0;
        int star = 0;
        int countStar = 0;
        // Duyệt từng giá trị của numberCandy
        if (mUser.getResources().getMaterial(MaterialType.EVENT_CANDY_CANDY).getNumber() > 0) {
            for (int i = 0; i < mUser.getResources().getMaterial(MaterialType.EVENT_CANDY_CANDY).getNumber(); i++) {
                int currentIndex = 0;
                int count = 0; // số ô
                // Duyệt qua từng phần tử trong
                while (currentIndex < mapSize && count < 4) {
                    JsonObject candyObject = lstMapArray.get(currentIndex).getAsJsonObject();
                    if (candyObject.get("id").getAsInt() != 0) {
                        int row = candyObject.get("x").getAsInt();
                        int column = candyObject.get("y").getAsInt();
                        if (candyBreakEntity.getIdByXY(row, column) != 0) {
                            String newShapeMap = candyBreakEntity.getNewSharpMap(row, column, 0);
                            if (!dbUpdateShapeMap(newShapeMap)) {
                                addErrResponse();
                                return;
                            }
                            candyBreakEntity.setSharpMap(newShapeMap);

                            count++;
                            countStar++;
                        }
                    }
                    currentIndex++;
                }
                if (count < 3) {
                    subCandy = i + 1;
                    break;
                }

            }
            star += countStar;
            ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, BonusBuilder.newInstance()
                    .addMaterial(MaterialType.EVENT_CANDY_CANDY, -subCandy)
                    .addMaterial(MaterialType.EVENT_CANDY_YELLOW_STAR, star)
                    .build(), "event_candy_break");
            if (serviceResult.success) {
                addResponse(Pbmethod.CommonVector.newBuilder()
                        .addAllALong(serviceResult.data)
                        .build());
            } else serviceResult.writeResponse(this);
        }
    }

    private void endMap() {
        ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, BonusBuilder.newInstance()
                .addMaterial(MaterialType.EVENT_CANDY_YELLOW_STAR, 20)
                .build(), "event_candy_break");
        if (serviceResult.success) {
            addResponse(Pbmethod.CommonVector.newBuilder()
                    .addAllALong(serviceResult.data)
                    .build());
        } else {
            serviceResult.writeResponse(this);
        }

        UserCandyBreakEntity candyBreakEntity = candyBreakService.getInfoCandyBreak(mUser);
        int mapIdNew = candyBreakEntity.getMapId() + 1;
        if(!dbUpdateMapId(mapIdNew)){
            addErrResponse();
            return;
        }
        candyBreakEntity.setMapId(mapIdNew);

        ResCandyBreakMapEntity resCandyBreakMap = ResCandyBreak.resCandyBreakMapById.get(candyBreakEntity.getMapId());
        if (candyBreakEntity.allIdsAreZero()) {
            int statusNew = 2;
            String shapeMapNew = resCandyBreakMap.getData();
            if (!dbUpdateStatusAndMapNew(statusNew, shapeMapNew)) {
                addErrResponse("Đã chuyển status!");
                return;
            }
            statusNew = 0;

            candyBreakEntity.setStatus(statusNew);
            candyBreakEntity.setSharpMap(shapeMapNew);
        }
    }

    private boolean dbUpdateCandyBreak(int shapeAfter, int shapeBefore) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_candy_break set shape_after=:shapeAfter, shape_before=:shapeBefore where user_id =:userId and event_id=:eventId");
            query.setParameter("shapeBefore", shapeBefore);
            query.setParameter("shapeAfter", shapeAfter);
            query.setParameter("userId", user.getId());
            query.setParameter("eventId", userCandyBreak.getEventId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdate(UserCandyBreakEntity candyBreakEntity, int newShapeAfter, int newShapeBefore) {
        if (!dbUpdateCandyBreak(newShapeAfter, newShapeBefore)) {
            addErrResponse();
            return false;
        }
        candyBreakEntity.setShapeAfter(newShapeAfter);
        candyBreakEntity.setShapeBefore(newShapeBefore);
        return true;
    }

    private boolean dbUpdateShapeMap(String shapeMap) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("UPDATE dson.user_candy_break SET sharp_map = :shapeMap WHERE user_id = :userId");
            query.setParameter("shapeMap", shapeMap);
            query.setParameter("userId", user.getId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateStatusAndMapNew(int status, String shapeMap) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("UPDATE dson.user_candy_break SET status = :status, sharp_map =:shapeMap WHERE user_id = :userId");
            query.setParameter("status", status);
            query.setParameter("shapeMap", shapeMap);
            query.setParameter("userId", user.getId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateMapId(int mapId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("UPDATE dson.user_candy_break SET map_id =:mapId WHERE user_id = :userId");
            query.setParameter("mapId", mapId);
            query.setParameter("userId", user.getId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }


}