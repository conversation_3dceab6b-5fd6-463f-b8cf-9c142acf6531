package monster.game.truongevent.dao;

import monster.dao.AbstractDAO;
import monster.game.truongevent.entity.UserCandyBreakEntity;

import jakarta.persistence.EntityManager;


@SuppressWarnings("unchecked")
public class CandyBreakDAO extends AbstractDAO {
    EntityManager entityManager;

    public UserCandyBreakEntity getCandyBreakEntity(int userId) {
        return doQuery(em -> em.createQuery("select c from UserCandyBreakEntity c where c.userId=:userId")
                .setParameter("userId", userId).getResultList().stream().findFirst().orElse(null));
    }
}