package monster.game.truongevent.service;

import monster.config.penum.BattleType;
import monster.dao.mapping.ConfigEvent;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserEventCounter;
import monster.dao.mapping.UserEventDetails3Entity;
import monster.object.MyUser;

import java.util.BitSet;
import java.util.List;

public interface TruongProEventService {
    List<ConfigEvent> getEventByEventType(int eventType);
    List<ConfigEvent> getListEventByEventType2(int eventType2);
    ConfigEvent getListEventByEventType2(int eventType2, int serverId);
    ConfigEvent getEventByEventType(int eventType, int serverId);
    ConfigEvent getEventByTemplate(int template,int serverId);
    UserEventDetails3Entity getLastUserEventDetails(int userId, int eventId);
    UserEventCounter getUserEventCounter(UserEntity user);
    boolean inGoldBossCampaign(MyUser mUser);
    List<Long> redRibbonBonus(MyUser mUser, BattleType battleType, int... number);
    List<Long> ruong304Bonus(MyUser mUser, BattleType battleType, int... number);
    Integer summonNewHeroRandomId(MyUser mUser);
    //long timeRemainQuickBonus(MyUser mUser);
    BitSet monthlyCardStatus(MyUser mUser);
    boolean murimChallengeNotify(MyUser mUser);
    int getMurimDayEvent(MyUser mUser);
    int getDayEvent(MyUser mUser, ConfigEvent configEvent);
    void checkRankingGroup(MyUser mUser);
    Integer getEventIdByEventType(int serverId, int eventType);
    boolean inEvent304(int serverId);
    BitSet monthlyKyNgoStatus(UserEntity user);
    BitSet monthlyAnimalChessStatus(UserEntity user);
    BitSet receiveGemFromMonthlyCard(MyUser mUser);
    BitSet monthlyBloodMagicStatus(MyUser mUser);
}
