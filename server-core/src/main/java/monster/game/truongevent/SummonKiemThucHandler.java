package monster.game.truongevent;

import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import monster.game.truongevent.config.ConfigSummonKiemThuc;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class SummonKiemThucHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(EVENT_SUMMON_KIEMTHUC, EVENT_SUMMON_KIEMTHUC_SUMMON, EVENT_SUMMON_KIEMTHUC_CHOOSE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            if (!ConfigSummonKiemThuc.inEvent(user.getServer())) {
                addErrResponse(getLang(Lang.event_ended));
                return;
            }
            switch (actionId) {
                case EVENT_SUMMON_KIEMTHUC -> info();
                case EVENT_SUMMON_KIEMTHUC_CHOOSE -> choose();
                case EVENT_SUMMON_KIEMTHUC_SUMMON -> summon();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void info() {
        List<Integer> infos = mUser.getUData().getInfoSummonKiemthuc(user.getServer());
        Pbmethod.ListCommonVector.Builder lsc = Pbmethod.ListCommonVector.newBuilder();
        // hero Select + danh sách các tướng được cho phép chọn
        Pbmethod.CommonVector.Builder cmm0 = Pbmethod.CommonVector.newBuilder();
        int heroSelect = infos.get(1);
        List<Integer> heroSummon = mUser.getUData().getInfoKiemthucSummonLimit();
        for (int j = 0; j < heroSummon.size(); j += 2) {
            if (heroSelect == heroSummon.get(j) && heroSummon.get(j + 1) == 0)
                heroSelect = 0; // xóa tướng select nếu nó hết lượt quay
        }
        cmm0.addAllALong(GsonUtil.toListLong(heroSummon));
        cmm0.addAString(getLang(Lang.insurance_summon_artifact));
        // heroSelect - ensure - maxEnsure
        lsc.addAVector(cmm0);
        lsc.addAVector(getCommonVector(heroSelect, infos.get(2), ConfigSummonKiemThuc.config.ensure, ConfigSummonKiemThuc.config.feeId));
        // rate reward
        Pbmethod.CommonVector.Builder cmm2 = Pbmethod.CommonVector.newBuilder();
        cmm2.addAllALong(GsonUtil.toListLong(ConfigSummonKiemThuc.rateSummon));
        cmm2.addAString(StringHelper.toDBString(ConfigSummonKiemThuc.config.bonus));
        lsc.addAVector(cmm2);
        // get limit
        List<Integer> limit = new ArrayList<>();
        List<Integer> heroIds = ConfigSummonKiemThuc.getKiemthucSummon(user.getServer());
        for (int i = 0; i < heroIds.size(); i += 2) {
            limit.add(heroIds.get(i + 1));
        }
        lsc.addAVector(getCommonIntVector(limit));
        addResponse(lsc.build());
    }

    private void choose() {
        int heroInput = getInputInt();
        if (!ConfigSummonKiemThuc.checkInputHero(user.getServer(), heroInput)) {
            addErrParams();
            return;
        }
        List<Integer> info = mUser.getUData().getInfoSummonKiemthuc(user.getServer());
        List<Integer> heroSummon = mUser.getUData().getInfoKiemthucSummonLimit();

        for (int i = 0; i < heroSummon.size(); i += 2) {
            int turn = heroSummon.get(i + 1);
            if (heroSummon.get(i) == heroInput && (turn == 0)) {
                addErrResponse(getLang(Lang.err_choose_hero_summon));
                return;
            }
        }
        if (info.get(1) != heroInput) {
            info.set(1, heroInput);
            if (!mUser.getUData().updateInfoSummonKiemthuc(info)) {
                addErrResponse(getLang(Lang.err_system_down));
                return;
            }
        }
        addResponse(getCommonVector(heroInput));
    }

    private void summon() {
        int number = getInputInt();
        if (number < 0) {
            addErrParams();
            return;
        }
        List<Integer> info = mUser.getUData().getInfoSummonKiemthuc(user.getServer());
        int heroSelect = info.get(1);
        if (heroSelect == 0) {
            addErrResponse(getLang(Lang.has_select_hero_first));
            return;
        }

        List<Integer> heroSummon = mUser.getUData().getInfoKiemthucSummonLimit();
        int indexHeroSummon = 0;
        for (int i = 0; i < heroSummon.size(); i += 2) {
            if (heroSummon.get(i) == heroSelect) {
                int lim = heroSummon.get(i + 1);
                if (lim == 0) {
                    addErrResponse(getLang(Lang.err_turn_hero_summon));
                    return;
                } else indexHeroSummon = i + 1;
            }
        }
        List<Long> aBonus = ConfigSummonKiemThuc.getFeeSummon(number);
        String err = Bonus.checkMoney(mUser, aBonus);
        if (err != null) {
            addErrResponse(err);
            return;
        }
        int ensure = info.get(2);
        int logIsHero = ensure;
        int curHero = info.get(3);
        int minEnsure = ConfigSummonKiemThuc.getMinInsure(curHero);
        boolean isHero = false;
        for (int i = 0; i < number; i++) {
            boolean isSummonOk = false;
            if (ensure >= ConfigSummonKiemThuc.config.ensure - 1) {
                aBonus.addAll(Bonus.viewMaterialById(heroSelect, 1));
                ensure = 0;
                curHero++;
                minEnsure = ConfigSummonKiemThuc.getMinInsure(curHero);
                heroSummon.set(indexHeroSummon, heroSummon.get(indexHeroSummon) - 1);
                isHero = true;
                isSummonOk = true;
            } else {
                ensure++;
                logIsHero = ensure;
                int rand = NumberUtil.getRandom(10000);
                for (int j = 0; j < ConfigSummonKiemThuc.rateAdd.size(); j++) {
                    if (rand < ConfigSummonKiemThuc.rateAdd.get(j)) {
                        // rơi ra tướng thì reset đảm bảo
                        if (j == 0) {
                            if (ensure > minEnsure) {
                                heroSummon.set(indexHeroSummon, heroSummon.get(indexHeroSummon) - 1);
                                isHero = true;
                                aBonus.addAll(Bonus.viewMaterialById(heroSelect, 1));
                                ensure = 0;
                                curHero++;
                                minEnsure = ConfigSummonKiemThuc.getMinInsure(curHero);
                                isSummonOk = true;
                                break;
                            } else {
                                // random lại
                                rand = NumberUtil.getRandom(10000);
                                for (int k = 1; k < ConfigSummonKiemThuc.rateAdd.size(); k++) {
                                    if (rand < ConfigSummonKiemThuc.rateAdd.get(k)) {
                                        aBonus.addAll(ConfigSummonKiemThuc.config.bonus.get(k));
                                        isSummonOk = true;
                                        break;
                                    }
                                }
                                break;
                            }
                        } else {
                            aBonus.addAll(ConfigSummonKiemThuc.config.bonus.get(j));
                            isSummonOk = true;
                            break;
                        }
                    }
                }
            }
            if (!isSummonOk) {
                Logs.debug("summonKiemthuc fail " + user.getId());
            }
        }
        try { // check số lượng bonus
            var parseBonus = Bonus.parse(aBonus);
            if (parseBonus.size() != number + 1) {
                Logs.error("summonKiemthuc err userId=%s number=%s bonus=%s".formatted(user.getId(), number, aBonus.toString()));
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
        MaterialService materialService = Guice.getInstance(MaterialService.class);
        info.set(2, ensure);
        info.set(3, curHero);
        if (mUser.getUData().updateInfoAndKiemthucSummon(info, heroSummon)) {
            ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, aBonus, "summon_kiemthuc");
            if (serviceResult.success) {
                Pbmethod.ListCommonVector.Builder pb = Pbmethod.ListCommonVector.newBuilder();
                pb.addAVector(getCommonVector(serviceResult.data));
                pb.addAVector(getCommonVector(ensure));
                pb.addAVector(getCommonIntVector(heroSummon));
                addResponse(pb.build());
                Actions.save(user, "summon_kiemthuc", "save", "number", number, "isArtifact", isHero ? 1 : 0, "ensure", logIsHero);
            } else {
                serviceResult.writeResponse(this);
            }
        } else {
            addErrResponse();
        }
    }
}
