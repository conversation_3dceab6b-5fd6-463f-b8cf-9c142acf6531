package monster.game.truongevent.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Data
@Table(name = "res_candy_break_map")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ResCandyBreakMapEntity {
    @Id
    private int id;
    private String name;
    private int mapLevel;
    private String data;
    private int isRelease;
}
