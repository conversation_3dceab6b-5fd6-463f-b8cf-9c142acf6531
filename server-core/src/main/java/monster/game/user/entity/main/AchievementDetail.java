package monster.game.user.entity.main;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "achievement_detail")
public class AchievementDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "group_id")
    private Integer groupId;

    @Column(name = "quest_type")
    private Integer questType;

    @Lob
    @Column(name = "required_value")
    private String requiredValue;

    @Lob
    @Column(name = "bonus_value")
    private String bonusValue;

}