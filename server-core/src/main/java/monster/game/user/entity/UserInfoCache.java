package monster.game.user.entity;

import grep.database.DBJPA;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "user_info_cache", catalog = "dson")
@Data
@NoArgsConstructor
public class UserInfoCache {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;

    private int userId;
    String k, k1;
    String v;

    public UserInfoCache(int userId, String k, String k1) {
        this.userId = userId;
        this.k = k;
        this.k1 = k1;
        this.v = "";
    }

    public long getLong() {
        if (v == null || v.isEmpty()) {
            return 0;
        }
        try {
            return Long.parseLong(v);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public int getInt() {
        if (v == null || v.isEmpty()) {
            return 0;
        }
        try {
            return Integer.parseInt(v);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public String getString() {
        return v;
    }

    public boolean updateIfBetter(long value) {
        if (getLong() < value) {
            v = String.valueOf(value);
            return update();
        }
        return false;
    }

    public boolean update() {
        return DBJPA.update("user_info_cache", List.of("v", v), List.of("id", id));
    }
}