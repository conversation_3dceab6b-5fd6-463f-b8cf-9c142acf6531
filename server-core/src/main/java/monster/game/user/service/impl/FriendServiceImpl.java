package monster.game.user.service.impl;

import com.google.inject.Inject;
import monster.dao.UserDAO;
import monster.dao.mapping.UserFriendRelationshipEntity;
import monster.game.user.service.FriendService;
import monster.game.user.service.dao.FriendDAO;

import java.util.List;

public class FriendServiceImpl implements FriendService {

    @Inject
    UserDAO userDAO;
    @Inject
    FriendDAO friendDAO;

    @Override
    public List<UserFriendRelationshipEntity> getListFriends(int userId) {
        return friendDAO.getListFriends(userId);
    }
}
