package monster.game.user.service.impl;

import com.google.inject.Inject;
import grep.database.DBJPA;
import grep.log.Logs;
import monster.config.penum.BattleType;
import monster.config.penum.NotifyType;
import monster.dao.HeroDAO;
import monster.dao.UserDAO;
import monster.dao.mapping.UserHeroAvatarEntity;
import monster.dao.mapping.UserPowerEntity;
import monster.game.hero.entity.UserBreathEntity;
import monster.game.user.entity.UserInfoCache;
import monster.game.user.service.UserService;
import monster.object.MyUser;
import monster.service.aop.CacheInGame;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.service.common.NotifyService;
import monster.service.resource.ResAvatar;
import monster.service.resource.ResHero;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UserServiceImpl implements UserService {

    @Inject
    UserDAO userDAO;
    @Inject
    HeroDAO heroDAO;
    @Inject
    NotifyService notifyService;

    @CacheInGame(expire = 300, cacheIfNull = false)
    @Override
    public UserInfoCache getUserInfoCache(int userId, String k, String... k1) {
        String realK1 = k1.length == 0 ? "" : k1[0];
        UserInfoCache cache = userDAO.getUserInfoCache(userId, k, realK1);
        if (cache == null) {
            cache = new UserInfoCache(userId, k, realK1);
            userDAO.save(cache);
        }
        return cache;
    }

    @Override
    public UserPowerEntity getUserPower(MyUser mUser) {
        UserPowerEntity userPower = userDAO.getUserPower(mUser.getUser().getId());
        if (userPower == null) {
            userPower = new UserPowerEntity(mUser.getUser().getId());
            DBJPA.save(userPower);
        }
        return userPower;
    }

    @CacheInGame(expire = 300, cacheIfNull = false)
    @Override
    public UserBreathEntity getUserBreath(int userId) {
        UserBreathEntity userBreath = userDAO.getUserBreath(userId);
        if (userBreath == null) {
            userBreath = UserBreathEntity.builder().userId(userId).build();
            DBJPA.save(userBreath);
        }
        return userBreath;
    }

    @Override
    public HeroInfoEntity getHeroBreath(int userId) {
        UserBreathEntity userBreath = getUserBreath(userId);
        HeroInfoEntity heroBreath = new HeroInfoEntity();
        if (userBreath != null && userBreath.getActiveSkill() > 0) {
            heroBreath.id = 1; //bừa thôi, ko quan trọng
            heroBreath.heroId = 1; //bừa thôi, ko quan trọng
            heroBreath.team = 1;
            heroBreath.passiveSkills = new ArrayList<>(List.of(ResHero.getSkill(userBreath.getActiveSkill())));
            heroBreath.position = BattleConfig.TEAM_INPUT;
            heroBreath.clazz = HeroType.CLASS_MAGE;
        }

        return heroBreath;
    }

    @Override
    public void updateMaxPower(MyUser mUser, BattleType battleType, long power, int... info) {
        try {
            getUserPower(mUser).checkUpdate(battleType, power, info.length > 0 ? info[0] : 0);
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    @CacheInGame
    @Override
    public List<UserHeroAvatarEntity> getListHeroAvatar(MyUser mUser) {
        return DBJPA.getList("user_hero_avatar", Arrays.asList("user_id", mUser.getUser().getId()), "", UserHeroAvatarEntity.class);
    }

    @Override
    public void checkHeroAvatarNotify(MyUser mUser) {
        boolean hasNotify = false;
        var listUserHeroAvatar = getListHeroAvatar(mUser);
        var map = listUserHeroAvatar.stream().collect(Collectors.toMap(UserHeroAvatarEntity::getHeroAvatarId, Function.identity()));
        if (listUserHeroAvatar != null) {
            for (Integer heroAvatarId : ResAvatar.mHeroAvatar.keySet()) {
                var resHeroAvatar = ResAvatar.mHeroAvatar.get(heroAvatarId);
                if (!map.containsKey(heroAvatarId)) {
                    if (resHeroAvatar.isConditionSatisfied(resHeroAvatar.checkConditionAvatar(mUser))) {
                        hasNotify = true;
                        break;
                    }
                }
            }
        }
        notifyService.sendNotify(mUser, NotifyType.HERO_AVATAR_NEW, hasNotify);
    }

}
