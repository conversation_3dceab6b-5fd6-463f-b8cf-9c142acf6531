package monster.game.user.service;

import monster.config.penum.BattleType;
import monster.dao.mapping.UserHeroAvatarEntity;
import monster.dao.mapping.UserPowerEntity;
import monster.game.hero.entity.UserBreathEntity;
import monster.game.user.entity.UserInfoCache;
import monster.object.MyUser;
import monster.service.battle.common.entity.HeroInfoEntity;

import java.util.List;

public interface UserService {
    UserInfoCache getUserInfoCache(int userId, String k, String... k1);
    UserPowerEntity getUserPower(MyUser mUser);
    UserBreathEntity getUserBreath(int userId);
    HeroInfoEntity getHeroBreath(int userId);
    void updateMaxPower(MyUser mUser, BattleType battleType, long power, int... info);
    List<UserHeroAvatarEntity> getListHeroAvatar(MyUser mUser);
    void checkHeroAvatarNotify(MyUser mUser);
}
