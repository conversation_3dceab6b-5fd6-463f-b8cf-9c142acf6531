package monster.game.user.dao;

import monster.dao.AbstractDAO;
import monster.game.user.entity.EventBadgeSummonEntity;

public class BadgeDAO extends AbstractDAO {

    public EventBadgeSummonEntity getBadgeSummon(int eventId, int clusterId, int pageId) {
        return doQuery(em -> em.createQuery("select c from EventBadgeSummonEntity c where c.eventId=:eventId and c.clusterId=:clusterId and c.pageId=:pageId")
                .setParameter("pageId", pageId).setParameter("eventId", eventId).setParameter("clusterId", clusterId)
                .getResultList().stream().findFirst().orElse(null));
    }

//    public boolean updateBadgeData(int eventId, int clusterId, int pageId, String data, int numberRefresh) {
//        return doUpdate(em -> em.createQuery("update EventBadgeSummonEntity c set c.pageData=:pageData where c.eventId=:eventId and c.clusterId=:clusterId and c.pageId=:pageId " +
//                        "and numberRefresh=:numberRefresh")
//                .setParameter("pageId", pageId).setParameter("eventId", eventId).setParameter("clusterId", clusterId)
//                .setParameter("numberRefresh", numberRefresh).setParameter("pageData", data).executeUpdate() > 0);
//    }

}
