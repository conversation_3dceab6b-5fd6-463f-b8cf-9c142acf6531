package monster.game.story.config;

import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import monster.game.story.entity.ResStoryBoss;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConfigBossStory {

    public static List<List<Integer>> bossId;
    public static int freeAtk, stepBuyAtk, numberBuyAtk;
    public static List<Long> damage;
    public static List<List<Long>> damageBonus;
    public static Map<Integer, ResStoryBoss> mBossScale;

    public static List<Long> getDamageBonus(long fromDamage, long toDamage) {
        List<Long> bonus = new ArrayList<>();
        for (int i = 0; i < damage.size(); i++) {
            if (fromDamage < damage.get(i) && damage.get(i) <= toDamage) {
                bonus.addAll(damageBonus.get(i));
            }
        }
        return bonus;
    }

    // {"bonus": [[2, 1], [2, 2], [2, 3], [2, 4]], "damage": [10000, 20000, 30000, 40000]}
    public static void setConfigDamageBonus(String value) {
        JsonObject json = GsonUtil.parseJsonObject(value);
        damage = GsonUtil.strToListLong(json.get("damage").getAsJsonArray().toString());
        damageBonus = GsonUtil.strTo2ListLong(json.get("bonus").getAsJsonArray().toString());
    }

    // {"bossId": [1004, 1005, 1006], "freeAtk": 10, "stepBuyAtk": 50, "numberBuyAtk": 10}
    public static void setConfigMain(String value) {
        JsonObject json = GsonUtil.parseJsonObject(value);
        bossId = GsonUtil.strTo2ListInt(json.get("bossId").getAsJsonArray().toString());
        freeAtk = json.get("freeAtk").getAsInt();
        stepBuyAtk = json.get("stepBuyAtk").getAsInt();
        numberBuyAtk = json.get("numberBuyAtk").getAsInt();

        mBossScale = new HashMap<>();
        ((List<ResStoryBoss>) DBJPA.getList("dson_main.res_story_boss", ResStoryBoss.class)).forEach(resStoryBoss -> mBossScale.put(resStoryBoss.getId(), resStoryBoss));
    }

}
