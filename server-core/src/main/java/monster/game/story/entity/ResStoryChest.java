package monster.game.story.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "res_story_chest", schema = "dson_main")
public class ResStoryChest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "bonus", length = 1024)
    private String bonus;
    int type; // type 1: bonus gen daily

}