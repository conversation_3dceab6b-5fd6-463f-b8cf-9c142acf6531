package monster.game.story.service.impl;

import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.inject.Inject;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.MonsterType;
import monster.config.penum.ResRewardType;
import monster.dao.SystemDAO;
import monster.dao.mapping.ServerInformation;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserMailEntity;
import monster.game.story.config.ConfigBossStory;
import monster.game.story.dao.StoryBossDAO;
import monster.game.story.entity.ResStoryBoss;
import monster.game.story.entity.UserStoryBoss;
import monster.game.story.service.StoryBossService;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.aop.CacheInGame;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.Point;
import monster.service.common.TeamService;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResMonster;
import monster.service.user.Actions;
import monster.task.dbcache.MailCreatorCache;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.List;

public class StoryBossServiceImpl implements StoryBossService {

    @Inject
    StoryBossDAO storyBossDAO;
    @Inject
    SystemDAO systemDAO;

    @CacheInGame
    public ServerInformation getBossInDb() {
        ServerInformation serverInformation = systemDAO.getServerInfo(0, "boss_geto");
        if (serverInformation == null) {
            JsonObject obj = new JsonObject();
            obj.add("bossIndex", new JsonPrimitive(0));
            obj.add("timeKey", new JsonPrimitive(DateTime.getTimeKey()));
            obj.add("level", new JsonPrimitive(1));
            serverInformation = ServerInformation.builder()
                    .serverId(0).k("boss_geto").v(obj.toString()).build();
            systemDAO.saveOrUpdate(serverInformation);
        }
        return serverInformation;
    }

    /**
     * @return {bossIndex, level}
     */
    @Override
    public int[] getBossInfo() {
        ServerInformation serverInformation = getBossInDb();
        JsonObject obj = GsonUtil.parseJsonObject(serverInformation.getV());
        int bossIndex = obj.get("bossIndex").getAsInt();
        int timeKey = obj.get("timeKey").getAsInt();
        int level = obj.get("level").getAsInt();
        if (timeKey != DateTime.getTimeKey()) {
            bossIndex++;
            if (bossIndex >= ConfigBossStory.bossId.size()) bossIndex = 0;
            level = Math.min(100, level + 1);
            obj.add("bossIndex", new JsonPrimitive(bossIndex));
            obj.add("level", new JsonPrimitive(level));
            obj.add("timeKey", new JsonPrimitive(DateTime.getTimeKey()));
            serverInformation.setV(obj.toString());
            systemDAO.saveOrUpdate(serverInformation);
        }
        return new int[]{bossIndex, level};
    }

    @CacheInGame(expire = 300, cacheIfNull = false)
    @Override
    public UserStoryBoss getUserStoryBoss(MyUser mUser, int timeKey) {
        var story = storyBossDAO.getUserBossStory(mUser.getUser().getId(), timeKey);
        if (story == null) {
            story = new UserStoryBoss(mUser.getUser(), timeKey);
            if (!storyBossDAO.save(story)) return null;
        }
        return story;
    }

    @Override
    public BattleTeam getBoss(MyUser mUser) {
        int[] bossInfo = getBossInfo();
        int bossIndex = bossInfo[0] >= ConfigBossStory.bossId.size() ? 0 : bossInfo[0];
        int level = bossInfo[1];

        List<Integer> bossIds = ConfigBossStory.bossId.get(bossIndex);
        List<Integer> levels = new ArrayList<>();
        for (Integer bossId : bossIds) {
            if (bossId > 0) levels.add(ResMonster.getMonster(bossId).getLevel());
            else levels.add(0);
        }
        BattleTeam team = Guice.getInstance(TeamService.class).getMonsterTeam(
                "[%s]".formatted(StringHelper.toDBString(bossIds)),
                "[%s]".formatted(StringHelper.toDBString(levels)), 1f, MonsterType.CAMPAIGN);
        ResStoryBoss resStoryBoss = ConfigBossStory.mBossScale.get(level);
        for (HeroInfoEntity hero : team.getAHero()) {
            if (hero != null && hero.heroId > 0) {
                Logs.debug("---> " + hero.heroId);
                Logs.debug("---> " + hero.point.getCurrentValue(Point.SPEC_REDUCE_DAMAGE_DOT));
                // scale chỉ số boss theo level ResStoryBoss.pointScale
                float pointScale = resStoryBoss.getPointScale();
                hero.point.setCurrentValue(Point.HP, (long) (hero.point.getCurrentHP() * pointScale));
                hero.point.setCurrentValue(Point.ATTACK, (long) (hero.point.getRealCurrentAttack() * pointScale));
                hero.point.setCurrentValue(Point.ARMOR, (long) (hero.point.getCurrentArmor() * pointScale));
                hero.point.setCurrentValue(Point.SPEED, (long) (hero.point.getCurrentSpeed() * pointScale));
                hero.point.setCurrentValue(Point.SKILL_DAMAGE, (int) (hero.point.getSkillDamage() * pointScale));
                hero.point.setCurrentValue(Point.PRECISION, (int) (hero.point.getCurrentValue(Point.PRECISION) * pointScale));
                hero.point.setCurrentValue(Point.BLOCK, (int) (hero.point.getCurrentValue(Point.BLOCK) * pointScale));
                hero.point.setCurrentValue(Point.CRIT, (int) (hero.point.getCrit() * pointScale));
                hero.point.setCurrentValue(Point.CRIT_DAMAGE, (int) (hero.point.getCritDamage() * pointScale));
                hero.point.setCurrentValue(Point.CONTROL_IMMUNE, (int) (hero.point.getCurrentControlImmune() * pointScale));
                hero.point.setCurrentValue(Point.ARMOR_BREAK, (int) (hero.point.getArmorBreak() * pointScale));
                hero.point.setCurrentValue(Point.REDUCE_DAMAGE, (int) (hero.point.getCurrentValue(Point.REDUCE_DAMAGE) * pointScale));
                hero.point.setCurrentValue(Point.HOLY_DAMAGE, (int) (hero.point.getHolyDamage() * pointScale));
                hero.point.setCalculatedValue();
            }
        }
        return team;
    }

    @Override
    public Pbmethod.PbListUser.Builder top(int serverId, int timeKey) {
        List<UserStoryBoss> topUser = storyBossDAO.getTopUserStoryBoss(serverId, timeKey); // < 50>
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        topUser.forEach(tmp -> {
            UserEntity user = UserOnline.getDbUser(tmp.getUserId());
            Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
            pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(tmp.getMaxDamage()));
            builder.addAUser(pbUser);
        });
        return builder;
    }

    @Override
    public Pbmethod.PbListUser.Builder top3(int serverId, int timeKey) {
        List<UserStoryBoss> topUser = storyBossDAO.getTop3UserStoryBoss(serverId, timeKey); // < 50>
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        topUser.forEach(tmp -> {
            UserEntity user = UserOnline.getDbUser(tmp.getUserId());
            Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
            pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(tmp.getMaxDamage()));
            builder.addAUser(pbUser);
        });
        return builder;
    }

    @Override
    public int getMyRank(MyUser mUser, UserStoryBoss userStoryBoss) {
        Integer myRank = storyBossDAO.getMyRank(userStoryBoss.getServerId(), userStoryBoss.getTimeKey(), userStoryBoss.getMaxDamage());
        return myRank == null ? 0 : myRank + 1;
    }

    @Override
    public void checkDailyReward(MyUser mUser) {
        try {
            var rewards = storyBossDAO.getUnReward(mUser.getUser().getId());
            for (UserStoryBoss reward : rewards) {
                reward.setDailyReceive(1);
                if (storyBossDAO.update(reward)) {
                    // mail here
                    List<Long> bonus = ResRewardType.STORY_BOSS.getReward().getBonus(reward.getDailyRank());
                    if (!bonus.isEmpty()) {
                        Actions.save(mUser.getUser(), "story_boss", "mail", "rank", reward.getDailyRank(), "bonus", StringHelper.toDBString(bonus));
                        MailCreatorCache.sendMail(UserMailEntity.builder()
                                .userId(reward.getUserId())
                                .title(mUser.getLang().get(Lang.story_boss_reward_title).formatted(reward.getTimeKey()))
                                .message(mUser.getLang().get(Lang.story_boss_reward_message).formatted(reward.getDailyRank()))
                                .bonus(StringHelper.toDBString(bonus)).origin("story_boss:" + reward.getTimeKey()).build());
                    }
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }
}