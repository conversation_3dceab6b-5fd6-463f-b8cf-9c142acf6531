package monster.game.territory.entity;

import grep.helper.GsonUtil;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;
import monster.config.penum.BattleType;
import monster.config.penum.MonsterType;
import monster.dao.mapping.UserPowerEntity;
import monster.dao.mapping.logs.UserPassFunction;
import monster.game.user.service.UserService;
import monster.object.BattleTeam;
import monster.object.ModePlay;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.IMath;
import monster.game.system.service.LogService;
import monster.service.common.TeamService;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "user_territory")
public class ResTerritoryEntity {
    @Getter
    @Id
    private int id;
    @Getter
    private int factionId, level, levelShow, starShow;
    @Getter
    private long powerShow;
    private String monsterId, monsterLevel, firstClearReward, sweepReward;
    @Getter
    private String condition;

    @Getter
    @Setter
    @Transient
    ModePlay modePlay;

    public List<List<Integer>> getListOfListMonsterId() {
        return GsonUtil.strTo2ListInt(monsterId);
    }

    public List<List<Integer>> getListOfListMonsterLevel() {
        return GsonUtil.strTo2ListInt(monsterLevel);
    }

    public List<Long> getFirstClearReward() {
        return GsonUtil.strToListLong(firstClearReward);
    }

    public List<Long> getSweepReward() {
        return GsonUtil.strToListLong(sweepReward);
    }

    public BattleTeam getBattleTeam(MyUser mUser, UserTerritoryEntity userTerritory, long atkPower) {
        int numberStageLevel = Math.max(userTerritory.getFirstLooseLevel() == 0 ? 0 : level - userTerritory.getFirstLooseLevel(), 0);
        UserPowerEntity userPower = mUser == null ? new UserPowerEntity() : Guice.getInstance(UserService.class).getUserPower(mUser);
        UserPassFunction passFunction = Guice.getInstance(LogService.class).getUserPassFunction(mUser.getUser().getServer(), BattleType.MODE_TERRITORY.value, level * 10 + factionId);
        float scaleMonsterPower = IMath.getScaleMonsterPower(atkPower, 0, passFunction.number, userTerritory.getFirstLoosePower(),
                userPower.getListMaxTerritory().get(factionId - 1), numberStageLevel);

        BattleTeam team = Guice.getInstance(TeamService.class).getMonsterTeam(monsterId, monsterLevel, scaleMonsterPower, MonsterType.TERRITORY);
        for (HeroInfoEntity heroInfo : team.getAHero()) {
            if (heroInfo != null) {
                heroInfo.level = levelShow;
                heroInfo.star = starShow;
                heroInfo.setHeroInfo(new ArrayList<>());
            }
        }
        team.setPowerShow(powerShow);
        return team;
    }
}
