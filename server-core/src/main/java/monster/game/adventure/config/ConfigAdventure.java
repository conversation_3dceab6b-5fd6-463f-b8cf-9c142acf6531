package monster.game.adventure.config;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.LogicUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import monster.game.adventure.entity.ResAdventureEntity;
import monster.object.MyUser;
import monster.service.battle.common.config.HeroType;
import monster.service.user.Actions;
import monster.service.user.Bonus;

import java.util.*;

public class ConfigAdventure {
    // tavern
    public static final int TYPE_REQ_STAR = 1;
    public static final int TYPE_REQ_FACTION = 2;
    public static final int TYPE_REQ_CLASS = 3;
    public static final int MAX_QUEST = 50;

    public static Adventure cfgAdventure;
    public static Map<Integer, ResAdventureEntity> resAdventureMapByStar;

    public static int getRandomStar(MyUser mUser, boolean monthlyDacQuyenKyNgo) {
        List<Float> listRate = monthlyDacQuyenKyNgo ? cfgAdventure.vipQuestRate : cfgAdventure.questRate;
        float range = 0;
        for (int i = 0; i < listRate.size(); i++) {
            range += listRate.get(i);
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 0; i < listRate.size(); i++) {
            top += listRate.get(i);
            if (random < top) {
                Actions.save(mUser.getUser(), "adventure", "random", "random_value", random, "star", i + 1, "require", StringHelper.toDBString(listRate));
                return i + 1;
            }
        }
        return 1;
    }

    public static List<List<Long>> getRandomAdventureBonus(int star, boolean monthlyDacQuyenKyNgo) {
        List<List<Long>> result = new ArrayList<>();
        ResAdventureEntity resAdventure = resAdventureMapByStar.get(star);
        int randomIndex = LogicUtil.getRandomIndexByRate(resAdventure.getListRate());
        if (randomIndex < 0) return new ArrayList<>();

        List<Long> reward = resAdventure.getListReward().get(randomIndex);
        List<Long> bonusReward = new ArrayList<>();
        if (monthlyDacQuyenKyNgo) bonusReward = Bonus.merge(bonusReward, reward);
        if (cfgAdventure.isBonusRewardTime() && !resAdventure.getListBonusReward().isEmpty())
            bonusReward = Bonus.merge(bonusReward, resAdventure.getListBonusReward());

        result.add(reward);
        result.add(bonusReward);
        return result;
    }

    public static int getRandomIdTavernNormal() {
        float range = 0;
        for (int i = 0; i < 3; i++) {
            range += cfgAdventure.questRate.get(i);
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 0; i < cfgAdventure.questRate.size(); i++) {
            top += cfgAdventure.questRate.get(i);
            if (random < top) {
                return i;
            }
        }
        return 0;
    }

    public static int getRandomIdTavernSenior() {
        float range = 0;
        for (int i = 3; i < 7; i++) {
            range += cfgAdventure.questRate.get(i);
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 3; i < cfgAdventure.questRate.size(); i++) {
            top += cfgAdventure.questRate.get(i);
            if (random < top) {
                return i;
            }
        }
        return 0;
    }

//    public static JSONArray getTavernBonusShow(int indexQuest) {
//        JSONArray arrBonus = new JSONArray();
//
//        int currIndex = indexQuest;
//        TavernBonusObject bonusObject = null;
//        if (currIndex < bonusTavern.size())
//            bonusObject = bonusTavern[currIndex];
//
//        if (bonusObject != null) {
//            int rangeIndexBonus = getBonusIndex(currIndex);
//
//            while (arrBonus.size() == 0) {
//                switch (rangeIndexBonus) {
//                    case 0:
//                        if (bonusObject.diamond.size() > 0) {
//                            arrBonus.addAll(Bonus.view(Bonus.BONUS_GEM, new Random().nextInt(bonusObject.diamond[1] - bonusObject.diamond[0] + 1) + bonusObject.diamond[0]));
//                        }
//                        break;
//                    case 1:
//                        if (bonusObject.basicScroll > 0) {
//                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.BASIC_SUMMON, bonusObject.basicScroll));
//                        }
//                        break;
//                    case 2:
//                        if (bonusObject.heroicScroll > 0) {
//                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.HEROIC_SUMMON, bonusObject.heroicScroll));
//                        }
//                        break;
//                    case 3:
//                        if (bonusObject.shard.size() > 0) {
//                            int star = bonusObject.shard[0];
//                            int numFragMin = bonusObject.shard[1];
//                            int numFragMax = bonusObject.shard[2];
//                            if (star == 3)
//                                arrBonus.addAll(Bonus.viewGold(NumberUtil.getRandom(numFragMin, numFragMax)));
//                            else if (star == 4)
//                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.HERO_SHARD_4, new Random().nextInt((numFragMax - numFragMin) + 1) + numFragMin));
//                            else if (star == 5)
//                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.HERO_SHARD_5, new Random().nextInt((numFragMax - numFragMin) + 1) + numFragMin));
//                        }
//                        break;
//                    case 4:
//                        if (bonusObject.arenaCard > 0) {
//                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARENA_TICKET, bonusObject.arenaCard));
//                        }
//                        break;
//                    case 5:
//                        if (bonusObject.prophetBlessing.size() > 0) {
//                            int numMin = bonusObject.prophetBlessing[0];
//                            int numMax = bonusObject.prophetBlessing[1];
//                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.PROPHET_BLESSING, new Random().nextInt((numMax - numMin) + 1) + numMin));
//                        }
//                        break;
//                    case 6:
//                        if (bonusObject.casinoChip > 0) {
//                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.CHIP, bonusObject.casinoChip));
//                        }
//                        break;
//                    case 7:
//                        if (bonusObject.artifactFragment.size() > 0) {
//                            int random = new Random().nextInt(bonusObject.artifactFragment.size() / 2);
//                            int quality = bonusObject.artifactFragment[random * 2];
//                            int number = bonusObject.artifactFragment[(random * 2) + 1];
//                            //
//                            if (quality == MaterialType.ARTIFACT_SHARD_GREEN.id) {
//                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_GREEN, number));
//                            } else if (quality == MaterialType.ARTIFACT_SHARD_RED_EXCLUSIVE.id) {
//                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_RED_EXCLUSIVE, number));
//                            } else if (quality == MaterialType.ARTIFACT_SHARD_RED.id) {
//                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_RED, number));
//                            }
//                        }
//                        break;
//                    case 8:
//                        if (bonusObject.prophetOrb > 0) {
//                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.PROPHET_ORB, bonusObject.prophetOrb));
//                        }
//                        break;
//                }
//                rangeIndexBonus = new Random().nextInt(9);
//            }
//        }
//
//        return arrBonus;
//    }

    public static List<Integer> getRequireQuest(int star) {
        int indexQuest = star - 1;
        List<Integer> lstResult = new ArrayList<>();
        Map<Integer, List<Integer>> mapResultRandom = new HashMap<Integer, List<Integer>>();
        // req star
        if (cfgAdventure.requireStar.get(indexQuest) > 0) {
            lstResult.add(TYPE_REQ_STAR);
            lstResult.add(cfgAdventure.requireStar.get(indexQuest));
        }
        //
        // req class or faction
        if (indexQuest < cfgAdventure.requireClassFaction.size()) {
            int numRequire = cfgAdventure.requireClassFaction.get(indexQuest);
            for (int i = 0; i < numRequire; i++) {
                int randomType = new Random().nextInt(2) + 2;
                if (!mapResultRandom.containsKey(randomType))
                    mapResultRandom.put(randomType, new ArrayList<>());

                if (randomType == TYPE_REQ_FACTION) {// faction
                    List<Integer> lstRequire = mapResultRandom.get(TYPE_REQ_FACTION);
                    List<HeroType> factions = new ArrayList<>(HeroType.mAllFaction.values());
                    List<Integer> rates = cfgAdventure.getFactionRate(star);
                    int factionValue = LogicUtil.getRandomByRate(factions, rates).value;
                    long count = 10000;
                    while (lstRequire.contains(factionValue) && count > 0) {
                        factionValue = LogicUtil.getRandomByRate(factions, rates).value;
                        count--;
                    }
                    lstRequire.add(factionValue);
                    mapResultRandom.put(TYPE_REQ_FACTION, lstRequire);
                } else if (randomType == TYPE_REQ_CLASS) {// class
                    List<Integer> lstRequire = mapResultRandom.get(TYPE_REQ_CLASS);
                    List<HeroType> classes = new ArrayList<>(HeroType.mAllClass.values());
                    int rand = NumberUtil.getRandomInList(classes).value;
                    while (lstRequire.contains(rand)) {
                        rand = NumberUtil.getRandomInList(classes).value;
                    }
                    lstRequire.add(rand);
                    mapResultRandom.put(TYPE_REQ_CLASS, lstRequire);
                }
            }

            for (Map.Entry<Integer, List<Integer>> entry : mapResultRandom.entrySet()) {
                for (int value : entry.getValue()) {
                    lstResult.add(entry.getKey());
                    lstResult.add(value);
                }
            }
        }

        return lstResult;
    }

    private static Map<Integer, List<Integer>> mIndex = new HashMap<Integer, List<Integer>>() {{
        put(0, Arrays.asList(0, 1));
        put(1, Arrays.asList(0, 1, 3));
        put(2, Arrays.asList(0, 1, 3, 4));
        put(3, Arrays.asList(0, 2, 3, 4, 6));
        put(4, Arrays.asList(0, 2, 3, 4, 6));
        put(5, Arrays.asList(0, 3, 5, 7, 9));
        put(6, Arrays.asList(0, 3, 5, 8, 9));
    }};

    private static Map<Integer, List<Integer>> mRate = new HashMap<Integer, List<Integer>>() {{
        put(0, Arrays.asList(50, 100));
        put(1, Arrays.asList(34, 67, 100));
        put(2, Arrays.asList(25, 50, 75, 100));
        put(3, Arrays.asList(25, 40, 60, 80, 100));
        put(4, Arrays.asList(25, 35, 60, 80, 100));
        put(5, Arrays.asList(25, 45, 70, 90, 100));
        put(6, Arrays.asList(30, 60, 80, 90, 100));
    }};

    private static int getBonusIndex(int questStar) {
        List<Integer> aIndex = mIndex.get(questStar);
        List<Integer> aRate = mRate.get(questStar);
        int rand = new Random().nextInt(100);
        for (int i = 0; i < aRate.size(); i++) {
            if (rand < aRate.get(i)) return aIndex.get(i);
        }
        return 0;
    }

    public static void setConfigAdventure(String value) {
        cfgAdventure = GsonUtil.parse(Adventure.class, value);
        List<ResAdventureEntity> listResAdventure = DBJPA.getList("dson_main.res_adventure", ResAdventureEntity.class);
        resAdventureMapByStar = new HashMap<>();
        listResAdventure.forEach(resAdventure -> resAdventureMapByStar.put(resAdventure.getStar(), resAdventure));
    }

    public class Adventure {
        private List<List<Integer>> factionRate;
        private List<Float> questRate, vipQuestRate;
        private List<Integer> feeSpeedUp, requireNumberHero, requireStar, requireClassFaction;
        private List<Long> timeComplete;
        @Getter
        private Integer hourRefresh, feeRefresh, maxStore, pointPerHourIdle, feeStart, maxPoint;
        private String bonusRewardStartTime, bonusRewardEndTime;
        private List<DefaultUserAdventure> defaultAdventure;

        public List<Integer> getFactionRate(int star) {
            return new ArrayList<>(factionRate.get(star - 1));
        }

        public List<Long> getTimeComplete() {
            return new ArrayList<>(timeComplete);
        }

        public List<Integer> getFeeSpeedUp() {
            return new ArrayList<>(feeSpeedUp);
        }

        public List<Integer> getRequireNumberHero() {
            return new ArrayList<>(requireNumberHero);
        }

        public List<Integer> getRequireStar() {
            return new ArrayList<>(requireStar);
        }

        public List<Integer> getRequireClassFaction() {
            return new ArrayList<>(requireClassFaction);
        }

        public boolean isBonusRewardTime() {
//            try {
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                Date startTime = sdf.parse(bonusRewardStartTime), endTime = sdf.parse(bonusRewardEndTime), now = new Date();
//                return now.after(startTime) && now.before(endTime);
//            } catch (Exception ex) {
//                Logs.error(ex);
//            }
//
//            return false;

            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            return List.of(Calendar.SATURDAY, Calendar.SUNDAY).contains(cal.get(Calendar.DAY_OF_WEEK));
        }

        public int getMaxStar() {
            return questRate.size();
        }

        public List<DefaultUserAdventure> getListDefaultUserAdventure() {
            return defaultAdventure;
        }
    }

    @AllArgsConstructor
    public static class DefaultUserAdventure {
        @Getter
        private int star;
        private List<Long> reward;

        public List<Long> getReward() {
            return new ArrayList<>(reward);
        }
    }
}
