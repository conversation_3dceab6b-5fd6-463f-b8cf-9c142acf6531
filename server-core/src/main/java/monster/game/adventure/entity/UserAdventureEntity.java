package monster.game.adventure.entity;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgTavern;

import jakarta.persistence.*;
import java.util.Arrays;
import java.util.List;

@Entity
@Table(name = "user_adventure")
@Data
@NoArgsConstructor
public class UserAdventureEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id;
    int userId, status, star;
    long timeStart;
    String reward, bonusReward, team, require;

    public UserAdventureEntity(int userId, int star) {
        this.userId = userId;
        this.status = 0;
        this.reward = "[]";
        this.bonusReward = "[]";
        this.team = "[]";
        this.timeStart = 0;
        this.star = star;
        this.require = "[]";
    }

    public boolean containHeroId(long heroId) {
        return getTeam().contains(heroId);
    }

    public boolean shouldComplete() {
        return getStatus() == 1 && getCountdown() == 0;
    }

    public boolean isComplete() {
        return getStatus() == 2;
    }

    public List<Long> getTeam() {
        return GsonUtil.strToListLong(team);
    }

    public List<Long> getReward() {
        return GsonUtil.strToListLong(reward);
    }

    public List<Long> getBonusReward() {
        return GsonUtil.strToListLong(bonusReward);
    }

    public List<Integer> getRequire() {
        return GsonUtil.strToListInt(require);
    }

    public long getCountdown() {
        long timeRemain = 0;
        if (status != 0) {
            timeRemain = CfgTavern.config.timeComplete[star - 1] - (System.currentTimeMillis() - timeStart) / 1000;
        }

        return timeRemain <= 0 ? 0 : timeRemain;
    }

    public boolean dbUpdateAdventureStatus(int newStatus) {
        return DBJPA.update("user_adventure", Arrays.asList("status", newStatus), Arrays.asList("id", id));
    }

    public boolean deleteAdventure() {
        return DBJPA.delete("user_adventure", "id", id);
    }

    public boolean dbUpdate(int newStatus, String newTeam, long newTimeStart) {
        return DBJPA.update("user_adventure", Arrays.asList("status", newStatus, "team", newTeam, "time_start", newTimeStart), Arrays.asList("id", id));
    }
}
