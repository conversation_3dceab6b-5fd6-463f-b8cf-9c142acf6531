package monster.game.raisecat.entity;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.game.raisecat.config.ConfigCat;
import monster.service.resource.ResCat;
import protocol.Pbmethod;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Entity
@Table(name = "user_cat", schema = "dson")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserCatEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private int userId, catId;
    private int slotId;
    private int statusCat;
    private int star, numberTayluyen;
    private Date waitingTimeForEat;
    /* statusFrame -> trạng thái khung ảnh
     0 -> được treo
     1 -> đã treo
     2 -> làm nhiệm vụ
  */
    private int statusFrame;
    /* statusOwn -> trạng thái sở hữu
        1 -> đã sở hữu
        0 -> chưa sở hữu
     */
    private int statusOwn;
    private int maturityPoint;
    private String bonus;
    String basicPoint, randomPoint;
    String cacheBasicPoint, cacheRandomPoint;
    // statusUnlock: 0: chưa đủ điều kiện, 1: đủ điều kiện unlock
    private int statusUnlock;
    private int numberHelped, timeKey;
    public String getBasicPoint() {
        return StringHelper.isEmpty(basicPoint) ? "[]" : basicPoint;
    }

    public String getRandomPoint() {
        return StringHelper.isEmpty(randomPoint) ? "[]" : randomPoint;
    }

    public String getCacheBasicPoint() {
        return StringHelper.isEmpty(cacheBasicPoint) ? "[]" : cacheBasicPoint;
    }

    public String getCacheRandomPoint() {
        return StringHelper.isEmpty(cacheRandomPoint) ? "[]" : cacheRandomPoint;
    }

    public List<Long> getListBonus() {
        return GsonUtil.strToListLong(bonus);
    }

    public List<Long> getListBasicPoint() {
        return GsonUtil.strToListLong(getBasicPoint());
    }

    public List<Long> getListRandomPoint() {
        return GsonUtil.strToListLong(getRandomPoint());
    }

    public List<Long> getCalculateBasicPoint() {
        float rate = ConfigCat.config.scalePointByStar[star - 1];
        List<Long> values = new ArrayList<>();
        List<Long> points = getListBasicPoint();
        for (int i = 0; i < points.size(); i += 3) {
            values.add(points.get(i + 1));
            values.add((long) (points.get(i + 2) * rate));
        }
        return values;
    }

    public List<Long> getCalculateRandomPoint() {
        float rate = ConfigCat.config.scalePointByStar[star - 1];
        List<Long> values = new ArrayList<>();
        List<Long> points = getListRandomPoint();
        for (int i = 0; i < points.size(); i += 3) {
            values.add(points.get(i + 1));
            values.add((long) (points.get(i + 2) * rate));
        }
        return values;
    }

    public List<Long> getListCacheBasicPoint() {
        return GsonUtil.strToListLong(getCacheBasicPoint());
    }

    public List<Long> getListCacheRandomPoint() {
        return GsonUtil.strToListLong(getCacheRandomPoint());
    }

    public Pbmethod.CommonVector toProto() {
        Date date = new Date();
        long secondsRemaining = waitingTimeForEat.getTime() - date.getTime();
        protocol.Pbmethod.CommonVector.Builder builder = protocol.Pbmethod.CommonVector.newBuilder();
        builder.addALong(isOwn() ? catId : -catId);
        builder.addALong(statusCat);
        builder.addALong(isTimeRemain() ? secondsRemaining / 1000 : 0);
        builder.addAllALong(getListBonus());
        builder.addALong(statusFrame);
        return builder.build();
    }

    public boolean isOwn() {
        return statusOwn == 1;
    }

    public boolean isTimeRemain() {
        return statusCat == 1;
    }

    public long getPower(int star) {
        ResInfoCatEntity resInfoCat = ResCat.getInfoCatBySlotId(slotId);
        List<Long> calPower = new ArrayList<>();
        calPower.addAll(ConfigCat.getPointByStar(getListBasicPoint(), star));
        calPower.addAll(ConfigCat.getPointByStar(getListRandomPoint(), star));
        return ConfigCat.getPower(resInfoCat, calPower);
    }

    public long getPowerTayluyen(int star) {
        ResInfoCatEntity resInfoCat = ResCat.getInfoCatBySlotId(slotId);
        List<Long> calPower = new ArrayList<>();
        calPower.addAll(ConfigCat.getPointByStar(getListBasicPoint(), star));
        calPower.addAll(ConfigCat.getPointByStar(getListCacheRandomPoint(), star));
        return ConfigCat.getPower(resInfoCat, calPower);
    }

    public long getPowerBoiDuong(int star) {
        ResInfoCatEntity resInfoCat = ResCat.getInfoCatBySlotId(slotId);
        List<Long> calPower = new ArrayList<>();
        calPower.addAll(ConfigCat.getPointByStar(getListCacheBasicPoint(), star));
        calPower.addAll(ConfigCat.getPointByStar(getListRandomPoint(), star));
        return ConfigCat.getPower(resInfoCat, calPower);
    }

    /**
     * {*
     * vLong: id mèo, star, power, levelRequirement, currentExp, maxExp, [point],-1,[point random]
     * vString: name
     * }
     */
    public Pbmethod.CommonVector.Builder toStarProto(int star) {
        protocol.Pbmethod.CommonVector.Builder builder = protocol.Pbmethod.CommonVector.newBuilder();
        builder.addALong(catId).addALong(star);
        builder.addALong(getPower(star)).addALong(ConfigCat.getLevelRequired(catId));
        builder.addALong(ConfigCat.getCurPointStar(maturityPoint, star)).addALong(ConfigCat.getPointUpStar(star)); // currentExp, maxExp
        builder.addALong(getNumberTayluyen());
        builder.addAllALong(ConfigCat.getPointByStar(getListBasicPoint(), star));
        builder.addALong(-1);
        builder.addAllALong(ConfigCat.getPointByStar(getListRandomPoint(), star));
        builder.addAString(ResCat.getInfoCatBySlotId(catId).getName())
                .addAString(ConfigCat.getPointByStar(GsonUtil.strToListLong(getCacheBasicPoint()), star).toString())
                .addAString(ConfigCat.getPointByStar(GsonUtil.strToListLong(getCacheRandomPoint()), star).toString());
        return builder;
    }

    public Pbmethod.CommonVector infoFrame() {
        protocol.Pbmethod.CommonVector.Builder builder = protocol.Pbmethod.CommonVector.newBuilder();
        builder.addALong(catId);
        builder.addALong(statusFrame);
        return builder.build();
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_cat", updateData, Arrays.asList("id", id));
    }

}
