package monster.game.raisecat.config;

import lombok.Getter;
import monster.game.raisecat.entity.ResInfoCatEntity;

import java.util.*;

public class ConfigCat {
    public static Map<Integer, Integer> mGroupIdByCatSlot = new HashMap<>() {{
        put(1, 9); // slot 1 - groupId = 9
        put(2, 10);
        put(3, 11);
        put(4, 12);
        put(5, 13);
        put(6, 14);
        put(7, 15);
        put(8, 16);
    }};
    public static DataConfig config;

    public static List<Long> getPointByStar(List<Long> point, int star) {
        int indexStar = star - 1;
        if (indexStar < 0) return point;
        float rate = config.scalePointByStar[indexStar];
        List<Long> newValue = new ArrayList<>(point);
        // pointType, pointIndex, pointValue -> sửa value ở vị trí số 3
        for (int i = 0; i < newValue.size(); i += 3) {
            newValue.set(i + 2, (long) (newValue.get(i + 2) * rate));
        }
        return newValue;
    }

    public static int getLevelRequired(int catKey) {
        for (InfoMission infoMission : config.infoMission) {
            if (infoMission.groupId == catKey) return infoMission.level;
        }
        return 0;
    }

    public static long getPower(ResInfoCatEntity resInfoCat, List<Long> listPoint) {
        long power = 0;
        for (int i = 0; i < listPoint.size(); i += 3) {
            int pointType = listPoint.get(0 + i).intValue();
            Long pointIndex = listPoint.get(1 + i);
            long pointValue = listPoint.get(2 + i);

            long minPower = switch (pointType) {
                case 0 -> 100;
                case 1 -> 300;
                case 2 -> 600;
                default -> 0;
            };
            long maxPower = switch (pointType) {
                case 0 -> 200;
                case 1 -> 500;
                case 2 -> 1000;
                default -> 0;
            };

            List<Long> points = switch (pointType) {
                case 0 -> resInfoCat.getListPointBasic();
                case 1 -> resInfoCat.getListPointRare();
                case 2 -> resInfoCat.getListPointLegend();
                default -> null;
            };
            List<List<Long>> pointValues = switch (pointType) {
                case 0 -> resInfoCat.getListPointBasicValue();
                case 1 -> resInfoCat.getListPointRareValue();
                case 2 -> resInfoCat.getListPointLegendValue();
                default -> null;
            };

            int index = points.indexOf(pointIndex);
            if (index != -1) {
                long minValue = pointValues.get(index).get(0);
                long maxValue = pointValues.get(index).get(1);
                power += minPower + (pointValue - minValue) * (maxPower - minPower) / (maxValue - minValue);
            }
        }
        return power;
    }

    public static long getCurPointStar(int curPoint, int star) {
        int index = star - 1;
        for (int i = 0; i < index; i++) {
            curPoint -= config.pointUpStar.get(i);
        }
        return curPoint;
    }

    public static long getPointUpStar(int star) {
        int index = star - 1;
        if (index >= config.pointUpStar.size()) return -1;
        return config.pointUpStar.get(index);
    }

    @Getter
    public class DataConfig {
        private List<List<Long>> bonusEatDone;
        private List<List<Integer>> bonusRate;
        private List<InfoMission> infoMission;
        private long timeReduceHelpFriend;
        public int numberMaxHelpCat;
        public int numberMaximumHelpACat;
        public int maxStar;
        public List<Long> bonusHelpCatYourFriend;
        public List<Integer> pointUpStar;
        //
        public float[] scalePointByStar;
        public float[] rateNewPoint;
        public int maxNumberPoint;
        public List<Long> feeBoiduong, feeTayluyen;
        public List<List<Integer>>  itemIdRateBoiduong;
        //
        public List<Float> normalRate;
        public List<List<Float>> rateItemBoiduong;
    }

    @Getter
    public class InfoMission {
        private int groupId;
        private int level;
    }

    public static List<Long> getRandomBonus() {
        List<Integer> flatBonusRate = config.bonusRate.stream().flatMap(List::stream).toList();
        int totalRate = flatBonusRate.stream().mapToInt(Integer::intValue).sum();
        Random random = new Random();
        int randValue = random.nextInt(totalRate);
        int cumulativeRate = 0;
        for (int i = 0; i < flatBonusRate.size(); i++) {
            cumulativeRate += flatBonusRate.get(i);
            if (randValue < cumulativeRate) {
                return config.bonusEatDone.get(i);
            }
        }
        return new ArrayList<>();
    }

}
