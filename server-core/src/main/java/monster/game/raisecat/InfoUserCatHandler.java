package monster.game.raisecat;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.RedLocked;
import grep.helper.StringHelper;
import grep.log.Logs;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import monster.config.penum.EventType;
import monster.config.penum.FunctionType;
import monster.config.penum.MaterialType;
import monster.controller.AHandler;
import monster.dao.mapping.UserFriendRelationshipEntity;
import monster.dao.mapping.UserMaterialEntity;
import monster.game.challenge.config.ConfigChallenge2;
import monster.game.challenge.entity.ChallengeDetail;
import monster.game.challenge.entity.GroupChallengeType;
import monster.game.challenge.service.ChallengeService;
import monster.game.raisecat.config.ConfigCat;
import monster.game.raisecat.entity.ResFoodCatEntity;
import monster.game.raisecat.entity.ResInfoCatEntity;
import monster.game.raisecat.entity.UserCatEntity;
import monster.game.raisecat.service.InfoUserCatService;
import monster.object.ServiceResult;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.resource.ResCat;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.BonusBuilder;
import protocol.Pbmethod;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class InfoUserCatHandler extends AHandler {
    InfoUserCatService userCatService = Guice.getInstance(InfoUserCatService.class);
    MaterialService materialService = Guice.getInstance(MaterialService.class);
    List<UserFriendRelationshipEntity> listFriends = new ArrayList<>();

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(RAISE_CAT_STATUS_CAT_USER, RAISE_CAT_UNLOCK_CAT, RAISE_CAT_CAT_EAT,
                RAISE_CAT_SKIP_TIME_CAT_EAT, RAISE_CAT_RECEIVE_CAT_EAT_DONE, RAISE_CAT_STATUS_CAT_FRIEND,
                RAISE_CAT_HELP_CAT_FRIEND, RAISE_CAT_LOG_ACTIVITY, RAISE_CAT_LIST_MISSION_BY_ID_CAT,
                RAISE_CAT_RECEIVE_MISSION_BY_ID_CAT, RAISE_CAT_BEING_COMPLETE_MISSION_BY_ID_CAT, RAISE_CAT_STATUS_MISSION_DAILY, RAISE_CAT_RECEIVE_MISSION_DAILY, RAISE_CAT_FILTER_STATUS_CAT_FRIEND);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        if (!FunctionType.CAT.isEnable(mUser, this)) return;
        this.listFriends = getListFriends();
        try {
            switch (actionId) {
                case RAISE_CAT_STATUS_CAT_USER -> statusCatOfUser(); // done
                case RAISE_CAT_UNLOCK_CAT -> unlockCat();
                case RAISE_CAT_CAT_EAT -> eating();
                case RAISE_CAT_SKIP_TIME_CAT_EAT -> skipTime();
                case RAISE_CAT_RECEIVE_CAT_EAT_DONE -> receiveCatEatDone();
                case RAISE_CAT_STATUS_CAT_FRIEND -> statusCatOfFriend();
                case RAISE_CAT_HELP_CAT_FRIEND -> helpCatFriend();
                case RAISE_CAT_LOG_ACTIVITY -> logActivity();
                case RAISE_CAT_LIST_MISSION_BY_ID_CAT -> listMissionBySlotId();
                case RAISE_CAT_RECEIVE_MISSION_BY_ID_CAT -> receiveMissionIdByIdCat();
                case RAISE_CAT_BEING_COMPLETE_MISSION_BY_ID_CAT -> beingCompleteMission();
                case RAISE_CAT_STATUS_MISSION_DAILY -> missionDailyRaiseCat();
                case RAISE_CAT_RECEIVE_MISSION_DAILY -> receiveMissionDaily();
                case RAISE_CAT_FILTER_STATUS_CAT_FRIEND -> filterStatusCatFriend();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void statusCatOfUser() {
        var userCats = userCatService.getInfoCatOfUser(mUser);
        userCats.forEach(userCatService::checkValidPoint);
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (UserCatEntity userCat : userCats) {
            Date date = new Date();
            if (userCat.getWaitingTimeForEat().getTime() < date.getTime() && userCat.getStatusCat() == 1) {
                if (!updateStatusCat(userCat.getId(), 2, 0)) {
                    addErrResponse();
                    return;
                }
                userCat.setStatusCat(2);
                userCat.setNumberHelped(0);
            }
            builder.addAVector(userCat.toProto());
        }
        // mèo được nhận nuôi tiếp theo
        Pbmethod.CommonVector.Builder commonVector = Pbmethod.CommonVector.newBuilder();
        UserCatEntity userCat = userCatService.getInfoCatOfUser(mUser).stream().filter(cat -> cat.getStatusOwn() == 0).findFirst().orElse(null);
        int catReceive = userCat == null ? -1 : userCat.getCatId();
        commonVector.addALong(catReceive);
        builder.addAVector(commonVector);
        addResponse(builder.build());
    }

    private void unlockCat() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int slotId = aLong.get(0).intValue();
        int levelUser = mUser.getUser().getLevel();

        ResInfoCatEntity resInfoCat = ResCat.getInfoCatBySlotId(slotId);
        UserCatEntity userCat = userCatService.getInfoCatOfUser(mUser).stream().filter(userCatBySlot -> userCatBySlot.getSlotId() == slotId).findFirst().orElse(null);
        if (userCat == null) return;
        if (resInfoCat == null) return;
        if (resInfoCat.getLevelUnlock() > levelUser) {
            addErrResponse("Level user đạt: " + resInfoCat.getLevelUnlock() + " để mở khóa!");
            return;
        }
        if (userCat.getStatusUnlock() == 0) {
            addErrResponse("Hoàn thành Thành tựu nhận nuôi để mở khóa!");
            return;
        }

        if (userCat.getStatusOwn() == 1) {
            addErrResponse("Bạn đã sở hữu thú cưng này rồi");
            return;
        }
        int statusFrameNew = 1, statusOwn = 1;
        // update db
        if (!dbUpdateStatusSlotId(userCat.getId(), statusFrameNew, statusOwn)) {
            addErrResponse();
            return;
        }
        userCat.setStatusFrame(statusFrameNew);
        userCat.setStatusOwn(statusOwn);

        Actions.save(user, "raise_cat", "unlock_cat", "user_id", mUser.getUser().getId(), "slot_id", slotId, "status_own", userCat.getStatusOwn());

        addResponse(null);
    }

    private void eating() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int slotId = aLong.get(0).intValue();
        int idFood = aLong.get(1).intValue();
        UserCatEntity userCat = userCatService.getInfoCatOfUser(mUser).stream().filter(userCatBySlot -> userCatBySlot.getSlotId() == slotId).findFirst().orElse(null);
        if (userCat == null) return;

        if (userCat.getStatusOwn() == 0) {
            addErrResponse("Bạn chưa sở hữu con thú cưng này!");
            return;
        }
        if (userCat.getStatusCat() == 1 || userCat.getStatusCat() == 2) {
            addErrResponse("Thú cưng đang ăn! Chờ chút nhé");
            return;
        }

        Date date = new Date();
        ResFoodCatEntity resFoodCat = ResCat.getInfoFoodById(idFood);
        if (resFoodCat == null) return;
        UserMaterialEntity userMaterial = mUser.getResources().getMaterial(1, idFood);
        if (userMaterial == null) {
            addErrResponse("Không có thức ăn!");
            return;
        }
        if (userMaterial.getNumber() <= 0) {
            addErrResponse("Số lượng không đủ!");
            return;
        }
        int timeWaiting = resFoodCat.getTimeWaiting();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, timeWaiting);

        Date newDate = calendar.getTime();
        int maturityPoint = resFoodCat.getMaturityPoint();
        int maturityPointNew = userCat.getMaturityPoint() + maturityPoint;
        int statusCatNew = 1; // đang ăn
        if (!dbUpdateTimeEating(userCat.getId(), maturityPointNew, newDate, statusCatNew)) {
            addErrResponse();
            return;
        }
        userCat.setMaturityPoint(maturityPointNew);
        userCat.setWaitingTimeForEat(newDate);
        userCat.setStatusCat(statusCatNew);
        ServiceResult<List<Long>> serviceResult = materialService.addBonus(mUser, BonusBuilder.newInstance()
                .addMaterial(MaterialType.getUsedItem(idFood), -1)
                .build(), "food_cat");
        if (serviceResult.success) {
            addResponse(Pbmethod.CommonVector.newBuilder()
                    .addAllALong(serviceResult.data)
                    .build());
            EventType.CAT_EAT.addEvent(mUser, 1);
            userCatService.addLogDateEatCat(mUser, new Date());
            userCatService.addLogInfoEatCat(mUser, idFood, userCat.getCatId());
        } else serviceResult.writeResponse(this);
        Actions.save(user, "raise_cat", "eating", "user_id", mUser.getUser().getId(), "slot_id", slotId, "maturity_point", userCat.getWaitingTimeForEat(), "waiting_time_for_eat", userCat.getWaitingTimeForEat());
    }

    private void skipTime() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int slotId = aLong.get(0).intValue();
        UserCatEntity userCat = userCatService.getInfoCatOfUser(mUser).stream().filter(userCatBySlot -> userCatBySlot.getSlotId() == slotId).findFirst().orElse(null);
        if (userCat == null) return;
        if (userCat.getStatusOwn() == 0) {
            addErrResponse("Bạn chưa sở hữu con thú cưng này!");
            return;
        }
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        Date dateWaiting = new Date();
        long milliseconds = userCat.getWaitingTimeForEat().getTime() - dateWaiting.getTime();
        long numberGem = (milliseconds / (60 * 1000)) + 1;
        if (mUser.getUser().getGem() < numberGem) {
            addErrResponse("Không đủ kim cương!");
            return;
        }

        List<Long> bonus = Bonus.view(Bonus.BONUS_GEM, -numberGem);
        List<Long> retBonus = Bonus.receiveListItem(mUser, "skip_time_cat", bonus);
        if (retBonus.isEmpty()) {
            addErrResponse();
            return;
        }
        int newStatusCat = 2; // Đợi nhận quà
        if (!updateTimeSkip(userCat.getId(), dateWaiting, newStatusCat, 0)) {
            addErrResponse();
            return;
        }
        userCat.setWaitingTimeForEat(dateWaiting);
        userCat.setStatusCat(newStatusCat);

        Actions.save(user, "raise_cat", "skip_time_eat", "user_id", mUser.getUser().getId(), "status_cat", userCat.getStatusCat(), "date_waiting", DateTime.getFullDate(userCat.getWaitingTimeForEat()));
        builder.addAllALong(retBonus);
        addResponse(builder.build());
        EventType.CAT_ACCELERATION.addEvent(mUser);
    }

    private void receiveCatEatDone() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int slotId = aLong.get(0).intValue();
        UserCatEntity userCat = userCatService.getInfoCatOfUser(mUser).stream().filter(userCatBySlot -> userCatBySlot.getSlotId() == slotId).findFirst().orElse(null);
        if (userCat == null) return;
        if (userCat.getStatusCat() == 0 || userCat.getStatusCat() == 1) {
            addErrResponse("Thú cưng đang trong thời gian chờ!");
            return;
        }
        List<Long> bonus = userCat.getListBonus();
        List<Long> aBonus = Bonus.receiveListItem(mUser, "receive_cat", bonus);
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }
        builder.addAllALong(aBonus);
        int newStatusCat = 0; // Đợi cho ăn
        List<Long> bonusNew = ConfigCat.getRandomBonus();
        if (!dbUpdateBonusCat(userCat.getId(), newStatusCat, bonusNew.toString(), 0)) {
            addErrResponse();
            return;
        }
        userCat.setStatusCat(newStatusCat);
        userCat.setBonus(bonusNew.toString());
        userCat.setNumberHelped(0);
        addResponse(builder.build());
    }

    private void statusCatOfFriend() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int userId = aLong.get(0).intValue();
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        List<UserCatEntity> userCatByUserId = userCatService.listCatByUserId(userId);
        if (userCatByUserId.isEmpty()) {
            addErrResponse("Bạn bè của bạn chưa nuôi thú cưng!");
            return;
        }
        for (UserCatEntity userCat : userCatService.listCatByUserId(userId)) {
            if (userCat.getWaitingTimeForEat().getTime() - new Date().getTime() < 0 && userCat.getStatusCat() == 1) {
                if (!updateStatusCat(userCat.getId(), 2, 0)) {
                    addErrResponse();
                    return;
                }
                userCat.setStatusCat(2);
                userCat.setNumberHelped(0);
            }
            builder.addAVector(userCat.toProto());
        }
        addResponse(builder.build());
    }

    private void helpCatFriend() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int friendId = aLong.get(0).intValue();
        int slotId = aLong.get(1).intValue();
        long now = System.currentTimeMillis();
        int numberHelpCatRemaining = mUser.getUData().getNumberHelpCat();
        if (numberHelpCatRemaining == ConfigCat.config.numberMaxHelpCat) {
            addErrResponse("Bạn đã hết lượt giúp đỡ bạn bè trong ngày hôm nay!");
            return;
        }
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        UserCatEntity userCatFriend = userCatService.listCatByUserId(friendId).stream().filter(cat -> cat.getSlotId() == slotId).findFirst().orElse(null);
        if (userCatFriend == null) return;
        if (userCatFriend.getNumberHelped() == ConfigCat.config.numberMaximumHelpACat) {
            addErrResponse("Thú cưng này đã được tối đa số lần giúp đỡ!");
            return;
        }
        // thời gian giúp đỡ bạn bè
        long timeReduceMinutes = ConfigCat.config.getTimeReduceHelpFriend();
        long timeReduceMillis = timeReduceMinutes * 60 * 1000L;

        long timeEat = userCatFriend.getWaitingTimeForEat().getTime();
        long newTimeEat = timeEat - timeReduceMillis;
        if (now > timeEat) {
            addErrResponse("Trạng thái của thú cưng hiện tại không thể giúp đỡ!");
        } else {
            RedLocked.lock("cat_friend_%s_%s".formatted(friendId, slotId), (languageKey -> {
                if (StringHelper.isEmpty(languageKey)) {
                    boolean updated = false;
                    if (newTimeEat > 0) {
                        Date newDate = new Date(newTimeEat);
                        if (!updateTimeHelpFriend(userCatFriend.getId(), newDate, userCatFriend.getNumberHelped() + 1)) {
                            addErrResponse();
                            return;
                        }
                        userCatFriend.setWaitingTimeForEat(newDate);
                        userCatFriend.setNumberHelped(userCatFriend.getNumberHelped() + 1);
                        infoAfterUpdateCatFriend(friendId, builder, userCatFriend, timeReduceMinutes, new Date());
                        EventType.CAT_HELP_FRIEND.addEvent(mUser, 1);
                        updated = true;
                    } else if ((timeEat - now) <= timeReduceMillis) {
                        Date nowDate = new Date();
                        int newStatus = 2;
                        if (!updateTimeSkip(userCatFriend.getId(), nowDate, newStatus, userCatFriend.getNumberHelped() + 1)) {
                            addErrResponse();
                            return;
                        }
                        userCatFriend.setWaitingTimeForEat(nowDate);
                        userCatFriend.setStatusCat(newStatus);
                        userCatFriend.setNumberHelped(userCatFriend.getNumberHelped() + 1);
                        infoAfterUpdateCatFriend(friendId, builder, userCatFriend, timeReduceMinutes, nowDate);
                        EventType.CAT_HELP_FRIEND.addEvent(mUser, 1);
                        updated = true;
                    }
                    if (updated) {
                        mUser.getUData().updateNumberHelpCat(1);
                        Actions.save(user, "raise_cat", "help_friend", "friend_id", friendId, "cat_id_help", userCatFriend.getCatId(), "time_before_cat_friend", timeEat, "time_after_cat_friend", userCatFriend.getWaitingTimeForEat().getTime(), "number_help_cat", mUser.getUData().getNumberHelpCat());
                    } else {
                        Logs.debug("Vuốt mèo thất bại: " + mUser.getUser().getId() + " giúp: " + friendId);
                    }
                } else {
                    addErrResponse("Mèo vip quá, chờ tới lượt sau bạn nhé");
                }
            }));
        }
    }

    private void infoAfterUpdateCatFriend(int friendId, Pbmethod.CommonVector.Builder builder, UserCatEntity userCatFriend, long timeReduceHelpFriend, Date date) {
        Actions.save(user, "cat_friend", "help_cat_friend", "user_id_help", userCatFriend.getUserId(), "timeRemain", userCatFriend.getWaitingTimeForEat(), "time_help", timeReduceHelpFriend / 1000);
        List<Long> bonus = ConfigCat.config.bonusHelpCatYourFriend;
        bonus = Bonus.receiveListItem(mUser, "help_cat_friend", bonus);
        builder.addAllALong(bonus);
        addResponse(builder.build());
        userCatService.addLogDate(friendId, date, mUser);
        userCatService.addLogInfo(friendId, timeReduceHelpFriend, userCatFriend.getCatId(), mUser);
    }

    private void logActivity() throws ParseException {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        List<Object[]> allLogs = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd-mm HH:mm:ss");
        List<String> dates = userCatService.getLogDate(mUser);
        List<String> infos = userCatService.getLogInfo(mUser);
        for (int i = 0; i < Math.min(dates.size(), infos.size()); i++) {
            Date parsedDate = sdf.parse(dates.get(i));
            allLogs.add(new Object[]{dates.get(i), infos.get(i), parsedDate});
        }
        dates = userCatService.getLogDateEatCat(mUser);
        infos = userCatService.getLogInfoEatCat(mUser);
        for (int i = 0; i < Math.min(dates.size(), infos.size()); i++) {
            Date parsedDate = sdf.parse(dates.get(i));
            allLogs.add(new Object[]{dates.get(i), infos.get(i), parsedDate});
        }
        dates = userCatService.getLogDatePushStar(user.getServer());
        infos = userCatService.getLogInfoPushStar(user.getServer());
        for (int i = 0; i < Math.min(dates.size(), infos.size()); i++) {
            Date parsedDate = sdf.parse(dates.get(i));
            allLogs.add(new Object[]{dates.get(i), infos.get(i), parsedDate});
        }
        dates = userCatService.getLogDateCleanCat(user.getServer());
        infos = userCatService.getLogInfoCleanCat(user.getServer());
        for (int i = 0; i < Math.min(dates.size(), infos.size()); i++) {
            Date parsedDate = sdf.parse(dates.get(i));
            allLogs.add(new Object[]{dates.get(i), infos.get(i), parsedDate});
        }
        dates = userCatService.getLogDateFosteringCat(user.getServer());
        infos = userCatService.getLogInfoFosteringCat(user.getServer());
        for (int i = 0; i < Math.min(dates.size(), infos.size()); i++) {
            Date parsedDate = sdf.parse(dates.get(i));
            allLogs.add(new Object[]{dates.get(i), infos.get(i), parsedDate});
        }
        allLogs.sort((a, b) -> ((Date) b[2]).compareTo((Date) a[2]));

        for (Object[] log : allLogs) {
            Pbmethod.CommonVector.Builder commonVector = Pbmethod.CommonVector.newBuilder();
            commonVector.addAString((String) log[0]); // date
            commonVector.addAString((String) log[1]); // info
            builder.addAVector(commonVector);
        }

        addResponse(builder.build());
    }

    private void listMissionBySlotId() {
        UserCatEntity userCat = userCatService.getInfoCatOfUser(mUser).stream().filter(cat -> cat.getStatusOwn() == 0).findFirst().orElse(null);
        if (userCat == null) return;
        int slotId = userCat.getSlotId();
        int level = ConfigCat.config.getInfoMission().get(slotId - 1).getLevel();
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        if (user.getLevel() < level) {
            String text = "Người chơi đạt LV." + level + " để mở khóa nhiệm vụ tiếp theo!";
            addResponse(builder.addAVector(Pbmethod.CommonVector.newBuilder().addAString(text)).build());
        } else {
            Map<Integer, Integer> findBySlotId = ConfigCat.mGroupIdByCatSlot;
            int groupId = findBySlotId.get(slotId);
            addResponse(Guice.getInstance(ChallengeService.class).status(mUser, groupId).build());
        }
    }

    private void receiveMissionIdByIdCat() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int challengeId = aLong.get(0).intValue();
        ChallengeDetail challengeDetail = ConfigChallenge2.getGroupByChallengeId(challengeId);
        Guice.getInstance(ChallengeService.class).receive(mUser, challengeDetail.getGroupId(), challengeId, this);
    }

    private void beingCompleteMission() {
        Guice.getInstance(ChallengeService.class).noteBeingComplete(mUser, GroupChallengeType.DAILY_MISSION_CAT.id, this);
    }

    private void receiveMissionDaily() {
        int groupId = GroupChallengeType.DAILY_MISSION_CAT.id;
        Guice.getInstance(ChallengeService.class).receiveAllReward(mUser, groupId)
                .writeResponse(this);
    }

    private void missionDailyRaiseCat() {
        int groupId = GroupChallengeType.DAILY_MISSION_CAT.id;
        addResponse(RAISE_CAT_STATUS_MISSION_DAILY, Guice.getInstance(ChallengeService.class).status(mUser, groupId).build());
    }

    private void filterStatusCatFriend() {
        /* status:
            - 0: chưa tham gia tính năng
            - 1: có mèo nhưng ko thể vuốt
            - 2: có mèo có thể vuốt
         */
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        List<Integer> friendIds = new ArrayList<>();
        for (UserFriendRelationshipEntity relationshipEntity : listFriends) {
            int friendId = (relationshipEntity.getUserId1() == user.getId()) ? relationshipEntity.getUserId2() : relationshipEntity.getUserId1();
            friendIds.add(friendId);
        }
        for (Integer userId : friendIds) {
            Pbmethod.CommonVector.Builder cmv = Pbmethod.CommonVector.newBuilder();
            List<UserCatEntity> listUserCatFriend = userCatService.listCatByUserId(userId);
            if (listUserCatFriend.isEmpty()) {
                cmv.addALong(userId);
                cmv.addALong(0);
            } else {
                UserCatEntity userCat = listUserCatFriend.stream().filter(cat -> cat.getStatusOwn() == 1).findFirst().orElse(null);
                int numberHelped = userCat.getNumberHelped();
                int statusCat = userCat.getStatusCat();
                if ((numberHelped == ConfigCat.config.numberMaximumHelpACat && (statusCat == 0 || statusCat == 2)) ||
                        (numberHelped == ConfigCat.config.numberMaximumHelpACat && statusCat == 1) ||
                        (numberHelped == 0 && (statusCat == 0 || statusCat == 2)) || (numberHelped < 2 && statusCat == 2)) {
                    cmv.addALong(userId);
                    cmv.addALong(1);
                } else if (statusCat == 1 && numberHelped < 2) {
                    cmv.addALong(userId);
                    cmv.addALong(2);
                }
            }
            builder.addAVector(cmv);
        }
        addResponse(builder.build());
    }

    private boolean dbUpdateStatusSlotId(long id, int statusFrameNew, int statusOwn) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_cat set status_frame=:statusFrameNew, status_own=:statusOwn where id=:id");
            query.setParameter("id", id);
            query.setParameter("statusFrameNew", statusFrameNew);
            query.setParameter("statusOwn", statusOwn);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean dbUpdateTimeEating(long id, int maturityPointNew, Date date, int statusCatNew) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_cat set maturity_point=:maturityPointNew, waiting_time_for_eat=:date, status_cat=:statusCatNew where id=:id");
            query.setParameter("id", id);
            query.setParameter("date", date);
            query.setParameter("maturityPointNew", maturityPointNew);
            query.setParameter("statusCatNew", statusCatNew);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean updateTimeSkip(long id, Date dateWaiting, int newStatusCat, int numberHelped) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_cat set status_cat=:newStatusCat, waiting_time_for_eat=:dateWaiting, number_helped=:numberHelped where id=:id");
            query.setParameter("id", id);
            query.setParameter("newStatusCat", newStatusCat);
            query.setParameter("dateWaiting", dateWaiting);
            query.setParameter("numberHelped", numberHelped);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean updateStatusCat(long id, int newStatusCat, int numberHelped) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_cat set status_cat=:newStatusCat,number_helped=:numberHelped where id=:id");
            query.setParameter("id", id);
            query.setParameter("newStatusCat", newStatusCat);
            query.setParameter("numberHelped", numberHelped);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }


    private boolean dbUpdateBonusCat(long id, int newStatusCat, String bonusNew, int numberHelped) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_cat set status_cat=:newStatusCat, bonus=:bonusNew,number_helped=:numberHelped where id=:id");
            query.setParameter("id", id);
            query.setParameter("newStatusCat", newStatusCat);
            query.setParameter("bonusNew", bonusNew);
            query.setParameter("numberHelped", numberHelped);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    private boolean updateTimeHelpFriend(long id, Date dateWaitingRemain, int numberHelped) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_cat set waiting_time_for_eat=:dateWaitingRemain, number_helped=:numberHelped where id=:id");
            query.setParameter("id", id);
            query.setParameter("dateWaitingRemain", dateWaitingRemain);
            query.setParameter("numberHelped", numberHelped);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    public static List<UserFriendRelationshipEntity> dbGetListFriends(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserFriendRelationshipEntity> lstFriends = session.createNativeQuery("select * from user_friend_relationship where (user_id1=" + userId + " or user_id2=" + userId + ") and relationship= 1", UserFriendRelationshipEntity.class).getResultList();
            return lstFriends;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    List<UserFriendRelationshipEntity> getListFriends() {
        List<UserFriendRelationshipEntity> lstUserFriend = null;
        if (lstUserFriend == null) {
            lstUserFriend = dbGetListFriends(user.getId());
            if (lstUserFriend == null) {
                addErrResponse();
                return null;
            }
        }

        return lstUserFriend;
    }
}
