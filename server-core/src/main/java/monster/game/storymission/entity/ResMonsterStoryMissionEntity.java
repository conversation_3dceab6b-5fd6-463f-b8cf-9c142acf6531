package monster.game.storymission.entity;

import grep.helper.GsonUtil;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.penum.MonsterType;
import monster.object.BattleTeam;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.common.TeamService;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "res_monster_story_mission", schema = "dson_main")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResMonsterStoryMissionEntity implements Serializable {
    @Id
    private int id;
    private int eventId;
    private int type, storyNow;
    private String monsterId, monsterLevel;
    private int levelShow;
    private int starShow;
    private String bonus;
    private long power;
    public BattleTeam getBattleTeam() {
        BattleTeam battleTeam = Guice.getInstance(TeamService.class)
                .getMonsterTeam(monsterId, monsterLevel, MonsterType.STORY_MISSION);
        for (HeroInfoEntity aHero : battleTeam.getAHero()) {
            if (aHero != null) {
                aHero.setLevel(levelShow);
                aHero.star = starShow;
                aHero.setHeroInfo(new ArrayList<>());
            }
        }
        return battleTeam;
    }

    public List<Long> getBonus() {
        return GsonUtil.strToListLong(bonus);
    }

}
