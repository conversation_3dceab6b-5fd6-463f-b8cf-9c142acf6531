package monster.game.storymission.entity;

import grep.helper.GsonUtil;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "user_story_mission", schema = "dson")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserStoryMissionEntity implements Serializable {
    @Id
    private long id;
    private int userId;
    private int eventId;
    /* statusFinish - status group
        0: chưa đủ điều kiện nhận
        1: được nhận
        2: đã nhận
    */
    private int type, storyNow, statusFinish;
    private long storyMax;
    private LocalDateTime timeOpen;
    private String statusReceive;

    public List<Long> getStatusReceive() {
        return GsonUtil.strToListLong(statusReceive);
    }

    public void setStatusReceive(List<Long> statusReceive) {
        this.statusReceive = statusReceive.toString();
    }

}
