package monster.game.storymission.dao;

import monster.dao.AbstractDAO;
import monster.game.storymission.entity.UserStoryMissionEntity;

import java.util.List;

public class StoryMissionDAO extends AbstractDAO {
    public List<UserStoryMissionEntity> getUserStoryMission(int userId, int eventId) {
        return doQuery(em -> em.createQuery("select e from UserStoryMissionEntity e where e.userId=:userId and e.eventId=:eventId", UserStoryMissionEntity.class)
                .setParameter("userId", userId).setParameter("eventId", eventId).getResultList());
    }
}
