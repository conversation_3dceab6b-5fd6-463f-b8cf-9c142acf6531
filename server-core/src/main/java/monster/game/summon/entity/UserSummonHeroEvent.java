package monster.game.summon.entity;

import grep.database.DBJPA;
import jakarta.persistence.*;
import lombok.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Entity
@Table(name = "user_summon_hero_event", schema = "dson")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSummonHeroEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;
    @Column(name = "user_id")
    private int userId;
    @Column(name = "event_id")
    private int eventId;
    @Column(name = "time_key")
    private int timeKey;
    @Column(name = "hero_select")
    private int heroSelect;
    @Column(name = "ensure1")
    private int ensure1;
    @Column(name = "ensure2")
    private int ensure2;
    @Column(name = "number_summon_hero")
    private int numberSummonHero;
    private int numberSummonGem;
    long damage;

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_summon_hero_event", updateData, Arrays.asList("user_id", userId, "event_id", eventId));
    }
}