package monster.game.summon.service.impl;

import grep.helper.DateTime;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgSummon;
import monster.config.penum.NotifyType;
import monster.game.summon.config.ConfigProphetTree;
import monster.game.summon.dao.SummonDAO;
import monster.game.summon.entity.UserProphetTree;
import monster.game.summon.service.SummonService;
import monster.object.MyUser;
import monster.object.UserInt;
import monster.service.aop.CacheInGame;
import monster.service.common.NotifyService;
import protocol.Pbmethod;

import javax.inject.Inject;

public class SummonServiceImpl implements SummonService {

    @Inject
    SummonDAO summonDAO;
    @Inject
    NotifyService notifyService;

    @CacheInGame(expire = 300)
    @Override
    public UserProphetTree getUserProphetTree(MyUser mUser) {
        var prophetTree = summonDAO.getUserProphetTree(mUser.getUser().getId());
        if (prophetTree == null) {
            prophetTree = UserProphetTree.builder().userId(mUser.getUser().getId()).timeKey(DateTime.getTimeKey())
                    .receiveIndex(StringHelper.toDBString(NumberUtil.genListInt(ConfigProphetTree.points.size(), 0)))
                    .factionUnlock(ConfigProphetTree.getFactionUnlock(mUser.getUser())).build();
            if (!summonDAO.save(prophetTree)) return null;
        }
        return prophetTree;
    }

    @Override
    public void checkNotify(MyUser mUser, Pbmethod.CommonVector.Builder cmm) {
        try {
            UserInt uInt = mUser.getUData().getUInt();
            // chiêu mộ thường
            if (CfgSummon.basicCountdown(uInt.getValue(UserInt.SUMMON_BASIC)) == 0) cmm.addALong(NotifyType.SUMMON_BASIC_CIRCLE.value);
            // chiêu mộ cao cấp
            if (CfgSummon.heroicCountdown(uInt.getValue(UserInt.SUMMON_HEROIC)) == 0) cmm.addALong(NotifyType.SUMMON_HEROIC_CIRCLE.value);
            else {
                for (int pos = 1; pos <= 5; pos++) {
                    boolean canGet = switch (pos) {
                        case 1 -> uInt.getValue(UserInt.BONUS_SUMMON_1) == 0;
                        case 2 -> uInt.getValue(UserInt.BONUS_SUMMON_2) == 0;
                        case 3 -> uInt.getValue(UserInt.BONUS_SUMMON_3) == 0;
                        case 4 -> uInt.getValue(UserInt.BONUS_SUMMON_4) == 0;
                        case 5 -> uInt.getValue(UserInt.BONUS_SUMMON_5) == 0;
                        default -> false;
                    };
                    if (canGet && CfgSummon.rateBonus.get(pos - 1) <= uInt.getValue(UserInt.BONUS_SUMMON_HEROIC)) {
                        cmm.addALong(NotifyType.SUMMON_HEROIC_CIRCLE.value);
                        break;
                    }
                }
            }
            // chiêu mộ đặc biệt
            var prophetTree = getUserProphetTree(mUser);
            var receiveIndex = prophetTree.getListReceiveIndex();
            for (int i = 0; i < ConfigProphetTree.points.size(); i++) {
                if (receiveIndex.get(i) == 0 && prophetTree.getPoint() >= ConfigProphetTree.points.get(i)) {
                    cmm.addALong(NotifyType.PROPHET_TREE.value);
                    break;
                }
            }
        } catch (Exception ex) {
            Logs.error("SummonServiceImpl", ex);
        }
    }

    @Override
    public void checkRemoveSummonNotify(MyUser mUser) {
        UserInt uInt = mUser.getUData().getUInt();

        notifyService.sendNotify(mUser, NotifyType.SUMMON_BASIC_CIRCLE, CfgSummon.basicCountdown(uInt.getValue(UserInt.SUMMON_BASIC)) == 0);

        if (CfgSummon.heroicCountdown(uInt.getValue(UserInt.SUMMON_HEROIC)) == 0) notifyService.sendNotify(mUser, NotifyType.SUMMON_HEROIC_CIRCLE, true);
        else {
            boolean hasNotify = false;
            for (int pos = 1; pos <= 5; pos++) {
                boolean canGet = switch (pos) {
                    case 1 -> uInt.getValue(UserInt.BONUS_SUMMON_1) == 0;
                    case 2 -> uInt.getValue(UserInt.BONUS_SUMMON_2) == 0;
                    case 3 -> uInt.getValue(UserInt.BONUS_SUMMON_3) == 0;
                    case 4 -> uInt.getValue(UserInt.BONUS_SUMMON_4) == 0;
                    case 5 -> uInt.getValue(UserInt.BONUS_SUMMON_5) == 0;
                    default -> false;
                };
                if (canGet && CfgSummon.rateBonus.get(pos - 1) <= uInt.getValue(UserInt.BONUS_SUMMON_HEROIC)) {
                    hasNotify = true;
                    break;
                }
            }
            notifyService.sendNotify(mUser, NotifyType.SUMMON_HEROIC_CIRCLE, hasNotify);
        }
    }

    @Override
    public void checkRemoveProphetTreeNotify(MyUser mUser) {
        boolean hasNotify = false;
        var prophetTree = getUserProphetTree(mUser);
        var receiveIndex = prophetTree.getListReceiveIndex();
        for (int i = 0; i < ConfigProphetTree.points.size(); i++) {
            if (receiveIndex.get(i) == 0 && prophetTree.getPoint() >= ConfigProphetTree.points.get(i)) {
                hasNotify = true;
                break;
            }
        }
        notifyService.sendNotify(mUser, NotifyType.PROPHET_TREE, hasNotify);
    }
}
