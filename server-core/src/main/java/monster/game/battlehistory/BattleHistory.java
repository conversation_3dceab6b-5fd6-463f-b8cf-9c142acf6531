package monster.game.battlehistory;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.Filer;
import grep.helper.GUtil;
import grep.helper.GZip;
import grep.log.Logs;
import monster.dao.mapping.BattleLogEntity;
import monster.dao.mapping.UserEntity;
import monster.game.battlehistory.entity.BattleHistoryEntity;
import monster.service.monitor.Telegram;

import jakarta.persistence.EntityManager;
import java.io.File;
import java.util.*;

public class BattleHistory {
    private static Map<String, List<BattleHistoryEntity>> listHistoryMapByKey = new HashMap<>();
    private static final String CAMPAIGN_KEY = "campaign_%s_%s";
    private static final String STORY_KEY = "story_%s_%s";
    private static final String TERRITORY_KEY = "territory_%s_%s_%s";
    private static final String BASE_PATH = "battlehistory/";

    public static String getCampaignKey(int serverId, int level) {
        return String.format(CAMPAIGN_KEY, serverId, level);
    }

    public static String getStoryKey(int serverId, int storyId) {
        return String.format(STORY_KEY, serverId, storyId);
    }

    public static String getTerritoryKey(int serverId, int faction, int level) {
        return String.format(TERRITORY_KEY, serverId, faction, level);
    }

    public static synchronized void checkHistory(UserEntity user, String historyKey, long battleId, byte[] data, byte[] outputData) {
        try {
            List<BattleHistoryEntity> listHistory = getListHistory(user.getServer(), historyKey);
            if (listHistory.size() < 5) {
                BattleHistoryEntity rank = new BattleHistoryEntity(user, historyKey, battleId);
                listHistory.addFirst(rank);
                DBJPA.save(listHistory.getFirst());
                updateBattleData(rank, battleId, data, outputData);
                listHistory.sort(Comparator.comparing(BattleHistoryEntity::getDateCreated));
            } else {
                BattleHistoryEntity rank = listHistory.getFirst();
                rank.update(user);
                DBJPA.update(rank);
                updateBattleData(rank, battleId, data, outputData);
                listHistory.sort(Comparator.comparing(BattleHistoryEntity::getDateCreated));
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public static List<BattleHistoryEntity> getListHistory(int serverId, String historyKey) {
        if (!listHistoryMapByKey.containsKey(historyKey)) {
            listHistoryMapByKey.put(historyKey, new ArrayList<>());
        }
        if (listHistoryMapByKey.get(historyKey).isEmpty()) {
            List<BattleHistoryEntity> ranks = dbGetListHistory(serverId, historyKey);
            if (ranks != null) listHistoryMapByKey.get(historyKey).addAll(ranks);
        }
        return listHistoryMapByKey.get(historyKey);
    }

    private static List<BattleHistoryEntity> dbGetListHistory(int serverId, String historyKey) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String strQuery = String.format("select * from battle_history where server_id=%s and history_key='%s' order by date_created desc limit 5", serverId, historyKey);
            return session.createNativeQuery(strQuery, BattleHistoryEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    private static boolean updateBattleData(BattleHistoryEntity history, long battleId, byte[] data, byte[] outputData) {
        EntityManager em = null;
        try {
            em = DBJPA.getEntityManager();
            em.getTransaction().begin();
            em.createNativeQuery("update battle_history set battle_id=:battleId where id=:id")
                    .setParameter("id", history.getId()).setParameter("battleId", battleId)
                    .executeUpdate();
            em.getTransaction().commit();

            File file = new File(BASE_PATH);
            if (!file.exists()) file.mkdirs();
            Filer.saveBinFile(getDataFileName(history), GZip.compress(data));
            Filer.saveBinFile(getOutputDataFileName(history), GZip.compress(outputData));
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            DBJPA.closeSession(em);
        }
        return false;
    }

    public static String getDataFileName(BattleHistoryEntity battleHistory) {
        return getLogPath(battleHistory.getHistoryKey()) + "data_" + (battleHistory.getBattleId() % 5);
    }

    public static String getOutputDataFileName(BattleHistoryEntity battleHistory) {
        return getLogPath(battleHistory.getHistoryKey()) + "outputdata_" + (battleHistory.getBattleId() % 5);
    }

    public static String getLogPath(String historyKey) {
        String[] splitStr = historyKey.split("_");
        String functionName = splitStr[0];
        splitStr = Arrays.copyOfRange(splitStr, 1, splitStr.length);
        String logPath = BASE_PATH + functionName + "/";
        for (String tmpStr : splitStr) {
            logPath += tmpStr + "/";
        }
        return logPath;
    }
}
