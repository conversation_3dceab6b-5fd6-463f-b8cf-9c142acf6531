package monster.game.pickafightdemonking.enity;

import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.penum.MonsterType;
import monster.object.BattleTeam;
import monster.server.config.Guice;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.common.TeamService;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonsterDemonKing;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.*;

@Entity
@Data
@Table(name = "demon_king_today")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DemonKingTodayEntity implements Serializable {
    @Id
    private int id;
    private String monsterIdToday, monsterLevelToday, listSkillToday;
    private int groupId;

    public BattleTeam getBattleTeam() {
        List<SkillEntity> addPassiveSkills = new ArrayList<>();
        List<Integer> listSkill = getListSkillToday();
        for (Integer integer : listSkill) {
            SkillEntity skillEntity = ResHero.getSkill(integer);
            if (skillEntity != null) addPassiveSkills.add(skillEntity);
        }

        return Guice.getInstance(TeamService.class)
                .getMonsterTeam(monsterIdToday, monsterLevelToday, MonsterType.DEMON_KING, addPassiveSkills);
    }
    @Transient
    private static Queue<List<Integer>> queueSkill = new LinkedList<>();
    @Transient
    private static Queue<List<Long>> queueMonsterId = new LinkedList<>();

    public List<Integer> getRandomSkill() {
        ResMonsterDemonKingEntity resMonsterDemonKing = ResMonsterDemonKing.mMonsterDemonKingMap.get(groupId);
        List<List<Integer>> getListSkill = resMonsterDemonKing.getListSkill();
        List<Integer> indices = new ArrayList<>();
        for (int i = 0; i < getListSkill.size(); i++) {
            indices.add(i);
        }
        Collections.shuffle(indices);
        List<Integer> listSkillNew = new ArrayList<>();
        for (int index : indices) {
            List<Integer> listSkill = getListSkill.get(index);
            for (int skill : listSkill) {
                if (listSkillNew.size() == 1) {
                    return listSkillNew;
                }
                listSkillNew.add(skill);
            }
        }
        return listSkillNew;
    }

    public List<Long> getRandomMonsterId() {
        ResMonsterDemonKingEntity resMonsterDemonKing = ResMonsterDemonKing.mMonsterDemonKingMap.get(groupId);
        List<List<Long>> monsterList = resMonsterDemonKing.getMonsterId();
        List<Integer> indices = new ArrayList<>();
        for (int i = 0; i < monsterList.size(); i++) {
            indices.add(i);
        }
        Collections.shuffle(indices);
        List<Long> listMonsterIdNew = new ArrayList<>();
        for (int index : indices) {
            List<Long> monsterGroup = monsterList.get(index);
            for (long monsterId : monsterGroup) {
                if (monsterGroup.size() >= 6) {
                    return monsterGroup;
                }
                monsterGroup.add(monsterId);
            }
        }
        return listMonsterIdNew;
    }
    public List<List<Long>> getMonsterIdToday() {
        return GsonUtil.strTo2ListLong(monsterIdToday);
    }

    public List<Integer> getListSkillToday() {
        return GsonUtil.strToListInt(listSkillToday);
    }
    public List<List<Long>> getMonsterLevel() {
        return GsonUtil.strTo2ListLong(monsterLevelToday);
    }

}
