package monster.game.hero;

import grep.log.Logs;
import monster.game.hero.config.ConfigHeroExpert;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.NotifyType;
import monster.controller.AHandler;
import monster.game.hero.entity.UserHeroExpertEntity;
import monster.game.hero.service.HeroExpertService;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.common.NotifyService;
import monster.service.user.Bonus;
import protocol.Pbmethod;
import protocol.Pbmethod.ResponseData;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Thông thạo tướng
 */
public class HeroExpertHandler extends AHandler {
    final String KEY_DATA = "heroExpert";

    HeroExpertService expertService = Guice.getInstance(HeroExpertService.class);

    @Override
    public void initAction(Map<Integer, <PERSON>Hand<PERSON>> mHandler) {
        List<Integer> actions = Arrays.asList(EXPERT_STATUS, EXPERT_HERO_DETAIL, EXPERT_POINT_DETAIL, EXPERT_RECEIVE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        if (!FunctionType.HERO_EXPERT.isEnable(mUser, this)) return;
        try {
            switch (actionId) {
                case EXPERT_STATUS -> status();
                case EXPERT_HERO_DETAIL -> heroDetail();
                case EXPERT_POINT_DETAIL -> pointDetail();
                case EXPERT_RECEIVE -> receive();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    // region Handle service
    void status() {
        List<UserHeroExpertEntity> aExpert = expertService.getListUserHeroExpert(mUser);
        if (aExpert != null) {
            Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
            builder.addALong(0).addALong(ConfigHeroExpert.getPointStatus(mUser));
            for (UserHeroExpertEntity expert : aExpert) {
                if (!expert.isFinish()) builder.addALong(expert.getHeroId()).addALong(expert.isNotify() ? 1 : 0);
            }
            for (UserHeroExpertEntity expert : aExpert) {
                if (expert.isFinish()) builder.addALong(expert.getHeroId()).addALong(expert.isNotify() ? 1 : 0);
            }
            addResponse(builder.build());
        } else addErrResponse();
    }

    private void pointDetail() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            List<Long> pointStatus = ConfigHeroExpert.getPoint(mUser);
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(pointStatus).addALong(0)); //.addALong(CfgHeroExpert.getPointStatus(mUser)));
        }
        {
            for (int i = 0; i < ConfigHeroExpert.config.finalPoint.size(); i++) {
                builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(ConfigHeroExpert.config.finalPoint.get(i)).addAllALong(ConfigHeroExpert.config.finalBonus.get(i)));
            }
        }
        addResponse(builder.build());
    }

    void heroDetail() {
        int heroId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        List<UserHeroExpertEntity> aExpert = expertService.getListUserHeroExpert(mUser);
        if (aExpert != null) {
            UserHeroExpertEntity heroExpert = aExpert.stream().filter(expert -> expert.getHeroId() == heroId).findFirst().orElse(null);
            if (heroExpert != null) {
                addResponse(heroExpert.toProto(mUser));
            } else addErrResponse(getLang(Lang.hero_not_found));
        } else addErrResponse();
    }

    private void receive() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int heroId = aLong.get(0).intValue();
        int index = aLong.get(1).intValue();
        if (heroId == 0) { // nhận thưởng điểm
            receivePoint();
        } else {
            List<UserHeroExpertEntity> aExpert = expertService.getListUserHeroExpert(mUser);
            if (aExpert != null) {
                UserHeroExpertEntity heroExpert = aExpert.stream().filter(expert -> expert.getHeroId() == heroId).findFirst().orElse(null);
                if (heroExpert != null) {
                    heroExpert.receive(this, index);
                } else addErrResponse(getLang(Lang.hero_not_found));
            } else addErrResponse();
        }
        Guice.getInstance(NotifyService.class).setNotify(mUser, NotifyType.HERO_EXPERT, ConfigHeroExpert.hasCacheNotify(mUser) ? 1 : 0);
    }

    private void receivePoint() {
        List<Long> pointStatus = ConfigHeroExpert.getPoint(mUser);
        int index = pointStatus.get(1).intValue();
        if (ConfigHeroExpert.config.finalPoint.get(index) < pointStatus.get(0)) {
            List<Long> aBonus = ConfigHeroExpert.config.finalBonus.get(index);
            if (ConfigHeroExpert.addPointIndex(mUser, 1)) {
                addResponse(getCommonVector(Bonus.receiveListItem(mUser, "hero_expert", aBonus)));
            }
        } else addErrResponse(Lang.hero_expert_not_enough_point);
    }
    //end region casino

    //region Logic
    private UserHeroExpertEntity getHeroExert(List<UserHeroExpertEntity> aExpert, int heroId) {

        return null;
    }

    //endregion

    //region Database
    //endregion

}
