package monster.game.hero;

import grep.log.Logs;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.main.ResSkinEntity;
import monster.protocol.CommonProto;
import monster.service.resource.ResHero;
import monster.service.resource.ResSkin;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Item and material
 */
public class HeroSkinHandler extends AHandler {

    @Override
    public AHandler newInstance() {
        return new HeroSkinHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(HERO_SKIN_EQUIP);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case HERO_SKIN_EQUIP:
                    skinChoose();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void skinChoose() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        long heroId = aLong.get(0);
        int skinId = Math.toIntExact(aLong.get(1));

        UserHeroEntity uHero = mUser.getResources().getMHero().get(heroId);
        if (uHero == null) {
            addErrResponse(getLang(Lang.hero_not_own));
            return;
        }
        if (skinId == 0) {
            if (uHero.updateSkin(skinId)) {
                addResponse(getCommonVector(uHero.getId(), (long) skinId));
                uHero.calculatePointHero(mUser);
            } else {
                addErrResponse();
            }
        } else {
            ResSkinEntity rSkin = ResSkin.mSkin.get(skinId);
            List<Integer> skinHero = ResHero.getHero(uHero.getHeroId()).getSkins();
            if (rSkin == null || !skinHero.contains(skinId)) {
                addErrResponse();
                return;
            }
            if (uHero.getSkinUse() == skinId) {
                addErrResponse(getLang(Lang.skin_in_use));
                return;
            }
            boolean hasSkin = mUser.getResources().mHeroSkin.get(skinId) != null;
            if (!hasSkin) {
                addErrResponse(getLang(Lang.skin_not_own));
                return;
            }
            if (uHero.updateSkin(skinId)) {
                addResponse(getCommonVector(uHero.getId(), (long) skinId));
                uHero.calculatePointHero(mUser);
            } else addErrResponse();
        }
    }


}
