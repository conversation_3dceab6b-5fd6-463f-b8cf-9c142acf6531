package monster.game.hero.config;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.log.Logs;
import monster.dao.mapping.UserHeroEntity;
import monster.game.hero.entity.ResBreathEntity;
import monster.game.user.service.UserService;
import monster.object.MyUser;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConfigBreath {
    public static DataConfig config;
    public static Map<Integer, ResBreathEntity> mResBreath = new HashMap<>();
    public static int minLevelActiveSkill = 0, minLevelPassiveSkill = 0;

    public static boolean hasNotify(MyUser mUser) {
        try {
            var userBreath = Guice.getInstance(UserService.class).getUserBreath(mUser.getUser().getId());
            long curPoint = (long) ConfigBreath.getCurrentPoint(mUser)[0];
            long pointUpLevel = ConfigBreath.getPointUpLevel(userBreath.getLevel());
            return !ConfigBreath.isMaxLevel(userBreath.getLevel()) && curPoint >= pointUpLevel;
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return false;
    }

    public static int getLevelPassiveSkillId(int skillId) {
        for (JsonElement jsonElement : config.levelSkillPassive) {
            JsonObject obj = jsonElement.getAsJsonObject();
            if (GsonUtil.strToListInt(obj.get("skillID").getAsJsonArray().toString()).contains(skillId)) {
                return obj.get("level").getAsInt();
            }
        }
        return -1;
    }

    public static int getLevelActiveSkillId(int skillId) {
        for (JsonElement jsonElement : config.levelSkillActive) {
            JsonObject obj = jsonElement.getAsJsonObject();
            if (GsonUtil.strToListInt(obj.get("skillID").getAsJsonArray().toString()).contains(skillId)) {
                return obj.get("level").getAsInt();
            }
        }
        return -1;
    }


    public static boolean isMaxLevel(int level) {
        return level >= config.maxLevel || config.levelExp.size() <= level;
    }

    public static int getPointUpLevel(int level) {
        return isMaxLevel(level) ? -1 : config.levelExp.get(level);
    }

    public static Object[] getCurrentPoint(MyUser mUser) {
        Map<Integer, UserHeroEntity> mHero = new HashMap<>();
        List<UserHeroEntity> heroes = mUser.getResources().getHeroes();
        for (UserHeroEntity hero : heroes) {
            if (hero.getStar() >= 6 && mResBreath.containsKey(hero.getHeroId())) {
                UserHeroEntity cacheHero = mHero.getOrDefault(hero.getHeroId(), new UserHeroEntity());
                if (cacheHero.getStar() < hero.getStar()) mHero.put(hero.getHeroId(), hero);
            }
        }
        long totalPoint = 0;
        for (Integer heroId : mHero.keySet()) {
            int star = mHero.get(heroId).getStar();
            int point = 0;
            List<Integer> points = mResBreath.get(heroId).getListPoint();
            for (int index = 6; index <= star; index++) {
                if (points.size() <= index - 6) continue;
                point += points.get(index - 6);
            }
            mHero.get(heroId).setBreathPoint(point);
            totalPoint += point;
        }
        return new Object[]{totalPoint, mHero};
    }

    public static Pbmethod.ListCommonVector getProtoBreathPointInfo(int curLevel) {
        List<JsonArray> points = List.of(config.statsMage, config.statsPirest, config.statsRanger, config.statsAssasin, config.statsWarrior);
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (JsonArray point : points) {
            List<Long> curPoint = curLevel == 0
                    ? ListUtil.ofLong(0, 0, 2, 0)
                    : GsonUtil.strToListLong(point.get(curLevel - 1).getAsJsonObject().get("point").getAsJsonArray().toString());
            List<Long> nextPoint = curLevel >= point.size()
                    ? ListUtil.ofLong(-1, -1, -1, -1)
                    : GsonUtil.strToListLong(point.get(curLevel).getAsJsonObject().get("point").getAsJsonArray().toString());
            builder.addAVector(CommonProto.getCommonVectorProto(curPoint));
            builder.addAVector(CommonProto.getCommonVectorProto(nextPoint));
        }
        return builder.build();
    }

    /**
     * {
     * "levelExp": [0, 80, 100, 120, 140, 160, 180, 200, 220, 240, 576, 685, 766, 847, 927, 1008, 1089, 1169, 1250, 1331, 1411, 1690, 1901, 2112, 2323, 2534, 2746, 2957, 3168, 3379, 4308, 4769, 5034, 5299, 5564, 5829, 6094, 6359, 6624, 6889, 8346, 9032, 9354, 9677, 9999, 10322, 10644, 10967, 11290, 11612, 14008, 15360, 16128, 16896, 17664, 18432, 19200, 19968, 21565, 22364, 26058, 27406, 28764, 30102, 31450, 32797, 35459, 36858, 38258, 39658, 45619, 47693, 49766, 53760, 55910, 58061, 60211, 62362, 65412, 66662],
     * "maxLevel": 300,
     * "statsMage": [{"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}],
     * "statsPirest": [{"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}],
     * "statsRanger": [{"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}],
     * "statsAssasin": [{"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, ],
     * "statsWarrior": [{"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}, {"point": [102, 100, 12, 100]}],
     * "levelSkillActive": [{"level":50,"skillID":[100001,100002,100003]}],
     * "levelSkillPassive": [{"level":50,"skillID":[100001,100002,100003]}]
     * }
     *
     * @param value
     */
    public static void init(String value) {
        mResBreath = new HashMap<>();
        List<ResBreathEntity> breaths = DBJPA.getList("dson_main.res_breath", ResBreathEntity.class);
        breaths.forEach(breath -> mResBreath.put(breath.heroId, breath));

        minLevelPassiveSkill = config.levelSkillPassive.get(0).getAsJsonObject().get("level").getAsInt();
        minLevelActiveSkill = config.levelSkillActive.get(0).getAsJsonObject().get("level").getAsInt();
    }

    public class DataConfig {
        public List<Integer> levelExp;
        public int maxLevel;
        public JsonArray statsMage, statsPirest, statsAssasin, statsWarrior, statsRanger;
        public JsonArray levelSkillActive, levelSkillPassive;
    }

}
