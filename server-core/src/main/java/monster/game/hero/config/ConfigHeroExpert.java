package monster.game.hero.config;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.EmojText;
import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.ExpertQuestType;
import monster.config.penum.NotifyType;
import monster.dao.mapping.UserDataEntity;
import monster.dao.mapping.UserMailEntity;
import monster.dao.mapping.UserPetEntity;
import monster.dao.mapping.main.ResAbyssHeroTypeEntity;
import monster.game.hero.entity.*;
import monster.game.hero.service.HeroExpertService;
import monster.object.BattleTeam;
import monster.object.ExpertAnalysis;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.battle.dependence.entity.SimulateHero;
import monster.service.battle.dependence.entity.SimulateResult;
import monster.service.common.NotifyService;
import monster.service.monitor.Telegram;
import monster.service.resource.ResHero;
import monster.task.dbcache.MailCreatorCache;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings({"rawtypes", "unchecked"})
public class ConfigHeroExpert {
    public static DataConfig config;
    public static Map<Integer, ResExpertQuestEntity> mExpertQuest = new HashMap<>();
    public static Map<Integer, ResExpertEntity> mExpert = new HashMap<>();
    public static List<ResExpertEntity> aExpert;
    //
    public static Map<Integer, ResHeroExpertType> mExpertType = new HashMap<>();
    public static Map<Integer, ResHeroExpert> mHeroExpert = new HashMap<>();
    public static List<ResHeroExpertTemplate> expertTemplates;
    public static int maxRound = 50;
    /**
     * Số lần chiến thắng celestial
     *
     * @param mUser
     * @param heroId
     */
    public static void checkUpdate(ExpertQuestType questType, MyUser mUser, int heroId, int value) {
        if (mUser.getUser().getServer() < 4000) {
            try {
                int hero5Star = ResHero.getHero(heroId).getHero5Star();
                List<UserHeroExpertEntity> aHeroExpert = Guice.getInstance(HeroExpertService.class).getListUserHeroExpert(mUser);
                if (aHeroExpert != null) {
                    UserHeroExpertEntity heroExpert = aHeroExpert.stream().filter(expert -> expert.getHeroId() == hero5Star).findFirst().orElse(null);
                    if (heroExpert != null) {
                        heroExpert.checkUpdate(questType, value);
                        Guice.getInstance(HeroExpertService.class).checkNotify(mUser);
                    }
                }
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
    }

    /**
     * đạt cấp X ở hero Y
     *
     * @param mUser
     * @param heroId
     */
    public static void checkUpdateLevel(MyUser mUser, int heroId, int heroLevel) {
        if (mUser.getUser().getServer() < 4000) {
            try {
                int hero5Star = ResHero.getHero(heroId).getHero5Star();
                List<UserHeroExpertEntity> aHeroExpert = Guice.getInstance(HeroExpertService.class).getListUserHeroExpert(mUser);
                if (aHeroExpert != null) {
                    UserHeroExpertEntity heroExpert = aHeroExpert.stream().filter(expert -> expert.getHeroId() == hero5Star).findFirst().orElse(null);
                    if (heroExpert != null) {
                        heroExpert.checkUpdate(ExpertQuestType.HERO_LEVEL, heroLevel);
                        Guice.getInstance(HeroExpertService.class).checkNotify(mUser);
                    }
                }
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
    }

    /**
     * Update theo battle trực tiếp
     *
     * @param mUser
     * @param battleResult
     */
    public static void checkUpdate(MyUser mUser, BattleResultEntityNew battleResult) {
        //        try {
        //            List<UserHeroExpertEntity> aHeroExpert = getListUserExpert(mUser);
        //            if (aHeroExpert != null) {
        //                for (AMode mode : battleResult.aMode) {
        //                    for (HeroBattleEntity heroEntity : mode.getAtkTeam()) {
        //                        UserHeroExpertEntity heroExpert = aHeroExpert.stream().filter(expert -> expert.getHeroId() == heroEntity.heroId).findFirst().orElse(null);
        //                        if (heroExpert != null) {
        //                            heroExpert.checkUpdateArena(battleResult.isWin(), heroEntity.numberKill, heroEntity.logDamage);
        //                        }
        //                    }
        //                }
        //            }
        //        } catch (Exception ex) {
        //            Logs.error(ex);
        //        }
    }

    public static JsonArray getQuestId(int heroId, int questGroup) {
        //        if (mExpert.get(heroId) == null) System.out.println("-----------" + heroId + "---------");
        return GsonUtil.parseJsonArray(mExpert.get(heroId).getQuestData()).get(questGroup).getAsJsonArray();
    }


    public static boolean addPointIndex(MyUser mUser, int addPoint) {
        return true;
    }


    /**
     * pointStatus[0] -> point
     * pointStatus[1] -> mốc reward
     *
     * @param pointStatus
     */
    private static void checkPointReward(UserDataEntity uData, List<Long> pointStatus) {
        int point = pointStatus.get(0).intValue(), rewardIndex = pointStatus.get(1).intValue();
        List<Long> finalPoint = ConfigHeroExpert.config.finalPoint;
        List<List<Long>> finalBonus = ConfigHeroExpert.config.finalBonus;

        List<UserMailEntity> aMail = new ArrayList<>();
        while (rewardIndex < finalPoint.size() && point >= finalPoint.get(rewardIndex)) {
            List<Long> bonus = finalBonus.get(rewardIndex);
            pointStatus.set(1, (long) rewardIndex + 1);
            if (uData.update("hero_expert", pointStatus.toString())) {
                uData.setHeroExpert(pointStatus.toString());

                //                sqls.add(DBHelper.sqlMail(uData.getUserId(), String.format(Lang.getTitle("title_hero_expert"), finalPoint.get(rewardIndex)), "", bonus.toString()));
                aMail.add(UserMailEntity.builder().userId(uData.getUserId()).title(String.format(Lang.getTitle("title_hero_expert"), finalPoint.get(rewardIndex))).bonus(bonus.toString()).build());
            }
            rewardIndex++;
        }
        //        DBJPA.rawSQL(sqls);
        MailCreatorCache.sendMail(aMail);
    }

    public static List<Long> getQuestInfo(int heroKey, int group) {
        return GsonUtil.strTo2ListLong(mHeroExpert.get(heroKey).getQuestData()).get(group);
    }

    public static void genHeroQuest() {
        boolean needGenQuest = mHeroExpert.values().stream().filter(v -> v.getQuestData() == null || v.getQuestData().length() < 10).findFirst().orElse(null) != null;
        if (needGenQuest) {
            List<ResAbyssHeroTypeEntity> abyssTypes = DBJPA.getList(CfgServer.DB_MAIN + "res_abyss_hero_type", ResAbyssHeroTypeEntity.class);
            Map<Integer, ResAbyssHeroTypeEntity> mAbyssType = abyssTypes.stream().collect(Collectors.toMap(ResAbyssHeroTypeEntity::getHeroKey, e -> e));
            for (ResHeroExpert resHeroExpert : mHeroExpert.values()) {
                if (resHeroExpert.getQuestData() == null || resHeroExpert.getQuestData().length() < 10) { // chưa gen quest
                    List<List<Long>> values = new ArrayList<>(); // mỗi phần tử 1 vòng
                    for (ResHeroExpertTemplate expertTemplate : expertTemplates) {
                        List<Long> genType = new ArrayList<>();
                        List<Integer> usedType = new ArrayList<>();
                        List<List<Long>> tempInfos = GsonUtil.strTo2ListLong(expertTemplate.getQuestInfo());
                        for (List<Long> tempInfo : tempInfos) {
                            genType.add(tempInfo.get(0));
                            //
                            if (tempInfo.get(1).intValue() == -1) { // cần ngẫu nhiên loại nhiệm vụ
                                if (mAbyssType.containsKey(resHeroExpert.getHeroId())) {
                                    ResAbyssHeroTypeEntity abyssType = mAbyssType.get(resHeroExpert.getHeroId());
                                    var expertTypes = mExpertType.values().stream().filter(v -> v.isMatchAbyssHero(abyssType)).toList();
                                    var newType = NumberUtil.getRandomInList(expertTypes);
                                    while (usedType.contains(newType.getId())) {
                                        newType = NumberUtil.getRandomInList(expertTypes);
                                    }
                                    usedType.add(newType.getId());
                                    genType.add(newType.getId().longValue());
                                    genType.add((long) (newType.getBaseValue() * expertTemplate.getScale()));
                                } else break;
                            } else genType.add(tempInfo.get(1));
                            //
                            if (tempInfo.get(2).intValue() != -1) genType.add(tempInfo.get(2));
                        }
                        if (genType.size() == 9) {
                            values.add(genType);
                        }
                    }
                    if (values.size() == 30) {
                        resHeroExpert.setQuestData(StringHelper.toDBString(values));
                        DBJPA.update(resHeroExpert);
                        Telegram.sendNotify(EmojText.FLOWER.get("Generate thông thạo tướng id = " + resHeroExpert.getHeroId()));
                    } else {
                        Telegram.sendNotify(EmojText.RED_CIRCLE.get("Generate thông thạo tướng id = " + resHeroExpert.getHeroId() + " Thất Bại"));
                    }
                }
            }
        }
    }

    public static void init(String value) {
        aExpert = DBJPA.getList(CfgServer.DB_MAIN + "res_expert", ResExpertEntity.class);

        mExpert.clear();
        aExpert.forEach(expert -> mExpert.put(expert.getHeroId(), expert));

        List<ResExpertQuestEntity> aExpertQuest = DBJPA.getList(CfgServer.DB_MAIN + "res_expert_quest", ResExpertQuestEntity.class);
        mExpertQuest.clear();
        aExpertQuest.forEach(quest -> mExpertQuest.put(quest.getId(), quest));

        List<ResHeroExpertType> expertTypes = DBJPA.getList(CfgServer.DB_MAIN + "res_hero_expert_type", ResHeroExpertType.class);
        mExpertType = expertTypes.stream().collect(Collectors.toMap(ResHeroExpertType::getId, e -> e));
        List<ResHeroExpert> heroExperts = DBJPA.getList(CfgServer.DB_MAIN + "res_hero_expert", ResHeroExpert.class);
        mHeroExpert = heroExperts.stream().collect(Collectors.toMap(ResHeroExpert::getId, e -> e));
        expertTemplates = DBJPA.getList(CfgServer.DB_MAIN + "res_hero_expert_template", ResHeroExpertTemplate.class);
        expertTemplates.sort(Comparator.comparing(ResHeroExpertTemplate::getId));
        genHeroQuest();

        maxRound = expertTemplates.size();
    }

    public class DataConfig {
        public List<Long> finalPoint;
        public List<List<Long>> finalBonus;
    }

}
