package monster.game.hero;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.controller.AHandler;
import monster.dao.mapping.UserHeroEntity;
import monster.game.hero.config.ConfigBreath;
import monster.game.hero.entity.UserBreathEntity;
import monster.game.user.service.UserService;
import monster.server.config.Guice;
import monster.service.user.Actions;
import protocol.Pbmethod;

import java.util.List;
import java.util.Map;

/**
 * Hơi Thở Khởi Nguyên
 */
public class HeroBreathHandler extends AHandler {

    UserService userService = Guice.getInstance(UserService.class);
    UserBreathEntity userBreath;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List.of(HERO_BREATH_INFO, HERO_BREATH_LEVEL_UP, HERO_BREATH_CHOOSE_SKILL, HERO_BREATH_SKILL_LIST, HERO_BREATH_POINT_INFO).forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        if (!FunctionType.HERO_BREATH.isEnable(mUser, this)) return;
        checkTimeMonitor("s");
        userBreath = userService.getUserBreath(user.getId());
        if (userBreath == null) {
            addErrResponse();
            return;
        }
        try {
            switch (actionId) {
                case HERO_BREATH_INFO -> info();
                case HERO_BREATH_LEVEL_UP -> levelUp();
                case HERO_BREATH_CHOOSE_SKILL -> chooseSkill(parseCommonInput().getALongList());
                case HERO_BREATH_SKILL_LIST -> skillList();
                case HERO_BREATH_POINT_INFO -> pointInfo();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void pointInfo() {
        addResponse(ConfigBreath.getProtoBreathPointInfo(userBreath.getLevel()));
    }

    private void skillList() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (JsonArray jsonElements : List.of(ConfigBreath.config.levelSkillActive, ConfigBreath.config.levelSkillPassive)) {
            Pbmethod.CommonVector.Builder skillObj = Pbmethod.CommonVector.newBuilder();
            for (JsonElement jsonElement : jsonElements) {
                JsonObject obj = jsonElement.getAsJsonObject();
                List<Integer> values = GsonUtil.strToListInt(obj.get("skillID").getAsJsonArray().toString());
                for (Integer value : values) skillObj.addALong(value).addALong(obj.get("level").getAsInt());
            }
            builder.addAVector(skillObj);
        }
        addResponse(builder.build());
    }

    void info() {
        Object[] data = ConfigBreath.getCurrentPoint(mUser);
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(userBreath.getLevel())
                .addALong((long) data[0]).addALong(ConfigBreath.getPointUpLevel(userBreath.getLevel()))
                .addALong(userBreath.getPassiveSkill()).addALong(userBreath.getActiveSkill()));
        var mHero = (Map<Integer, UserHeroEntity>) data[1];
        Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
        mHero.forEach((k, v) -> cmm.addALong(v.getHeroId()).addALong(v.getStar()).addALong(v.getLevel()).addALong(v.getBreathPoint()));
        builder.addAVector(cmm);
        addResponse(builder.build());
    }

    void levelUp() {
        long curPoint = (long) ConfigBreath.getCurrentPoint(mUser)[0];
        long pointUpLevel = ConfigBreath.getPointUpLevel(userBreath.getLevel());
        if (ConfigBreath.isMaxLevel(userBreath.getLevel())) addErrResponse(Lang.max_level);
        else if (curPoint < pointUpLevel) addErrResponse("Không đủ điểm");
        else if (DBJPA.update("user_breath", List.of("level", userBreath.getLevel() + 1), List.of("user_id", user.getId()))) {
            userBreath.setLevel(userBreath.getLevel() + 1);
            addResponse(null);
            Actions.save(user, "breath", "level", "level", userBreath.getLevel(), "point", curPoint);
            mUser.getResources().heroes.stream().forEach(hero -> hero.calculatePointHero(mUser));
        } else addErrResponse();
    }

    void chooseSkill(List<Long> value) {
        int skillType = value.get(0).intValue();
        int skillId = value.get(1).intValue();
        int level = skillType == 1 ? ConfigBreath.getLevelActiveSkillId(skillId) : ConfigBreath.getLevelPassiveSkillId(skillId);
        if (skillId > 0 && (level == -1 || userBreath.getLevel() < level)) addErrResponse(Lang.err_not_enough_level);
        else {
            boolean isOk = skillType == 1
                    ? DBJPA.update("user_breath", List.of("active_skill", skillId), List.of("user_id", user.getId()))
                    : DBJPA.update("user_breath", List.of("passive_skill", skillId), List.of("user_id", user.getId()));
            if (isOk) {
                if (skillType == 1) userBreath.setActiveSkill(skillId);
                else {
                    userBreath.setPassiveSkill(skillId);
                    mUser.calculateHeroPoint();
                }
                addResponse(null);
            } else addErrResponse();
        }
    }

}
