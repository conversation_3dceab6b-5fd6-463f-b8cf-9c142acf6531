package monster.game.hero.service;

import monster.dao.mapping.UserHeroEntity;
import monster.object.MyUser;

import java.util.List;

public interface HeroService {
    void jobAfterUpgradeStar(MyUser mUser, UserHeroEntity userHero, int... addStar);
    void heroAwakenUnavailable(MyUser mUser, int[] countStarForAwaken, int numberHeroLevel100);
    long top6HeroPower(MyUser mUser);
    List<Long> calculateUpgradeResource(UserHeroEntity userHero, int wishStar);
    List<Long> calculateFoodHeroResource(UserHeroEntity userHero, boolean... isReUp);
    List<Long> calculateReUpResource(UserHeroEntity... userHeroes);
    List<Long> calculateDecayResource(UserHeroEntity... userHeroes);
    List<Long> calculateSacrificeResource(UserHeroEntity... userHeroes);
    List<Long> calculateAltarResource(UserHeroEntity... userHeroes);
}
