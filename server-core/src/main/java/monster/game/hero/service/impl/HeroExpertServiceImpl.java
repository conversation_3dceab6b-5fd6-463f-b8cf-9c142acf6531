package monster.game.hero.service.impl;

import com.google.inject.Inject;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.ExpertQuestType;
import monster.config.penum.FunctionType;
import monster.config.penum.NotifyType;
import monster.dao.mapping.UserDataEntity;
import monster.dao.mapping.UserMailEntity;
import monster.game.hero.config.ConfigHeroExpert;
import monster.game.hero.dao.HeroExpertDAO;
import monster.game.hero.entity.ResHeroExpert;
import monster.game.hero.entity.UserHeroExpertEntity;
import monster.game.hero.service.HeroExpertService;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.common.NotifyService;
import monster.task.dbcache.MailCreatorCache;

import java.util.ArrayList;
import java.util.List;

public class HeroExpertServiceImpl implements HeroExpertService {

    @Inject
    HeroExpertDAO expertDAO;
    @Inject
    NotifyService notifyService;

    @Override
    public List<UserHeroExpertEntity> getListUserHeroExpert(MyUser mUser) {
        List<UserHeroExpertEntity> aExpert = expertDAO.getListUserHeroExpert(mUser.getUser().getId());
        if (aExpert != null) {
            for (ResHeroExpert resExpert : ConfigHeroExpert.mHeroExpert.values()) {
                if (resExpert.isEnable()) {
                    UserHeroExpertEntity userHeroExpert = aExpert.stream().filter(expert -> expert.getHeroId() == resExpert.getHeroId())
                            .findFirst().orElse(null);
                    if (userHeroExpert == null) {
                        userHeroExpert = new UserHeroExpertEntity(mUser.getUser().getId(), resExpert);
                        if (DBJPA.save(userHeroExpert)) {
                            aExpert.add(userHeroExpert);
                        }
                    }
                }
            }
        }
        return aExpert;
    }

    /**
     * Mảng gồm 2 phần tử
     * 1: số point hiện tại của user
     * 2: mốc phần thưởng chưa nhận -> bắt đầu từ 0
     */
    @Override
    public List<Long> getPoint(MyUser mUser) {
        String pointData = mUser.getUData().getHeroExpert();
        if (pointData == null || pointData.length() < 5) {
            pointData = "[0,0]";
        }
        return GsonUtil.strToListLong(pointData);
    }

    @Override
    public void addPoint(MyUser mUser, int addPoint) {
        try {
            List<Long> aLong = getPoint(mUser);
            aLong.set(0, aLong.get(0) + addPoint);
            mUser.getUData().setHeroExpert(aLong.toString());
            mUser.getUData().update("hero_expert", aLong.toString());
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    @Override
    public int getPointStatus(MyUser mUser) {
        List<Long> pointStatus = getPoint(mUser);
        {
            checkPointReward(mUser.getUData(), pointStatus);
        }
        int index = pointStatus.get(1).intValue();
        if (index < ConfigHeroExpert.config.finalPoint.size()) {
            if (ConfigHeroExpert.config.finalPoint.get(index) < pointStatus.get(0)) return 1;
        }
        return 0;
    }

    @Override
    public void checkNotify(MyUser mUser) {
        boolean hasNotify = false;
        List<UserHeroExpertEntity> aHeroExpert = Guice.getInstance(HeroExpertService.class).getListUserHeroExpert(mUser);
        if (aHeroExpert != null) {
            for (UserHeroExpertEntity heroExpert : aHeroExpert) {
                if (heroExpert.isNotify()) {
                    hasNotify = true;
                    break;
                }
            }
        }
        if (getPointStatus(mUser) == 1) {
            hasNotify = true;
        }
        //        return false;
    }

    @Override
    public void checkUpdate(MyUser mUser, FunctionType functionType, ExpertQuestType questType, List<Integer> heroKeys, int value) {
        for (Integer heroKey : heroKeys) {
            checkUpdate(mUser, functionType, questType, heroKey, value);
        }
    }

    @Override
    public void checkUpdate(MyUser mUser, FunctionType functionType, ExpertQuestType questType, int heroKey, int value) {
        try {
            if (!FunctionType.HERO_EXPERT.isEnable(mUser)) return;

            if (ConfigHeroExpert.mHeroExpert.containsKey(heroKey)) {
                UserHeroExpertEntity heroExpert = getListUserHeroExpert(mUser).stream().filter(expert -> expert.getHeroId() == heroKey).findFirst().orElse(null);
                if (heroExpert != null && heroExpert.getQuestGroup() < ConfigHeroExpert.maxRound) {
                    List<List<Long>> quest2Data = GsonUtil.strTo2ListLong(heroExpert.getQuestData());
                    List<Long> questData = quest2Data.get(heroExpert.getQuestGroup());
                    List<Long> questInfos = GsonUtil.strTo2ListLong(ConfigHeroExpert.mHeroExpert.get(heroKey).getQuestData()).get(heroExpert.getQuestGroup());
                    boolean hasUpdate = false;
                    boolean hasNotify = false;
                    for (int i = 0; i < questInfos.size(); i += 3) {
                        if (functionType.id == questInfos.get(i).intValue() && questType.id == questInfos.get(i + 1).intValue()) {
                            long requiredValue = questInfos.get(i + 2).longValue();
                            long oldValue = questData.get(i / 3).longValue();
                            if (oldValue < requiredValue) {
                                long newValue = questType.maxValue ? Math.max(oldValue, value) : oldValue + value;
                                if (newValue > oldValue) {
                                    questData.set(i / 3, newValue);
                                    hasUpdate = true;
                                    if (newValue >= requiredValue) hasNotify = true;
                                }
                            }
                            break;
                        }
                    }
                    if (hasUpdate) {
                        String saveQuestData = StringHelper.toDBString(quest2Data);
                        if (heroExpert.update("quest_data", saveQuestData, "is_notify", hasNotify ? 1 : 0)) {
                            heroExpert.setQuestData(saveQuestData);
                            if (hasNotify) notifyService.add(mUser, NotifyType.HERO_EXPERT);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    @Override
    public void checkUpdateArena(MyUser mUser, FunctionType functionType, ExpertQuestType questType, int heroKey, int value) {

    }

    void checkPointReward(UserDataEntity uData, List<Long> pointStatus) {
        int point = pointStatus.get(0).intValue(), rewardIndex = pointStatus.get(1).intValue();
        List<Long> finalPoint = ConfigHeroExpert.config.finalPoint;
        List<List<Long>> finalBonus = ConfigHeroExpert.config.finalBonus;

        List<UserMailEntity> aMail = new ArrayList<>();
        while (rewardIndex < finalPoint.size() && point >= finalPoint.get(rewardIndex)) {
            List<Long> bonus = finalBonus.get(rewardIndex);
            pointStatus.set(1, (long) rewardIndex + 1);
            if (uData.update("hero_expert", pointStatus.toString())) {
                uData.setHeroExpert(pointStatus.toString());

                //                sqls.add(DBHelper.sqlMail(uData.getUserId(), String.format(Lang.getTitle("title_hero_expert"), finalPoint.get(rewardIndex)), "", bonus.toString()));
                aMail.add(UserMailEntity.builder().userId(uData.getUserId()).title(String.format(Lang.getTitle("title_hero_expert"), finalPoint.get(rewardIndex))).bonus(bonus.toString()).build());
            }
            rewardIndex++;
        }
        //        DBJPA.rawSQL(sqls);
        MailCreatorCache.sendMail(aMail);
    }
}
