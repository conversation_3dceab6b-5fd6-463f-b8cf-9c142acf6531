package monster.game.hero.entity;

import grep.helper.GsonUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "res_hero_expert_template", catalog = "dson_main")
public class ResHeroExpertTemplate {
    @Id
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "scale")
    private Float scale;

    @Column(name = "quest_info", length = 1024)
    private String questInfo;

    @Column(name = "quest_bonus", length = 1024)
    private String questBonus;

    @Column(name = "point")
    private Integer point;

    public List<Long> getListQuestBonus(int index, int heroId) {
        var values = GsonUtil.strTo2ListLong(questBonus).get(index);
        for (int i = 0; i < values.size(); i++) {
            if (values.get(i) == -1) {
                values.set(i, (long) heroId);
            }
        }
        return values;
    }

}