package monster.game.hero.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "res_breath", catalog = "dson_main")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ResBreathEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public int id;
    public int heroId;
    public int star6, star7, star8, star9, star10, star11, star12, star13, star14, star15;

    public List<Integer> getListPoint() {
        return List.of(star6, star7, star8, star9, star10, star11, star12, star13, star14, star15);
    }
}
