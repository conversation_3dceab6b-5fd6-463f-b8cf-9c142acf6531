package monster.game.hero.entity;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.lang.Lang;
import monster.config.penum.ExpertQuestType;
import monster.config.penum.FunctionType;
import monster.config.penum.Status;
import monster.controller.AHandler;
import monster.game.hero.config.ConfigHeroExpert;
import monster.game.hero.service.HeroExpertService;
import monster.object.ExpertAnalysis;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.resource.ResHero;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_hero_expert")
@Data
@NoArgsConstructor
public class UserHeroExpertEntity implements Serializable {
    @Id
    private int userId, heroId;
    private int questGroup;
    private String questData, questReceive;
    private boolean isNotify;

    public UserHeroExpertEntity(int userId, ResHeroExpert resHeroExpert) {
        this.userId = userId;
        this.heroId = resHeroExpert.getHeroId();
        this.questData = resHeroExpert.getDefaultData();
        this.questReceive = resHeroExpert.getDefaultData();
    }

    /**
     * update các quest liên quan tới đấu trường
     *
     * @param functionId
     * @param isWin
     * @param expertData -> log data của arena battle
     * @return true if has notify
     */
    public boolean checkUpdateArena(int functionId, boolean isWin, ExpertAnalysis.ExpertHeroData expertData) {
        try {
            List<List<Long>> arrData = GsonUtil.strTo2ListLong(questData);
            List<List<Integer>> arrReceive = GsonUtil.strTo2ListInt(questReceive);
            if (arrData.size() <= questGroup) return false; // đã hoàn thành nhiệm vụ

            List<Long> curData = arrData.get(questGroup);
            List<Integer> curReceive = arrReceive.get(questGroup);
            List<Long> questInfos = ConfigHeroExpert.getQuestInfo(heroId, questGroup);
            boolean hasUpdate = false, hasNotify = false;
            for (int i = 0; i < curReceive.size(); i++) {
                int receive = curReceive.get(i);
                long curValue = curData.get(i);
                if (functionId == questInfos.get(i * 3).intValue() && receive == 0) { // trùng tính năng + chưa nhận
                    ExpertQuestType questType = ExpertQuestType.get(questInfos.get(i * 3 + 1).intValue());
                    if (questType != null) {
                        long requiredValue = questInfos.get(i * 3 + 2);
                        if (curValue < requiredValue) {
                            long newValue = curValue + switch (questType) {
                                case WIN -> isWin ? 1 : 0;
                                case ENTER -> 1;
                                case ACTIONS_ACTIVE -> expertData.getTotalSkillAttack();
                                case ACTIONS_NORMAL -> expertData.getTotalNormalAttack();
                                case TOTAL_DMG -> expertData.getTotalDamage();
                                case HEAL -> expertData.getTotalHeal();
                                case INCOMING_DMG -> expertData.getTotalDef();
                                case COUNTER_ATTACK -> expertData.getTotalCounterAttack();
                                
                                default -> 0;
                            };
                            if (newValue == curValue) {
                                newValue = switch (questType) {
                                    case BEST_DMG -> Math.max(curValue, expertData.getMaxDamage());
                                    default -> curValue;
                                };
                            }
                            if (newValue > curValue) {
                                hasUpdate = true;
                                curData.set(i, newValue);
                                if (newValue >= requiredValue) hasNotify = true;
                            }
                        }
                    }
                    if (hasUpdate) {
                        String saveQuestData = StringHelper.toDBString(arrData);
                        if (update("quest_data", saveQuestData, "is_notify", hasNotify ? 1 : 0)) {
                            setQuestData(saveQuestData);
                            return true;
                        }
                    }
                }

                //                ResExpertQuestEntity quest = ConfigHeroExpert.mExpertQuest.get(questId.get(i).getAsInt());
                //                if (receive == 0 && quest != null) { // Chưa nhận thưởng quest
                //                    ExpertQuestType questType = ExpertQuestType.get(quest.getQuestType());
                //                    if (questType != null) {
                //                        long requiredValue = quest.getValue();
                //                        if (functionId == 3) { // đánh boss
                //                            hasUpdate = checkUpdateBossAttack(expertData, questType, curData, curValue, i);
                //                            curValue = curData.get(i).getAsLong();
                //                            if (curValue > requiredValue) hasNotify = true;
                //                        } else {
                //                            switch (questType) {
                //                                case HERO_LEVEL:
                //                                    break;
                //                                case WIN_ARENA_CRYSTAL:
                //                                    if (isWin && functionId == 1) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                case WIN_ARENA_TRIAL:
                //                                    if (isWin && functionId == 2) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                case WIN_ARENA:
                //                                    if (isWin) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                case DAMAGE_MAX_IN_ARENA:
                //                                    long maxDamage = expertData.getMaxDamage();
                //                                    if (maxDamage > curValue) {
                //                                        curData.set(i, new JsonPrimitive(maxDamage));
                //                                        hasUpdate = true;
                //                                        if (maxDamage >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                case DAMAGE_IN_ARENA_CRYSTAL: {
                //                                    long totalDamage = expertData.getTotalDamage();
                //                                    if (functionId == 1) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalDamage));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalDamage >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case DAMAGE_IN_ARENA_TRIAL: {
                //                                    long totalDamage = expertData.getTotalDamage();
                //                                    if (functionId == 2) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalDamage));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalDamage >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case DAMAGE_IN_ARENA: {
                //                                    long totalDamage = expertData.getTotalDamage();
                //                                    if (totalDamage > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalDamage));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalDamage >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case HEAL_MAX_IN_ARENA: {
                //                                    long maxHeal = expertData.getMaxHeal();
                //                                    if (maxHeal > curValue) {
                //                                        curData.set(i, new JsonPrimitive(maxHeal));
                //                                        hasUpdate = true;
                //                                        if (maxHeal >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case HEAL_IN_ARENA_CRYSTAL: {
                //                                    long totalHeal = expertData.getTotalHeal();
                //                                    if (functionId == 1) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalHeal));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalHeal >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case HEAL_IN_ARENA_TRIAL: {
                //                                    long totalHeal = expertData.getTotalHeal();
                //                                    if (functionId == 2) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalHeal));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalHeal >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case HEAL_IN_ARENA: {
                //                                    long totalHeal = expertData.getTotalHeal();
                //                                    if (totalHeal > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalHeal));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalHeal >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case DEFENSE_IN_ARENA_CRYSTAL: {
                //                                    long totalDef = expertData.getTotalDef();
                //                                    if (functionId == 1) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalDef));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalDef >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case DEFENSE_IN_ARENA_TRIAL: {
                //                                    long totalDef = expertData.getTotalDef();
                //                                    if (functionId == 2) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalDef));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalDef >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case DEFENSE_IN_ARENA: {
                //                                    long totalDef = expertData.getTotalDef();
                //                                    if (totalDef > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalDef));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalDef >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_ONE_KILL: {
                //                                    long maxKill = expertData.getMaxKill();
                //                                    if (maxKill >= 1) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_DOUBLE_KILL: {
                //                                    long maxKill = expertData.getMaxKill();
                //                                    if (maxKill >= 2) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIPLE_KILL: {
                //                                    long maxKill = expertData.getMaxKill();
                //                                    if (maxKill >= 3) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_QUADRA_KILL: {
                //                                    long maxKill = expertData.getMaxKill();
                //                                    if (maxKill >= 4) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_PNETA_KILL: {
                //                                    long maxKill = expertData.getMaxKill();
                //                                    if (maxKill >= 5) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_HEXA_KILL: {
                //                                    long maxKill = expertData.getMaxKill();
                //                                    if (maxKill >= 6) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_NORMAL_ATTACK: {
                //                                    int totalNumber = expertData.getTotalNormalAttack();
                //                                    if (totalNumber > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_ACTIVE_SKILL: {
                //                                    int totalNumber = expertData.getTotalSkillAttack();
                //                                    if (totalNumber > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_COUNTER_ATTACK: {
                //                                    int totalNumber = expertData.getTotalCounterAttack();
                //                                    if (totalNumber > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_NORMAL_ATTACK: {
                //                                    int totalNumber = expertData.getTotalNormalAttack();
                //                                    if (functionId == 2 && totalNumber > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_ACTIVE_SKILL: {
                //                                    int totalNumber = expertData.getTotalSkillAttack();
                //                                    if (functionId == 2 && totalNumber > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_COUNTER_ATTACK: {
                //                                    int totalNumber = expertData.getTotalCounterAttack();
                //                                    if (functionId == 2 && totalNumber > 0) {
                //                                        curData.set(i, new JsonPrimitive(curValue + totalNumber));
                //                                        hasUpdate = true;
                //                                        if (curValue + totalNumber >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_WIN_TOGETHER_HERO: {
                //                                    int requiredHeroId = (int) quest.getValue2();
                //                                    if (expertData.teammates.contains(requiredHeroId)) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_WIN_TOGETHER_FACTION: {
                //                                    int requiredFaction = (int) quest.getValue2();
                //                                    for (Integer teammate : expertData.teammates) {
                //                                        if (teammate > 0 && teammate != heroId) {
                //                                            ResHeroEntity resHero = ResHero.getHero(teammate);
                //                                            if (resHero != null && resHero.getHeroFaction().value == requiredFaction) {
                //                                                curData.set(i, new JsonPrimitive(curValue + 1));
                //                                                hasUpdate = true;
                //                                                if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                                break;
                //                                            }
                //                                        }
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_WIN_TOGETHER_CLASS: {
                //                                    int requiredClass = (int) quest.getValue2();
                //                                    for (Integer teammate : expertData.teammates) {
                //                                        if (teammate > 0 && teammate != heroId) {
                //                                            ResHeroEntity resHero = ResHero.getHero(teammate);
                //                                            if (resHero != null && resHero.getHeroClass().value == requiredClass) {
                //                                                curData.set(i, new JsonPrimitive(curValue + 1));
                //                                                hasUpdate = true;
                //                                                if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                                break;
                //                                            }
                //                                        }
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_WIN_TOGETHER_PET: {
                //                                    int requiredPet = (int) quest.getValue2();
                //                                    if (expertData.pets.contains(requiredPet)) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_WIN_TOGETHER_HERO: {
                //                                    int requiredHeroId = (int) quest.getValue2();
                //                                    if (functionId == 2 && expertData.teammates.contains(requiredHeroId)) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_WIN_TOGETHER_FACTION: {
                //                                    int requiredFaction = (int) quest.getValue2();
                //                                    if (functionId == 2) {
                //                                        for (Integer teammate : expertData.teammates) {
                //                                            if (teammate > 0 && teammate != heroId) {
                //                                                ResHeroEntity resHero = ResHero.getHero(teammate);
                //                                                if (resHero != null && resHero.getHeroFaction().value == requiredFaction) {
                //                                                    curData.set(i, new JsonPrimitive(curValue + 1));
                //                                                    hasUpdate = true;
                //                                                    if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                                    break;
                //                                                }
                //                                            }
                //                                        }
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_WIN_TOGETHER_CLASS: {
                //                                    int requiredClass = (int) quest.getValue2();
                //                                    if (functionId == 2) {
                //                                        for (Integer teammate : expertData.teammates) {
                //                                            if (teammate > 0 && teammate != heroId) {
                //                                                ResHeroEntity resHero = ResHero.getHero(teammate);
                //                                                if (resHero != null && resHero.getHeroClass().value == requiredClass) {
                //                                                    curData.set(i, new JsonPrimitive(curValue + 1));
                //                                                    hasUpdate = true;
                //                                                    if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                                    break;
                //                                                }
                //                                            }
                //                                        }
                //                                    }
                //                                    break;
                //                                }
                //                                case ARENA_TRIAL_WIN_TOGETHER_PET: {
                //                                    int requiredPet = (int) quest.getValue2();
                //                                    if (functionId == 2 && expertData.pets.contains(requiredPet)) {
                //                                        curData.set(i, new JsonPrimitive(curValue + 1));
                //                                        hasUpdate = true;
                //                                        if (curValue + 1 >= requiredValue) hasNotify = true;
                //                                    }
                //                                    break;
                //                                }
                //                            }
                //                        }
                //                    }
                //                }
            }
            //            if (newvalue) {
            //                arrData.set(questGroup, curData);
            //                List<Object> updateValues = new ArrayList<>();
            //                updateValues.addAll(Arrays.asList("quest_data", arrData.toString()));
            //                if (hasNotify) {
            //                    updateValues.addAll(Arrays.asList("is_notify", true));
            //                }
            //                if (DBJPA.update("user_hero_expert", updateValues, Arrays.asList("user_id", userId, "hero_id", heroId))) {
            //                    questData = arrData.toString();
            //                    setNotify(hasNotify);
            //                    //                    Logs.debug(String.format("udpate %s -> %s -> %s", userId, heroId, questData));
            //                }
            //            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(ex);
        }
        return false;
    }

    public boolean checkUpdateBossAttack(ExpertAnalysis.ExpertHeroData expertData, ExpertQuestType questType, JsonArray curData, long curValue, int index) {
        switch (questType) {
            case BOSS_SERVER_NUMBER_ATTACK:
                curData.set(index, new JsonPrimitive(curValue + expertData.number));
                return true;
            case BOSS_SERVER_DEFENSE: {
                long totalDef = expertData.getTotalDef();
                if (totalDef > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalDef));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_HEAL: {
                long totalHeal = expertData.getTotalHeal();
                if (totalHeal > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalHeal));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_DAMAGE:
                long totalDamage = expertData.getTotalDamage();
                if (totalDamage > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalDamage));
                    return true;
                }
                break;
            case BOSS_SERVER_ACTIVE_SKILL: {
                int totalNumber = expertData.getTotalSkillAttack();
                if (totalNumber > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalNumber));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_NORMAL_ATTACK: {
                int totalNumber = expertData.getTotalNormalAttack();
                if (totalNumber > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalNumber));
                    return true;
                }
                break;
            }
            case BOSS_SERVER_COUNTER_ATTACK:
                int totalNumber = expertData.getTotalCounterAttack();
                if (totalNumber > 0) {
                    curData.set(index, new JsonPrimitive(curValue + totalNumber));
                    return true;
                }
                break;
        }
        return false;
    }

    /**
     * update hero level
     *
     * @param updateQuestType -> loại quest
     * @param value           -> thông số cần update
     */
    public void checkUpdate(ExpertQuestType updateQuestType, int value) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);
        if (arrData.size() <= questGroup) return; // đã hoàn thành nhiệm vụ
        JsonArray curData = arrData.get(questGroup).getAsJsonArray();
        JsonArray curReceive = arrReceive.get(questGroup).getAsJsonArray();
        JsonArray questId = ConfigHeroExpert.getQuestId(heroId, questGroup);

        boolean hasUpdate = false, hasNotify = false;
        for (int i = 0; i < curReceive.size(); i++) {
            int receive = curReceive.get(i).getAsInt();
            long curValue = curData.get(i).getAsLong();
            ResExpertQuestEntity quest = ConfigHeroExpert.mExpertQuest.get(questId.get(i).getAsInt());
            if (receive == 0 && quest != null) { // Chưa nhận thưởng quest
                ExpertQuestType questType = ExpertQuestType.get(quest.getQuestType());
                if (questType == updateQuestType) {
                    switch (questType) {
                        case HERO_LEVEL: {
                            long requiredValue = quest.getValue();
                            if (curValue < value) {
                                curData.set(i, new JsonPrimitive(value));
                                hasUpdate = true;
                                if (value >= requiredValue) hasNotify = true;
                            }
                            break;
                        }
                        case WIN_CELESTIAL:
                        case TAVERN_4_STAR:
                        case TAVERN_5_STAR:
                        case TAVERN_6_STAR: {
                            long requiredValue = quest.getValue();
                            curData.set(i, new JsonPrimitive(curValue + value));
                            hasUpdate = true;
                            if (curValue + value >= requiredValue) hasNotify = true;
                            break;
                        }
                    }
                }
            }
        }
        if (hasUpdate) {
            arrData.set(questGroup, curData);
            List<Object> updateValues = new ArrayList<>();
            updateValues.addAll(Arrays.asList("quest_data", arrData.toString()));
            if (hasNotify) {
                updateValues.addAll(Arrays.asList("is_notify", true));
            }
            if (DBJPA.update("user_hero_expert", updateValues, Arrays.asList("user_id", userId, "hero_id", heroId))) {
                questData = arrData.toString();
                setNotify(hasNotify);
            }
        }
    }

    public void receive(AHandler handler, int index) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);

        JsonArray aData = arrData.get(questGroup).getAsJsonArray();
        JsonArray aReceive = arrReceive.get(questGroup).getAsJsonArray();
        List<Long> questInfos = ConfigHeroExpert.getQuestInfo(heroId, questGroup);
        try {
            ResHeroExpertTemplate template = ConfigHeroExpert.expertTemplates.get(questGroup);
            long number = aData.get(index).getAsLong();
            long maxNumber = questInfos.get(index * 3 + 2);
            int receive = aReceive.get(index).getAsInt();
            if (number >= maxNumber) {
                if (receive == 1) {
                    handler.addErrResponse(handler.getLang(Lang.bonus_already_received));
                } else {
                    aReceive.set(index, new JsonPrimitive(1));
                    arrReceive.set(questGroup, aReceive);
                    if (DBJPA.update("user_hero_expert", Arrays.asList("quest_receive", arrReceive.toString(), "is_notify", 0), Arrays.asList("user_id", userId, "hero_id", heroId))) {
                        setNotify(false);
                        questReceive = arrReceive.toString();
                        handler.addResponse(handler.getCommonVector(Bonus.receiveListItem(handler.getMUser(), "hero_expert",
                                template.getListQuestBonus(index, heroId))));
                        Actions.save(handler.getUser(), "hero_expert", "receive", "heroKey", heroId, "group", questGroup, "index", index);
                        Guice.getInstance(HeroExpertService.class).addPoint(handler.getMUser(), template.getPoint());
                        checkUpdateNotify(handler.getMUser());
                    } else handler.addErrResponse();
                }
            } else {
                handler.addErrResponse(handler.getLang(Lang.quest_incomplete));
            }
        } catch (Exception ex) {
            Logs.error(ex);
            handler.addErrResponse();
        }
    }

    /**
     * Sau khi nhận thưởng, kiểm tra lại
     * - Còn notify nữa không
     * - đang sang group mới chưa
     */
    private void checkUpdateNotify(MyUser mUser) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);

        JsonArray aData = arrData.get(questGroup).getAsJsonArray();
        JsonArray aReceive = arrReceive.get(questGroup).getAsJsonArray();
        List<Long> questInfos = ConfigHeroExpert.getQuestInfo(heroId, questGroup);

        boolean hasNotify = false;
        boolean nextGroup = true;
        for (int i = 0; i < aData.size(); i++) {
            long number = aData.get(i).getAsLong();
            int receive = aReceive.get(i).getAsInt();
            int questId = questInfos.get(i * 3 + 1).intValue();

            if (receive == 0) {
                nextGroup = false;
                long maxNumber = questInfos.get(i + 3 + 2);
                if (number >= maxNumber) {
                    hasNotify = true;
                    break;
                }
            }
        }
        List<Object> updateValues = new ArrayList<>();
        updateValues.addAll(Arrays.asList("is_notify", hasNotify ? 1 : 0));
        if (nextGroup) {
            updateValues.addAll(Arrays.asList("quest_group", questGroup + 1));
        }
        if (DBJPA.update("user_hero_expert", updateValues, Arrays.asList("user_id", userId, "hero_id", heroId))) {
            isNotify = hasNotify;
            if (nextGroup) {
                questGroup += 1;

                //                try {
                //                    ResHeroEntity resHero = ResHero.getHero(heroId);
                //                    int maxLevel = mUser.getResources().heroes.stream().filter(hero -> resHero.sameHero(hero.getHeroId()))
                //                            .mapToInt(UserHeroEntity::getLevel).max().orElse(0);
                //                    if (maxLevel > 0) ConfigHeroExpert.checkUpdateLevel(mUser, heroId, maxLevel);
                //                } catch (Exception ex) {
                //                    Logs.error(ex);
                //                }
            }
        }
    }

    public Pbmethod.ListCommonVector toProto(MyUser mUser) {
        JsonArray arrData = GsonUtil.parseJsonArray(questData);
        JsonArray arrReceive = GsonUtil.parseJsonArray(questReceive);

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(heroId)
                    .addALong(questGroup + 1 > arrData.size() ? arrData.size() : questGroup + 1).addALong(arrData.size())
                    .addAString(ResHero.getHero(heroId).getName()));
        }
        {
            if (arrData.size() > questGroup) {
                boolean isAllQuestDone = true;
                JsonArray aData = arrData.get(questGroup).getAsJsonArray();
                JsonArray aReceive = arrReceive.get(questGroup).getAsJsonArray();
                List<Long> questInfos = ConfigHeroExpert.getQuestInfo(heroId, questGroup);
                try {
                    ResHeroExpertTemplate template = ConfigHeroExpert.expertTemplates.get(questGroup);
                    for (int i = 0; i < aData.size(); i++) {
                        FunctionType functionType = FunctionType.get(questInfos.get(i * 3).intValue());
                        ResHeroExpertType expertType = ConfigHeroExpert.mExpertType.get(questInfos.get(i * 3 + 1).intValue());
                        ExpertQuestType questType = ExpertQuestType.get(expertType.getId());
                        long number = aData.get(i).getAsLong();
                        long maxNumber = questInfos.get(i * 3 + 2);
                        int receive = aReceive.get(i).getAsInt();

                        if (functionType == FunctionType.HERO_UPGRADE_STAR) {
                            int curStar = mUser.getResources().heroes.stream().filter(v -> v.getHeroId() == heroId)
                                    .map(v -> v.getStar()).max(Integer::compareTo).orElse(0);
                            if (number < curStar) {
                                aData.set(i, new JsonPrimitive(curStar));
                                arrData.set(questGroup, aData);
                                if (DBJPA.update("user_hero_expert", Arrays.asList("quest_data", arrData.toString()), Arrays.asList("user_id", userId, "hero_id", heroId))) {
                                    questData = arrData.toString();
                                    number = curStar;
                                }
                            }
                        }

                        Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
                        cmm.addALong(i).addALong(functionType == null ? 0 : functionType.clientToGo).addALong(getStatus(receive, number, maxNumber));
                        if (functionType == FunctionType.HERO_UPGRADE_STAR) {
                            cmm.addALong(Math.min(number, maxNumber) >= maxNumber ? 1 : 0).addALong(1);
                        } else {
                            cmm.addALong(Math.min(number, maxNumber)).addALong(maxNumber);
                        }
                        cmm.addALong(template.getPoint()).addAllALong(template.getListQuestBonus(i, heroId));
                        cmm.addAString(
                                "[%s] %s".formatted(functionType == null ? "" : functionType.name, mUser.getLang().get(expertType.getDescription())
                                ).formatted(maxNumber));

                        builder.addAVector(cmm);
                        if (getStatus(receive, number, maxNumber) != Status.MISSION_RECEIVED.value) {
                            isAllQuestDone = false;
                        }
                    }
                    if (isAllQuestDone) checkUpdateNotify(mUser);
                } catch (Exception ex) {
                    //                    Logs.error(questId.toString() + " " + heroId + " " + questGroup);
                    Logs.error(ex);
                }
            } else { // finish status
                int fakeGroup = questGroup - 1;
                JsonArray aData = arrData.get(fakeGroup).getAsJsonArray();
                JsonArray aReceive = arrReceive.get(fakeGroup).getAsJsonArray();
                List<Long> questInfos = ConfigHeroExpert.getQuestInfo(heroId, fakeGroup);
                try {
                    ResHeroExpertTemplate template = ConfigHeroExpert.expertTemplates.get(questGroup);
                    for (int i = 0; i < aData.size(); i++) {
                        FunctionType functionType = FunctionType.get(questInfos.get(i * 3).intValue());
                        ResHeroExpertType expertType = ConfigHeroExpert.mExpertType.get(questInfos.get(i * 3 + 1).intValue());
                        long number = aData.get(i).getAsLong();
                        long maxNumber = questInfos.get(i + 3 + 2);
                        int receive = aReceive.get(i).getAsInt();

                        Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
                        cmm.addALong(i).addALong(Status.MISSION_RECEIVED.value);
                        cmm.addALong(number > maxNumber ? maxNumber : number).addALong(maxNumber).addALong(template.getPoint())
                                .addAllALong(GsonUtil.strTo2ListLong(template.getQuestBonus()).get(i));
                        cmm.addAString(
                                "[%s] %s".formatted(functionType == null ? "" : functionType.name, mUser.getLang().get(expertType.getDescription())
                                ).formatted(maxNumber));
                        builder.addAVector(cmm);
                    }
                } catch (Exception ex) {
                    Logs.error(ex);
                }
            }
        }
        return builder.build();
    }

    /**
     * status
     * 2 là đã nhận phần thưởng rồi
     * 1 và đủ đk nhận thưởng
     * 0 chưa đủ
     *
     * @param receive
     * @param number
     * @param maxNumber
     * @return
     */
    private int getStatus(int receive, long number, long maxNumber) {
        if (receive == 1) return 2;
        if (number >= maxNumber) return 1;
        return 0;
    }

    public boolean isFinish() {
        return questGroup >= GsonUtil.parseJsonArray(questData).size();
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_hero_expert", updateData, Arrays.asList("user_id", userId, "hero_id", heroId));
    }
}
