package monster.game.hero.entity;

import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import monster.config.CfgServer;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "res_hero_expert", catalog = "dson_main")
public class ResHeroExpert {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "hero_id")
    private Integer heroId;

    @Column(name = "quest_data", length = 1024)
    private String questData;

    int enable = 0; // có hiển thị lên sv thật không?

    public boolean isEnable() {
        if (CfgServer.isTestServer() || enable == 1) {
            return questData != null && questData.length() > 10;
        }
        return false;
    }

    public String getDefaultData(int group) {
        List<List<Long>> values = GsonUtil.strTo2ListLong(questData);
        List<List<Long>> defaultValue = new ArrayList<>();
        for (List<Long> value : values) {
            defaultValue.add(ListUtil.genListLong(value.size() / 3, 0L));
        }
        return StringHelper.toDBString(defaultValue);
    }
}