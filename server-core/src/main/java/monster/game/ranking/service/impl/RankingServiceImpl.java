package monster.game.ranking.service.impl;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.NotifyType;
import monster.config.penum.RankType;
import monster.config.penum.TopType;
import monster.dao.UserDAO;
import monster.dao.mapping.TopUserEntity;
import monster.dao.mapping.UserEntity;
import monster.game.ranking.ResRankingEntity;
import monster.game.ranking.UserRankingEntity;
import monster.game.ranking.config.ConfigServerRanking;
import monster.game.ranking.dao.RankingDAO;
import monster.game.ranking.entity.ServerRankingEntity;
import monster.game.ranking.service.RankingService;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.aop.CacheInGame;
import monster.service.common.NotifyService;
import monster.service.monitor.TopMonitor;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResRanking;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import javax.inject.Inject;
import jakarta.persistence.EntityManager;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RankingServiceImpl implements RankingService {

    @Inject
    UserDAO userDAO;
    @Inject
    RankingDAO rankingDAO;

    @CacheInGame(expire = 60 * 15)
    @Override
    public UserRankingEntity getUserRanking(MyUser mUser) {
        return userDAO.getUserRanking(mUser);
    }

    @Override
    public ServiceResult status(MyUser mUser, int rankId) {
        RankType rankType = RankType.get(rankId);
        if (rankType == null) {
            return ServiceResult.error(Lang.err_ranking_board);
        }
        TopType topType = rankType.topType;
        Pbmethod.PbListUser pbListUser = TopMonitor.getInstance().get(topType, String.valueOf(mUser.getUser().getServer()));
        List<Pbmethod.PbUser> pbUsers = pbListUser.getAUserList();
        Pbmethod.PbUser myProto = pbUsers.stream().filter(pbUser -> pbUser.getId() == mUser.getUser().getId()).findFirst().orElse(null);
        if (myProto != null) {
            int index = pbUsers.indexOf(myProto);
            return ServiceResult.success(pbListUser.toBuilder().setMyInfo(myProto).setMyRank(index + 1).build());
        } else if (!StringHelper.isEmpty(topType.sqlMyRank) && !StringHelper.isEmpty(topType.sqlMyInfo)) {
            Integer myRank = dbGetRank(String.format(topType.sqlMyRank, mUser.getUser().getServer(), mUser.getUser().getId()));
            TopUserEntity topUser = dbGetInfo(String.format(topType.sqlMyInfo, mUser.getUser().getId(), mUser.getUser().getServer()));
            if (myRank != null && topUser != null) {
                if (myRank == 0) myRank = 9999;
                return ServiceResult.success(pbListUser.toBuilder().setMyInfo(topUser.toProto()).setMyRank(myRank).build());
            } else {
                myRank = 9999;
                return ServiceResult.success(pbListUser.toBuilder().setMyInfo(TopUserEntity.toNullProto(mUser.getUser())).setMyRank(myRank).build());
            }
        }
        return ServiceResult.success(pbListUser);
    }

    @Override
    public ServiceResult info(MyUser mUser, int rankId) {
        RankType rankType = RankType.get(rankId);
        if (rankType == null) {
            return ServiceResult.error(Lang.err_ranking_board);
        }
        Pbmethod.PbListUser.Builder pbUser = Pbmethod.PbListUser.newBuilder();
        ServerRankingEntity serverRanking = getServerRank(mUser.getUser().getServer());
        if (serverRanking == null) return ServiceResult.success(pbUser.build());
        List<List<Long>> objs = serverRanking.getRankingObjById(rankType);
        if (objs == null) {
            return ServiceResult.error(Lang.err_params);
        }
        for (int i = 0; i < objs.size(); i++) { // chỉ lấy thằng đầu tiên
            if (objs.get(i).size() == 0) break;
            UserEntity user = UserOnline.getDbUser(Math.toIntExact(objs.get(i).get(0)));
            if (user == null) continue;
            Pbmethod.PbUser.Builder pb = user.toProto().toBuilder();
            pb.setPower(user.getPower());
            pb.setInfo(CommonProto.getCommonVectorProto(objs.get(i).get(1), objs.get(i).get(2)));
            pbUser.addAUser(pb.build());
        }
        return ServiceResult.success(pbUser.build());
    }

    @Override
    public ServiceResult infoDetail(MyUser mUser, List<Long> inputs) {
        int rankId = inputs.get(0).intValue();
        RankType rankType = RankType.get(rankId);
        if (rankType == null) {
            return ServiceResult.error(Lang.err_ranking_board);
        }
        int index = inputs.get(1).intValue();
        Pbmethod.PbListUser.Builder pbUser = Pbmethod.PbListUser.newBuilder();
        ServerRankingEntity serverRanking = getServerRank(mUser.getUser().getServer());
        if (serverRanking == null) return ServiceResult.success(pbUser.build());
        List<List<Long>> objs = serverRanking.getRankingObjById(rankType);
        if (objs == null) {
            return ServiceResult.error(Lang.err_params);
        }
        List<Long> dataRank = objs.get(index);
        for (int i = 0; i < dataRank.size(); i += 3) {
            UserEntity user = UserOnline.getDbUser(Math.toIntExact(dataRank.get(i)));
            if (user == null) continue;
            Pbmethod.PbUser.Builder pb = user.toProto().toBuilder();
            pb.setInfo(CommonProto.getCommonVectorProto(dataRank.get(i + 1), dataRank.get(i + 2)));
            pbUser.addAUser(pb.build());
        }
        return ServiceResult.success(pbUser.build());
    }

    @Override
    public ServiceResult state(MyUser mUser) {
        try {
            UserRankingEntity uRank = getUserRanking(mUser);
            Pbmethod.ListCommonVector.Builder lcm = Pbmethod.ListCommonVector.newBuilder();
            ServerRankingEntity serverRanking = getServerRank(mUser.getUser().getServer());
            for (int index = 0; index < RankType.values().length; index++) {
                RankType rankType = RankType.values()[index];
                List<Integer> myData = uRank.getDataById(rankType);
                if (serverRanking != null) {
                    List<List<Long>> data = serverRanking.getRankingObjById(rankType);
                    ResRankingEntity res = ConfigServerRanking.getRankingEntity(rankType.value);
                    while (data.size() < res.getSize()) data.add(new ArrayList<>());

                    for (int i = 0; i < data.size(); i++) {
                        if (myData.size() > i && myData.get(i) == 0 && data.size() > i && data.get(i).size() > 0) {
                            myData.set(i, 1);
                        }
                    }
                    lcm.addAVector(CommonProto.getCommonIntVectorProto(myData));
                }
            }
            return ServiceResult.success(lcm.build());
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }


    private Integer dbGetRank(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List listResult = session.createNativeQuery(sql).getResultList();
            return listResult.isEmpty() ? null : ((Long) listResult.get(0)).intValue();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    private TopUserEntity dbGetInfo(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<TopUserEntity> aUser = session.createNativeQuery(sql, TopUserEntity.class).getResultList();
            return aUser.isEmpty() ? null : aUser.get(0);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    @Override
    public ServiceResult receive(MyUser mUser, List<Long> inputs) {
        int id = inputs.get(0).intValue();
        int index = inputs.get(1).intValue();
        UserRankingEntity uRank = getUserRanking(mUser);
        List<Integer> listData = uRank.getDataById(RankType.get(id));
        ServerRankingEntity serverRanking = getServerRank(mUser.getUser().getServer());
        if (serverRanking == null) return ServiceResult.error(Lang.no_reward_yet);

        List<List<Long>> rankingData = serverRanking.getRankingObjById(RankType.get(id));
        if (rankingData.size() == 0) return ServiceResult.error(Lang.no_reward_yet);

        List<Integer> bonusIndex = new ArrayList<>();
        for (int i = 0; i < rankingData.size(); i++) {
            if (rankingData.get(i).size() > 0 && listData.get(i) < 2) { // check lại data - đã có thằng đạt được + chưa nhận quà
                bonusIndex.add(i);
            }
        }
        if (bonusIndex.isEmpty()) return ServiceResult.error(Lang.no_reward_yet);
        bonusIndex.forEach(value -> listData.set(value, 2));
        RankType type = RankType.get(id);
        String nameColumn = type.nameColumn;
        if (DBJPA.update("user_ranking", Arrays.asList(nameColumn, StringHelper.toDBString(listData)), Arrays.asList("user_id", mUser.getUser().getId()))) {
            switch (type) {
                case POWER -> uRank.setPower(listData.toString());
                case CAMPAIGN -> uRank.setCampaign(listData.toString());
                case ASPEN_DUNGEON -> uRank.setAspen(listData.toString());
                case TERRITORY_WATER -> uRank.setTerritoryWater(listData.toString());
                case TERRITORY_FIRE -> uRank.setTerritoryFire(listData.toString());
                case TERRITORY_WIND -> uRank.setTerritoryWind(listData.toString());
                case TERRITORY_DARK -> uRank.setTerritoryDark(listData.toString());
                case TERRITORY_LIGHT -> uRank.setTerritoryLight(listData.toString());
                case TOWER_2 -> uRank.setTower2(listData.toString());
            }
            List<Long> bonus = new ArrayList<>();
            bonusIndex.forEach(value -> {
                ResRankingEntity resRankingEntity = ResRanking.mRank.get(id);
                if (resRankingEntity.getSize() > value)
                    bonus.addAll(ResRanking.mRank.get(id).getBonus(value));
            });

            Actions.save(mUser.getUser(), "ranking", "receive", "id", id, "index", StringHelper.toDBString(bonusIndex));
            if (!ResRanking.isNotify(mUser, uRank)) { // nhận rồi thì cần xóa
                Guice.getInstance(NotifyService.class).sendNotify(mUser, NotifyType.HOME_TOP_RANK, false);
            }
            return ServiceResult.success(CommonProto.getCommonVectorProto(Bonus.receiveListItem(mUser, "receive_ranking_bonus", bonus)));
        } else {
            return ServiceResult.error(Lang.err_system_down);
        }
    }

    @Override
    @CacheInGame(expire = 5)
    public ServerRankingEntity getServerRank(int serverId) {
        var rank = rankingDAO.getServerRanking(serverId);
        if (rank == null) {
            rank = new ServerRankingEntity(serverId);
            if (!rankingDAO.save(rank)) return null;
        }
        return rank;
    }
}
