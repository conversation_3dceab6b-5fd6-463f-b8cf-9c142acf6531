package monster.game.ranking.service.impl;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.NotifyType;
import monster.config.penum.RankType;
import monster.config.penum.TopType;
import monster.dao.UserDAO;
import monster.dao.mapping.TopUserEntity;
import monster.game.ranking.MiniUserEntity;
import monster.game.ranking.entity.ServerRankingEntity;
import monster.game.ranking.UserRankingEntity;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.aop.CacheInGame;
import monster.service.common.NotifyService;
import monster.game.ranking.service.RankingService;
import monster.service.monitor.TopMonitor;
import monster.service.resource.ResRanking;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

public class RankingServiceImpl implements RankingService {

    @Inject
    UserDAO userDAO;

    @CacheInGame(expire = 60 * 15)
    @Override
    public UserRankingEntity getUserRanking(MyUser mUser) {
        return userDAO.getUserRanking(mUser);
    }

    @Override
    public ServiceResult status(MyUser mUser, int type) {
        TopType topType = TopType.get(type);
        if (topType == null) {
            return ServiceResult.error(Lang.err_ranking_board);
        }
        Pbmethod.PbListUser pbListUser = TopMonitor.getInstance().get(topType, String.valueOf(mUser.getUser().getServer()));
        List<Pbmethod.PbUser> pbUsers = pbListUser.getAUserList();
        Pbmethod.PbUser myProto = pbUsers.stream().filter(pbUser -> pbUser.getId() == mUser.getUser().getId()).findFirst().orElse(null);
        if (myProto != null) {
            int index = pbUsers.indexOf(myProto);
            return ServiceResult.success(pbListUser.toBuilder().setMyInfo(myProto).setMyRank(index + 1).build());
        } else if (!StringHelper.isEmpty(topType.sqlMyRank) && !StringHelper.isEmpty(topType.sqlMyInfo)) {
            Integer myRank = dbGetRank(String.format(topType.sqlMyRank, String.valueOf(mUser.getUser().getServer()), mUser.getUser().getId()));
            TopUserEntity topUser = dbGetInfo(String.format(topType.sqlMyInfo, mUser.getUser().getId(), String.valueOf(mUser.getUser().getServer())));
            if (myRank != null && topUser != null) {
                if (myRank == 0) myRank = 9999;
                return ServiceResult.success(pbListUser.toBuilder().setMyInfo(topUser.toProto()).setMyRank(myRank).build());
            }
        }
        return ServiceResult.success(pbListUser);
    }

    @Override
    public ServiceResult info(MyUser mUser, int id) {
        Pbmethod.PbListUser.Builder pbUser = Pbmethod.PbListUser.newBuilder();
        ServerRankingEntity serverRanking = ResRanking.getServerRank(mUser.getUser().getServer());
        if (serverRanking == null) return ServiceResult.success(pbUser.build());
        List<List<Long>> objs = serverRanking.getRankingObjById(RankType.get(id));
        if (objs == null) {
            return ServiceResult.error(Lang.err_params);
        }
        for (int i = 0; i < objs.size(); i++) { // chỉ lấy thằng đầu tiên
            if (objs.get(i).size() == 0) break;
            MiniUserEntity user = ResRanking.getUser(Math.toIntExact(objs.get(i).get(0)));
            if (user == null) continue;
            Pbmethod.PbUser.Builder pb = user.toProto().toBuilder();
            pb.setInfo(CommonProto.getCommonVectorProto(objs.get(i).get(1), objs.get(i).get(2)));
            pbUser.addAUser(pb.build());
        }
        return ServiceResult.success(pbUser.build());
    }

    @Override
    public ServiceResult infoDetail(MyUser mUser, List<Long> inputs) {
        int id = inputs.get(0).intValue();
        int index = inputs.get(1).intValue();
        Pbmethod.PbListUser.Builder pbUser = Pbmethod.PbListUser.newBuilder();
        ServerRankingEntity serverRanking = ResRanking.getServerRank(mUser.getUser().getServer());
        if (serverRanking == null) return ServiceResult.success(pbUser.build());
        List<List<Long>> objs = serverRanking.getRankingObjById(RankType.get(id));
        if (objs == null) {
            return ServiceResult.error(Lang.err_params);
        }
        List<Long> dataRank = objs.get(index);
        for (int i = 0; i < dataRank.size(); i += 3) {
            MiniUserEntity user = ResRanking.getUser(Math.toIntExact(dataRank.get(i)));
            if (user == null) continue;
            Pbmethod.PbUser.Builder pb = user.toProto().toBuilder();
            pb.setInfo(CommonProto.getCommonVectorProto(dataRank.get(i + 1), dataRank.get(i + 2)));
            pbUser.addAUser(pb.build());
        }
        return ServiceResult.success(pbUser.build());
    }

    @Override
    public ServiceResult state(MyUser mUser) {
        try {
            UserRankingEntity uRank = getUserRanking(mUser);
            Pbmethod.ListCommonVector.Builder lcm = Pbmethod.ListCommonVector.newBuilder();
            ServerRankingEntity serverRanking = ResRanking.getServerRank(mUser.getUser().getServer());
            for (int index = 0; index < RankType.values().length; index++) {
                RankType rankType = RankType.values()[index];
                List<Integer> mData = uRank.getDataById(rankType);
                if (serverRanking != null) {
                    List<List<Long>> data = serverRanking.getRankingObjById(rankType);
                    for (int i = 0; i < data.size(); i++) {
                        if (mData.get(i) == 0 && data.size() > i && data.get(i).size() > 0) {
                            mData.set(i, 1);
                        }
                    }
                    lcm.addAVector(CommonProto.getCommonIntVectorProto(mData));
                }
            }
            return ServiceResult.success(lcm.build());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }


    private Integer dbGetRank(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List listResult = session.createNativeQuery(sql).getResultList();
            return listResult.isEmpty() ? null : ((BigInteger) listResult.get(0)).intValue();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    private TopUserEntity dbGetInfo(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<TopUserEntity> aUser = session.createNativeQuery(sql, TopUserEntity.class).getResultList();
            return aUser.isEmpty() ? null : aUser.get(0);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    @Override
    public ServiceResult receive(MyUser mUser, List<Long> inputs) {
        int id = inputs.get(0).intValue();
        int index = inputs.get(1).intValue();
        UserRankingEntity uRank = getUserRanking(mUser);
        List<Integer> mData = uRank.getDataById(RankType.get(id));
        ServerRankingEntity serverRanking = ResRanking.getServerRank(mUser.getUser().getServer());
        if (serverRanking == null) {
            return ServiceResult.error(Lang.no_reward_yet);
        }
        List<List<Long>> data = serverRanking.getRankingObjById(RankType.get(id));
        if (data.size() == 0) {
            return ServiceResult.error(Lang.no_reward_yet);
        }
        int status = mData.get(index);

        if (status == 2) {
            return ServiceResult.error(Lang.err_hero_dic_received);
        }
        // check lại data - đã có thằng đạt được
        if (data.get(index).size() > 0) {
            status = 1;
        }
        if (status == 0) {
            return ServiceResult.error(Lang.no_reward_yet);
        }
        if (status != 1) {
            return ServiceResult.error(Lang.err_params);
        }
        mData.set(index, 2);
        RankType type = RankType.get(id);
        String nameColumn = type.name;
        if (DBJPA.update("user_ranking", Arrays.asList(nameColumn, StringHelper.toDBString(mData)), Arrays.asList("user_id", mUser.getUser().getId()))) {
            switch (type) {
                case POWER -> uRank.setPower(mData.toString());
                case CAMPAIGN -> uRank.setCampaign(mData.toString());
                case TOWER -> uRank.setTower(mData.toString());
                case ASPEN_DUNGEON -> uRank.setAspen(mData.toString());
                case FACTION_1 -> uRank.setFaction1(mData.toString());
                case FACTION_2 -> uRank.setFaction2(mData.toString());
                case FACTION_3 -> uRank.setFaction3(mData.toString());
                case FACTION_4 -> uRank.setFaction4(mData.toString());
                case TOWER_2 -> uRank.setTower2(mData.toString());
            }
            List<Long> bonus = ResRanking.mRank.get(id).getBonus(index);
            Actions.save(mUser.getUser(), "ranking", "receive", "id", id, "index", index);
            if (!ResRanking.isNotify(mUser, uRank)) { // nhận rồi thì cần xóa
                Guice.getInstance(NotifyService.class).sendNotify(mUser, NotifyType.HOME_TOP_RANK, false);
            }
            return ServiceResult.success(CommonProto.getCommonVectorProto(Bonus.receiveListItem(mUser, "receive_ranking_bonus", bonus)));
        } else {
            return ServiceResult.error(Lang.err_system_down);
        }
    }
}
