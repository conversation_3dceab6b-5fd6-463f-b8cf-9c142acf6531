package monster.game.ranking.entity;


import grep.helper.GsonUtil;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import monster.config.CfgHeroRanking;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@NoArgsConstructor
@Table(name = "hero_server_ranking")
public class HeroServerRankingEntity {
    @Id
    int cluster;
    int eventId;
    String data; // [user_id, number, time]
    @Transient
    @Getter
    List<Integer> userIds;

    public void init() {// Only Init
        userIds = new ArrayList<>();
        List<Long> ids = getRankingData();
        for (int i = 0; i < ids.size(); i += 3) {
            userIds.add(Math.toIntExact(ids.get(i)));
        }
    }

    public HeroServerRankingEntity(int cluster) {
        this.cluster = cluster;
        this.eventId = CfgHeroRanking.getEventId();
        this.data = "[]";
    }

    public void setData(List<Long> data) {
        this.data = GsonUtil.toJson(data);
    }

    public List<Long> getRankingData() { // [user_id, number, time]
        return GsonUtil.strToListLong(data);
    }
}
