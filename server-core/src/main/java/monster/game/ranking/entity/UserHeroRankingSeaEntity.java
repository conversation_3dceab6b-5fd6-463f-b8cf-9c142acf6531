package monster.game.ranking.entity;


import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserEntity;
import monster.service.resource.ResHeroRanking;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
@Entity
@NoArgsConstructor
@Table(name = "user_hero_ranking_sea")
public class UserHeroRankingSeaEntity implements Serializable {
    @Id
    int userId, eventId; // eventId chính là clusterId
    int server, send;
    String bonusLocal, bonusServer;
    long power, time;
    int userRank;

    public UserHeroRankingSeaEntity(UserEntity user, int eventId) {
        this.userId = user.getId();
        this.eventId = eventId;
        genNewEvent(user.getServer());
    }

    public List<Integer> getBonusServer() {
        List<Integer> data = GsonUtil.strToListInt(bonusServer);
        while (data.size() < ResHeroRanking.maxSize) data.add(0);
        return data;
    }

    private void genNewEvent(int server) {
        this.bonusLocal = NumberUtil.genListDBInt(ResHeroRanking.maxSize, 0);
        this.bonusServer = NumberUtil.genListDBInt(ResHeroRanking.maxSize, 0);
        this.server = server;
        this.power = 0;
        this.send = 0;
        this.userRank = 0;
    }


    public boolean update(List<Object> objects) {
        return DBJPA.update("user_hero_ranking_sea", objects, Arrays.asList("user_id", userId));
    }

    public boolean updatePower(long power) {
        long time = System.currentTimeMillis() / 1000;
        if (update(List.of("power", power, "time", time))) {
            this.power = power;
            this.time = time;
            return true;
        }
        return false;
    }

    public boolean updateLocalBonus(String listStatus) {
        if (update(List.of("bonus_local", listStatus))) {
            this.bonusLocal = listStatus;
            return true;
        }
        return false;
    }

    public boolean updateGlobalBonus(List<Integer> litStatus) {
        if (update(List.of("bonus_server", StringHelper.toDBString(litStatus)))) {
            this.bonusServer = litStatus.toString();
            return true;
        }
        return false;
    }


}
