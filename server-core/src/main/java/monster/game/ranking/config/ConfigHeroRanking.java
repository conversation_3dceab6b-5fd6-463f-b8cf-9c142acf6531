package monster.game.ranking.config;

import com.google.gson.Gson;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResHeroRankingEntity;
import monster.game.ranking.entity.HeroServerRankingEntity;
import monster.game.ranking.entity.UserHeroRankingEntity;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.battle.dependence.IMath;
import monster.service.common.SystemService;
import monster.service.resource.ResHero;
import monster.service.resource.ResHeroRanking;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class ConfigHeroRanking {
    public static DataConfig config;
    public static Date timeStart;
    public static Date timeEnd;
    //    private static Map<Integer, List<Integer>> mClusterServer = new HashMap<>(); // từ cluster -> server
    //    private static Map<Integer, Integer> mServerCluster = new HashMap<>(); // từ server -> cluster

    /**
     * Cơ chế chia nhóm server tự động:
     * - Mỗi cụm gồm 10 server,
     * - Cụm cuối cùng nếu lẻ thì sẽ lấy lớn hơn 10 server (<20 sv)
     */
    public static int getClusterByServer(int server) {
        if (CfgServer.isTestServer() || server < 4) return 0;
        int numberSystemServer = Guice.getInstance(SystemService.class).getNumberServer();
        int maxCluster = numberSystemServer < 10 ? 1 : numberSystemServer / 10;
        return Math.min((server - 4) / 10 + 1, maxCluster);
    }

    public static int getEventId() {
        if (CfgServer.isTestServer()) return -Math.abs(config.eventId);
        return config.eventId;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        try {
            timeStart = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(config.timeStart);
            timeEnd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(config.timeEnd);
        } catch (ParseException e) {
            System.out.println("e.getMessage() = " + e.getMessage());
        }

        // clusterId index from 1
        //        for (int i = 0; i < config.cluster.size(); i++) {
        //            //            mClusterServer.put(i + 1, config.cluster.get(i));
        //            for (int j = 0; j < config.cluster.get(i).size(); j++) {
        //                mServerCluster.put(config.cluster.get(i).get(j), i + 1);
        //            }
        //        }
        //        Logs.warn("heroRanking cluster = " + GsonUtil.toJson(mServerCluster));
    }

    public static boolean inEvent(int server) {
        List<Integer> server7Day = Guice.getInstance(SystemService.class).listServerOpen7Days();
        boolean inServer = config.server.get(0) == 0 || config.server.contains(server) || CfgServer.isTestServer();
        return (CfgServer.isTestServer() || server7Day.contains(server)) && DateTime.beforeDate(config.timeEnd) && DateTime.afterDate(config.timeStart) && inServer;
    }

    public static boolean isNotifyServer(MyUser mUser) {
        if (!(inEvent(mUser.getUser().getServer()))) return false;
        UserHeroRankingEntity uHeroRanking = Services.userService.getUserHeroRanking(mUser);
        if (uHeroRanking == null) {
            return false;
        }
        List<Integer> status = uHeroRanking.getBonusServer();
        // Rollback
        HeroServerRankingEntity serverRanking = ResHeroRanking.getHeroServerRank(uHeroRanking.getCluster()); // CfgHeroRanking.getClusterByServer(mUser.getUser().getServer())
        if (serverRanking != null) {
            List<Long> data = serverRanking.getRankingData();
            for (int i = 0; i < status.size(); i++) {
                if (status.get(i) == 1 || (status.get(i) == 0 && data.size() > (i * 3) && data.get(i * 3) != 0)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isNotifyLocal(MyUser mUser) {
        UserHeroRankingEntity uHeroRanking = Services.userService.getUserHeroRanking(mUser);
        List<Long> status = GsonUtil.strToListLong(uHeroRanking.getBonusLocal());
        for (int i = 0; i < status.size(); i++) {
            if (status.get(i) == 1) return true;
            else if (status.get(i) == 0) {
                ResHeroRankingEntity res = ResHeroRanking.mHeroRanking.get(i + 1);
                if (uHeroRanking.getPower() >= res.getPoint()) {
                    return true;
                }
            }
        }
        return false;
    }

    public class DataConfig {
        public int showInHome;
        public int heroId;
        private int eventId;
        List<Integer> server;
        public String eventName, eventIcon;
        public String timeStart, timeEnd;
        //        public List<List<Integer>> cluster;
    }
}
