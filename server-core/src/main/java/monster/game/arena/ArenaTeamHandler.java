package monster.game.arena;

import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.log.Logs;
import lombok.NoArgsConstructor;
import monster.cache.redis.JCache;
import monster.cache.redis.JCachePubSub;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.FunctionType;
import monster.config.penum.TeamType;
import monster.controller.AHandler;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.main.ResRankingReward;
import monster.game.arena.config.ConfigArenaServer;
import monster.game.arena.config.ConfigArenaTeam;
import monster.game.arena.dao.ArenaTeamDAO;
import monster.game.arena.entity.ArenaTeamState;
import monster.game.arena.entity.UserArenaThreeStar;
import monster.game.arena.entity.UserArenaThreeStarTeam;
import monster.game.arena.entity.UserArenaThreeStarTop;
import monster.game.arena.service.ArenaTeamService;
import monster.game.system.service.LogService;
import monster.object.BattleTeam;
import monster.protocol.CommonProto;
import monster.pubsub.PubSubService;
import monster.pubsub.entity.PsUserMessage;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.service.user.BonusBuilder;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import java.util.List;
import java.util.Map;

/**
 * Arena Team - Common
 */
@NoArgsConstructor
public class ArenaTeamHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List.of(ARENA_TEAM_TOP3, ARENA_TEAM_STATUS, ARENA_TEAM_INFO, ARENA_TEAM_SETTING, ARENA_TEAM_DEF_TEAM,
                ARENA_TEAM_MEMBER_LIST, ARENA_TEAM_TOP_SEASON, ARENA_TEAM_SEASON_REWARD,
                ARENA_TEAM_TOP_VINH_DANH, ARENA_TEAM_LIKE_TOP_VINH_DANH, ARENA_TEAM_LIKE_ALL_TOP).forEach(action -> mHandler.put(action, this));
    }

    ArenaTeamDAO arenaTeamDAO = Guice.getInstance(ArenaTeamDAO.class);
    ArenaTeamState arenaState = Guice.getInstance(ArenaTeamState.class);
    ArenaTeamService arenaService = Guice.getInstance(ArenaTeamService.class);
    UserArenaThreeStar userArena;
    UserArenaThreeStarTeam userArenaTeam;

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        if (!arenaState.isOpen(mUser)) {
            addErrResponse(Lang.event_ended);
            return;
        }
        if (!FunctionType.ARENA_TEAM.isEnable(mUser, this)) return;
        userArena = arenaService.getUserArena(user.getId());
        userArenaTeam = userArena.getTeamId() > 0 ? arenaService.getArenaTeamById(userArena.getTeamId()) : null;
        if (userArena == null || (userArena.getTeamId() > 0 && userArenaTeam == null)) {
            addErrResponse();
            return;
        }
        try {
            switch (actionId) {
                case ARENA_TEAM_STATUS -> status();
                case ARENA_TEAM_DEF_TEAM -> saveDefTeam();
                case ARENA_TEAM_INFO -> info(getInputInt());
                case ARENA_TEAM_MEMBER_LIST -> memberList(getInputInt());
                case ARENA_TEAM_SETTING -> setting(parseCommonInput());
                case ARENA_TEAM_SEASON_REWARD -> seasonReward();
                case ARENA_TEAM_TOP_SEASON -> top();
                case ARENA_TEAM_TOP3 -> top3();
                case ARENA_TEAM_TOP_VINH_DANH -> topVinhDanh();
                case ARENA_TEAM_LIKE_TOP_VINH_DANH -> likeTopVinhDanh(getInputInt());
                case ARENA_TEAM_LIKE_ALL_TOP -> allTopVinhDanh();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void allTopVinhDanh() {
        String KEY = "arenaTeam:like:" + user.getId();
        Integer likeUserId = JCache.getInstance().getIntValue(KEY);
        int cluster = arenaState.getCluster(user.getServer());
        List<UserArenaThreeStarTop> tops = arenaTeamDAO.getTopVinhDanh(cluster, 20);

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < tops.size(); i++) {
            var topUser = tops.get(i);
            UserEntity user = UserOnline.getDbUser(topUser.getUserId());
            if (user != null) {
                builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(topUser.getUserId()).addALong(likeUserId == null || likeUserId.intValue() != user.getId() ? 0 : 1)
                        .addALong(user.getLevel()).addALong(topUser.getNumberLike()).addALong(topUser.getNumberTop()).addALong(user.getPower())
                        .addAllALong(GsonUtil.strToListLong(user.getListAvatar().toString()))
                        .addAString(user.getName()).addAString(CfgServer.getServerName(user.getServer())));
            }
        }
        addResponse(builder.build());
    }

    void topVinhDanh() {
        int cluster = arenaState.getCluster(user.getServer());
        List<UserArenaThreeStarTop> tops = arenaTeamDAO.getTopVinhDanh(cluster, 3);

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < tops.size(); i++) {
            var topUser = tops.get(i);
            UserEntity u = UserOnline.getDbUser(topUser.getUserId());
            if (u != null) {
                String KEY = "arenaTeam:like:" + user.getId() + "_" + u.getId();
                Integer likeUserId = JCache.getInstance().getIntValue(KEY);

                builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(topUser.getUserId()).addALong(likeUserId == null ? 0 : 1)
                        .addALong(u.getLevel()).addALong(topUser.getNumberLike()).addALong(topUser.getNumberTop()).addALong(u.getPower())
                        .addAllALong(GsonUtil.strToListLong(u.getListAvatar().toString()))
                        .addAString(u.getName()).addAString(CfgServer.getServerName(u.getServer())));
            }
        }
        addResponse(builder.build());
    }

    void likeTopVinhDanh(int userId) {
        String KEY = "arenaTeam:like:" + user.getId() + "_" + userId;
        if (JCache.getInstance().exists(KEY)) {
            addErrResponse(Lang.err_arena_like_daily);
            return;
        }
        if (JCache.getInstance().setValue(KEY, String.valueOf(user.getId()), (int) DateTime.secondsUntilEndDay())) {
            arenaTeamDAO.vinhDinhByUserId(userId);
            BonusBuilder.newInstance().add(ConfigArenaTeam.likeBonus).process(mUser, "arena_team_like").writeResponse(this);
        } else addErrResponse();
    }


    void top3() {
        List<UserArenaThreeStarTeam> teams = arenaService.top3(user.getServer());
        for (int i = 0; i < teams.size(); i++) {
            teams.get(i).setRank(i + 1);
        }
        addResponse(arenaService.toProtoTeam(teams).build());
    }

    void top() {
        List<UserArenaThreeStarTeam> teams = arenaService.topSeason(user.getServer());
        for (int i = 0; i < teams.size(); i++) {
            teams.get(i).setRank(i + 1);
        }
        addResponse(arenaService.toProtoTeam(teams).build());
    }

    private void seasonReward() {
        ResRankingReward resArenaReward = ConfigArenaServer.mReward.get(ConfigArenaTeam.REWARD_TYPE);
        var ranks = resArenaReward.getListRank();
        var rewards = resArenaReward.getListReward();

        Pbmethod.ListCommonVector.Builder round = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < ranks.size(); i++) {
            var rank = ranks.get(i);
            List<Long> reward = rewards.get(i);
            round.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(reward)
                    .addAString(rank.size() == 1 ? String.valueOf(rank.get(0))
                            : "%s-%s".formatted(rank.get(0), rank.get(rank.size() - 1)))
                    .build());
        }
        addResponse(round.build());
    }

    void status() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector(arenaState.getStatus(), arenaState.getRegStatus(mUser, userArenaTeam),
                arenaState.getStatus() == 1 ? arenaState.countdownToEndPrepared() : arenaState.countdownToEndEvent(), userArena.getRemainAttack(),
                ConfigArenaTeam.feeAttack));
        if (userArenaTeam != null) {
            if (userArenaTeam.getNumberMember() == 3)
                userArenaTeam.setRank(arenaService.getRank(userArenaTeam));
            builder.addAVector(userArenaTeam.toProto());
        } else
            builder.addAVector(Pbmethod.CommonVector.newBuilder()
                    .addAllALong(ListUtil.ofLong(0, 0, 0, 0, 0, 0, 0, 0))
                    .addAString(""));
        {
            BattleTeam team = UserTeam.getBattleTeam(user.getId(), TeamType.ARENA_THREE_STAR_DEF);
            if (team == null || userArena.getPower() == 0)
                builder.addAVector(Pbmethod.CommonVector.newBuilder().build());
            else {
                Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
                for (HeroInfoEntity hero : team.getAHero()) {
                    cmm.addALong(hero == null ? 0 : hero.id);
                }
                cmm.addALong(team.getAPet()[0] == null ? 0 : team.getAPet()[0].getPetId());
                builder.addAVector(cmm);
            }
        }
        builder.addAVector(getBetInfo());
        addResponse(builder.build());
        Guice.getInstance(LogService.class).dau(mUser, "three_star", "dau");
    }

    Pbmethod.CommonVector.Builder getBetInfo() {
        int numberDayEvent = ConfigArenaTeam.getDayEvent();
        String message = switch (numberDayEvent) {
            case 0 -> "Đặt cược bắt đầu sau ";
            case 1 -> "Đặt cược kết thúc sau ";
            default -> "Đã hết thời gian đặt cược ";
        };
        long countdown = numberDayEvent == 0 || numberDayEvent == 1 ? DateTime.secondsUntilEndDay() : 0;
        return Pbmethod.CommonVector.newBuilder().addAllALong(userArena.getListBetting()).addALong(countdown).addAString(message);
    }

    void saveDefTeam() {
        List<Long> heroIds = CommonProto.parseCommonVector(requestData).getALongList();
        if (heroIds.size() != BattleConfig.TEAM_INPUT) {
            addErrResponse(Lang.err_params);
            return;
        }
        BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
        if (myTeam == null) {
            addErrResponse(Lang.err_hero_duplicate);
            return;
        }
        boolean isOk = false;
        for (HeroInfoEntity hero : myTeam.getAHero())
            if (hero != null)
                isOk = true;

        if (!isOk)
            addErrResponse(Lang.err_params);
        else {
            long power = myTeam.getPower();

            if (UserTeam.saveOrUpdateUserTeam(mUser.getUser(), TeamType.ARENA_THREE_STAR_DEF, myTeam, 0)
                    && userArena.update("power", power)) {
                userArena.setPower(power);

                // mUser.getResources().heroes.forEach(hero ->
                // hero.setLockedCrystal(heroIds.contains(hero.getId()) ? 1 : 0));
                addResponse(null);
                arenaService.updateTeamPower(userArena.getTeamId());
                JCachePubSub.getInstance().publishAllGame(PubSubService.UPDATE_USER_TEAM.getMessage(
                        PsUserMessage.builder().userId(user.getId())
                                .message(String.valueOf(TeamType.ARENA_THREE_STAR_DEF.value)).build().toString()));
                Actions.save(user, "three_star", "defteam", "power", power);
            } else
                addErrResponse();
        }
    }

    void memberList(long idTeam) {
        UserArenaThreeStarTeam team = arenaService.getArenaTeamById(idTeam);
        if (team == null) {
            addErrResponse(Lang.arena_team_not_found);
            return;
        }
        List<Integer> userIds = GsonUtil.strToListInt(team.getSortMember());
        List<UserArenaThreeStar> members = arenaTeamDAO.getTeamMember(idTeam);
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        for (Integer memberId : userIds) {
            UserArenaThreeStar member = members.stream().filter(m -> m.getUserId() == memberId).findFirst()
                    .orElse(null);
            if (member == null) {
                builder.addAUser(Pbmethod.PbUser.newBuilder().setId(0)
                        .addAllAvatar(List.of(0, 0, 0, 0))
                        .setInfo(getCommonVector(idTeam, 0)));
            } else
                builder.addAUser(arenaService.protoUser(member.getUserId(), member.getTeamId()));
        }
        addResponse(builder.build());
    }

    void info(long idTeam) {
        UserArenaThreeStarTeam team = arenaService.getArenaTeamById(idTeam);
        if (team == null) {
            addErrResponse(Lang.arena_team_not_found);
            return;
        }
        addResponse(team.toProto());
    }

    void setting(Pbmethod.CommonVector cmm) {
        int powerRequired = (int) cmm.getALong(0);
        int approveRequired = (int) cmm.getALong(1) == 0 ? 0 : 1;
        String name = cmm.getAString(0);

        if (powerRequired < 0 || powerRequired > 2_000_000_000) {
            addErrResponse(Lang.err_params);
            return;
        }

        if (userArena.getTeamId() == 0) {
            addErrResponse(Lang.arena_team_create_first);
            return;
        }

        UserArenaThreeStarTeam team = arenaService.getArenaTeamById(userArena.getTeamId());
        team.setName(name);
        team.setRequiredPower(powerRequired);
        team.setRequiredApprove(approveRequired);
        if (arenaTeamDAO.update(team)) {
            addResponse(null);
            Actions.save(user, "three_star", "setting", "id", team.getId(), "power", powerRequired, "approve", approveRequired);
        } else
            addErrResponse();
    }

}