package monster.game.arena;

import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.RedLocked;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.NoArgsConstructor;
import monster.config.lang.Lang;
import monster.config.penum.BattleMode;
import monster.config.penum.BattleType;
import monster.config.penum.EventType;
import monster.controller.AHandler;
import monster.game.arena.config.ConfigArenaTeam;
import monster.game.arena.dao.ArenaTeamDAO;
import monster.game.arena.entity.ArenaTeamState;
import monster.game.arena.entity.UserArenaThreeStar;
import monster.game.arena.entity.UserArenaThreeStarOpponent;
import monster.game.arena.entity.UserArenaThreeStarTeam;
import monster.game.arena.service.ArenaTeamService;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.common.MaterialService;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.Pbmethod;

import java.util.List;
import java.util.Map;

/**
 * Arena Team - Quản lý member
 */
@NoArgsConstructor
public class ArenaTeamBattleHandler extends AHandler {

    private static final Logger log = LoggerFactory.getLogger(ArenaTeamBattleHandler.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List.of(ARENA_TEAM_CHAT, ARENA_TEAM_CHAT_LIST, ARENA_TEAM_LIST_OPP,
                ARENA_TEAM_ATTACK, ATK_VERIFY_THREE_STAR).forEach(action -> mHandler.put(action, this));
    }

    ArenaTeamDAO arenaTeamDAO = Guice.getInstance(ArenaTeamDAO.class);
    ArenaTeamState arenaState = Guice.getInstance(ArenaTeamState.class);
    ArenaTeamService arenaService = Guice.getInstance(ArenaTeamService.class);
    UserArenaThreeStar userArena;
    UserArenaThreeStarTeam userArenaTeam;

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        if (!arenaState.isOpen(mUser)) {
            addErrResponse(Lang.event_ended);
            return;
        }

        //        if (Calendar.getInstance().get(Calendar.HOUR_OF_DAY) <= 1) {
        //            addErrResponse("Hệ thống đang trong thời gian sắp xếp đối thủ");
        //            return;
        //        }

        userArena = arenaService.getUserArena(user.getId());
        userArenaTeam = userArena.getTeamId() > 0 ? arenaService.getArenaTeamById(userArena.getTeamId()) : null;
        if (userArena == null || userArena.getTeamId() == 0 || userArenaTeam == null) {
            addErrResponse("Người chơi chưa tham gia tổ đội");
            return;
        }
        if (!arenaState.isBattleDay()) {
            addErrResponse(Lang.war_battle_end);
            return;
        }
        try {
            switch (actionId) {
                case ARENA_TEAM_CHAT -> chat(parseCommonInput());
                case ARENA_TEAM_CHAT_LIST -> chatList(parseCommonInput());
                case ARENA_TEAM_LIST_OPP -> listOpp();
                case ARENA_TEAM_ATTACK -> attack(parseCommonInput().getALongList());
                case ATK_VERIFY_THREE_STAR -> getBattleInputCache(BattleType.MODE_THREE_STAR_ARENA, true);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void attack(List<Long> values) {
        if (values.size() != 4)
            addErrResponse(Lang.err_params);
        else {
            long oppTeamId = values.get(0);
            List<Integer> userIds = GsonUtil.strToListInt(values.subList(1, values.size()).toString());
            List<Integer> sortUserIds = GsonUtil.strToListInt(userArenaTeam.getSortMember());
            if (!userIds.containsAll(sortUserIds)) {
                addErrResponse(Lang.err_params);
                return;
            }
            UserArenaThreeStarTeam oppTeam = arenaService.getArenaTeamById(oppTeamId);
            UserArenaThreeStarOpponent opponent = arenaTeamDAO.getOpponent(userArena.getTeamId(), ConfigArenaTeam.getOpponentTimeKey());
            if (opponent == null) {
                addErrResponse(Lang.arena_team_opponent_prepared);
            } else if (oppTeam == null) {
                addErrResponse(Lang.arena_team_not_found);
            } else {
                // cần sync or lock ở đây để không dùng chung dữ liệu
                RedLocked.lock("arena_team_attack_" + userArenaTeam.getId(), (languageKey -> {
                    if (StringHelper.isEmpty(languageKey)) {
                        int myIndex = userArenaTeam.getMyIndex(user.getId());
                        List<Long> oppTeamIds = opponent.getListOpponentId();
                        List<Integer> oppResults = opponent.getListOpponentResult();
                        int index = myIndex + oppTeamIds.indexOf(oppTeamId);
                        if (oppResults.get(index) > 0) {
                            addErrResponse(Lang.arena_team_already_win);
                            return;
                        }
                        int feeAttack = userArena.getNumberAttack() >= 1 ? ConfigArenaTeam.feeAttack : 0;
                        if (feeAttack > 0 && user.getGem() < feeAttack) {
                            addErrResponse(Lang.err_not_enough_gem);
                            return;
                        }
                        BattleTeam myBattleTeam = arenaService.getTeamBattleTeam(userArenaTeam, userIds);
                        BattleTeam oppBattleTeam = arenaService.getTeamBattleTeam(oppTeam, GsonUtil.strToListInt(oppTeam.getSortMember()));
                        if (myBattleTeam == null || oppBattleTeam == null) {
                            addErrResponse();
                            return;
                        }
                        // Đánh nhau thôi
                        var battleResult = BattleBuilder.builder().setTeam(myBattleTeam, oppBattleTeam).setMode(BattleType.MODE_THREE_STAR_ARENA, BattleMode.NORMAL)
                                .setInfo(mUser.getUser().getId()).battle();
                        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(UserOnline.getDbUser(userArenaTeam.getUserId()), UserOnline.getDbUser(oppTeam.getUserId()), "Đại chiến Tam Tinh");
                        int addPoint = 0;
                        if (battleResult.isWin()) {
                            addPoint = arenaService.calculateWinPoint(opponent.getMyPoint(), opponent.getOpponentPointValue(oppTeamIds.indexOf(oppTeamId)), true);
                            oppResults.set(index, 1);
                            userArenaTeam.setTimeChangePoint(System.currentTimeMillis());
                        }
                        ServiceResult<List<Long>> serviceResult = Guice.getInstance(MaterialService.class).addGem(mUser, -feeAttack, "arena_team_attack");
                        if (serviceResult.success) {
                            if (arenaTeamDAO.updateBattleResult(userArena, addPoint, opponent.getId(), StringHelper.toDBString(oppResults), userArenaTeam.getTimeChangePoint())) {
                                opponent.setOpponentResult(StringHelper.toDBString(oppResults));
                                userArenaTeam.setPoint(userArenaTeam.getPoint() + addPoint);
                                userArena.setNumberAttack(userArena.getNumberAttack() + 1);
                                addResponse(builder.build());

                                mUser.getMBattleInput().put(BattleType.MODE_THREE_STAR_ARENA, BattleInputCache.builder()
                                        .battleResult(battleResult)
                                        .output(
                                                List.of(
                                                        serviceResult.data,
                                                        ListUtil.ofLong(userArenaTeam.getPoint(), addPoint),
                                                        List.of(myBattleTeam.getPower(), oppBattleTeam == null ? 0 : oppBattleTeam.getPower()),
                                                        arenaService.getListPower(userIds),
                                                        arenaService.getListPower(GsonUtil.strToListInt(oppTeam.getSortMember()))
                                                )
                                        )
                                        .outputStr(
                                                List.of(
                                                        List.of(""),
                                                        List.of(""),
                                                        List.of(userArenaTeam.getName(), oppTeam.getName()),
                                                        arenaService.getListName(userIds),
                                                        arenaService.getListName(GsonUtil.strToListInt(oppTeam.getSortMember()))
                                                )
                                        )
                                        .build());
                                Actions.save(user, "three_star", "attack", "teamId", userArenaTeam.getId(), "oppTeamId", oppTeam.getId(), "win", battleResult.isWin() ? 1 : 0, "point", userArenaTeam.getPoint(), "addPoint", addPoint);
                                EventType.ARENA_TEAM_ATTACK.addEvent(mUser);
                                if (battleResult.isWin()) {
                                    userArenaTeam.addChat("Hệ thống: <color='#fcba03'>%s</color> thao tác đội khiêu chiến <color='#fcba03'>%s</color> dành được %s điểm".formatted(user.getName(),
                                            oppTeam.getName(), addPoint));
                                } else {
                                    userArenaTeam.addChat("Hệ thống: <color='#fcba03'>%s</color> thao tác đội khiêu chiến <color='#fcba03'>%s</color> không cẩn thận bị thua".formatted(user.getName(), oppTeam.getName()));
                                }
                            } else {
                                Guice.getInstance(MaterialService.class).addGem(mUser, feeAttack, "ateam_attack_fail");
                            }
                        } else
                            serviceResult.writeResponse(this);
                    } else addErrResponse(languageKey);
                }));
            }
        }
    }

    void chat(Pbmethod.CommonVector cmm) {
        String message = cmm.getAStringCount() > 0 ? cmm.getAString(0) : "test - no input msg";
        userArenaTeam.addChat("<color='#03e8fc'>%s</color>: %s".formatted(user.getName(), message));
        chatList(cmm);
    }

    void chatList(Pbmethod.CommonVector cmm) {
        long lastMsgId = cmm.getALongCount() > 0 ? cmm.getALong(0) : 0;
        List<String> chats = userArenaTeam.getChatList();
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        long newLastMsgId = lastMsgId;
        for (int i = chats.size() - 1; i >= 0; i--) {
            String chat = chats.get(i);
            long msgId = chat.contains("@") ? Long.parseLong(chat.substring(0, chat.indexOf("@"))) : 0;
            String realMsg = chat.contains("@") ? chat.substring(chat.indexOf("@") + 1) : "msgErr";
            if (msgId > lastMsgId) {
                newLastMsgId = msgId;
                builder.addAString(realMsg);
            }
        }
        builder.addALong(newLastMsgId);
        addResponse(builder.build());
    }

    void listOpp() {
        if (userArenaTeam != null && userArenaTeam.getNumberMember() < 3) {
            addErrResponse("Tổ đội không đủ điều kiện để tham gia");
            return;
        }
        int timeKey = ConfigArenaTeam.getOpponentTimeKey();
        UserArenaThreeStarOpponent opponent = arenaTeamDAO.getOpponent(userArenaTeam.getId(), timeKey);
        if (opponent == null) {
            addErrResponse(Lang.arena_team_opponent_prepared);
        } else {
            int myIndex = userArenaTeam.getMyIndex(user.getId());
            List<Integer> results = opponent.getListOpponentResult();
            List<Long> teamIds = GsonUtil.strToListLong(opponent.getOpponentId());
            List<UserArenaThreeStarTeam> teams = arenaTeamDAO.getListTeamById(teamIds);

            Pbmethod.BigListCommonVector.Builder builder = arenaService.toProtoTeam(teams);
            for (int index = 0; index < builder.getBigVectorBuilderList().size(); index++) {
                Pbmethod.ListCommonVector.Builder listCommonVector = builder.getBigVectorBuilder(index);
                for (int i = 0; i < teamIds.size(); i++) {
                    if (listCommonVector.getAVectorBuilder(0).getALong(0) == teamIds.get(i)) {
                        listCommonVector.getAVectorBuilder(0).addALong(results.get(myIndex + i));
                        break;
                    }
                }
            }
            addResponse(builder.build());
        }
    }
}