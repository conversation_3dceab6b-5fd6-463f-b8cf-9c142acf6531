package monster.game.arena;

import grep.helper.DateTime;
import grep.helper.ListUtil;
import grep.log.Logs;
import lombok.NoArgsConstructor;
import monster.config.CfgServer;
import monster.config.penum.EventType;
import monster.config.penum.FunctionType;
import monster.controller.AHandler;
import monster.dao.mapping.UserEntity;
import monster.game.arena.config.ConfigArena3Server;
import monster.game.arena.dao.Arena3ServerDAO;
import monster.game.arena.entity.Arena3ServerState;
import monster.game.arena.entity.UserArena3ServerBet;
import monster.game.arena.entity.UserArena3ServerFinal;
import monster.game.arena.service.Arena3ServerService;
import monster.server.config.Guice;
import protocol.Pbmethod;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * Arena Lien Server - Vòng bảng
 */
@NoArgsConstructor
public class Arena3ServerFinalHandler extends AHandler {

    Arena3ServerDAO arenaServerDAO = Guice.getInstance(Arena3ServerDAO.class);
    Arena3ServerService arenaServerService = Guice.getInstance(Arena3ServerService.class);
    Arena3ServerState arenaServerState = Guice.getInstance(Arena3ServerState.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List.of(
                ARENA3_SERVER_FINAL_STATUS, ARENA3_SERVER_FINAL_BET, ARENA3_SERVER_FINAL_BET_HISTORY, ARENA3_SERVER_FINAL_BET_REWARD
        ).forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        if (!FunctionType.ARENA3_SERVER.isEnable(mUser, this)) return;

        try {
            switch (actionId) {
                case ARENA3_SERVER_FINAL_STATUS -> status();
                case ARENA3_SERVER_FINAL_BET -> bet(getInputALong());
                case ARENA3_SERVER_FINAL_BET_HISTORY -> betHistory();
                case ARENA3_SERVER_FINAL_BET_REWARD -> betReward();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void bet(List<Long> params) {
        if (CfgServer.isTestServer() || (arenaServerState.getRoundId() == 5 && List.of(18, 19).contains(Calendar.getInstance().get(Calendar.HOUR_OF_DAY)))) {
            int chooseUserId = params.get(1).intValue();
            List<UserArena3ServerFinal> arenaFinals = arenaServerDAO.getListUserArenaFinal(arenaServerState.getEventId(user.getServer()));
            UserArena3ServerFinal arenaFinal = null;
            int betIndex = -1;
            for (int i = 0; i < arenaFinals.size(); i++) {
                if (arenaFinals.get(i).getBattleId() == 0) { // trận đấu chưa diễn ra
                    if (arenaFinals.get(i).getUserId1() == chooseUserId || arenaFinals.get(i).getUserId2() == chooseUserId) {
                        arenaFinal = arenaFinals.get(i);
                        betIndex = i + 1;
                        break;
                    }
                }
            }
            if (arenaFinal == null) {
                addErrResponse("Thông tin người chơi bạn chọn không hợp lệ");
                return;
            }
            if (arenaFinal.getBattleId() > 0) {
                addErrResponse("Trận đấu dã diễn ra");
                return;
            }
            var bets = arenaServerDAO.getListUserBet(arenaServerState.getEventId(user.getServer()), user.getId());
            for (UserArena3ServerBet bet : bets) {
                if (bet.getMatchIndex() == betIndex) {
                    addErrResponse("Bạn đã chọn người thắng cho trận đấu này rồi");
                    return;
                }
            }
            if (arenaServerDAO.betFinalRound(new UserArena3ServerBet(arenaServerState.getEventId(user.getServer()), user.getId(), betIndex, chooseUserId), arenaFinal.getId())) {
                addResponse(null);
                EventType.ARENA3_SERVER_BET.addEvent(mUser);
            } else addErrResponse();
        } else addErrResponse("Chưa tới thời gian đặt cược");
    }

    private void betReward() {
        var listBetWin = ConfigArena3Server.getBetWinReward();
        var listBeLoose = ConfigArena3Server.getBetLooseReward();
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = listBetWin.size() - 1; i >= 0; i--) {
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(listBetWin.get(i)).addALong(-1).addAllALong(listBeLoose.get(i)));
        }
        addResponse(builder.build());
    }

    /**
     * { *n
     * vLong: betIndex, userIdSelect, userIdWin
     * }
     */
    private void betHistory() {
        var bets = arenaServerDAO.getListUserBet(arenaServerState.getEventId(user.getServer()), user.getId());
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (UserArena3ServerBet bet : bets) {
            builder.addAVector(getCommonVector(bet.getMatchIndex(), bet.getWinUserId(), bet.getResult()));
        }
        addResponse(builder.build());
    }

    /**
     * {
     * vLong: countdown, state (0: chuẩn bị, 1: tự động đánh), status(0: ko được tham gia, 1: được tham gia)
     * vStr: textTime
     * }
     * {vLong: [bonus]}
     * {vLong: [heroKeys]}
     * {vLong: [pointIndex value]}
     * 3 vector * số trận
     * {vLong: battleId}
     * { user1
     * vLong: userId, serverId, level, power, win, numberBet, [avatarType, avatar, 2, frameId] (avatar trả giống trong pbUser)
     * vStr: name, clanName
     * }
     * { user2
     * vLong: userId, serverId, level, power, win, numberBet, [avatarType, avatar, 2, frameId] (avatar trả giống trong pbUser)
     * vStr: name, clanName
     * }
     */
    private void status() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();

        builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(ListUtil.convertLstIntToLong(ConfigArena3Server.getBuffHeroId())));
        builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(ListUtil.convertLstIntToLong(ConfigArena3Server.buffPoint)));

        boolean joinFinalRound = false;
        int myRank = 0;
        List<UserArena3ServerFinal> arenaFinals = arenaServerDAO.getListUserArenaFinal(arenaServerState.getEventId(user.getServer()));
        for (int i = 0; i < arenaFinals.size(); i++) {
            UserArena3ServerFinal arenaFinal = arenaFinals.get(i);
            if (!joinFinalRound && (arenaFinal.getUserId1() == user.getId() || arenaFinal.getUserId2() == user.getId())) {
                joinFinalRound = true;
                if (i < 8) myRank = 16;
                else if (i < 12) myRank = 8;
                else if (i < 14) myRank = 3;
                else if (i < 15) {
                    if (arenaFinal.getWin1() == arenaFinal.getWin2()) myRank = 2;
                    else if (arenaFinal.getWinUserId() == user.getId()) myRank = 1;
                    else myRank = 2;
                }
            }

            builder.addAVector(getCommonVector(arenaFinal.getBattleId()));
            UserEntity user1 = arenaServerService.getUserEntity(arenaFinal.getUserId1());
            builder.addAVector(Pbmethod.CommonVector.newBuilder()
                    .addALong(user1.getId()).addALong(user1.getServer()).addALong(user1.getLevel()).addALong(arenaFinal.getPower1()).addALong(arenaFinal.getWin1() >= 2 ? 1 : 0).addALong(arenaFinal.getNumberBet1())
                    .addAllALong(ListUtil.convertLstIntToLong(user1.getListAvatar()))
                    .addAString(user1.getName()).addAString(user1.getClanName()));
            UserEntity user2 = arenaServerService.getUserEntity(arenaFinal.getUserId2());
            builder.addAVector(Pbmethod.CommonVector.newBuilder()
                    .addALong(user2.getId()).addALong(user2.getServer()).addALong(user2.getLevel()).addALong(arenaFinal.getPower2()).addALong(arenaFinal.getWin2() >= 2 ? 1 : 0).addALong(arenaFinal.getNumberBet2())
                    .addAllALong(ListUtil.convertLstIntToLong(user2.getListAvatar()))
                    .addAString(user2.getName()).addAString(user2.getClanName()));
        }
        List<Object> finalInfo = arenaServerState.countdownFinal();
        Integer countdown = (Integer) finalInfo.get(0);
        String text1 = (String) finalInfo.get(1);
        String text2 = (String) finalInfo.get(2);
        int state = (Integer) finalInfo.get(3); // 0: chuẩn bị, 1: tự động đánh, 2: kết thúc

        Calendar calendar = DateTime.getCalendar(Calendar.SECOND, countdown);
        builder.addAVector(0, Pbmethod.CommonVector.newBuilder().addAllALong(ConfigArena3Server.getReward(5).getListBonus(myRank)));
        builder.addAVector(0, Pbmethod.CommonVector.newBuilder().addALong(countdown).addALong(state).addALong(joinFinalRound ? 1 : 0)
                .addAString(text2.equals("") ? "" : text2 + " " + new SimpleDateFormat("HH:ss dd/MM/yyyy").format(calendar.getTime()))
                .addAString(text1));

        addResponse(builder.build());
    }
}