package monster.game.arena.config;

import com.google.inject.Inject;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.CfgArenaCrystal;
import monster.config.CfgArenaTrial;
import monster.config.CfgServer;
import monster.config.penum.EventRewardType;
import monster.dao.ArenaDAO;
import monster.dao.EventDAO;
import monster.dao.mapping.UserArenaTrialEntity;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.game.arena.entity.ArenaState;
import monster.object.BattleTeam;
import monster.object.RankController;
import monster.service.monitor.ClanMonitor;
import monster.service.monitor.Telegram;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResBot;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.List;

public class TrialState extends ArenaState {

    @Inject
    ArenaDAO arenaDAO;
    @Inject
    EventDAO eventDAO;

    public TrialState() {
        keyCacheRank = "rank:trial";
    }

    @Override
    public void checkSeasonBonus() {
        List<Integer> serverIds = CfgServer.setServerId.stream().toList();
        for (Integer serverId : serverIds) {
            checkSeasonBonus(serverId);
        }
    }

    private boolean checkSeasonBonus(int serverId) {
        if (CfgArenaTrial.isOpen()) return true; // Đang sự kiện thì không trao thưởng
        int seasonId = CfgArenaTrial.getEventId();
        EventRewardType eventType = EventRewardType.ARENA_TRIAL_SEASON;
        int ret = eventDAO.needReward(serverId, eventType.value, String.valueOf(seasonId));
        if (ret == 1) {
            CfgArenaTrial.jobSeasonRankingDone.add(String.format("%s_%s", seasonId, serverId));
            return true;
        }

        //        // chạy khi đóng sự kiện
        //        int oldEventId = CfgArenaTrial.getEventId();
        //        EventRewardType eventType = EventRewardType.ARENA_TRIAL_SEASON;
        //
        //        int ret = arenaDAO.dbNeedReward(eventType.value, eventType.value, String.valueOf(oldEventId));
        //        if (ret == 0) return false; // error
        //        if (ret == 1) return true; // không cần trao thưởng
        //
        //        String key = String.format("r%s_%s", serverId, eventType.value);
        //        if (arenaDAO.sendRewards(new EventRewardEntity(serverId, eventType.value, String.valueOf(oldEventId)),
        //                String.format("update user_arena_trial set last_point=point, daily_rank=0, daily_receive=1, last_season_rank=0, last_season_receive=0 where " +
        //                        "event_id=%s and server_id=%s and is_team_null=0", oldEventId, serverId),
        //                String.format("SET @%s=0", key),
        //                String.format("update user_arena_trial SET last_season_rank = @%s\\:=(@%s +1) where event_id=%s and server_id=%s and is_team_null=0 ORDER BY " +
        //                        "point DESC, date_point asc limit " + CfgArenaTrial.config.maxRank, key, key, oldEventId, serverId)
        //                //                String.format("update user_arena_trial set point=1000,def_notify=0,logs='[]' where server_id=%s", serverId)
        //        )) {
        //            Logs.warn("ArenaTrial season bonus server=" + serverId);
        //            //            ResBot.removeAndAddBotTrial(serverId);
        //            return true;
        //        }
        return false;
    }

    @Override
    public void cacheTop() {
        List<Integer> serverIds = CfgServer.setServerId.stream().toList();
        for (Integer serverId : serverIds) {
            cacheTop(serverId);
        }
    }

    private void cacheTop(int serverId) {
        try {
            List<String> topUser = getRankController(serverId).getTop();
            if (topUser != null && !topUser.isEmpty()) {
                Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
                for (String tuple : topUser) {
                    Integer userId = Integer.parseInt(tuple);
                    UserArenaTrialEntity userArena = CfgArenaTrial.getArenaTrial(userId);
                    if (userArena == null) continue;

                    if (userId < 0) {
                        builder.addAUser(pbUserInfoForBot(userArena));
                        continue;
                    }

                    UserEntity user = UserOnline.getDbUser(userArena.getUserId());
                    if (user == null) continue;

                    builder.addAUser(pbUserInfo(userArena, user));
                }
                mTop.put(serverId, builder.build());
            }
        } catch (Exception ex) {
            Logs.error("cacheTop trial s" + serverId, ex);
        }
    }

    @Override
    public Pbmethod.PbListUser getTopSeason(int serverId) {
        if (!mTop.containsKey(serverId))
            cacheTop(serverId);
        return mTop.get(serverId);
    }

    @Override
    public void checkEventId() {
        if (eventId != CfgArenaTrial.getEventId()) {
            //            initRankController(CfgArenaCrystal.getEventId());
            this.eventId = CfgArenaTrial.getEventId();
        }
    }

    @Override
    public synchronized RankController getRankController(int serverId) {
        String key = getRankControllerRedisKey(eventId, serverId);
        if (!mRankController.containsKey(key)) {
            initRankController(serverId, eventId);
            if (arenaDAO.isBotTrialNotExist(eventId, serverId)) {
                ResBot.removeAndAddBotTrial(serverId);
            }
        }
        return mRankController.get(key);
    }

    private void initRankController(int serverId, int eventId) {
        String redisKey = CfgServer.isTestServer() ? String.format("test:%s:%s:%s", keyCacheRank, serverId, eventId) : String.format("%s:%s:%s", keyCacheRank, serverId, eventId);
        RankController rankController = rankController = RankController.builder().serverId(serverId).numberTop(50).key(redisKey).sql(String.format("select " +
                        "user_id, point from user_arena_trial where event_id=%s and server_id=%s order by point desc limit 0,%s", CfgArenaTrial.getEventId(), serverId,
                CfgArenaCrystal.config.maxRank)).build();
        rankController.init();
        mRankController.put(getRankControllerRedisKey(eventId, serverId), rankController);
    }

    private Pbmethod.PbUser.Builder pbUserInfo(UserArenaTrialEntity userArena, UserEntity user) {
        Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());
        return pbUser;
    }

    private Pbmethod.PbUser.Builder pbUserInfoForBot(UserArenaTrialEntity userArena) {
        Pbmethod.PbUser.Builder pbUser = Pbmethod.PbUser.newBuilder();
        pbUser.setId(userArena.getUserId());
        pbUser.setName(ResBot.getBotTrial(userArena.getResBotId()).getName());
        int monsterId = GsonUtil.strToListInt(ResBot.getBotCrystal(userArena.getResBotId()).getTeam()).get(0);
        ResMonsterEntity monster = ResMonster.getMonster(monsterId);
        if (monster == null) {
            Telegram.sendNotify(String.format("BotTrial monsterId %s is null", monsterId));
            pbUser.addAllAvatar(Arrays.asList(0, 10002, 3, -1));
        } else {
            ResHeroEntity resHero = ResHero.getHero(monster.getHeroLinkNew());
            pbUser.addAllAvatar(Arrays.asList(0, resHero.getAvatar(), 3, -1));
        }
        pbUser.setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(0).build());
        pbUser.setClanInfo(Pbmethod.CommonVector.newBuilder().addAString("").addALong(0).addALong(0).addALong(ClanMonitor.getClanAvatar(0)).build());
        pbUser.setPower(userArena.getPower());
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());
        return pbUser;
    }
}
