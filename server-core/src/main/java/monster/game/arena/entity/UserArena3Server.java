package monster.game.arena.entity;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserEntity;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.service.resource.ResHero;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_arena3_server")
@Data
@NoArgsConstructor
public class UserArena3Server implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    private int userId, eventId;
    private int serverId;
    private int userRank = 0;
    private long power;
    int skillLevel;
    String skillId;
    String roundRanking;
    Date dateCreated;

    public UserArena3Server(UserEntity user, int eventId) {
        this.userId = user.getId();
        this.eventId = eventId;
        this.serverId = user.getServer();
        this.roundRanking = "[]";
        this.skillId = "[-1,-1,-1,-1,-1]";
        this.dateCreated = new Date();
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    private boolean update(List<Object> values) {
        return DBJPA.update("user_arena3_server", values, Arrays.asList("id", id));
    }

    public List<Integer> getListSkillId() {
        if (StringHelper.isEmpty(skillId) || skillId.length() < 4) {
            skillId = "[-1,-1,-1,-1,-1]";
        }
        var values = GsonUtil.strToListInt(skillId);
        for (int i = 0; i < values.size(); i++) {
            if (values.get(i) == 0) values.set(i, -1);
        }
        return values;
    }

    public HeroInfoEntity toHeroInfo(int team) {
        HeroInfoEntity hero = new HeroInfoEntity();
        hero.id = 1; //bừa thôi, ko quan trọng
        hero.heroId = 1; //bừa thôi, ko quan trọng
        hero.team = team;
        hero.position = BattleConfig.TEAM_INPUT + 6;
        hero.passiveSkills = new ArrayList<>();
        for (int skillId : getListSkillId()) {
            SkillEntity skill = ResHero.getSkill(skillId);
            if (skill != null) hero.passiveSkills.add(skill);
        }
        return hero;
    }
}
