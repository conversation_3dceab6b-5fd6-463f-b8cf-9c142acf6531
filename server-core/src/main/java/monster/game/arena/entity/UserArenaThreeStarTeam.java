package monster.game.arena.entity;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.Getter;
import lombok.Setter;
import monster.cache.redis.JCache;
import monster.game.arena.config.ConfigArenaTeam;
import monster.server.config.Guice;
import protocol.Pbmethod;

import jakarta.persistence.*;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Entity
@Table(name = "user_arena_three_star_team", schema = "dson")
public class UserArenaThreeStarTeam {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "event_id")
    private Integer eventId;

    @Column(name = "cluster")
    private Integer cluster;
    private int userId;
    @Column(name = "name")
    private String name;

    @Column(name = "point")
    private Integer point;

    @Column(name = "required_power")
    private Integer requiredPower;

    @Column(name = "required_approve")
    private Integer requiredApprove;
    int numberMember, timeKey;
    int autoMerge, finalTeam, teamRanking, sendReward;
    long betRank1, betRank2, betRank3, betRank4;
    long timeChangePoint;
    long power;
    String sortMember;

    @Transient
    int rank;

    public UserArenaThreeStarTeam() {
        this.id = 0L;
    }

    public UserArenaThreeStarTeam(int userId, int cluster, String name, int requiredPower, int requiredApprove) {
        this.userId = userId;
        this.cluster = cluster;
        this.eventId = Guice.getInstance(ArenaTeamState.class).getEventId();
        this.name = name;
        this.requiredPower = requiredPower;
        this.requiredApprove = requiredApprove;
        this.point = ConfigArenaTeam.DEFAULT_POINT;
        this.numberMember = 1;
        this.autoMerge = -1;
        this.timeChangePoint = System.currentTimeMillis();
    }

    public int getMyIndex(int userId) {
        List<Integer> userIds = GsonUtil.strToListInt(sortMember);
        userIds.sort(Comparator.comparing(Integer::intValue));
        return userIds.indexOf(userId) * 6;
    }

    public String getKey(String action) {
        return "three_star_request_%s:%s".formatted(id, action);
    }

    public List<Integer> listUserIdRequest() {
        String value = JCache.getInstance().hget(getRequestKey(), getRequestField());
        if (StringHelper.isEmpty(value)) {
            value = "[]";
        }
        return GsonUtil.strToListInt(value);
    }

    public void addRequest(int userId) {
        String value = JCache.getInstance().hget(getRequestKey(), getRequestField());
        if (StringHelper.isEmpty(value)) {
            value = "[]";
        }
        var newValues = GsonUtil.strToListInt(value);
        newValues.remove(Integer.valueOf(userId));
        newValues.add(0, userId);
        while (newValues.size() > 15) newValues.remove(newValues.size() - 1);
        JCache.getInstance().hset(getRequestKey(), getRequestField(), StringHelper.toDBString(newValues));
    }

    public void removeRequest(int userId) {
        String value = JCache.getInstance().hget(getRequestKey(), getRequestField());
        if (StringHelper.isEmpty(value)) {
            value = "[]";
        }
        var newValues = GsonUtil.strToListInt(value);
        newValues.remove(Integer.valueOf(userId));
        while (newValues.size() > 15) newValues.remove(newValues.size() - 1);
        JCache.getInstance().hset(getRequestKey(), getRequestField(), StringHelper.toDBString(newValues));
    }

    public void addChat(String message) {
        long msgId = System.currentTimeMillis() / 1000;
        message = msgId + "@" + message;
        String k = "three_star_chat_%s".formatted(id);
        long size = JCache.getInstance().lpush(k, message);
        if (size <= 2) JCache.getInstance().expire(k, DateTime.DAY_SECOND * 5);
        if (System.currentTimeMillis() % 20 == 0) {
            JCache.getInstance().ltrim(k, 0, 30);
        }
    }

    public List<String> getChatList() {
        List<String> values = JCache.getInstance().lrange("three_star_chat_%s".formatted(id), 0, 20);
        return values;
    }

    public boolean isFinalTeam() {
        return finalTeam == 1;
    }

    public int getRank() {
        if (rank > 0) return rank;
        return isFinalTeam() ? 1 : 0;
    }

    public void addMember(int value) {
        numberMember += value;
    }

    public String getRequestKey() {
        return "three_star_request_" + eventId;
    }

    public String getRequestField() {
        return "team_" + id;
    }

    public boolean isNotFakeTeam() {
        return id > 0;
    }

    public Pbmethod.CommonVector toProto() {
        // vLong: id, rank, point, power, powerRequired, approveRequired, ownerId, ghépTựĐộng
        return Pbmethod.CommonVector.newBuilder().addALong(id).addALong(getRank()).addALong(point)
                .addALong(power)
                .addALong(getRequiredPower()).addALong(getRequiredApprove())
                .addALong(getUserId()).addALong(autoMerge == -1 ? 0 : autoMerge)
                .addAString(getName())
                .build();
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    public boolean update(List<Object> updateData) {
        return DBJPA.update("user_arena_three_star_team", updateData, Arrays.asList("id", id));
    }

}