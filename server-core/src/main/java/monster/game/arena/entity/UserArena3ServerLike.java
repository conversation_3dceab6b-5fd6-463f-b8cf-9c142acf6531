package monster.game.arena.entity;

import lombok.*;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "user_arena3_server_like")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserArena3ServerLike {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "event_id")
    private Integer eventId;

    @Column(name = "like_user_id")
    private String likeUserId;

}