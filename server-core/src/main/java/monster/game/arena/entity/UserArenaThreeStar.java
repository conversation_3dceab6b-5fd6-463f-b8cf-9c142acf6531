package monster.game.arena.entity;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import lombok.*;
import monster.cache.redis.JCache;

import javax.persistence.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Entity
@Table(name = "user_arena_three_star", schema = "dson")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserArenaThreeStar {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "event_id")
    private Integer eventId;

    @Column(name = "cluster")
    private Integer cluster;

    private int userId;
    Long teamId;
    long power;
    int timeKey, numberAttack;
    String betting;

    public List<Long> getListBetting() {
        if (StringHelper.isEmpty(betting) || betting.length() < 5) {
            betting = "[0,0,0,0]";
        }
        return GsonUtil.strToListLong(betting);
    }

    public void addRequest(long teamId) {
        String value = JCache.getInstance().hget(getRequestKey(), getRequestField());
        if (StringHelper.isEmpty(value)) value = "[]";
        var newValues = GsonUtil.strToListLong(value);
        newValues.remove(Long.valueOf(teamId));
        newValues.add(0, teamId);
        while (newValues.size() > 15) newValues.remove(newValues.size() - 1);
        JCache.getInstance().hset(getRequestKey(), getRequestField(), StringHelper.toDBString(newValues));
    }

    public List<Long> getListRequest() {
        String value = JCache.getInstance().hget(getRequestKey(), getRequestField());
        if (StringHelper.isEmpty(value)) value = "[]";
        return GsonUtil.strToListLong(value);
    }

    public void removeRequest(long teamId) {
        List<Long> listRequest = getListRequest();
        listRequest.remove(teamId);
        JCache.getInstance().hset(getRequestKey(), getRequestField(), StringHelper.toDBString(listRequest));
    }

    public void checkTimeKey() {
        int curTimeKey = DateTime.getTimeKey();
        if (curTimeKey != timeKey) {
            timeKey = curTimeKey;
            numberAttack = 0;
            update("time_key", timeKey, "number_attack", 0);
        }
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    private boolean update(List<Object> values) {
        return DBJPA.update("user_arena_three_star", values, Arrays.asList("id", id));
    }

    public String getRequestKey() {
        return "three_star_invite_" + eventId;
    }

    public String getRequestField() {
        return "user_" + userId;
    }

    public int getRemainAttack() {
        return Math.max(0, 1 - numberAttack);
    }
}