package monster.game.arena.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "user_arena_server_bet")
@NoArgsConstructor
public class UserArenaServerBet {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "event_id", nullable = false)
    private Integer eventId;

    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Column(name = "match_index", nullable = false)
    private Integer matchIndex;

    @Column(name = "win_user_id", nullable = false)
    private Integer winUserId;

    @Column(name = "result", nullable = false)
    private int result;

    @Column(name = "receive", nullable = false)
    private int receive;

    @Column(name = "date_created")
    private Date dateCreated;

    public UserArenaServerBet(int eventId, int userId, int matchIndex, int winUserId) {
        this.eventId = eventId;
        this.userId = userId;
        this.matchIndex = matchIndex;
        this.winUserId = winUserId;
        this.dateCreated = new Date();
    }

}