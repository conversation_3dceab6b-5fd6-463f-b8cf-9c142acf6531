package monster.game.arena.entity;

import com.google.gson.JsonArray;
import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserEntity;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "user_arena_trial_log")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserArenaTrialLogEntity implements java.io.Serializable {

    @Id
    private long id;
    private int eventId;
    private int attackerId, defenderId;
    private String attackerInfo, defenderInfo;
    int win;
    long battleId;
    Date dateCreated;

    public UserArenaTrialLogEntity setAttackerInfo(UserArenaTrialEntity attacker, UserEntity user, int oldRank, int newRank, long atkPower) {
        this.attackerId = attacker.getUserId();
        this.eventId = attacker.getEventId();
        this.attackerInfo = GsonUtil.toArrayString(user.getName(),
                user.getAvatarType(), user.getAvatar(), user.getAvatarFrame(),
                user.getLevel(), oldRank, newRank, atkPower);
        return this;
    }

    public UserArenaTrialLogEntity setDefenderInfo(UserArenaTrialEntity defender, UserEntity user, int oldRank, int newRank, long defPower) {
        this.defenderId = defender.getUserId();
        String name = user.getName();// user.getId() > 0 ? user.getName() : "Chiến Binh Liên Đấu " + defender.getUserRank();
        this.defenderInfo = GsonUtil.toArrayString(name,
                user.getAvatarType(), user.getAvatar(), user.getAvatarFrame(),
                user.getLevel(), oldRank, newRank, defPower);
        return this;
    }

    public Pbmethod.CommonVector.Builder toProtocol(int userId) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        JsonArray oppInfo = userId == attackerId ? GsonUtil.parseJsonArray(defenderInfo) : GsonUtil.parseJsonArray(attackerInfo);
        JsonArray myInfo = userId == attackerId ? GsonUtil.parseJsonArray(attackerInfo) : GsonUtil.parseJsonArray(defenderInfo);
        String name = oppInfo.get(0).getAsString();
        int avatarType = oppInfo.get(1).getAsInt(), avatar = oppInfo.get(2).getAsInt(), avatarFrame = oppInfo.get(3).getAsInt();
        int level = oppInfo.get(4).getAsInt();
        int oldRank = myInfo.get(5).getAsInt(), newRank = myInfo.get(6).getAsInt();
        long power = oppInfo.get(7).getAsLong();
        int isWin = userId == attackerId ? win : 1 - win;

        builder.addAString(name);
        builder.addALong(id);
        builder.addALong(userId == attackerId ? defenderId : attackerId).addALong(battleId);
        builder.addALong(avatarType).addALong(avatar);
        builder.addALong(level).addALong(oldRank).addALong(newRank);
        builder.addALong(power);
        builder.addALong((System.currentTimeMillis() - dateCreated.getTime()) / 1000);
        builder.addALong(isWin).addALong(attackerId).addALong(avatarFrame);
        return builder;
    }

}
