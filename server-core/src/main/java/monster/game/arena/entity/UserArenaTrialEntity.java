package monster.game.arena.entity;

import grep.database.DBJPA;
import grep.helper.StringHelper;
import lombok.Data;
import monster.config.CfgArenaTrial;
import monster.config.lang.Lang;
import monster.config.penum.TeamType;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserMailEntity;
import monster.object.BattleTeam;
import monster.object.UserInt;
import monster.service.resource.ResBot;
import monster.service.user.Actions;
import monster.service.user.UserTeam;
import monster.task.dbcache.MailCreatorCache;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "user_arena_trial")
@Data
public class UserArenaTrialEntity implements java.io.Serializable {
    @Id
    private int userId, eventId;
    private int serverId;
    private int point = 1000, defNotify = 0, lastPoint;
    private int dailyRank = -1, lastSeasonRank = -1, dailyReceive, lastSeasonReceive = 2;
    private int isTeamNull = 1;
    private long power;
    int numberAttack;
    String logs;
    Date datePoint = new Date();
    private int resBotId = -1;

    public UserArenaTrialEntity() {
    }

    public UserArenaTrialEntity(int userId, int serverId) {
        this.userId = userId;
        this.eventId = CfgArenaTrial.getEventId();
        this.serverId = serverId;
        this.logs = "[]";
    }

    public BattleTeam getDefTeamEntity() {
        if (isBot()) {
            return BattleTeam.builder().aHero(ResBot.getHeroInfoForBotTrial(ResBot.getBotTrial(resBotId))).build();
        }
        return UserTeam.getBattleTeam(userId, TeamType.ARENA_TRIAL_DEF);
    }

    public synchronized int addPoint(int value) {
        if (value != 0) datePoint = new Date();
        if (point + value < 0) {
            value = -point;
            point = 0;
        } else point += value;
        return value;
    }

    public Pbmethod.ListCommonVector toLogProto() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        JSONArray arr = JSONArray.fromObject(logs);
        for (int i = arr.size() - 1; i >= 0; i--) {
            JSONObject obj = arr.getJSONObject(i);
            int win = obj.containsKey("win") ? obj.getInt("win") : 1;
            long timePass = (System.currentTimeMillis() - obj.getLong("time")) / 1000;
            int atkId = obj.containsKey("atkId") ? obj.getInt("atkId") : 0;
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addAString(obj.getString("name"))
                    .addALong(obj.getLong("userId")).addALong(obj.getLong("battleId")).addALong(obj.getLong("avatarType"))
                    .addALong(obj.getLong("avatar")).addALong(obj.getLong("level")).addALong(obj.getLong("point")).addALong(timePass)
                    .addALong(win).addALong(atkId).addALong(obj.getLong("avatarFrame")));
        }
        return builder.build();
    }

    public void addLog(int atkId, UserEntity user, int point, int battleId, boolean isWin) {
        JSONObject obj = new JSONObject();
        obj.put("atkId", atkId);
        obj.put("userId", user.getId());
        obj.put("name", user.getName());
        obj.put("avatarType", user.getAvatarType());
        obj.put("avatar", user.getAvatar());
        obj.put("avatarFrame", user.getAvatarFrame());
        obj.put("level", user.getLevel());
        obj.put("point", point);
        obj.put("battleId", battleId);
        obj.put("time", System.currentTimeMillis());
        obj.put("win", isWin ? 1 : 0);
        JSONArray arr = JSONArray.fromObject(logs);
        arr.add(obj);
        while (arr.size() > 10) {
            arr.remove(0);
        }
        logs = arr.toString();
    }

    public boolean canRevenge(int userId) {
        JSONArray arr = JSONArray.fromObject(logs);
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            if (obj.getInt("userId") == userId && obj.getInt("point") < 0) {
                return true;
            }
        }
        return false;
    }

    public void sendReward(UserInt uInt) {
        // lastSeasonReceive = 2 chua tinh rank, 0 la duoc nhan, 1 la da nhan
        if (CfgArenaTrial.jobSeasonRankingDone.contains(String.format("%s_%s", eventId, serverId))) { // Job ranking đã xong
            if (lastSeasonReceive == 0 && lastSeasonRank >= 0) { // season reward
                if (DBJPA.update("user_arena_trial", Arrays.asList("last_season_receive", 1), Arrays.asList("user_id", userId, "event_id", eventId))) {
                    List<Long> aBonus = CfgArenaTrial.getSeasonReward(lastSeasonRank);
                    Actions.save(serverId, userId, "arena_trial", "mail_season", "rank", lastSeasonRank, "bonus", StringHelper.toDBString(aBonus));
                    if (aBonus != null) {
                        String title = Lang.getTitle("title_arena_trial_season");
                        String body = Lang.getTitle("content_arena_trial_season");
                        MailCreatorCache.sendMail(UserMailEntity.builder().userId(userId)
                                .title(String.format(title, CfgArenaTrial.getEventId()))
                                .message(String.format(body, lastSeasonRank))
                                .bonus(aBonus.toString()).origin("arena_trial:"+CfgArenaTrial.getEventId())
                                .build());
                        // đã gửi quà mùa này
                        uInt.setValue(UserInt.AWARD_ARENA_TRIAL, CfgArenaTrial.getEventId());
                        uInt.update(userId);
                    }
                }
            }
        }
    }

    public boolean isBot() {
        return this.userId < 0;
    }

    public void updateNotify(int value) {
        DBJPA.update("user_arena_trial", Arrays.asList("def_notify", value), Arrays.asList("user_id", userId, "event_id", eventId));
        defNotify = value;
    }

    public void updatePower() {
        if (power > 0) {
            DBJPA.update("user_arena_trial", Arrays.asList("power", power), Arrays.asList("user_id", userId, "event_id", eventId));
        }
    }
}
