package monster.game.arena.entity;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserEntity;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "user_arena_crystal_log")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserArenaCrystalLogEntity implements java.io.Serializable {

    @Id
    private long id;
    private int eventId;
    private int attackerId, defenderId;
    private String attackerInfo, defenderInfo;
    int win;
    long battleId;
    boolean revenge;
    Date dateCreated;

    public UserArenaCrystalLogEntity setAttackerInfo(UserArenaCrystalEntity attacker, UserEntity user, int point, int addPoint, long atkPower) {
        this.attackerId = attacker.getUserId();
        this.eventId = attacker.getEventId();
        this.attackerInfo = GsonUtil.toArrayString(user.getName(),
                user.getAvatarType(), user.getAvatar(), user.getAvatarFrame(),
                user.getLevel(), point, addPoint, atkPower);
        return this;
    }

    public UserArenaCrystalLogEntity setDefenderInfo(UserArenaCrystalEntity defender, UserEntity user, int point, int addPoint, long defPower) {
        this.defenderId = defender.getUserId();
        this.defenderInfo = GsonUtil.toArrayString(user.getName(),
                user.getAvatarType(), user.getAvatar(), user.getAvatarFrame(),
                user.getLevel(), point, addPoint, defPower);
        return this;
    }

    public Pbmethod.CommonVector.Builder toProtocol(int userId) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        JsonArray oppInfo = userId == attackerId ? GsonUtil.parseJsonArray(defenderInfo) : GsonUtil.parseJsonArray(attackerInfo);
        JsonArray myInfo = userId == attackerId ? GsonUtil.parseJsonArray(attackerInfo) : GsonUtil.parseJsonArray(defenderInfo);
        String name = oppInfo.get(0).getAsString();
        int avatarType = oppInfo.get(1).getAsInt(), avatar = oppInfo.get(2).getAsInt(), avatarFrame = oppInfo.get(3).getAsInt();
        int level = oppInfo.get(4).getAsInt();
        int addPoint = myInfo.get(6).getAsInt();
        long power = oppInfo.get(7).getAsLong();

        int isWin = userId == attackerId ? win : 1 - win;
        builder.addAString(name);
        builder.addALong(id);
        builder.addALong(userId == attackerId ? defenderId : attackerId).addALong(battleId);
        builder.addALong(avatarType).addALong(avatar);
        builder.addALong(level).addALong(addPoint);
        builder.addALong(power);
        builder.addALong((System.currentTimeMillis() - dateCreated.getTime()) / 1000);
        builder.addALong(isWin).addALong(attackerId).addALong(avatarFrame);
        builder.addALong(couldRevenge(userId) ? 1 : 0); // thủ và thua
        return builder;
    }

    public boolean couldRevenge(int userId) {
        return attackerId != userId && (userId == attackerId ? win : 1 - win) == 0 && !revenge;
    }

    public boolean updateRevenge() {
        revenge = true;
        return DBJPA.update("user_arena_crystal_log", List.of("revenge", 1), List.of("id", id));
    }

}
