package monster.game.arena.entity;

import grep.helper.ListUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import monster.config.CfgServer;
import monster.dao.mapping.UserEntity;
import monster.game.arena.service.ArenaServerService;
import monster.server.config.Guice;
import protocol.Pbmethod;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "user_arena_server_attack_log")
@NoArgsConstructor
@AllArgsConstructor
public class UserArenaServerAttackLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "event_id", nullable = false)
    private Integer eventId;

    @Column(name = "atk_id")
    private Integer atkId;

    @Column(name = "def_id")
    private Integer defId;

    @Column(name = "win", nullable = false)
    private Integer win;

    @Column(name = "battle_id", nullable = false)
    private Long battleId;

    @Column(name = "date_created", nullable = false)
    private Date dateCreated;

    int round;

    public UserArenaServerAttackLog(UserArenaServerQualifyEntity userArenaQualify, int defId, int win, long battleId) {
        this.eventId = userArenaQualify.getEventId();
        this.round = userArenaQualify.getRound();
        this.atkId = userArenaQualify.getUserId();
        this.defId = defId;
        this.win = win;
        this.battleId = battleId;
        this.dateCreated = new Date();
    }

    /**
     * { *n
     * vLong: roundId, battleId, timePass, win(0/1), numberWin, numberLoose, atkId, defId, level, power, [avatar]
     * vStr: name, name server
     * }
     */
    public Pbmethod.CommonVector.Builder toProto(int userId) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(round).addALong(battleId);
        builder.addALong((System.currentTimeMillis() - dateCreated.getTime()) / 1000);
        builder.addALong(win).addALong(win == 1 ? 1 : 0).addALong(win == 1 ? 0 : 1);
        builder.addALong(atkId).addALong(defId);
        int oppId = atkId == userId ? defId : atkId;
        UserEntity user = Guice.getInstance(ArenaServerService.class).getUserEntity(oppId);
        builder.addALong(user.getLevel()).addALong(user.getPower()).addAllALong(ListUtil.convertLstIntToLong(user.getListAvatar()));
        builder.addAString(user.getName()).addAString(CfgServer.getServerName(user.getServer()));
        return builder;
    }
}