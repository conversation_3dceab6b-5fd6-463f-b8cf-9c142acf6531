package monster.game.arena.entity;

import grep.database.DBJPA;
import grep.helper.DateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.config.CfgArenaSwap;
import monster.config.CfgCluster;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.TeamType;
import monster.dao.mapping.UserEntity;
import monster.object.BattleTeam;
import monster.object.UserInt;
import monster.service.resource.ResBot;
import monster.service.user.Actions;
import monster.service.user.UserTeam;
import monster.util.DBHelper;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import protocol.Pbmethod;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "user_arena_swap")
@Data
@NoArgsConstructor
public class UserArenaSwapEntity implements java.io.Serializable {

    @Id
    private int userId;
    private int eventId, serverId, cluster;
    private int defNotify = 0, physicalServer;
    private int point = 0;
    private int attackNumber, attackDate;
    private int buyNumber, buyDate;
    private long power;
    private boolean receive;
    String logs; //defTeam,
    Date datePoint = new Date();
    private int resBotId = -1;
    private int userRank = 1000;

    public UserArenaSwapEntity(int userId, int serverId) {
        this.userId = userId;
        this.serverId = serverId;
        this.physicalServer = CfgServer.serverId;
        this.logs = "[]";
    }

    public int getAttackNumber() {
        int curDate = Integer.parseInt(DateTime.getDateyyyyMMdd(new Date()));
        if (curDate != attackDate) {
            attackDate = curDate;
            attackNumber = 5;
            update(Arrays.asList("attack_date", curDate, "attack_number", 5));
        }
        return attackNumber;
    }

    public int getBuyNumber() {
        int curDate = Integer.parseInt(DateTime.getDateyyyyMMdd(new Date()));
        if (curDate != buyDate) {
            buyDate = curDate;
            buyNumber = 0;
            update(Arrays.asList("buy_date", curDate, "buy_number", 0));
        }
        return buyNumber;
    }

    public BattleTeam getDefTeamEntity() {
        if (userId < 0) {
            return ResBot.getSwapBattleTeam(Math.abs(userId));
        }
        return UserTeam.getBattleTeam(userId, TeamType.ARENA_SWAP_DEF);
    }

    public int addPoint(int value) {
        return value;
    }

    public Pbmethod.ListCommonVector toLogProto() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        JSONArray arr = JSONArray.fromObject(logs);
        for (int i = arr.size() - 1; i >= 0; i--) {
            JSONObject obj = arr.getJSONObject(i);
            int win = obj.containsKey("win") ? obj.getInt("win") : 1;
            int atkId = obj.containsKey("atkId") ? obj.getInt("atkId") : 0;
            long timePass = (System.currentTimeMillis() - obj.getLong("time")) / 1000;
            String name = obj.getString("name");
            if (name.indexOf(" ") >= 0) {
                name = name.replaceFirst(" ", "");
            }
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addAString(name)
                    .addALong(obj.getLong("userId")).addALong(obj.getLong("battleId")).addALong(obj.getLong("avatarType"))
                    .addALong(obj.getLong("avatar")).addALong(obj.getLong("level"))
                    .addALong(obj.getLong("oldRank")).addALong(obj.getLong("newRank")).addALong(timePass)
                    .addALong(win).addALong(atkId).addALong(obj.getLong("avatarFrame")));
        }
        return builder.build();
    }

    public void addLog(int atkId, UserEntity user, int oldRank, int newRank, int battleId, boolean isWin) {
        JSONObject obj = new JSONObject();
        obj.put("atkId", atkId);
        obj.put("userId", user.getId());
        obj.put("name", String.format("S%s. %s", CfgCluster.getShowServer(user.getServer()), user.getName()));
        obj.put("avatarType", user.getAvatarType());
        obj.put("avatar", user.getAvatar());
        obj.put("avatarFrame", user.getAvatarFrame());
        obj.put("level", user.getLevel());
        obj.put("oldRank", oldRank);
        obj.put("newRank", newRank);
        obj.put("battleId", battleId);
        obj.put("time", System.currentTimeMillis());
        obj.put("win", isWin ? 1 : 0);
        JSONArray arr = JSONArray.fromObject(logs);
        arr.add(obj);
        while (arr.size() > 10) {
            arr.remove(0);
        }
        logs = arr.toString();
    }

    public boolean canRevenge(int userId) {
        JSONArray arr = JSONArray.fromObject(logs);
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            if (obj.getInt("userId") == userId && obj.getInt("point") < 0) {
                return true;
            }
        }
        return false;
    }

    public void sendReward(UserInt uInt) {
        if (!receive) {
            if (DBJPA.update("user_arena_swap", Arrays.asList("receive", 1), Arrays.asList("user_id", userId, "event_id", eventId))) {
                List<Long> aBonus = CfgArenaSwap.config.getBonusSeason(point);
                if (aBonus != null) {
                    String title = Lang.getTitle("title_corrida_reward");
                    String body = Lang.getTitle("content_corrida_reward");
                    //                    String title = Lang.instance().isVi() ? "Đấu trường CORRIDA (%s)" : "Arena All Stars Reward";
                    //                    String body = Lang.instance().isVi() ? "Chúc mừng bạn được %s điểm đấu trường" : "Reward for %s point";
                    Actions.save(serverId, userId, "arena_swap", "mail_season", "point", point, "bonus", aBonus.toString());
                    DBJPA.rawSQL(DBHelper.sqlMail(userId,
                            String.format(title, eventId),
                            String.format(body, point), aBonus.toString()));

                    // đã gửi quà mùa này
                    uInt.setValue(UserInt.AWARD_ARENA_SWAP, eventId);
                    uInt.update(userId);
                }
            }
        }
    }

    public void updateNotify(int value) {
        DBJPA.update("user_arena_swap", Arrays.asList("def_notify", value), Arrays.asList("user_id", userId, "event_id", eventId));
        defNotify = value;
    }

    private void update(List<Object> values) {
        DBJPA.update("user_arena_swap", values, Arrays.asList("user_id", userId, "event_id", eventId));
    }
}
