package monster.game.arena.entity;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import monster.dao.mapping.UserEntity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user_arena_server_qualify")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserArenaServerQualifyEntity implements java.io.Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;
    private int userId, eventId;
    private int serverId, round;
    private int cluster;
    private int userRank = 1000;
    private long point = 0;
    private long power;
    int numberWin, numberLoose;
    String summaryAttack; // oppId battleId isWin numberWin numberLoose
    long timeWin;

    public UserArenaServerQualifyEntity(UserEntity user, int eventId) {
        this.userId = user.getId();
        this.eventId = eventId;
        this.serverId = user.getServer();
    }

    public List<Integer> getAttackedUserId() {
        List<Integer> userIds = new ArrayList<>();
        List<List<Long>> summaryAttacks = GsonUtil.strTo2ListLong(getSummaryAttack());
        for (List<Long> attack : summaryAttacks) {
            userIds.add(attack.get(0).intValue());
        }
        return userIds;
    }

    public void reset(int round, int cluster) {
        this.id = 0;
        this.round = round;
        this.cluster = cluster;
        this.numberLoose = 0;
        this.numberWin = 0;
        this.summaryAttack = "[]";
        this.point = 0;
        this.timeWin = 0;
    }

    public long getTimeWinFormat() {
        if (timeWin == 0) return 0;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeWin);
        return calendar.get(Calendar.HOUR_OF_DAY) * 3600 + calendar.get(Calendar.MINUTE) * 60 + calendar.get(Calendar.SECOND);
    }

    public boolean update(Object... values) {
        return update(Arrays.stream(values).collect(Collectors.toList()));
    }

    private boolean update(List<Object> values) {
        return DBJPA.update("user_arena_server_qualify", values, Arrays.asList("id", id));
    }

}
