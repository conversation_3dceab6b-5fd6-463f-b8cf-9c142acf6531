package monster.game.arena.entity;

import grep.helper.GsonUtil;
import lombok.*;

import jakarta.persistence.*;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "user_arena_three_star_opponent", schema = "dson")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserArenaThreeStarOpponent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    long teamId;
    int myPoint;
    String opponentId, opponentResult;
    String opponentPoint;
    int timeKey;

    public List<Long> getListOpponentId() {
        return GsonUtil.strToListLong(opponentId);
    }

    public List<Integer> getListOpponentResult() {
        List<Integer> list = GsonUtil.strToListInt(opponentResult);
        while (list.size() < 18) list.add(0);
        return list;
    }

    public int getOpponentPointValue(int index) {
        return GsonUtil.strToListInt(opponentPoint).get(index);
    }

}