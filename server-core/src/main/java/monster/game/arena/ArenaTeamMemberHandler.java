package monster.game.arena;

import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.NoArgsConstructor;
import monster.config.lang.Lang;
import monster.config.penum.TeamType;
import monster.controller.AHandler;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserFriendRelationshipEntity;
import monster.game.arena.config.ArenaThreeStarDistributedLocked;
import monster.game.arena.config.ConfigArenaTeam;
import monster.game.arena.dao.ArenaTeamDAO;
import monster.game.arena.entity.ArenaTeamState;
import monster.game.arena.entity.UserArenaThreeStar;
import monster.game.arena.entity.UserArenaThreeStarTeam;
import monster.game.arena.service.ArenaTeamService;
import monster.game.clan.service.ClanService;
import monster.game.user.service.FriendService;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Arena Team - Quản lý member
 */
@NoArgsConstructor
public class ArenaTeamMemberHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List.of(ARENA_TEAM_REQUEST_WAIT, ARENA_TEAM_REQUEST, ARENA_TEAM_APPROVE_REQUEST, ARENA_TEAM_CREATE,
                ARENA_TEAM_LEAVE, ARENA_TEAM_LIST_INVITE_ME, ARENA_TEAM_INVITE,
                ARENA_TEAM_ANSWER_INVITE_ME, ARENA_TEAM_LIST_INVITE_FRIEND, ARENA_TEAM_LIST_INVITE_CLAN,
                ARENA_TEAM_FIND, ARENA_TEAM_FIND_RANDOM, ARENA_TEAM_SETTING_AUTO_MERGE, ARENA_TEAM_SORTING).forEach(action -> mHandler.put(action, this));
    }

    ArenaTeamDAO arenaTeamDAO = Guice.getInstance(ArenaTeamDAO.class);
    ArenaTeamState arenaState = Guice.getInstance(ArenaTeamState.class);
    ArenaTeamService arenaService = Guice.getInstance(ArenaTeamService.class);
    FriendService friendService = Guice.getInstance(FriendService.class);
    ClanService clanService = Guice.getInstance(ClanService.class);
    UserArenaThreeStar userArena;
    UserArenaThreeStarTeam userArenaTeam;

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        if (!arenaState.isOpen(mUser)) {
            addErrResponse(Lang.event_ended);
            return;
        }

        userArena = arenaService.getUserArena(user.getId());
        userArenaTeam = userArena.getTeamId() > 0 ? arenaService.getArenaTeamById(userArena.getTeamId()) : null;
        if (userArena == null || (userArena.getTeamId() > 0 && userArenaTeam == null)) {
            addErrResponse();
            return;
        }
        if (actionId != ARENA_TEAM_FIND && !arenaState.isPreparedDay()) {
            addErrResponse(Lang.war_prepared_end);
            return;
        }
        try {
            switch (actionId) {
                case ARENA_TEAM_CREATE -> create(parseCommonInput());
                case ARENA_TEAM_REQUEST_WAIT -> requestList();
                case ARENA_TEAM_REQUEST -> request(getInputLong());
                case ARENA_TEAM_LIST_INVITE_ME -> listInviteMe();
                case ARENA_TEAM_ANSWER_INVITE_ME -> answerInviteMe(parseCommonInput().getALongList());
                case ARENA_TEAM_FIND -> find(parseCommonInput());
                case ARENA_TEAM_FIND_RANDOM -> findRandom();
                default -> {
                    if (userArena.getTeamId() == 0 || userArenaTeam == null) {
                        addErrResponse(Lang.arena_team_create_first);
                    } else {
                        switch (actionId) {
                            case ARENA_TEAM_APPROVE_REQUEST -> approveRequest(getInputALong());
                            case ARENA_TEAM_LEAVE -> leave();
                            case ARENA_TEAM_INVITE -> invite(getInputInt());
                            case ARENA_TEAM_LIST_INVITE_FRIEND -> listFriend();
                            case ARENA_TEAM_LIST_INVITE_CLAN -> listClan();
                            case ARENA_TEAM_SETTING_AUTO_MERGE -> autoMerge(getInputInt());
                            case ARENA_TEAM_SORTING -> memberSorting(ListUtil.convertLstLongToInt(getInputALong()));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void create(Pbmethod.CommonVector cmm) {
        arenaService.createTeam(mUser, userArena, cmm).writeResponse(this);
    }

    void memberSorting(List<Integer> values) {
        if (user.getId() != userArenaTeam.getUserId()) {
            addErrResponse(Lang.arena_team_captain_required);
            return;
        }
        int count = (int) values.stream().filter(i -> i > 0).count();
        if (userArenaTeam.getNumberMember() != count) {
            addErrResponse("Đội hình có %s thành viên, bạn mới chọn %s".formatted(userArenaTeam.getNumberMember(), count));
            return;
        }
        List<Integer> oldValues = GsonUtil.strToListInt(userArenaTeam.getSortMember());
        if (oldValues.containsAll(values) && oldValues.size() == values.size() && userArenaTeam.update("sort_member", StringHelper.toDBString(values))) {
            userArenaTeam.setSortMember(StringHelper.toDBString(values));
            addResponse(getCommonVector(ListUtil.convertLstIntToLong(values)));
            Actions.save(user, "three_star", "sort", "teamId", userArenaTeam.getId(), "value", StringHelper.toDBString(values));
        } else addErrResponse(Lang.err_params);
    }

    void autoMerge(int value) {
        if (user.getId() != userArenaTeam.getUserId()) {
            addErrResponse(Lang.arena_team_captain_required);
            return;
        }
        value = value == 0 ? value : 1;
        if (value == 0 && userArenaTeam.getNumberMember() < 3) {
            addErrResponse("Không đủ thành viên, cần tích tuỳ chọn để hệ thống ghép đội tự động");
            return;
        }
        if (userArenaTeam.update("auto_merge", value)) {
            int oldValue = userArenaTeam.getAutoMerge();
            userArenaTeam.setAutoMerge(value);
            addResponse(getCommonVector(value));
            if (arenaState.isPreparedDay()) {
                addErrResponse(oldValue == -1 ? "Ghi danh thành công" : "Đội đã ghi danh");
            }
        } else addErrResponse();
    }

    void find(Pbmethod.CommonVector cmm) {
        long teamId = cmm.getALongCount() > 0 ? cmm.getALong(0) : 0;
        String name = cmm.getAStringCount() > 0 ? cmm.getAString(0) : "";
        if (teamId == 0 && StringHelper.isEmpty(name)) addErrResponse(Lang.err_params);
        else {
            List<UserArenaThreeStarTeam> teams = new ArrayList<>();
            if (teamId > 0) {
                var team = arenaService.getArenaTeamById(teamId);
                if (team != null) teams.add(team);
            } else if (!StringHelper.isEmpty(name))
                teams.addAll(arenaService.getArenaTeamByName(user.getServer(), name));
            if (teams.isEmpty()) {
                addErrResponse("Không tìm thấy đội tương ứng");
                return;
            }
            addResponse(arenaService.toProtoTeam(teams).build());
        }
    }

    void findRandom() {
        List<UserArenaThreeStarTeam> teams = arenaService.listTeamSuggest(user.getServer());
        addResponse(arenaService.toProtoTeam(teams).build());
    }

    void listFriend() {
        List<UserFriendRelationshipEntity> friends = friendService.getListFriends(user.getId());
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        for (UserFriendRelationshipEntity friend : friends) {
            UserEntity friendUser = UserOnline.getDbUser(friend.getFriendId(user.getId()));
            if (friendUser != null && friendUser.getLevel() >= ConfigArenaTeam.minLevel) {
                if (arenaState.getCluster(user.getServer()) == arenaState.getCluster(friendUser.getServer()))
                    builder.addAUser(friendUser.protoTinyUser().setInfo(getCommonVector(0)));
            }
        }
        addResponse(builder.build());
    }

    void listClan() {
        if (user.getClan() == 0) {
            addErrResponse(Lang.clan_no_clan);
        } else {
            List<UserEntity> clanMembers = clanService.getListClanMember(user.getClan());
            Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
            for (UserEntity clanMember : clanMembers) {
                if (clanMember.getLevel() >= ConfigArenaTeam.minLevel && clanMember.getId() != user.getId()) {
                    builder.addAUser(clanMember.protoTinyUser().setInfo(getCommonVector(0)));
                }
            }
            addResponse(builder.build());
        }
    }

    private void answerInviteMe(List<Long> values) {
        if (userArenaTeam != null) {
            addErrResponse(Lang.arena_team_leave_first);
        } else {
            long teamId = values.get(0);
            boolean isAccept = values.get(1).intValue() == 1;
            if (!isAccept) {
                userArena.removeRequest(values.get(0));
            } else {
                ServiceResult serviceResult = ServiceResult.error("");
                arenaService.joinTeam(serviceResult, user.getServer(), teamId, user.getId());
                if (serviceResult.success) {
                    userArena.setTeamId(teamId);
                    addResponse(getCommonVector(values));
                    userArena.removeRequest(values.get(0));
                } else serviceResult.writeResponse(this);
            }
        }
    }

    void listInviteMe() {
        List<Long> listTeamInvite = userArena.getListRequest();
        List<UserArenaThreeStarTeam> teams = arenaTeamDAO.getListTeamById(listTeamInvite);
        addResponse(arenaService.toProtoTeam(teams).build());
    }

    void requestList() {
        List<Integer> userIds = userArenaTeam.listUserIdRequest();
        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        for (Integer reqId : userIds) {
            builder.addAUser(arenaService.protoUser(reqId, 0));
        }
        addResponse(builder.build());
    }

    void request(long teamId) {
        if (userArenaTeam != null) {
            addErrResponse(Lang.arena_team_leave_first);
            return;
        }
        var reqTeam = arenaService.getArenaTeamById(teamId);
        if (reqTeam == null) {
            addErrResponse(Lang.arena_team_not_found);
            return;
        }
        if (reqTeam.getAutoMerge() >= 0) {
            addErrResponse(Lang.arena_team_check_in);
            return;
        }
        UserArenaThreeStarTeam team = arenaService.getArenaTeamById(teamId);
        if (arenaState.getCluster(user.getServer()) != team.getCluster()) {
            addErrResponse("Đội khác cụm");
            return;
        }
        long arenaDefPower = UserTeam.getTeam(user.getId(), TeamType.ARENA_THREE_STAR_DEF).getPower();
        if (team.getRequiredPower() > arenaDefPower) {
            addErrResponse(Lang.arena_team_not_enough_power);
        } else if (team.getRequiredApprove() == 0) { // đủ thể lực và không cần duyệt
            ServiceResult serviceResult = ServiceResult.error("");
            arenaService.joinTeam(serviceResult, user.getServer(), teamId, user.getId());
            if (serviceResult.success) {
                userArena.setTeamId(teamId);
                addResponse(getCommonVector(teamId));
            } else serviceResult.writeResponse(this);
        } else {
            reqTeam.addRequest(user.getId());
            addResponse(getCommonVector(0));
            addErrResponse("Gửi yêu cầu gia nhập thành công");
        }
    }

    void approveRequest(List<Long> values) {
        int reqUserId = values.get(0).intValue();
        int approve = values.get(1).intValue();
        if (user.getId() != userArenaTeam.getUserId()) {
            addErrResponse(Lang.arena_team_captain_required);
            return;
        }
        if (approve == 0) {
            userArenaTeam.removeRequest(reqUserId);
            addResponse(null);
        } else {
            if (userArenaTeam.getAutoMerge() >= 0) {
                addErrResponse(Lang.arena_team_check_in);
                return;
            }
            UserArenaThreeStar reqUserArena = arenaService.getUserArena(reqUserId);
            if (reqUserArena.getTeamId() > 0) addErrResponse(Lang.arena_team_already_teamup);
            else {
                ServiceResult serviceResult = ServiceResult.error("");
                arenaService.joinTeam(serviceResult, user.getServer(), userArenaTeam.getId(), values.get(0).intValue());
                if (serviceResult.success) {
                    addResponse(null);
                    userArenaTeam.removeRequest(reqUserId);
                } else serviceResult.writeResponse(this);
            }
        }
    }

    void invite(int inviteUserId) {
        if (user.getId() != userArenaTeam.getUserId()) {
            addErrResponse(Lang.arena_team_captain_required);
        } else if (userArenaTeam.getAutoMerge() >= 0) {
            addErrResponse(Lang.arena_team_check_in);
        } else if (userArenaTeam.getRequiredApprove() >= 3) {
            addErrResponse(Lang.arena_team_full);
        } else if (arenaState.getCluster(user.getServer()) != arenaState.getCluster(UserOnline.getDbUser(inviteUserId).getServer())) {
            addErrResponse("Người choi khác cụm");
        } else {
            ArenaThreeStarDistributedLocked.lockedThreeStarUser(inviteUserId, (languageKey) -> {
                if (StringHelper.isEmpty(languageKey)) {
                    UserArenaThreeStar reqUserArena = arenaService.getUserArena(inviteUserId);
                    if (reqUserArena.getTeamId() > 0) addErrResponse(Lang.arena_team_already_teamup);
                    else {
                        reqUserArena.addRequest(userArenaTeam.getId());
                        addResponse(null);
                        addErrResponse("Mời thành công");
                    }
                } else addErrResponse(languageKey);
            });
        }
    }

    void leave() {
        if (userArenaTeam == null) addErrResponse(Lang.arena_team_join_first);
        else if (userArenaTeam.getAutoMerge() >= 0) addErrResponse(Lang.arena_team_check_in);
        else {
            ServiceResult serviceResult = ServiceResult.error("");
            arenaService.leaveTeam(serviceResult, user.getServer(), userArena.getTeamId(), userArena);
            if (serviceResult.success) {
                addResponse(null);
            } else serviceResult.writeResponse(this);
        }
    }

}