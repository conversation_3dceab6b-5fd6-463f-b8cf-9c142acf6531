package monster.game.arena.dao;

import monster.dao.AbstractDAO;
import monster.game.arena.entity.UserArenaServer;
import monster.game.arena.entity.UserArenaServerQualifyEntity;

import java.util.List;

public class ArenaServerJobDAO extends AbstractDAO {

    public List<UserArenaServer> getListRegisterWin(int eventId) {
        return doQuery(em -> em.createQuery("select c from UserArenaServer c where eventId=:eventId and power >0 order by power desc")
                .setParameter("eventId", eventId).setMaxResults(1024).getResultList());
    }

    public List<UserArenaServerQualifyEntity> getListQualifiedRound(int eventId, int round) {
        return doQuery(em -> em.createQuery("select c from UserArenaServerQualifyEntity c where eventId=:eventId and c.round=:round")
                .setParameter("eventId", eventId).setParameter("round", round).getResultList());
    }

}
