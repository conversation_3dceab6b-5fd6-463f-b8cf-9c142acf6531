package monster.game.arena.service;

import monster.game.arena.entity.UserArenaThreeStar;
import monster.game.arena.entity.UserArenaThreeStarTeam;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.object.ServiceResult;
import protocol.Pbmethod;

import java.util.List;

public interface ArenaTeamService {

    UserArenaThreeStar getUserArena(int userId);
    UserArenaThreeStarTeam getArenaTeamById(long teamId);
    List<UserArenaThreeStarTeam> getArenaTeamByName(int serverId, String name);
    List<UserArenaThreeStarTeam> listTeamSuggest(int serverId);
    List<UserArenaThreeStarTeam> top3(int serverId);
    List<UserArenaThreeStarTeam> topSeason(int serverId);
    List<UserArenaThreeStarTeam> topLastSeason(int serverId);
    UserArenaThreeStar getLastUserArena(int userId);
    List<UserArenaThreeStarTeam> getLastSeasonTeam(List<Long> teamIds);
    List<UserArenaThreeStarTeam> topBetSeason(int serverId);
    Pbmethod.PbUser.Builder protoUser(int userId, long idTeam);
    void joinTeam(ServiceResult serviceResult, int serverId,  long teamId, int userId);
    void leaveTeam(ServiceResult serviceResult, int serverId,  long teamId, UserArenaThreeStar userArena);
    BattleTeam getMemberBattleTeam(MyUser mUser);
    Pbmethod.BigListCommonVector.Builder toProtoTeam(List<UserArenaThreeStarTeam> teams);
    ServiceResult<Pbmethod.CommonVector> createTeam(MyUser mUser, UserArenaThreeStar userArenaThreeStar,Pbmethod.CommonVector cmm);
    BattleTeam getTeamBattleTeam(UserArenaThreeStarTeam team, List<Integer> userIds);
    List<Long> getListPower(List<Integer> userIds);
    List<String> getListName(List<Integer> userIds);
    boolean updateTeamPower(long teamId);
    int calculateWinPoint(int atkPoint, int defPoint, boolean isWin);
    int getRank(UserArenaThreeStarTeam team);
    void checkNotify(MyUser mUser, Pbmethod.CommonVector.Builder cmm);
}
