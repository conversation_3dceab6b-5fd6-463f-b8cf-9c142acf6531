package monster.game.arena.service.impl;

import com.google.inject.Inject;
import grep.helper.GsonUtil;
import grep.helper.ListUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.NotifyType;
import monster.config.penum.TeamType;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserPetEntity;
import monster.game.arena.config.ArenaThreeStarDistributedLocked;
import monster.game.arena.config.ConfigArenaTeam;
import monster.game.arena.dao.ArenaTeamDAO;
import monster.game.arena.entity.ArenaTeamState;
import monster.game.arena.entity.UserArenaThreeStar;
import monster.game.arena.entity.UserArenaThreeStarOpponent;
import monster.game.arena.entity.UserArenaThreeStarTeam;
import monster.game.arena.service.ArenaTeamService;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.service.aop.CacheInGame;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ArenaTeamServiceImpl implements ArenaTeamService {

    @Inject
    ArenaTeamDAO dao;
    @Inject
    ArenaTeamState state;

    @Override
    public UserArenaThreeStar getUserArena(int userId) {
        UserArenaThreeStar userArena = dao.getUserArena(userId, state.getEventId());
        if (userArena == null) {
            userArena = UserArenaThreeStar.builder().eventId(state.getEventId()).userId(userId).teamId(0L)
                    .cluster(state.getCluster(UserOnline.getDbUser(userId).getServer())).build();
            if (!dao.save(userArena)) return null;
        }
        if (userArena != null && userArena.getTeamId() == null) {
            userArena.setTeamId(0L);
            userArena.update("team_id", 0);
            Logs.error("UserArenaThreeStar: userId=%s teamId null".formatted(userArena.getUserId()));
        }
        return userArena;
    }

    //  + diem, maxInt - time hien tai
    @Override
    public UserArenaThreeStarTeam getArenaTeamById(long teamId) {
        return dao.getTeamById(state.getEventId(), teamId);
    }

    @Override
    public List<UserArenaThreeStarTeam> getArenaTeamByName(int serverId, String name) {
        return dao.getTeamByName(state.getEventId(), state.getCluster(serverId), name);
    }

    @Override
    public List<UserArenaThreeStarTeam> listTeamSuggest(int serverId) {
        return dao.listTeamSuggest(state.getEventId(), state.getCluster(serverId));
    }

    @Override
    public List<UserArenaThreeStarTeam> top3(int serverId) {
        return dao.top3(state.getEventId(), state.getCluster(serverId));
    }

    @Override
    public List<UserArenaThreeStarTeam> topSeason(int serverId) {
        return dao.topSeason(state.getEventId(), state.getCluster(serverId));
    }

    @CacheInGame(expire = 300)
    @Override
    public List<UserArenaThreeStarTeam> topLastSeason(int serverId) {
        if (CfgServer.isTestServer()) {
            // fake for server test
            var teams = topSeason(serverId);
            while (teams.size() > 4) teams.remove(teams.size() - 1);
            return teams;
        }
        var teams = dao.topSeason(state.getEventId() - 1, state.getCluster(serverId));
        while (teams.size() > 4) teams.remove(teams.size() - 1);
        return teams;
    }

    @Override
    public UserArenaThreeStar getLastUserArena(int userId) {
        return dao.getUserArena(userId, CfgServer.isTestServer() ? state.getEventId() : state.getEventId() - 1);
    }

    @Override
    public List<UserArenaThreeStarTeam> getLastSeasonTeam(List<Long> teamIds) {
        return dao.getListTeamById(teamIds);
    }

    @Override
    public List<UserArenaThreeStarTeam> topBetSeason(int serverId) {
        return dao.topPower(state.getEventId(), state.getCluster(serverId));
    }

    @Override
    public Pbmethod.PbUser.Builder protoUser(int userId, long idTeam) {
        UserEntity user = UserOnline.getDbUser(userId);
        Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
        BattleTeam battleTeam = UserTeam.getBattleTeam(user.getId(), TeamType.ARENA_THREE_STAR_DEF);
        BattleTeam.toPbUserProto(pbUser, battleTeam);
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(idTeam).addALong(battleTeam == null ? 0 : battleTeam.getPower()));
        return pbUser;
    }

    @Override
    public void joinTeam(ServiceResult serviceResult, int serverId, long teamId, int userId) {
        ArenaThreeStarDistributedLocked.lockedThreeStarUser(userId, userKey -> {
            if (StringHelper.isEmpty(userKey)) {
                UserArenaThreeStar userArena = getUserArena(userId);
                if (userArena.getTeamId() == 0) {
                    var arenaTeam = getArenaTeamById(teamId);
                    if (arenaTeam != null && arenaTeam.getNumberMember() < 3) {
                        List<Integer> memberIds = GsonUtil.strToListInt(arenaTeam.getSortMember());
                        for (int i = 0; i < memberIds.size(); i++) {
                            if (memberIds.get(i) == 0) {
                                memberIds.set(i, userArena.getUserId());
                                break;
                            }
                        }
                        if (dao.joinMember(teamId, userArena.getId(), StringHelper.toDBString(memberIds))) {
                            arenaTeam.setNumberMember(arenaTeam.getNumberMember() + 1);
                            arenaTeam.setSortMember(StringHelper.toDBString(memberIds));
                            userArena.setTeamId(teamId);
                            serviceResult.success = true;
                            updateTeamPower(teamId);
                            UserEntity joinUser = UserOnline.getDbUser(userId);
                            if (joinUser != null) {
                                Actions.save(joinUser, "three_star", "join", "id", arenaTeam.getId(), "number", arenaTeam.getNumberMember());
                            }
                        } else serviceResult.setMessage(Lang.err_system_down);
                    } else serviceResult.setMessage(Lang.arena_team_full);
                } else serviceResult.setMessage(Lang.arena_team_already_teamup);
            } else serviceResult.setMessage(userKey);
        });
    }

    @Override
    public void leaveTeam(ServiceResult serviceResult, int serverId, long teamId, UserArenaThreeStar userArena) {
        ArenaThreeStarDistributedLocked.lockedThreeStarTeam(teamId, (languageKey -> {
            if (StringHelper.isEmpty(languageKey)) {
                UserArenaThreeStarTeam arenaTeam = getArenaTeamById(teamId);
                if (arenaTeam != null && userArena.getTeamId() == teamId) {
                    List<Integer> memberIds = GsonUtil.strToListInt(arenaTeam.getSortMember());
                    memberIds.set(memberIds.indexOf(userArena.getUserId()), 0);
                    if (userArena.getUserId() == arenaTeam.getUserId()) { // chuyển vị trí chủ team
                        for (int i = 0; i < memberIds.size(); i++) {
                            if (memberIds.get(i) != 0) {
                                arenaTeam.setUserId(memberIds.get(i));
                                break;
                            }
                        }
                    }
                    if (dao.leaveMember(teamId, userArena.getUserId(), StringHelper.toDBString(memberIds), arenaTeam.getUserId())) {
                        arenaTeam.setNumberMember(arenaTeam.getNumberMember() - 1);
                        arenaTeam.setSortMember(StringHelper.toDBString(memberIds));
                        userArena.setTeamId(0L);
                        if (arenaTeam.getNumberMember() == 0)  // xóa team
                            dao.destroyTeam(teamId);
                        else updateTeamPower(teamId);
                        serviceResult.success = true;
                        UserEntity joinUser = UserOnline.getDbUser(userArena.getUserId());
                        if (joinUser != null) {
                            Actions.save(joinUser, "three_star", "leave", "id", arenaTeam.getId(), "number", arenaTeam.getNumberMember());
                        }
                    } else serviceResult.setMessage(Lang.err_system_down);
                } else serviceResult.setMessage(Lang.err_params);
            } else serviceResult.setMessage(languageKey);
        }));
    }

    @Override
    public BattleTeam getMemberBattleTeam(MyUser mUser) {
        BattleTeam team = UserTeam.getBattleTeam(mUser.getUser().getId(), TeamType.ARENA_THREE_STAR_DEF);
        if (team == null) {
            team = UserTeam.getBattleTeam(mUser.getUser().getId(), TeamType.CAMPAIGN);
        }
        return team;
    }

    @Override
    public Pbmethod.BigListCommonVector.Builder toProtoTeam(List<UserArenaThreeStarTeam> teams) {
        List<UserArenaThreeStar> userArenas = dao.getListUserArenaByTeamId(teams.stream().filter(UserArenaThreeStarTeam::isNotFakeTeam)
                .map(UserArenaThreeStarTeam::getId).collect(Collectors.toList()));
        Pbmethod.BigListCommonVector.Builder builder = Pbmethod.BigListCommonVector.newBuilder();
        for (UserArenaThreeStarTeam team : teams) {
            if (team.getId() > 0) {
                var members = userArenas.stream().filter(ua -> ua.getTeamId().longValue() == team.getId().longValue()).collect(Collectors.toList());
                Pbmethod.ListCommonVector.Builder listVector = Pbmethod.ListCommonVector.newBuilder();
                var sorting = GsonUtil.strToListInt(team.getSortMember());
                for (Integer userId : sorting) {
                    UserArenaThreeStar member = members.stream().filter(ua -> ua.getUserId() == userId).findFirst().orElse(null);
                    if (member == null) {
                        listVector.addAVector(Pbmethod.CommonVector.newBuilder().addALong(0).addALong(0).addALong(0));
                    } else {
                        UserEntity user = UserOnline.getDbUser(member.getUserId());
                        listVector.addAVector(Pbmethod.CommonVector.newBuilder().addALong(member.getUserId()).addALong(member.getPower()).addALong(user.getLevel()).addAllALong(ListUtil.convertLstIntToLong(user.getListAvatar())).addAString(user.getName()).addAString(CfgServer.getServerName(user.getServer())));
                    }
                }
                listVector.addAVector(0, Pbmethod.CommonVector.newBuilder()
                        .addALong(team.getId()).addALong(members.size()).addALong(team.getPower()).addALong(team.getRequiredPower()).addALong(team.getRequiredApprove())
                        .addALong(0).addALong(team.getPoint()).addALong(team.getRank())
                        .addAString(team.getName()));
                builder.addBigVector(listVector);
            } else {
                builder.addBigVector(Pbmethod.ListCommonVector.newBuilder()
                        .addAVector(Pbmethod.CommonVector.newBuilder())
                        .addAVector(Pbmethod.CommonVector.newBuilder())
                        .addAVector(Pbmethod.CommonVector.newBuilder())
                        .addAVector(Pbmethod.CommonVector.newBuilder())
                );
            }
        }
        return builder;
    }

    @Override
    public ServiceResult<Pbmethod.CommonVector> createTeam(MyUser mUser, UserArenaThreeStar userArena, Pbmethod.CommonVector cmm) {
        int powerRequired = (int) cmm.getALong(0);
        int approveRequired = (int) cmm.getALong(1) == 0 ? 0 : 1;
        String name = cmm.getAString(0);

        if (powerRequired < 0 || powerRequired > 2_000_000_000) return ServiceResult.error(Lang.err_params);
        if (userArena.getTeamId() > 0) return ServiceResult.error(Lang.arena_team_leave_first);

        UserArenaThreeStarTeam team = new UserArenaThreeStarTeam(mUser.getUser().getId(), state.getCluster(mUser.getUser().getServer()), name, powerRequired, approveRequired);
        team.setPower(userArena.getPower());
        team.setSortMember("[%s,0,0]".formatted(mUser.getUser().getId()));
        if (dao.createTeam(team, userArena)) {
            userArena.setTeamId(team.getId());
            Actions.save(mUser.getUser(), "three_star", "create", "id", team.getId(), "power", powerRequired, "approve", approveRequired);
            return ServiceResult.success(Pbmethod.CommonVector.newBuilder().addALong(team.getId()).build());
        }
        return ServiceResult.error(Lang.err_system_down);
    }

    @Override
    public BattleTeam getTeamBattleTeam(UserArenaThreeStarTeam team, List<Integer> userIds) {
        HeroInfoEntity[] aHero = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE * 3];
        UserPetEntity[] aPet = new UserPetEntity[3];
        int index = 0;
        for (int i = 0; i < userIds.size(); i++) {
            int userId = userIds.get(i);
            BattleTeam battleTeam = UserTeam.getBattleTeam(userId, TeamType.ARENA_THREE_STAR_DEF);
            for (HeroInfoEntity hero : battleTeam.getAHero()) {
                aHero[index++] = hero;
            }
            aPet[i] = battleTeam.getAPet()[0];
        }
        return BattleTeam.builder().aHero(aHero).aPet(aPet).build();
    }

    @Override
    public List<Long> getListPower(List<Integer> userIds) {
        List<Long> listPower = new ArrayList<>();
        for (Integer userId : userIds) {
            BattleTeam battleTeam = UserTeam.getBattleTeam(userId, TeamType.ARENA_THREE_STAR_DEF);
            listPower.add(battleTeam != null ? battleTeam.getPower() : 0);
        }
        return listPower;
    }

    @Override
    public List<String> getListName(List<Integer> userIds) {
        List<String> listName = new ArrayList<>();
        for (Integer userId : userIds) {
            UserEntity user = UserOnline.getDbUser(userId);
            listName.add(user != null ? user.getName() : "");
        }
        return listName;
    }

    @Override
    public boolean updateTeamPower(long teamId) {
        if (teamId == 0) return false;
        return dao.updateTeamPower(teamId);
    }

    @Override
    public int calculateWinPoint(int atkPoint, int defPoint, boolean isWin) {
        int weight = atkPoint - defPoint;
        if (isWin) {
            float point1 = -0.0362f * weight + 26;
            point1 = (float) Math.ceil(point1);
            if (point1 <= 5) point1 = 5;
            if (point1 > 90) point1 = 90;
            return (int) point1;
        }
        return 0;
    }

    @Override
    public int getRank(UserArenaThreeStarTeam team) {
        var rank = dao.getRank(team.getEventId(), team.getCluster(), team.getPoint());
        return rank == null ? 0 : rank;
    }

    /**
     * Khi có lời mời gia nhập đội -> prepared day - 69
     * Khi có yêu cầu gia nhập đội -> prepared day - 70
     * khi có quà có thể nhận -> battle day - 71
     * khi còn lượt có thể đánh - 68
     * khi chưa ghi danh đội -> prepared day - 68
     *
     * @param mUser
     * @param cmm
     */
    @Override
    public void checkNotify(MyUser mUser, Pbmethod.CommonVector.Builder cmm) {
        try {
            List<Long> values = new ArrayList<>();
            UserArenaThreeStar userArenaThreeStar = getUserArena(mUser.getUData().getUserId());
            if (state.isPreparedDay()) {
                if (userArenaThreeStar.getTeamId() > 0) {
                    UserArenaThreeStarTeam team = getArenaTeamById(userArenaThreeStar.getTeamId());
                    if (team != null && team.getUserId() == mUser.getUser().getId()) {
                        if (team.getNumberMember() == 3 && team.getAutoMerge() == -1)
                            values.add((long) NotifyType.THREE_STAR.value);
                        if (team.getNumberMember() < 3 && !team.listUserIdRequest().isEmpty())
                            values.add((long) NotifyType.THREE_STAR_HAS_REQUEST.value);
                        if (team.getNumberMember() < 3)
                            values.add((long) NotifyType.THREE_STAR_TEAM_NOT_FULL.value);
                    }
                } else {
                    if (userArenaThreeStar.getTeamId() == 0) values.add((long) NotifyType.THREE_STAR_UN_JOIN_TEAM.value);
                    if (!userArenaThreeStar.getListRequest().isEmpty())
                        values.add((long) NotifyType.THREE_STAR_HAS_INVITE.value);
                }
            } else if (state.isBattleDay() && userArenaThreeStar.getTeamId() > 0) {
                UserArenaThreeStarTeam team = getArenaTeamById(userArenaThreeStar.getTeamId());
                if (team != null) {
                    int timeKey = ConfigArenaTeam.getOpponentTimeKey();
                    UserArenaThreeStarOpponent opponent = dao.getOpponent(team.getId(), timeKey);
                    if (opponent != null) {
                        int myIndex = team.getMyIndex(mUser.getUser().getId());
                        List<Long> oppIds = opponent.getListOpponentId();
                        List<Integer> oppResults = opponent.getListOpponentResult();
                        for (int i = 0; i < oppIds.size(); i++) {
                            if (oppResults.get(i + myIndex) == 0) {
                                values.add((long) NotifyType.THREE_STAR.value);
                                break;
                            }
                        }
                    }
                }
            }
            if (!values.isEmpty() && !values.contains((long) NotifyType.THREE_STAR.value))
                values.add((long) NotifyType.THREE_STAR.value);
            cmm.addAllALong(values);
        } catch (Exception ex) {
            Logs.error("userId=%s".formatted(mUser.getUser().getId()), ex);
        }
    }

}
