package monster.game.arena.service.impl;

import com.google.inject.Inject;
import grep.helper.GsonUtil;
import monster.config.CfgArenaCrystal;
import monster.config.CfgArenaSwap;
import monster.dao.ArenaDAO;
import monster.game.arena.entity.UserArenaCrystalEntity;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.main.ResBotCrystalEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.game.arena.service.ArenaState;
import monster.object.BattleTeam;
import monster.object.RankController;
import monster.service.monitor.ClanMonitor;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResBot;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class SwapState extends ArenaState {

    Set<Integer> setServerId = new HashSet<>();
    @Inject
    ArenaDAO arenaDAO;

    public SwapState() {
        keyCacheRank = "rank:swap";
    }

    @Override
    public void cacheTop() {
        List<Integer> serverIds = setServerId.stream().toList();
        for (Integer serverId : serverIds) {
            cacheTop(serverId);
        }
    }

    private void cacheTop(int serverId) {
        List<String> topUser = getRankController(serverId).getTop();
        if (topUser != null && !topUser.isEmpty()) {
            Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
            for (String tuple : topUser) {
                Integer userId = Integer.parseInt(tuple);
                UserArenaCrystalEntity userArena = CfgArenaCrystal.getArenaCrystal(userId);
                if (userArena == null) continue;

                if (userId < 0) {
                    builder.addAUser(pbUserInfoForBot(userArena));
                    continue;
                }

                UserEntity user = UserOnline.getDbUser(userArena.getUserId());
                if (user == null) continue;

                builder.addAUser(pbUserInfo(userArena, user));
            }
            mTop.put(serverId, builder.build());
        }
    }

    @Override
    public Pbmethod.PbListUser getTopSeason(int serverId) {
        if (!mTop.containsKey(serverId))
            cacheTop(serverId);
        return mTop.get(serverId);
    }

    @Override
    public void checkEventId() {
        if (eventId != CfgArenaCrystal.getEventId()) {
            //            initRankController(CfgArenaCrystal.getEventId());
            setServerId.clear();
            this.eventId = CfgArenaSwap.getEventId();
        }
    }

    @Override
    public synchronized RankController getRankController(int serverId) {
        //        if (!setServerId.contains(serverId)) {
        //            initRankController(serverId, eventId);
        //            setServerId.add(serverId);
        //            if (arenaDAO.isBotCrystalNotExist(eventId, serverId)) {
        //                ResBot.removeAndAddBotCrystal(serverId);
        //            }
        //        }
        //        return mRankController.get(serverId);
        return null;
    }

    private void initRankController(int serverId, int eventId) {
        //        String redisKey = CfgServer.isTestServer() ? String.format("test:%s:%s:%s", keyCacheRank, serverId, eventId) : String.format("%s:%s:%s", keyCacheRank, serverId, eventId);
        //        //        RankController rankController = RankController.builder().serverId(serverId).numberTop(100).key(redisKey).defaultPoint(CfgArenaCrystal.DEFAULT_POINT)
        //        //                .sql(String.format("select user_id, point from " + CfgArenaCrystal.getTable() + " where server_id=%s and event_id=%s and is_team_null=0 order by point desc limit 0,%s", serverId, eventId, CfgArenaCrystal.config.maxRank)).build();
        //        RankController rankController = RankController.builder().serverId(serverId).numberTop(50).key(redisKey)
        //                .sql(String.format("select user_id, point from user_arena_crystal where event_id=%s and server_id=%s order by point desc limit 0,%s",
        //                        CfgArenaCrystal.getEventId(), serverId, CfgArenaCrystal.config.maxRank)).build();
        //        rankController.init();
        //        mRankController.put(serverId, rankController);
    }

    private Pbmethod.PbUser.Builder pbUserInfo(UserArenaCrystalEntity userArena, UserEntity user) {
        Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());
        return pbUser;
    }

    private Pbmethod.PbUser.Builder pbUserInfoForBot(UserArenaCrystalEntity userArena) {
        ResBotCrystalEntity resBotEntity = ResBot.getBotCrystal(userArena.getResBotId());
        Pbmethod.PbUser.Builder pbUser = Pbmethod.PbUser.newBuilder();
        pbUser.setId(userArena.getUserId());
        pbUser.setName(resBotEntity.getName());
        int monsterId = GsonUtil.strToListInt(ResBot.getBotCrystal(userArena.getResBotId()).getTeam()).get(0);
        ResMonsterEntity monster = ResMonster.getMonster(monsterId);
        ResHeroEntity resHero = ResHero.getHero(monster.getHeroLinkNew());
        pbUser.addAllAvatar(Arrays.asList(0, resHero.getAvatar(), 3, -1));
        pbUser.setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(0).build());
        pbUser.setClanInfo(Pbmethod.CommonVector.newBuilder().addAString("").addALong(0).addALong(0)
                .addALong(ClanMonitor.getClanAvatar(0)).build());
        pbUser.setPower(userArena.getPower());
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());
        return pbUser;
    }
}
