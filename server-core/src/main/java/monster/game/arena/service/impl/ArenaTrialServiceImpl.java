package monster.game.arena.service.impl;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import monster.config.CfgArenaTrial;
import monster.config.penum.TeamType;
import monster.dao.ArenaDAO;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserPetEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.game.arena.service.ArenaState;
import monster.game.arena.entity.UserArenaTrialEntity;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.object.RankController;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.game.arena.service.ArenaService;
import monster.service.monitor.ClanMonitor;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResBot;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import javax.inject.Inject;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ArenaTrialServiceImpl extends ArenaService {

    @Inject
    ArenaDAO arenaDAO;

    public ArenaTrialServiceImpl() {
        this.state = Guice.getInstance(ArenaState.class, "trial");
        this.rankType = RankController.arenaTrial;
    }

    @Override
    public <T> void updateDefTeam(MyUser mUser) {
        UserEntity user = mUser.getUser();
        UserArenaTrialEntity userArena = CfgArenaTrial.getArenaTrial(user.getId());
        if (userArena != null) {
            BattleTeam defTeam = userArena.getDefTeamEntity();
            if (defTeam != null) {
                HeroInfoEntity[] aHero = defTeam.getAHero();
                UserPetEntity[] aPet = defTeam.getAPet();
                List<Long> heroIds = new ArrayList<>();

                for (int i = 0; i < aHero.length; i++) {
                    HeroInfoEntity hero = aHero[i];
                    heroIds.add(hero == null ? 0L : hero.id);
                    if (i == BattleConfig.TEAM_HERO_SIZE - 1) { // end of team -> add pet
                        UserPetEntity pet = aPet[i / BattleConfig.TEAM_HERO_SIZE];
                        heroIds.add(pet == null ? 0L : pet.getPetId());
                    }
                }

                BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
                if (myTeam != null && myTeam.getAHero() != null) {
                    aHero = myTeam.getAHero();
                    boolean isOk = false;
                    for (int i = 0; i < 6; i++) {
                        if (aHero[i] != null) {
                            isOk = true;
                            break;
                        }
                    }
                    if (!isOk) {
                        return;
                    }
                    //
                    isOk = false;
                    for (int i = 6; i < 12; i++) {
                        if (aHero[i] != null) isOk = true;
                    }
                    if (!isOk) {
                        return;
                    }
                    //
                    isOk = false;
                    for (int i = 12; i < 18; i++) {
                        if (aHero[i] != null) isOk = true;
                    }
                    if (!isOk) {
                        return;
                    }

                    long power = myTeam.getPower();
                    String strHeroIds = Stream.of(aHero).filter(hero -> hero != null).map(hero -> String.valueOf(hero.id)).collect(Collectors.joining(","));
                    if (UserTeam.saveOrUpdateUserTeam(mUser.getUser(), TeamType.ARENA_TRIAL_DEF, myTeam, 0)
                            && Services.daoArena.updateArenaTrialDefTeam(mUser.getUser().getId(), power, strHeroIds)) {
                        userArena.setPower(power);
                    }
                }
            }
        }
    }

    @Override
    public Pbmethod.PbListUser topLastSeason(MyUser mUser) {
        int lastEventId = CfgArenaTrial.getLastEventId();
        List<UserArenaTrialEntity> aUserArena = DBJPA.getList("user_arena_trial", Arrays.asList("event_id", lastEventId, "server_id",
                mUser.getUser().getServer()), "order by point desc limit 50", UserArenaTrialEntity.class);
        UserArenaTrialEntity myArena = arenaDAO.getUserArenaTrial(lastEventId, mUser.getUser().getId());

        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        if (myArena != null) {
            builder.setMyInfo(mUser.getUser().protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(myArena.getPoint())));
            builder.setMyRank(myArena.getLastSeasonRank() == -1 ? 9999 : myArena.getLastSeasonRank());
        } else {
            builder.setMyInfo(mUser.getUser().protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(0)));
            builder.setMyRank(9999);
        }

        for (int i = 0; i < aUserArena.size(); i++) {
            UserArenaTrialEntity userArena = aUserArena.get(i);
            if (userArena.isBot()) {
                Pbmethod.PbUser.Builder pbUser = Pbmethod.PbUser.newBuilder();
                pbUser.setId(userArena.getUserId());
                pbUser.setName(ResBot.getBotTrial(userArena.getResBotId()).getName());
                int monsterId = GsonUtil.strToListInt(ResBot.getBotCrystal(userArena.getResBotId()).getTeam()).get(0);
                ResMonsterEntity monster = ResMonster.getMonster(monsterId);
                ResHeroEntity resHero = ResHero.getHero(monster.getHeroLinkNew());
                pbUser.addAllAvatar(Arrays.asList(0, resHero.getAvatar(), 3, -1));
                pbUser.setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(0).build());
                pbUser.setClanInfo(Pbmethod.CommonVector.newBuilder().addAString("").addALong(0).addALong(0).addALong(ClanMonitor.getClanAvatar(0)).build());
                pbUser.setPower(userArena.getPower());
                pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
                builder.addAUser(pbUser);
            } else {
                UserEntity user = UserOnline.getDbUser(userArena.getUserId());
                if (user != null)
                    builder.addAUser(user.protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint())));
            }
        }
        return builder.build();
    }

    @Override
    public Pbmethod.PbListUser topSeason(MyUser mUser) {
        Pbmethod.PbListUser.Builder builder = state.getTopSeason(mUser.getUser().getServer()).toBuilder();

        UserArenaTrialEntity userArena = getUserArena(mUser);
        builder.setMyInfo(mUser.getUser().protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint())));
        builder.setMyRank(getRank(mUser.getUser().getServer(), userArena.getUserId()));

        return builder.build();
    }

    @Override
    public <T> void updateIsTeamDefNull(MyUser mUser, T object) {
        UserArenaTrialEntity userArena = (UserArenaTrialEntity) object;
        userArena.setIsTeamNull(0);
        DBJPA.update("user_arena_trial", List.of("is_team_null", 0), List.of("user_id", userArena.getUserId(), "event_id", userArena.getEventId()));
    }

    @Override
    public UserArenaTrialEntity getUserArena(MyUser mUser) {
        return CfgArenaTrial.getArenaTrial(mUser.getUser().getId());
    }

    @Override
    public List<UserArenaTrialEntity> findOpponent(MyUser mUser, Object object) {
        UserArenaTrialEntity userArena = (UserArenaTrialEntity) object;
        List<UserArenaTrialEntity> aOpp = arenaDAO.trialFindOpponent(userArena.getServerId(), userArena.getPoint(), mUser.getUser().getPower());
        aOpp = aOpp.stream().filter(arena -> arena.getUserId() != mUser.getUser().getId()).collect(Collectors.toList());
        if (aOpp != null) {
            // remove duplicate
            for (int i = aOpp.size() - 1; i >= 0; i--) {
                for (int j = 0; j < i; j++) {
                    if (aOpp.get(i).getUserId() == aOpp.get(j).getUserId()) {
                        aOpp.remove(i);
                        break;
                    }
                }
            }

            // remove if quá nhiều :D
            if (aOpp.size() > 12) {
                aOpp.sort(Comparator.comparingLong(UserArenaTrialEntity::getPower).reversed());
                while (aOpp.size() > 12) {
                    int index = new Random().nextInt(aOpp.size());
                    if (index > 3) aOpp.remove(index);
                }
            }
            return aOpp;
        }
        return null;
    }
}
