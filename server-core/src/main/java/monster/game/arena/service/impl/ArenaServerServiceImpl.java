package monster.game.arena.service.impl;

import com.google.inject.Inject;
import grep.helper.DateTime;
import grep.helper.NumberUtil;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.config.penum.NotifyType;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterTeam;
import monster.game.arena.config.ConfigArenaServer;
import monster.game.arena.dao.ArenaServerDAO;
import monster.game.arena.entity.*;
import monster.game.arena.service.ArenaServerService;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.service.aop.CacheInGame;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.IMath;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;

import java.util.Calendar;
import java.util.List;

public class ArenaServerServiceImpl implements ArenaServerService {

    @Inject
    ArenaServerDAO arenaServerDAO;
    @Inject
    ArenaServerState arenaServerState;

    @Override
    public UserArenaServer getArenaServer(MyUser mUser) {
        int eventId = arenaServerState.getEventId(mUser.getUser().getServer());
        var ett = arenaServerDAO.getArenaServer(mUser.getUser().getId(), eventId);
        if (ett == null) {
            ett = new UserArenaServer(mUser.getUser(), eventId);
            if (!arenaServerDAO.save(ett)) return null;
        }
        return ett;
    }

    @Override
    public UserArenaServerQualifyEntity getArenaServerQualify(MyUser mUser, int round) {
        return arenaServerDAO.getArenaServerQualify(mUser.getUser().getId(), arenaServerState.getEventId(mUser.getUser().getServer()), round);
    }

    @Override
    public long calculateRankingPoint(int point) {
        Calendar calendar = Calendar.getInstance();
        long time = DateTime.DAY_SECOND - (calendar.get(Calendar.HOUR_OF_DAY) * DateTime.HOUR_SECOND + +calendar.get(Calendar.MINUTE) * 60 + calendar.get(Calendar.SECOND));
        return point * NumberUtil.million + time;
    }

    @Override
    public long getQualifyUserRank(UserArenaServerQualifyEntity userArenaServerQualify) {
        return arenaServerDAO.getRank(userArenaServerQualify.getEventId(), userArenaServerQualify.getRound(), userArenaServerQualify.getCluster(), userArenaServerQualify.getPoint());
    }

    @Override
    public void buffBattleTeam(MyUser mUser, BattleTeam team) {
        for (int i = 0; i < team.getAHero().length; i++) {
            HeroInfoEntity heroInfo = team.getAHero()[i];
            if (heroInfo != null) {
                ResHeroEntity resHero = ResHero.getHero(heroInfo.heroId);
                if (resHero != null && ConfigArenaServer.realBuffAvatar.contains(resHero.getAvatar())) {
                    UserHeroEntity userHero = mUser.getResources().getHero(heroInfo.id);
                    if (userHero != null) {
                        UserHeroEntity cloner = userHero.cloneHero();
                        IMath.calculateAll(mUser, cloner, cloner.getListSkillIds());
                        team.getAHero()[i] = cloner.toHeroInfo(heroInfo.team, heroInfo.position);
                    }
                }
            }
        }
    }

    @Override
    public List<UserArenaServerTop> topLastEvent(MyUser mUser) {
        int minEventId = Math.max(arenaServerState.getEventId(mUser.getUser().getServer()) - 10, CfgServer.isTestServer() ? 0 : 1);
        return arenaServerDAO.getListUserArenaServerTop(minEventId);
    }

    @Override
    public List<UserArenaServerLike> getUserLikeTop(MyUser mUser) {
        int minEventId = Math.max(arenaServerState.getEventId(mUser.getUser().getServer()) - 10, CfgServer.isTestServer() ? 0 : 1);
        return arenaServerDAO.getListUserArenaServerLike(mUser.getUser().getId(), minEventId);
    }

    @Override
    public UserEntity getUserEntity(int userId) {
        if (userId < 0) { // bot
            ResMonsterTeam team = ResMonster.getMonsterTeam(Math.abs(userId));
            return UserEntity.builder().id(userId).server(-1).name(team.getMonsterId()).username("username_" + Math.abs(userId)).name(team.getName()).avatar(team.getAvatar()).level(200).power(team.getPower()).clanName("").build();
        }
        return UserOnline.getDbUser(userId);
    }

    @CacheInGame(expire = 120)
    @Override
    public long getNumberBetSuccess(MyUser mUser) {
        List<UserArenaServerBet> userBets = arenaServerDAO.getListUserBet(arenaServerState.getEventId(mUser.getUser().getServer()), mUser.getUser().getId());
        return userBets.stream().filter(bet -> bet.getResult() == 1).count();
    }

    @Override
    public void checkNotify(MyUser mUser, protocol.Pbmethod.CommonVector.Builder cmm) {
        try {
            int roundId = arenaServerState.getRoundId();
            if (roundId == 0) {
                UserArenaServer userArenaServer = getArenaServer(mUser);
                if (userArenaServer.getPower() == 0) cmm.addALong(NotifyType.ARENA_SERVER_UNREGISTER.value);
            } else if (roundId == 5) {
                List<UserArenaServerBet> bets = arenaServerDAO.getListUserBet(arenaServerState.getEventId(mUser.getUser().getServer()), mUser.getUser().getId());
                int numberBet = List.of(8, 12, 14, 15).get(arenaServerState.getFinalState() - 1);
                if (bets.size() < numberBet) cmm.addALong(NotifyType.ARENA_SERVER_DU_DOAN.value);
            }
            // like ???
            if (arenaServerDAO.getNumberLike(mUser.getUser().getId()) < arenaServerState.getEventId(mUser.getUser().getServer()))
                cmm.addALong(NotifyType.ARENA_SERVER_UNLIKE.value);
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }
}
