package monster.game.arena.service;

import monster.object.RankController;

import java.util.HashMap;
import java.util.Map;

public abstract class ArenaState {

    public int eventId = 0;
    protected Map<String, RankController> mRankController = new HashMap<>();
    protected String keyCacheRank = "";
    protected Map<Integer, protocol.Pbmethod.PbListUser> mTop = new HashMap<>();

    public abstract void cacheTop();

    public abstract protocol.Pbmethod.PbListUser getTopSeason(int serverId);

    public abstract void checkEventId();

    public abstract RankController getRankController(int serverId);

    public void checkDailyBonus() {

    }

    public void checkSeasonBonus() {

    }

    protected String getRankControllerRedisKey(int eventId, int serverId) {
        return String.format("%s_%s", eventId, serverId);
    }

    //    public void checkUpdatePoint(int userId, int point) {
    //        String redisKey = String.format("%s:%s", eventId, keyCacheRank);
    //        Double score = JCache.getInstance().zscore(redisKey, String.valueOf(userId));
    //        if (score == null) {
    //            rankController.updatePoint(userId, point);
    //        }
    //    }
}
