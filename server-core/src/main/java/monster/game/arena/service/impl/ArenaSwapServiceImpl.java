package monster.game.arena.service.impl;

import grep.database.DBJPA;
import grep.helper.NumberUtil;
import monster.config.CfgArenaCrystal;
import monster.config.CfgArenaSwap;
import monster.config.penum.TeamType;
import monster.dao.ArenaDAO;
import monster.game.arena.entity.UserArenaCrystalEntity;
import monster.game.arena.entity.UserArenaSwapEntity;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserPetEntity;
import monster.game.arena.service.ArenaState;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.object.RankController;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.game.arena.service.ArenaService;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResBot;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ArenaSwapServiceImpl extends ArenaService {

    @Inject
    ArenaDAO arenaDAO;

    public ArenaSwapServiceImpl() {
        this.state = Guice.getInstance(ArenaState.class, "swap");
        this.rankType = RankController.arenaCrystal;
    }

    @Override
    public <T> void updateDefTeam(MyUser mUser) {
        UserEntity user = mUser.getUser();
        UserArenaCrystalEntity userArena = CfgArenaCrystal.getArenaCrystal(user.getId());
        if (userArena != null) {
            BattleTeam defTeam = userArena.getDefTeamEntity();
            if (defTeam != null) {
                HeroInfoEntity[] aHero = defTeam.getAHero();
                UserPetEntity[] aPet = defTeam.getAPet();
                List<Long> heroIds = new ArrayList<>();
                for (HeroInfoEntity hero : aHero)
                    heroIds.add(hero == null ? 0L : hero.id);
                for (UserPetEntity pet : aPet)
                    heroIds.add(pet == null ? 0L : pet.getPetId());

                BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
                if (myTeam != null && myTeam.getAHero() != null) {
                    boolean isOk = false;
                    for (HeroInfoEntity hero : myTeam.getAHero())
                        if (hero != null) isOk = true;

                    if (isOk) {
                        long power = myTeam.getPower();
                        String strHeroIds = Stream.of(myTeam.getAHero()).filter(hero -> hero != null).map(hero -> String.valueOf(hero.id)).collect(Collectors.joining(","));
                        if (UserTeam.saveOrUpdateUserTeam(mUser.getUser(), TeamType.ARENA_CRYSTAL_DEF, myTeam, 0)
                                && Services.daoArena.updateArenaCrystalDefTeam(mUser.getUser().getId(), power, strHeroIds)) {
                            userArena.setPower(power);
                        }
                    }
                }
            }
        }
    }

    @Override
    public Pbmethod.PbListUser topLastSeason(MyUser mUser) {
        List<UserArenaSwapEntity> aUserArena = DBJPA.getList("user_arena_swap", Arrays.asList("cluster",
                CfgArenaSwap.getCluster(mUser.getUser().getServer())), "order" +
                " by last_rank asc limit 50", UserArenaCrystalEntity.class);
        UserArenaSwapEntity myArena = DBJPA.getUnique("user_arena_swap", UserArenaSwapEntity.class, "user_id", mUser.getUser().getId());

        Pbmethod.PbListUser.Builder builder = Pbmethod.PbListUser.newBuilder();
        if (myArena != null) {
            builder.addAUser(mUser.getUser().protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(myArena.getUserRank())));
            builder.setMyRank(myArena.getUserRank());
        } else {
            builder.addAUser(mUser.getUser().protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(0)));
            builder.setMyRank(9999);
        }

        for (int i = 0; i < aUserArena.size(); i++) {
            UserArenaSwapEntity userArena = aUserArena.get(i);
            UserEntity user = UserOnline.getDbUser(userArena.getUserId());
            if (user != null)
                builder.addAUser(user.protoTinyUser().setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getUserRank())));
        }
        return builder.build();
    }

    @Override
    public Pbmethod.PbListUser topSeason(MyUser mUser) {
        return null;
    }

    @Override
    public <T> void updateIsTeamDefNull(MyUser mUser, T t) {
        // nothing here
    }

    @Override
    public <T> T getUserArena(MyUser mUser) {
        return null;
    }

    @Override
    public List<UserArenaSwapEntity> findOpponent(MyUser mUser, Object object) {
        UserArenaSwapEntity userArena = (UserArenaSwapEntity) object;
        List<Integer> rankOpp = getRankOpp(userArena.getUserRank());
        if (rankOpp.isEmpty()) return new ArrayList<>();
        List<UserArenaSwapEntity> opponents = arenaDAO.swapFindOpponent(userArena.getCluster(), rankOpp);
        for (Integer rank : rankOpp) {
            UserArenaSwapEntity matchOpp = opponents.stream().filter(opp -> opp.getUserRank() == rank).findFirst().orElse(null);
            if (matchOpp == null) { // cần thêm bot vô đây
                int botId = rank; //Math.min(ResBot.mBotSwap.size(), rank);
                UserArenaSwapEntity bot = new UserArenaSwapEntity(-botId, userArena.getServerId());
                bot.setUserRank(rank);
                opponents.add(bot);
            }
        }
        return opponents;
    }

    private List<Integer> getRankOpp(int curRank) {
        if (curRank == 1) return new ArrayList<>();
        List<Integer> aInt = new ArrayList<>();
        int startRank = 1;
        if (curRank <= 20) startRank = 1;
        else if (curRank <= 40) startRank = 10;
        else if (curRank <= 100) startRank = 20;
        else if (curRank <= 200) startRank = 50;
        else startRank = 100;
        for (int i = 0; i < 3; i++) {
            int rank = NumberUtil.getRandom(startRank, curRank - 1);
            if (!aInt.contains(rank)) aInt.add(rank);
        }
        return aInt.stream().distinct().collect(Collectors.toList());
    }
}
