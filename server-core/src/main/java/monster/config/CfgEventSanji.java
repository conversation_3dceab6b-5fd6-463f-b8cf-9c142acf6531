package monster.config;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Getter;
import monster.cache.redis.JCache;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import monster.controller.EventSanjiHandler;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserEventSanjiEntity;
import monster.dao.mapping.UserMailEntity;
import monster.object.RankController;
import monster.server.AppInit;
import monster.service.monitor.UserOnline;
import monster.service.monitor.UserOnline;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.task.dbcache.MailCreatorCache;

import jakarta.persistence.EntityManager;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class CfgEventSanji implements Serializable {
    public static DataConfig config;
    public static Map<Integer, DishObject> mDish = new HashMap<>();
    public static Map<Long, List<Long>> mPointLevelVuiVe = new HashMap<>();
    public static List<Long> aPointVuiVe = new ArrayList<>();
    public static Map<Integer, BonusTopObject> mBonusTop = new HashMap<>();
    public static long maxPointLevelVuiVe;

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        init();
    }

    public class DataConfig {
        public String timeOpenEvent;
        public String timeEndEvent;
        public List<DishObject> dishs;
        public List<LevelVuiVe> vuives;
        public List<BonusTopObject> bonusTop;
        public List<ItemChef> itemsChef;
    }

    public static void init() {
        mDish.clear();
        mPointLevelVuiVe.clear();
        mBonusTop.clear();
                aPointVuiVe.clear();
        config.dishs.forEach(dish -> mDish.put(dish.id, dish));
        maxPointLevelVuiVe = config.vuives.get(0).point;
        config.vuives.forEach(v -> {
            mPointLevelVuiVe.put(v.point, v.bonus);
            aPointVuiVe.add(v.point);
            if (v.point > maxPointLevelVuiVe) maxPointLevelVuiVe = v.point;
        });
        Collections.sort(aPointVuiVe);
        config.bonusTop.forEach(v -> mBonusTop.put(v.top, v));
    }

    @Getter
    public class DishObject {
        private int id;
        private int numVuiVe;
        private int numThia;
        private int timeCountDown;
        private int feeSend;
        private int feeSkip;

        public long getTimeCountDown() {
            return timeCountDown * DateTime.HOUR_SECOND;
        }
    }

    @Getter
    public class LevelVuiVe {
        private int level;
        private long point;
        private List<Long> bonus;
    }

    @Getter
    public class BonusTopObject {
        private int top;
        private long point;
        private List<Long> bonus;
    }

    @Getter
    public class ItemChef {
        private int id;
        private List<Long> item;
        private List<Long> price;
        private int limit;
    }

    //region Cache UserBossServer
    private static Map<Integer, LoadingCache<Integer, UserEventSanjiEntity>> mCache = new HashMap<>();

    private static synchronized LoadingCache<Integer, UserEventSanjiEntity> getCache(int serverId) {
        if (!mCache.containsKey(serverId)) {
            mCache.put(serverId, CacheBuilder.newBuilder().maximumSize(3000).expireAfterAccess(1, TimeUnit.HOURS).build(new CacheLoader<Integer, UserEventSanjiEntity>() {
                @Override
                public UserEventSanjiEntity load(Integer userId) throws Exception {
                    EntityManager session = null;
                    try {
                        session = DBJPA.getEntityManager();
                        List<UserEventSanjiEntity> aUser = session.createNativeQuery(
                                "select * from dson.user_event_sanji where user_id=" + userId + " and server_id=" + serverId, UserEventSanjiEntity.class).getResultList();
                        if (aUser.isEmpty()) {
                            UserEventSanjiEntity userEventSan = new UserEventSanjiEntity(userId, serverId);
                            session.getTransaction().begin();
                            session.persist(userEventSan);
                            session.getTransaction().commit();
                            return userEventSan;
                        }
                        return aUser.get(0);
                    } catch (Exception ex) {
                        Logs.error(GUtil.exToString(ex));
                    } finally {
                        DBJPA.closeSession(session);
                    }
                    return null;
                }
            }));
        }
        return mCache.get(serverId);
    }

    public static UserEventSanjiEntity getUserEventSanji(int serverId, int userId) {
        try {
            return getCache(serverId).get(userId);
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }

    public static boolean isOpen() {
        return DateTime.afterDate(config.timeOpenEvent) && DateTime.beforeDate(config.timeEndEvent);
    }

    public static long getCountDownToCloseEvent() {
        try {
            long endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(config.timeEndEvent).getTime();
            return (endTime - System.currentTimeMillis()) / 1000;
        } catch (Exception e) {
            Logs.error(e);
        }

        return 0;
    }

    public static String getDefaultBonusStatus() {
        List<Integer> aInt = new ArrayList<>();
        for (int i = 0; i < aPointVuiVe.size(); i++) {
            aInt.add(0);
        }

        return StringHelper.toDBString(aInt);
    }

    private static List<UserEventSanjiEntity> getTopUserOfEventSanji(int numberTopLimit) {
        List<UserEventSanjiEntity> aUser = new ArrayList<>();
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            aUser = session.createNativeQuery(
                    "select * from dson.user_event_sanji order by point_vui_ve desc limit " + numberTopLimit, UserEventSanjiEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return aUser;
    }

    public static void sendRewardForTop(DataConfig config, CfgServer.DataConfig configServer) {
        List<UserEventSanjiEntity> topUserOfEventSanji = getTopUserOfEventSanji(config.bonusTop.size());
        int currentBonusTop = 1;
        try {
            AppInit.initAll();
            for (int i = 0; i < topUserOfEventSanji.size(); i++) {
                UserEventSanjiEntity userEventSanji = topUserOfEventSanji.get(i);
                for (BonusTopObject bonusTopObject : config.bonusTop) {
                    if (bonusTopObject.top < currentBonusTop) continue;

                    if (userEventSanji.getPointVuiVe() < bonusTopObject.point) continue;

                    List<Long> aBonus = bonusTopObject.bonus;
                    Actions.save(userEventSanji.getServerId(), userEventSanji.getUserId(), "event_sanji", "mail_season",
                            "top", bonusTopObject.top,
                            "bonus", new Gson().toJson(aBonus));
                    String title = String.format(Lang.getTitle("title_event_sanji", configServer.mainLanguage));
                    String message = String.format(Lang.getTitle("content_event_sanji", configServer.mainLanguage), i + 1, bonusTopObject.top);
                    MailCreatorCache.sendMail(UserMailEntity.builder().userId(userEventSanji.getUserId())
                            .title(title).message(message).bonus(aBonus.toString()).origin("sanji")
                            .build());
                    currentBonusTop = bonusTopObject.top + 1;
                    break;
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public static int getEventId() {
        try {
            SimpleDateFormat simpleDateFormat = DateTime.getSDFFullDate();
            Date openDate = simpleDateFormat.parse(config.timeOpenEvent);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            return Integer.parseInt(sdf.format(openDate));
        } catch (Exception exception) {
            Logs.error(exception);
        }

        return 0;
    }
}
