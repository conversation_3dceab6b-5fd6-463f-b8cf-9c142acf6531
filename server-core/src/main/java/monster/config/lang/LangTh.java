package monster.config.lang;

import lombok.Data;

/**
 * Created by <PERSON><PERSON> on 5/4/2015.
 */
@Data
public class LangTh extends Lang {

    static LangTh mInstance;

    public static LangTh instance() {
        if (mInstance == null) {
            mInstance = new LangTh();
        }
        return mInstance;
    }

    public LangTh() {
        locale = LOCALE_TH;
    }

    public String get(String key) {
        return get(key, locale);
    }
}
