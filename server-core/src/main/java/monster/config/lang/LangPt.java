package monster.config.lang;

import lombok.Data;

@Data
public class LangPt extends Lang {

    static LangPt mInstance;

    public static LangPt instance() {
        if (mInstance == null) {
            mInstance = new LangPt();
        }
        return mInstance;
    }

    public LangPt() {
        locale = LOCALE_PT;
    }

    public String get(String key) {
        return get(key, locale);
    }
}
