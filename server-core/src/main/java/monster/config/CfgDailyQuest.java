package monster.config;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.Quest2Type;
import monster.config.penum.QuestType;
import monster.dao.mapping.*;
import monster.dao.mapping.main.ResQuest2Entity;
import monster.object.MyUser;
import monster.protocol.CommonProto;
import monster.server.IAction;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.common.QuestService;
import monster.service.user.Actions;
import net.sf.json.JSONObject;

import java.util.*;

public class CfgDailyQuest {
    public static List<ResQuest2Entity> aQuest2;
    private static Map<Integer, ResQuest2Entity> mQuest2 = new HashMap<>();
    public static JSONObject json;
    public static DataConfig config;

    public static void checkQuest2NotifyComplete(MyUser mUser, Quest2Type questType) {
        UserQuestEntity uQuest = Guice.getInstance(QuestService.class).getUserQuest(mUser);
        if (uQuest != null) {
            ResQuest2Entity resQuest = getResQuest(uQuest.getQuest2());
            if (resQuest != null && resQuest.getQuestType() == questType.value) {
                checkQuest2NotifyComplete(mUser, questType, uQuest, resQuest);
            }
        }
    }

    /**
     * Chú ý quest HERO_LEVEL -> cần hạn chế số lần check ntn bây giờ  : Tú: Em nghĩ nên để client check có cần update data hay k vì số lượng người
     * vào ra màn hình home nhiều, mà cứ ra là request, NV này có set theo type thì dựa vào type đó, client có thể biết được lúc nào cần gửi data, nếu k thì đọc
     * từ cache k cần ơhải request sv.
     * => cái này e đang query trực tiếp vào db nên khả năng là nặng :(
     *
     * @param mUser
     * @param questType
     * @param uQuest
     * @param resQuest
     */
    public static void checkQuest2NotifyComplete(MyUser mUser, Quest2Type questType, UserQuestEntity uQuest, ResQuest2Entity resQuest) {
        try {
            if (!uQuest.isNotifyCompleted() && getStatus(mUser, questType, uQuest, resQuest) == 2) {
                uQuest.setNotifyCompleted(true);
                mUser.getMsgNotify().add(CommonProto.getPbAction(IAction.QUESST2_DONE, CommonProto.getErrorMsg(mUser.getLang().get(Lang.quest_completed))));
            }
            int status = getStatus(mUser, questType, uQuest, resQuest);
            if (status == 2) uQuest.setQuest2Number(resQuest.getValue1());
            //protocol.Pbmethod.CommonVector.Builder builder = protocol.Pbmethod.CommonVector.newBuilder();
            // builder.addALong(status).addALong(uQuest.getQuest2()).addALong(uQuest.getQuest2number()).addALong(resQuest.getValue1());
            //mUser.getMsgNotify().add(CommonProto.getPbAction(IAction.QUEST2_STATUS, builder.build()));
        } catch (Exception ex) {
            Logs.error(mUser.getUser().getId() + ", type=" + questType.value + " -> " + new Gson().toJson(uQuest));
            throw ex;
        }
    }

    public static void upgradeQuest2Number(MyUser mUser, Quest2Type type, int number) {
        // quest 2
        UserQuestEntity uQuest = Guice.getInstance(QuestService.class).getUserQuest(mUser);
        if (uQuest != null) {
            int quest2Number = uQuest.getQuest2number();
            ResQuest2Entity resQuest = mQuest2.get(uQuest.getQuest2());
            if (resQuest != null && resQuest.getQuestType() == type.value) {
                uQuest.setQuest2Number(quest2Number + number);
            }
            checkQuest2NotifyComplete(mUser, type, uQuest, getResQuest(uQuest.getQuest2()));
        }
    }

    public static int getStatus(MyUser mUser, Quest2Type questType, UserQuestEntity uQuest, ResQuest2Entity resQuest) {
        if (resQuest == null) return 0;

        UserEntity user = mUser.getUser();
        switch (questType) {
            case TOWER1: {
                UserOblivionEntity userO = Services.userService.getOblivion(mUser);
                int curLevel = userO.getLevel();
                uQuest.setQuest2Number(curLevel);
                return curLevel >= resQuest.getValue1() ? 2 : 1;
            }
            case TOWER2: {
                UserTower2Entity userO = Services.userService.getTower2(mUser);
                int curLevel = userO.getLevel();
                uQuest.setQuest2Number(curLevel);
                return curLevel >= resQuest.getValue1() ? 2 : 1;
            }
            case HERO_LEVEL: {
                int curLevel = (int) mUser.getResources().getMHero().entrySet().stream().filter(hero -> hero.getValue().getLevel() >= resQuest.getValue2()).count();
                uQuest.setQuest2Number(curLevel);
                return curLevel >= resQuest.getValue1() ? 2 : 1;
            }
            case CAMPAIGN: {
                try {
                    UserCampaignEntity userCam = Services.userService.getCampaign(mUser);
                    int curLevel = userCam.getLevel();
                    uQuest.setQuest2Number(curLevel - 1);
                    return curLevel > resQuest.getValue1() ? 2 : 1;
                } catch (Exception ex) {
                    Logs.error(mUser.getUser().getId() + " -> type=" + questType.value + " " + resQuest + " " + uQuest.getQuest2());
                    throw ex;
                }
            }
            case USER_LEVEL: {
                int userLevel = user.getLevel();
                uQuest.setQuest2Number(userLevel);
                return userLevel >= resQuest.getValue1() ? 2 : 1;
            }
            case UPGRADE_TREASURE:
            case UPGRADE_CLAN_SKILL:
            case CELESTIAL_ATTACK:
            case HERO_SUMMON:
            case HERO_ALTAR:
            case CRYSTAL_ARENA:
            case POT_ATTACK:
            case BUY_EVENT_RAID_TURN: {
                return uQuest.getQuest2number() >= resQuest.getValue1() ? 2 : 1;
            }
        }
        return 0;
    }


    public static void addQuest(MyUser mUser, QuestType quest) {
        addQuest(mUser, quest, 1);
    }

    public static void addQuest(MyUser mUser, QuestType quest, int number) {
        try {
            UserQuestEntity userQuest = Guice.getInstance(QuestService.class).getUserQuest(mUser);
            if (userQuest != null) {
                userQuest.addQuest(mUser, quest, number);
            } else {
                Actions.debug(mUser.getUser(), "quest", "userquest", "null");
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public static QuestObject getQuest(QuestType questType) {
        for (QuestObject quest : config.quests) {
            if (quest.questType == questType.id) return quest;
        }
        return null;
    }

    public static ResQuest2Entity getResQuest(int id) {
        return mQuest2.get(id);
    }

    public static void loadConfig(String strJson) {
        json = JSONObject.fromObject(strJson);
        config = new Gson().fromJson(strJson, DataConfig.class);

        aQuest2 = DBJPA.getList(CfgServer.DB_MAIN + "res_quest2", ResQuest2Entity.class);
        if (aQuest2.isEmpty()) throw new NullPointerException();
        mQuest2.clear();
        for (ResQuest2Entity quest : aQuest2) mQuest2.put(quest.getId(), quest);
    }

    public class DataConfig {
        public List<List<Long>> finishBonus;
        public List<Long> finishPoint;
        public List<QuestObject> quests;

        public String getTitle(Lang lang) {
            return lang.get(Lang.config_daily_quest_title);
        }
    }

    public class QuestObject {
        public int id, number;
        public int questType;
        public List<Long> bonus;
        public int[] range;

        public String getTitle(Lang lang) {
            return lang.get(String.format("config_quest_%s", questType));
        }

        public List<Long> getBonus() {
            if (range == null) return new ArrayList<>(bonus);

            List<Long> tmp = new ArrayList<>(bonus);
            tmp.add((long) (range[0] + new Random().nextInt(range[1] - range[0])) * 1000);
            return tmp;
        }
    }
}