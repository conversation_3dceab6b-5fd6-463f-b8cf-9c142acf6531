package monster.config;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.ListUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.game.balancearena.entity.ResBalanceTeamEntity;
import monster.game.balancearena.entity.ResHeroBalanceEntity;
import monster.game.balancearena.entity.UserBalanceArenaEntity;
import monster.object.Rank2Controller;
import monster.object.RewardEntity;

import java.util.*;

public class CfgBalanceArena {
    public static DataConfig config;
    public static Map<Integer, ResHeroBalanceEntity> resHeroBalanceMapByHeroKey = new HashMap<>();
    public static Map<Integer, List<ResBalanceTeamEntity>> listResBalanceTeamMapByLevel = new HashMap<>();
    public static Map<Integer, ResBalanceTeamEntity> resBalanceTeamMapById = new HashMap<>();

    private static Map<Integer, Rank2Controller> rank2ControllerMapByCluster = new HashMap<>();

    public static final int EVENT_DURATION = 21;

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
        List<ResHeroBalanceEntity> listResHeroBalance = DBJPA.getList(CfgServer.DB_MAIN + "res_hero_balance", ResHeroBalanceEntity.class);
        resHeroBalanceMapByHeroKey.clear();
        listResHeroBalance.forEach(resHeroBalance -> resHeroBalanceMapByHeroKey.put(resHeroBalance.getHeroKey(), resHeroBalance));

        List<ResBalanceTeamEntity> listResBalanceTeam = DBJPA.getList(CfgServer.DB_MAIN + "res_balance_team", ResBalanceTeamEntity.class);
        listResBalanceTeamMapByLevel.clear();
        resBalanceTeamMapById.clear();
        listResBalanceTeam.forEach(resBalanceTeam -> {
            if (!listResBalanceTeamMapByLevel.containsKey(resBalanceTeam.getLevel()))
                listResBalanceTeamMapByLevel.put(resBalanceTeam.getLevel(), new ArrayList<>());
            listResBalanceTeamMapByLevel.get(resBalanceTeam.getLevel()).add(resBalanceTeam);

            resBalanceTeamMapById.put(resBalanceTeam.getId(), resBalanceTeam);
        });
    }

    public static int getEventId() {
        Date now = new Date();
        if (now.before(getStartCountDate())) return -1;

        return (int) (DateTime.numberDayPassed(getStartCountDate(), now) / EVENT_DURATION);
    }

    public static Date getStartCountDate() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, 2024);
        cal.set(Calendar.MONTH, Calendar.JULY);
        cal.set(Calendar.DAY_OF_MONTH, 25);
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getStartDate() {
        return DateTime.getDateOffset(getStartCountDate(), getEventId() * EVENT_DURATION);

    }

    public static Date getNextStartDate() {
        return DateTime.getDateOffset(getStartCountDate(), (getEventId() + 1) * EVENT_DURATION);

    }

    public static Date getEndDate() {
        return DateTime.getDateOffset(getStartCountDate(), getEventId() * EVENT_DURATION + 14);
    }

    public static boolean isOpen() {
        try {
            Date now = new Date();
            return now.after(getStartDate()) && now.before(getEndDate());
        } catch (Exception ex) {
            Logs.error(ex);
        }

        return false;
    }

    public static long getCountdownToEndEvent() {
        try {
            return (getEndDate().getTime() - new Date().getTime()) / 1000;
        } catch (Exception exception) {
            Logs.error(exception);
        }

        return 0;
    }

    public static int getCluster(int server) {
        return CfgServer.isTestServer() ? 0 : 1;
    }

    public static void initRankController(int cluster, int eventId) {
        String keyCacheRank = "rank:barena";
        int numberTop = CfgBalanceArena.config.numberTop;
        String redisKey = CfgServer.isTestServer() ?
                String.format("test3:%s:%s:%s", keyCacheRank, eventId, cluster) : String.format("%s:%s:%s", keyCacheRank, eventId, cluster);
        String strQuery = String.format("select user_id, point from dson.user_balance_arena where event_id=%s and cluster=%s order by point desc, last_time_finish limit 0,%s", eventId, cluster, numberTop);
        Rank2Controller rank2Controller = Rank2Controller.builder().numberTop(numberTop).key(redisKey).defaultPoint(0)
                .sql(strQuery).build();
        rank2Controller.init();
        rank2ControllerMapByCluster.put(cluster, rank2Controller);
    }

    public static Rank2Controller getRankController(UserBalanceArenaEntity userBalanceArena) {
        if (!rank2ControllerMapByCluster.containsKey(userBalanceArena.getCluster()))
            initRankController(userBalanceArena.getCluster(), userBalanceArena.getEventId());
        return rank2ControllerMapByCluster.get(userBalanceArena.getCluster());
    }

    public class DataConfig {
        public String timeOpenEvent, timeEndEvent;
        public Integer numberTurnDaily, numberTop, maxNumberLoose, feeRoll, basePoint;
        private List<Integer> listBuyTurnPrice;
        private List<List<Long>> listWinStreakReward;
        private List<Integer> listWinStreakMorePoint;
        public List<RewardEntity> listEndSeasonReward;
        public Integer heroStar;

        public void init() {
            if (numberTurnDaily == null) numberTurnDaily = 1;
            if (numberTop == null) numberTop = 100;
            if (maxNumberLoose == null) maxNumberLoose = 3;
            if (feeRoll == null) feeRoll = 100;
            if (basePoint == null) basePoint = 10;
            if (heroStar == null) heroStar = 14;
        }

        public List<Integer> getListBuyTurnPrice() {
            return new ArrayList<>(listBuyTurnPrice);
        }

        public int getMaxBuyTurn() {
            return listBuyTurnPrice.size();
        }

        public List<List<Long>> getListWinStreakReward() {
            return ListUtil.getClone(listWinStreakReward);
        }

        public List<Integer> getListWinStreakMorePoint() {
            return new ArrayList<>(listWinStreakMorePoint);
        }

        public int getMaxWinStreak() {
            return listWinStreakReward.size() - 1;
        }

        public String getDefaultNumberRolls() {
            List<Integer> listInt = new ArrayList<>();
            for (int i = 0; i < getMaxWinStreak(); i++) {
                listInt.add(0);
            }

            return StringHelper.toDBString(listInt);
        }

        public List<Long> getRankReward(int userRank) {
            RewardEntity rewardEntity = listEndSeasonReward.stream().filter(tmpRewardEntity -> tmpRewardEntity.inRank(userRank)).findFirst().orElse(null);
            if (rewardEntity == null) return null;

            return rewardEntity.getBonus();
        }
    }
}
