package monster.config;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserPetEntity;
import monster.dao.mapping.UserPetTopEntity;
import monster.dao.mapping.main.ResPetEntity;
import monster.service.user.Bonus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgPet {
    public static DataConfig config;
    public static int maxLevel = 120, maxRune = 100, maxPassive = 30;
    public static List<ResPetEntity> resPet = new ArrayList<>();

    public static List<Long> getResetRes(UserPetEntity uPet) {
        // level
        int monsterSoul = 0, item2091 = 0;
        for (int i = 0; i < Math.min(uPet.getLevel(), 180) - 1; i++)
            monsterSoul += config.petLevel.get("stone").getAsJsonArray().get(i).getAsInt();
        if (uPet.getLevel() > 180) {
            for (int i = 0; i < uPet.getLevel() - 180; i++)
                item2091 += config.petLevel2.get("item").getAsJsonArray().get(i).getAsInt();
        }

        for (int i = 0; i < uPet.getTier() - 1; i++)
            monsterSoul += config.evolve.get("soul").getAsJsonArray().get(i).getAsInt();

        int chaosStone = 0, item2090 = 0;
        for (int i = 0; i < uPet.getPassive1() - 1; i++)
            chaosStone += config.passive1Upgrade.get("chaos").getAsJsonArray().get(i).getAsInt();
        for (int i = 0; i < uPet.getPassive2() - 1; i++)
            chaosStone += config.passive2Upgrade.get("chaos").getAsJsonArray().get(i).getAsInt();
        for (int i = 0; i < uPet.getPassive3() - 1; i++)
            chaosStone += config.passive3Upgrade.get("chaos").getAsJsonArray().get(i).getAsInt();
        for (int i = 0; i < uPet.getPassive4() - 1; i++)
            chaosStone += config.passive4Upgrade.get("chaos").getAsJsonArray().get(i).getAsInt();

        for (Integer rune : Arrays.asList(uPet.getRune1(), uPet.getRune2(), uPet.getRune3())) {
            for (int i = 0; i < Math.min(rune, 60) - 1; i++)
                chaosStone += config.runeUpgrade.get("chaos").getAsJsonArray().get(i).getAsInt();
            if (rune > 60) {
                for (int i = 0; i < rune - 60; i++)
                    item2090 += config.runeUpgrade2.get("item").getAsJsonArray().get(i).getAsInt();
            }
        }

        List<Long> aLong = new ArrayList<>();
        aLong.addAll(Bonus.viewMaterial(MaterialType.MONSTER_SOUL, monsterSoul));
        aLong.addAll(Bonus.viewMaterial(MaterialType.CHAOS_STONE, chaosStone));
        if (item2091 > 0) aLong.addAll(Bonus.view(Bonus.BONUS_MATERIAL, 1, 2091, item2091));
        if (item2090 > 0) aLong.addAll(Bonus.view(Bonus.BONUS_MATERIAL, 1, 2090, item2090));
        return aLong;
    }

    public static boolean isMaxLevel(int level, int tier) {
        return level >= getMaxLevel(tier);
    }

    public static int getMaxLevel(int tier) {
        return config.evolve.get("level").getAsJsonArray().get(tier - 1).getAsInt();
    }

    public static List<Long> getResLevel(int level) {
        if (level >= config.petLevel.get("gold").getAsJsonArray().size()) return null;
        level--;
        List<Long> aLong = new ArrayList<>();
        aLong.addAll(Bonus.viewGold(-config.petLevel.get("gold").getAsJsonArray().get(level).getAsInt()));
        aLong.addAll(Bonus.viewMaterial(MaterialType.MONSTER_SOUL, -config.petLevel.get("stone").getAsJsonArray().get(level).getAsInt()));
        return aLong;
    }

    public static List<Long> getResLevel2(int level) {
        if (level >= config.petLevel2.get("gold").getAsJsonArray().size()) return null;
        List<Long> aLong = new ArrayList<>();
        aLong.addAll(Bonus.viewGold(-config.petLevel2.get("gold").getAsJsonArray().get(level).getAsInt()));
        aLong.addAll(Bonus.view(Bonus.BONUS_MATERIAL, 1, 2091, -config.petLevel2.get("item").getAsJsonArray().get(level).getAsInt()));
        return aLong;
    }

    public static List<Long> getResEvolve(int level, int tier) {
        if ((level == 30 && tier == 1) || (level == 60 && tier == 2) || (level == 90 && tier == 3)) {
            List<Long> aLong = new ArrayList<>();
            aLong.addAll(Bonus.viewGold(-config.evolve.get("gold").getAsJsonArray().get(tier - 1).getAsInt()));
            aLong.addAll(Bonus.viewMaterial(MaterialType.MONSTER_SOUL, -config.evolve.get("soul").getAsJsonArray().get(tier - 1).getAsInt()));
            return aLong;
        }
        return null;
    }

    public static List<Long> getResPassive1(int level) {
        return getResPassive(config.passive1Upgrade, level);
    }

    public static List<Long> getResPassive2(int level) {
        return getResPassive(config.passive2Upgrade, level);
    }

    public static List<Long> getResPassive3(int level) {
        return getResPassive(config.passive3Upgrade, level);
    }

    public static List<Long> getResPassive4(int level) {
        return getResPassive(config.passive4Upgrade, level);
    }

    private static List<Long> getResPassive(JsonObject obj, int level) {
        if (level >= obj.get("gold").getAsJsonArray().size() || level < 1) return null;
        List<Long> aLong = new ArrayList<>();
        aLong.addAll(Bonus.viewGold(-obj.get("gold").getAsJsonArray().get(level - 1).getAsInt()));
        aLong.addAll(Bonus.viewMaterial(MaterialType.CHAOS_STONE, -obj.get("chaos").getAsJsonArray().get(level - 1).getAsInt()));
        return aLong;
    }

    public static List<Long> getResRune(int level) {
        if (level > config.runeUpgrade.get("gold").getAsJsonArray().size() || level < 1) return null;
        List<Long> aLong = new ArrayList<>();
        aLong.addAll(Bonus.viewGold(-config.runeUpgrade.get("gold").getAsJsonArray().get(level - 1).getAsInt()));
        aLong.addAll(Bonus.viewMaterial(MaterialType.CHAOS_STONE, -config.runeUpgrade.get("chaos").getAsJsonArray().get(level - 1).getAsInt()));
        return aLong;
    }

    public static List<Long> getResRune2(int level) {
        if (level > config.runeUpgrade2.get("gold").getAsJsonArray().size()) return null;
        List<Long> aLong = new ArrayList<>();
        aLong.addAll(Bonus.viewGold(-config.runeUpgrade2.get("gold").getAsJsonArray().get(level).getAsInt()));
        aLong.addAll(Bonus.view(Bonus.BONUS_MATERIAL, 1, 2090, -config.runeUpgrade2.get("item").getAsJsonArray().get(level).getAsInt()));
        return aLong;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
        if (CfgServer.isTestServer()) maxLevel = 180;
    }

    public class DataConfig {
        public JsonObject evolve, petLevel, petLevel2;
        public JsonObject passive1Upgrade, passive2Upgrade, passive3Upgrade, passive4Upgrade;
        public JsonObject runeUpgrade, runeUpgrade2;

        public void init() {
            resPet = DBJPA.getList(CfgServer.DB_MAIN + "res_pet", ResPetEntity.class);
            if (resPet == null || resPet.isEmpty()) throw new NullPointerException();
        }
    }

    public static List<UserPetTopEntity> getAllTimeTopPet(List<UserPetEntity> listPet,
                                                          List<UserPetTopEntity> oldTop6Pet, int numberTop) {
        //Top 6 hero ở hiện tại
        List<UserPetTopEntity> listPetTop = getListPetTop(listPet);

        if (listPetTop.isEmpty()) return new ArrayList<>();

        listPetTop.sort(Comparator.comparing(UserPetTopEntity::getPower));

        int numberNotEnough = numberTop - listPetTop.size();
        for (int i = 0; i < numberNotEnough; i++) {
            listPetTop.add(listPetTop.get(0));
        }

        listPetTop = listPetTop.subList(listPetTop.size() - numberTop, listPetTop.size());

        listPetTop.sort(Comparator.comparing(UserPetTopEntity::getPower));

        for (int i = 0; i < listPetTop.size(); i++) {
            listPetTop.set(i, new UserPetTopEntity(listPetTop.get(i), numberTop - i));
        }

        List<UserPetTopEntity> finalListPetTop = new ArrayList<>();

        if (oldTop6Pet != null && !oldTop6Pet.isEmpty()) {
            for (int i = 0; i < listPetTop.size(); i++) {
                UserPetTopEntity newPetTop = getPetTop(listPetTop, i + 1);
                UserPetTopEntity oldPetTop = getPetTop(oldTop6Pet, i + 1);
                finalListPetTop.add(istStrongerPet(newPetTop, oldPetTop) ? oldPetTop : newPetTop);
            }

            return finalListPetTop;
        }

        return listPetTop;
    }

    private static UserPetTopEntity getPetTop(List<UserPetTopEntity> listPetTop, int top) {
        for (UserPetTopEntity petTop : listPetTop) {
            if (petTop.getTop() != top) continue;

            return petTop;
        }

        return null;
    }

    private static List<UserPetTopEntity> getListPetTop(List<UserPetEntity> listPet) {
        List<UserPetTopEntity> listPetTop = new ArrayList<>();
        for (UserPetEntity uPet : listPet) {
            listPetTop.add(uPet.toPetTop());
        }

        return listPetTop;
    }

    private static boolean istStrongerPet(UserPetTopEntity heroTop, UserPetTopEntity anotherHeroTop) {
        if (heroTop == null) return true;
        if (anotherHeroTop == null) return false;
        return anotherHeroTop.getPower() > heroTop.getPower();
    }
}
