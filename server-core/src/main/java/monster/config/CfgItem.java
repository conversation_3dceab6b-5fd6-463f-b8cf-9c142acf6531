package monster.config;

import com.google.gson.Gson;
import grep.helper.DateTime;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class CfgItem {
    public static DataConfig config;

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
    }

    public static Date getStartCountDate() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, 2024);
        cal.set(Calendar.MONTH, Calendar.APRIL);
        cal.set(Calendar.DAY_OF_MONTH, 17);
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static int getEventId() {
        Date now = new Date();
        return (int) (DateTime.getHourDiff(getStartCountDate(), now) / config.hourRefresh);
    }

    public static Date getStartDate() {
        return DateTime.getHourOffset(getStartCountDate(), getEventId() * config.hourRefresh);
    }

    public static Date getEndDate() {
        return DateTime.getHourOffset(getStartCountDate(), (getEventId() + 1) * config.hourRefresh);
    }

    public static long getTimeCountdownForNextEvent() {
        Date nextEventStartDate = DateTime.getHourOffset(getStartCountDate(), (getEventId() + 1) * config.hourRefresh);
        return (nextEventStartDate.getTime() - new Date().getTime()) / 1000;
    }

    public static class DataConfig {
        private List<Long> feeForgeSetClass;
        private List<Long> feeForgeSetResonance;
        private List<Long> feeReplaceSetClass;
        private List<Long> feeReplaceSetResonance;
        private List<Long> feeReplace;
        private List<Long> feeReplaceVip;
        @Getter
        private Integer replaceWarranty, limitSetClass;
        private List<Integer> limitSetResonance;
        private Integer hourRefresh;

        public void init() {
            if (limitSetClass == null) limitSetClass = 3;
            if (hourRefresh == null) hourRefresh = Math.toIntExact(DateTime.DAY_HOUR * 7);
            if (limitSetResonance == null) limitSetResonance = new ArrayList<>(List.of(3));
            if (replaceWarranty == null) replaceWarranty = 6;
        }

        public List<Long> getFeeForgeSetClass() {
            return new ArrayList<>(feeForgeSetClass);
        }

        public List<Long> getFeeForgeSetResonance() {
            return new ArrayList<>(feeForgeSetResonance);
        }

        public List<Long> getFeeReplaceSetClass() {
            return new ArrayList<>(feeReplaceSetClass);
        }

        public List<Long> getFeeReplaceSetResonance() {
            return new ArrayList<>(feeReplaceSetResonance);
        }

        public List<Long> getFeeReplace() {
            return new ArrayList<>(feeReplace);
        }

        public List<Long> getFeeReplaceVip() {
            return new ArrayList<>(feeReplaceVip);
        }

        public List<Integer> getLimitSetResonance() {
            return new ArrayList<>(limitSetResonance);
        }
    }
}
