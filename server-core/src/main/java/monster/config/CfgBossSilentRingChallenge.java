package monster.config;

import com.google.gson.Gson;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Getter;
import monster.controller.AHandler;
import monster.dao.mapping.UserBossSilentRingChallenge;
import monster.object.MyUser;
import monster.object.RewardEntity;
import monster.service.user.Actions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CfgBossSilentRingChallenge {
    public static DataConfig config;


    public static void add(A<PERSON><PERSON><PERSON> handler, MyUser mUser, int style, int state, int bossIndex, long number) {
        try {
            UserBossSilentRingChallenge userBoss = mUser.getCache().getUserBossSilentRingEntityChallenge(handler, mUser);
            List<Long> myChallenges = userBoss.getChallenge();
            if (myChallenges.size() % 3 != 0) {
                handler.addErrResponse("error challenge 1");
                return;
            }


            List<ChallengeObj> challenges = getListChallenge(style, state, bossIndex);

            for (int j = 0; j < challenges.size(); j++) {
                ChallengeObj challengeObj = challenges.get(j);

                if (challengeObj == null) {
                    continue;
                }

                int id = challengeObj.id;
                int count = myChallenges.size() / 3;

                boolean check = false;

                for (int i = 0; i < count; i++) {
                    int challengeId = myChallenges.get(3 * i).intValue();
                    long total = myChallenges.get(3 * i + 1);
                    int status = myChallenges.get(3 * i + 2).intValue();

                    // status = 0 : chua hoan thanh , 1: hoan thanh nhung chua nhan ; 2 da nhan
                    if (challengeId == id) {
                        if (number == -1) number = Math.max(number, total + 1);
                        myChallenges.set(3 * i + 1, number > total ? number : total);
                        if (style == 2){
                            number += total;
                            myChallenges.set(3 * i + 1, number);
                        }
                        long totalRequire = challengeObj.total;
                        if (status == 0 && (number >= totalRequire)) status = 1;

                        myChallenges.set(3 * i + 2, (long) status);
                        check = true;
                        break;
                    }
                }

                if (!check) {
                    if (number == -1) number = 1;
                    myChallenges.add((long) id);
                    myChallenges.add((long) number);
                    myChallenges.add(challengeObj.total <= number ? (long) 1 : (long) 0);
                }
            }

            if (!userBoss.updateChallenge(myChallenges)) handler.addErrResponse("Challenge update db error");
            Actions.save(mUser.getUser().getServer(), mUser.getUser().getId(), "boss_silent_ring", "update_challenge",
                    "difficult", state, "style", style, "boss_index", bossIndex,
                    "data", StringHelper.toDBString(myChallenges));
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public static boolean isDoneAll(List<Integer> challenges) {
        if (challenges.size() % 3 != 0) return false;
        int count = challenges.size() / 3;
        for (int i = 0; i < count; i++) {
            if (challenges.get(3 * i + 2) == 0) return false;
        }
        return true;
    }


    public static List<Long> getChallengeStatus(List<Long> challenges, int id) {
        List<Long> result = new ArrayList<>();
        if (challenges.size() % 3 != 0) return null;

        int count = challenges.size() / 3;
        for (int i = 0; i < count; i++) {
            if (challenges.get(3 * i) == id) {
                result.add(challenges.get(3 * i + 1));
                result.add(challenges.get(3 * i + 2));
                return result;
            }
        }
        result.add((long) 0);
        result.add((long) 0);
        return result;
    }


    public static ChallengeObj getChallenge(int id) {
        for (int i = 0; i < config.challengeObjs.size(); i++) {
            if (config.challengeObjs.get(i).id == id) return config.challengeObjs.get(i);
        }
        return null;
    }


    public static List<ChallengeObj> getListChallenge(int style, int state, int bossIndex) {
        List<ChallengeObj> result = new ArrayList<>();
        for (int i = 0; i < config.challengeObjs.size(); i++) {
            ChallengeObj challengeObj = config.challengeObjs.get(i);
            if (challengeObj.style == style && challengeObj.state == state) {
                if (challengeObj.style != 3 && challengeObj.bossIndex != bossIndex) continue;
                result.add(config.challengeObjs.get(i));
            }
        }
        return result;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
    }


    public class DataConfig {
        @Getter
        List<ChallengeObj> challengeObjs;
        List<Long> newbieReward, newbieRewardSeason;
        public List<RewardEntity> demonBloodByRank;

        public List<Long> getNewbieReward() {
            return new ArrayList<>(newbieReward);
        }

        public List<Long> getNewbieRewardSeason() {
            return new ArrayList<>(newbieRewardSeason);
        }
    }


    public class ChallengeObj {
        @Getter
        int id, style, state;
        @Getter
        Integer bossIndex;
        @Getter
        long total;
        @Getter
        String rewards;
        private String desc;

        public String getDesc(AHandler handler) {
            if (CfgServer.isSeaVersion()) return handler.getLang("bossSilentRingChallenge_desc_" + id);
            return desc;
        }
    }
}


