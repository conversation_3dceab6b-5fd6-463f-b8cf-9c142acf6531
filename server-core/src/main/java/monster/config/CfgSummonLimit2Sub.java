package monster.config;

import com.google.gson.Gson;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import monster.cache.CacheStore;
import monster.cache.CacheStoreBeans;
import monster.config.penum.MaterialType;
import monster.dao.mapping.ConfigEvent;
import monster.game.truongevent.config.TruongConfig;
import monster.game.truongevent.service.TruongProEventService;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.user.Bonus;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class CfgSummonLimit2Sub {
    public static DataConfig config;
    public static CacheStore<ConfigEvent> cacheConfigEvent = CacheStoreBeans.getMinute(ConfigEvent.class, 5);
    public static List<Integer> rateSummon; // rate * 100
    public static List<Integer> rateAdd;

    public static boolean isSummonLimitSub(MyUser mUser, int heroSelect) {
        var values = getHeroId2Summon(mUser.getUser().getServer());
        for (int i = 0; i < values.size(); i += 2) {
            if (values.get(i).intValue() == heroSelect) {
                return true;
            }
        }
        return false;
    }

    public static List<Long> getFeeSummon(int number) {
        return Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_USED_ITEM, config.feeId, -number);
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        rateSummon = new ArrayList<>();
        rateAdd = new ArrayList<>();
        int rate = config.rate.get(0);
        for (int i = 0; i < config.rate.size(); i++) {
            rateSummon.add(config.rate.get(i));
            if (i > 0) {
                rate += config.rate.get(i);
            }
            rateAdd.add(rate);
        }
    }

    public static int getMinInsure(int curHero) {
        int ensure = curHero * config.addEnsure + config.minEnsure;
        return ensure > config.maxEnsure ? config.maxEnsure : ensure;
    }

    public static boolean inEvent(int server) {
        //        List<Integer> server7Day = Guice.getInstance(SystemService.class).listServerOpen7Days();
        //        boolean inServer = config.server.get(0) == 0 || config.server.contains(server);
        return new Date().after(getConfigEvent(server).getFromTime()) && new Date().before(getConfigEvent(server).getToTime());
    }

    public static List<Integer> getHeroId2Summon(int serverId) {
        String data = String.format("[%s]", GsonUtil.parseJsonObject(getConfigEvent(serverId).getMetadata()).get("desc").getAsString());
        System.out.println("data = " + data);
        return GsonUtil.strToListInt(data);
    }

    public static boolean checkInputHero(int serverId, int heroInput) {
        List<Integer> ids = getHeroId2Summon(serverId);
        for (int i = 0; i < ids.size(); i += 2) {
            if (ids.get(i) == heroInput) return true;
        }
        return false;
    }

    public static ConfigEvent getConfigEvent(int serverId) {
        ConfigEvent configEvent = cacheConfigEvent.get(getKey(serverId));
        if (configEvent == null) {
            configEvent = Guice.getInstance(TruongProEventService.class).getEventByEventType(TruongConfig.EVENT_TYPE_HERO_SUMMON2, serverId);
            if (configEvent == null) {
                configEvent = ConfigEvent.builder()
                        .fromTime(DateTime.getCalendar(Calendar.DATE, -30).getTime()).toTime(DateTime.getCalendar(Calendar.DATE, -29).getTime())
                        .metadata("{\"heroId\":\"0,1\"}")
                        .build();
            }
            if (configEvent != null) cacheConfigEvent.add(getKey(serverId), configEvent);
        }
        return configEvent;
    }

    public static String getKey(int serverId) {
        return String.format("summonLimit2_%s", serverId);
    }

    public class DataConfig {
        //        public int eventId;
        public int showInHome;
        //        public List<Integer> server;
        public int feeId;
        public String eventName, eventIcon;
        //        public List<Integer> heroId;
        public List<List<Long>> bonus;
        public List<Integer> rate;
        public int ensure, minEnsure, addEnsure, maxEnsure;
    }
}
