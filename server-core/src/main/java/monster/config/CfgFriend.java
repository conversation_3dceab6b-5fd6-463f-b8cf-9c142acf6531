package monster.config;

import com.google.gson.Gson;
import grep.database.DBJPA;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserEntity;
import monster.service.user.Bonus;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class CfgFriend {
    public static final boolean FRIEND_GLOBAL = true;
    public static final int STT_NO_RECEIVE = 0;
    public static final int STT_HAVE_RECEIVE = 1;
    public static final int STT_RECEIVED = 2;
    //
    public static final int STT_DISABLE_GIVE = 0;
    public static final int STT_ENABLE_GIVE = 1;
    public static final int STT_GAVE = 2;
    //
    public static final int SCOUT_NORMAL = 0;
    public static final int SCOUT_BOSS = 1;
    //
    public static DataConfig config;
    public static JSONObject json;

    private static List<UserEntity> aSuggestUser = new ArrayList<>();
    private static List<UserEntity> aSuggestUser330 = new ArrayList<>();
    private static List<UserEntity> aSuggestUserTest = new ArrayList<>();
    private static long lastCache = 0;

    public static boolean isSameCluster(int myServer, int friendServer) {
        if (myServer >= CfgServer.newClusterServer && friendServer >= CfgServer.newClusterServer) return true;
        if (myServer < 4 && friendServer < 4) return true;
        if (myServer >= 4 && myServer < CfgServer.newClusterServer
                && friendServer >= 4 && friendServer < CfgServer.newClusterServer) return true;
        return false;
    }

    public static List<UserEntity> getASuggestUser(int serverId) {
        if (System.currentTimeMillis() - lastCache > 10000) {
            lastCache = System.currentTimeMillis();
            cacheSuggestUser(serverId);
        }
        List<UserEntity> randomUser;
        if (serverId >= CfgServer.newClusterServer) randomUser = new ArrayList<>(aSuggestUser330);
        else if (serverId >= 4) randomUser = new ArrayList<>(aSuggestUser);
        else randomUser = new ArrayList<>(aSuggestUserTest);

        Collections.shuffle(randomUser);
        return randomUser.subList(0, randomUser.size() > 25 ? 25 : randomUser.size());
    }

    private static void cacheSuggestUser(int serverId) {
        List<UserEntity> aUser = new ArrayList<>();
        if (CfgServer.isRealServer() && serverId >= 4) { // sv thật và acc chơi thật
            if (serverId >= CfgServer.newClusterServer) {
                aUser = DBJPA.getSelectQuery("select * from user where server>=330 and level>10 and number_friend<50 order by last_action desc limit 100", UserEntity.class);
            } else {
                aUser = DBJPA.getSelectQuery("select * from user where server>=4 and server<330 and level>10 and number_friend<50 order by last_action desc limit 100",
                        UserEntity.class);
            }
        } else {
            aUser = DBJPA.getSelectQuery("select * from user where server<4 and level>10 and number_friend<50 order by last_action desc limit 100", UserEntity.class);
        }
        if (aUser != null && !aUser.isEmpty()) {
            if (serverId >= CfgServer.newClusterServer) aSuggestUser330 = aUser;
            else if (serverId >= 4) aSuggestUser = aUser;
            else aSuggestUserTest = aUser;
        }
    }

    public static int getRandomScout() {
        int random = new Random().nextInt(100);
        float top = 0;
        for (int i = 0; i < config.percentRandom.length; i++) {
            top += config.percentRandom[i];
            if (random < top) {
                return i;
            }
        }
        return 0;
    }

    public static List<Long> getRewardScout(int currLevel) {
        List<Long> bonus = new ArrayList<>();
        if (currLevel > config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1]) {
            currLevel = config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1];
        }

        for (int i = 0; i < config.scoutBonus.limitLevel.length; i++) {
            if (currLevel <= config.scoutBonus.limitLevel[i]) {
                RewardScoutObject rewardScoutObject = config.scoutBonus.bonus[i];
                int rand = new Random().nextInt(3);
                switch (rand) {
                    case 0:
                        int minGold = rewardScoutObject.gold - (int) (rewardScoutObject.gold * config.rewardAmplitude);
                        int maxGold = rewardScoutObject.gold + (int) (rewardScoutObject.gold * config.rewardAmplitude);
                        bonus.addAll(Bonus.view(Bonus.BONUS_GOLD, new Random().nextInt((maxGold - minGold) + 1) + minGold));
                        break;
                    case 1:
                        int minSpirit = rewardScoutObject.spirit - (int) (rewardScoutObject.spirit * config.rewardAmplitude);
                        int maxSpirit = rewardScoutObject.spirit + (int) (rewardScoutObject.spirit * config.rewardAmplitude);
                        bonus.addAll(Bonus.viewMaterial(MaterialType.SPIRIT, new Random().nextInt((maxSpirit - minSpirit) + 1) + minSpirit));
                        break;
                    case 2:
                        int minDust = rewardScoutObject.magicDust - (int) (rewardScoutObject.magicDust * config.rewardAmplitude);
                        int maxDust = rewardScoutObject.magicDust + (int) (rewardScoutObject.magicDust * config.rewardAmplitude);
                        bonus.addAll(Bonus.viewMaterial(MaterialType.MAGIC_DUST, new Random().nextInt((maxDust - minDust) + 1) + minDust));
                        break;
                }
                break;
            }
        }
        return bonus;
    }

    public static List<Integer> getTeamMonster(int currLevel) {
        List<Integer> team = new ArrayList<>();
        if (currLevel > config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1]) {
            currLevel = config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1];
        }

        for (int i = 0; i < config.boss.limitLevel.length; i++) {
            if (currLevel <= config.boss.limitLevel[i]) {
                team.addAll(config.boss.monster.get(i));
                break;
            }
        }
        return team;
    }

    public static List<List<Long>> getRewardAtkBoss(int currLevel) {
        List<List<Long>> bonus = new ArrayList<>();
        if (currLevel > config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1]) {
            currLevel = config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1];
        }

        for (int i = 0; i < config.boss.limitLevel.length; i++) {
            if (currLevel <= config.boss.limitLevel[i]) {
                RewardBossObject rewardBossObject = config.boss.bonus[i];
                //
                int minGold1 = rewardBossObject.gold1 - (int) (rewardBossObject.gold1 * config.rewardAmplitude);
                if (minGold1 < 0)
                    minGold1 = 0;
                int maxGold1 = rewardBossObject.gold1 + (int) (rewardBossObject.gold1 * config.rewardAmplitude);
                bonus.add(Bonus.view(Bonus.BONUS_GOLD, new Random().nextInt((maxGold1 - minGold1) + 1) + minGold1));
                //
                int minGold2 = rewardBossObject.gold2 - (int) (rewardBossObject.gold2 * config.rewardAmplitude);
                if (minGold2 < 0)
                    minGold2 = 0;
                int maxGold2 = rewardBossObject.gold2 + (int) (rewardBossObject.gold2 * config.rewardAmplitude);
                bonus.add(Bonus.view(Bonus.BONUS_GOLD, new Random().nextInt((maxGold2 - minGold2) + 1) + minGold2));
                //
                int minHeart = (int) Math.min((float) rewardBossObject.heart - ((float) rewardBossObject.heart * config.rewardAmplitude), (float) rewardBossObject.heart);
                int maxHeart = rewardBossObject.heart + (int) (rewardBossObject.heart * config.rewardAmplitude);
                int numbHeart = new Random().nextInt((maxHeart - minHeart) + 1) + minHeart;
                if (numbHeart <= 0)
                    numbHeart = 1;
                bonus.add(Bonus.viewMaterial(MaterialType.HEART, numbHeart));
                break;
            }
        }
        return bonus;
    }

    public static List<Long> getRewardFinal(int currLevel) {
        List<Long> bonus = new ArrayList<>();
        if (currLevel > config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1]) {
            currLevel = config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1];
        }

        for (int i = 0; i < config.boss.limitLevel.length; i++) {
            if (currLevel <= config.boss.limitLevel[i]) {
                bonus.addAll(Bonus.view(Bonus.BONUS_GEM, config.boss.finalReward[i]));
                break;
            }
        }
        return bonus;
    }

    public static List<Long> getRewardKillBoss(int currLevel) {
        List<Long> bonus = new ArrayList<>();
        if (currLevel > config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1]) {
            currLevel = config.scoutBonus.limitLevel[config.scoutBonus.limitLevel.length - 1];
        }

        for (int i = 0; i < config.boss.limitLevel.length; i++) {
            if (currLevel <= config.boss.limitLevel[i]) {
                bonus.addAll(Bonus.view(Bonus.BONUS_GEM, config.boss.killReward[i]));
                break;
            }
        }
        return bonus;
    }

    public static int getLimitLevelAtkBoss(int currLevel) {
        int levelLimit = 0;
        if (currLevel > config.boss.limitLevel[config.boss.limitLevel.length - 1]) {
            levelLimit = config.boss.limitLevel[config.boss.limitLevel.length - 1];
        }

        for (int i = 0; i < config.boss.limitLevel.length; i++) {
            if (currLevel <= config.boss.limitLevel[i]) {
                levelLimit = config.boss.limitLevel[i];
                break;
            }
        }
        return levelLimit;
    }

    public static void loadConfig(String value) {
        json = JSONObject.fromObject(value);
        config = new Gson().fromJson(value, CfgFriend.DataConfig.class);

        new Thread(() -> cacheSuggestUser(4)).start();
    }

    public class DataConfig {
        public int limitedFriend, limitedAtkBoss, timeRefreshScout, timeIncStamina;
        public int[] percentRandom;
        public float rewardAmplitude;
        public ScoutBonusObject scoutBonus;
        public BossObject boss;
    }

    class ScoutBonusObject {
        public int[] limitLevel;
        public RewardScoutObject[] bonus;
    }

    class RewardScoutObject {
        public int gold;
        public int spirit;
        public int magicDust;
    }

    //
    public class BossObject {
        public int[] limitLevel;
        public List<List<Integer>> monster;
        public int[] finalReward;
        public int[] killReward;
        public RewardBossObject[] bonus;
    }

    class RewardBossObject {
        public int gold1, gold2, heart;
    }
}
