package monster.config;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import lombok.Data;
import monster.dao.mapping.main.ResBotCrystalEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.game.arena.entity.UserArenaCrystalEntity;
import monster.object.BattleTeam;
import monster.object.RandomRewardEntity;
import monster.object.RewardEntity;
import monster.service.monitor.ClanMonitor;
import monster.service.monitor.Telegram;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResBot;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;

import java.util.*;
import java.util.concurrent.TimeUnit;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgArenaCrystal {
    public static DataConfig config;
    public static int minWinPoint = 0, maxWinPoint = 100;
    public static int minLoosePoint = 0, maxLoosePoint = -50;
    public static Set<String> jobSeasonRankingDone = new HashSet<>();

    //region cache Opponent
    private static LoadingCache<String, List<UserArenaCrystalEntity>> mCacheOpponent = CacheBuilder.newBuilder().expireAfterAccess(15, TimeUnit.MINUTES).build(new CacheLoader<String, List<UserArenaCrystalEntity>>() {
        @Override
        public List<UserArenaCrystalEntity> load(String key) throws Exception {
            String[] tmp = key.split("_");
            int serverId = Integer.parseInt(tmp[0]);
            long power = Long.parseLong(tmp[1]);
            int curPoint = Integer.parseInt(tmp[2]);
            EntityManager session = null;
            try {
                session = DBJPA.getEntityManager();
                int lowPoint = curPoint * 8 / 10, highPoint = curPoint * 12 / 10;

                List<UserArenaCrystalEntity> aUserArena = new ArrayList<>();
                aUserArena.addAll(session.createNativeQuery("select * from user_arena_crystal " +
                        "where server_id=" + serverId + " and point<" + highPoint + " and is_team_null=0 order by point desc limit 20", UserArenaCrystalEntity.class).getResultList());
                if (!aUserArena.isEmpty() && aUserArena.get(aUserArena.size() - 1).getPoint() <= curPoint) {
                    aUserArena.addAll(session.createNativeQuery("select * from user_arena_crystal " +
                            "where server_id=" + serverId + " and point>" + curPoint + " and is_team_null=0 order by point asc limit 20", UserArenaCrystalEntity.class).getResultList());
                }
                aUserArena.addAll(session.createNativeQuery("select * from user_arena_crystal " +
                        "where server_id=" + serverId + " and point<" + lowPoint + " and is_team_null=0 order by point desc limit 10", UserArenaCrystalEntity.class).getResultList());
                aUserArena.addAll(session.createNativeQuery("select * from user_arena_crystal " +
                        "where server_id=" + serverId + " and power<" + power + " and is_team_null=0 order by power desc limit 10", UserArenaCrystalEntity.class).getResultList());
                return aUserArena;
            } catch (Exception ex) {
                Logs.error(GUtil.exToString(ex));
            } finally {
                DBJPA.closeSession(session);
            }
            return null;
        }
    });

    public static List<UserArenaCrystalEntity> getOpponent(UserArenaCrystalEntity userArena) {
        try {
            return mCacheOpponent.get(String.format("%s_%s_%s", userArena.getServerId(), userArena.getPower(), userArena.getPoint()));
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }
    //endregion

    //region Cache UserArena
    private static LoadingCache<String, UserArenaCrystalEntity> cacheArenaCrystal = CacheBuilder.newBuilder().expireAfterAccess(30, TimeUnit.MINUTES).build(new CacheLoader<>() {
        @Override
        public UserArenaCrystalEntity load(String k) throws Exception {
            EntityManager session = null;
            try {
                String[] values = k.split("_");
                int eventId = Integer.parseInt(values[0]);
                int userId = Integer.parseInt(values[1]);
                session = DBJPA.getEntityManager();
                List<UserArenaCrystalEntity> aUserArena = session.createNativeQuery("select * from user_arena_crystal where user_id=" + userId + " and event_id=" + eventId,
                        UserArenaCrystalEntity.class).getResultList();
                if (aUserArena.isEmpty()) {
                    UserArenaCrystalEntity userArena = new UserArenaCrystalEntity(userId, UserOnline.getDbUser(userId).getServer());
                    session.getTransaction().begin();
                    session.persist(userArena);
                    session.getTransaction().commit();
                    return userArena;
                }
                return aUserArena.get(0);
            } catch (Exception ex) {
                Logs.error(GUtil.exToString(ex));
            } finally {
                DBJPA.closeSession(session);
            }
            return null;
        }
    });

    public static UserArenaCrystalEntity getArenaCrystal(int userId) {
        try {
            return cacheArenaCrystal.get(String.format("%s_%s", getEventId(), userId));
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }

    public static void clearCache() {
        cacheArenaCrystal.invalidateAll();
    }
    //endregion

    //    public static void main(String[] args) {
    //        System.out.println(new Gson().toJson(calculatePoint(0, 694, false)));
    //    }

    public static int[] calculatePoint(int atkPoint, int defPoint, boolean isWin) {
        int weight = atkPoint - defPoint;
        if (isWin) {
            float point1 = -0.0362f * weight + 26;
            point1 = (float) Math.ceil(point1);
            if (point1 < 0) point1 = 0;
            if (point1 > 90) point1 = 90;
            float point2 = point1;

            if (weight > 700) point2 = -point1;
            else if (weight > 350) point2 = -point1 - (700 - weight) / 50;
            else if (weight >= 0) point2 = -point1 - weight / 50;
            else if (weight > -700) point2 = -point2 - weight / 70;
            else point2 = -point1 - 10;
            if (point2 == 0) point2 = -1;

            return new int[]{checkPoint((int) point1), checkPoint((int) point2)};
        }

        float point1 = -0.0318f * weight - 35;
        point1 = (float) Math.ceil(point1);
        if (point1 > 0) point1 = 0;
        if (point1 < -50) point1 = -50;
        float point2 = Math.abs(point1);
        if (weight > 0) point2 = Math.abs(point1) + weight / 16;
        if (point2 > 100) point2 = 100;
        return new int[]{checkPoint((int) point1), checkPoint((int) point2)};
    }

    public static int calculatePointForQuickBattle(int atkPoint) {
        QuickBattleConfig quickBattleConfig = config.quickBattle.stream().filter(tmpConfig -> tmpConfig.isInRange(atkPoint)).findFirst().orElse(null);
        return quickBattleConfig == null ? 0 : quickBattleConfig.addPoint;
    }

    public static String getTable() {
        return CfgServer.isTestServer() ? "user_arena_crystal" : "user_arena_crystal";
    }

    public static long getSearchKey(int eventId, int serverId, int isTeamNull) {
        return eventId * 100000 + serverId * 10 + isTeamNull;
    }

    public static int checkPoint(int value) {
        if (value >= 0) {
            if (value < minWinPoint) return minWinPoint;
            if (value > maxWinPoint) return maxWinPoint;
            return value;
        }
        if (value < maxLoosePoint) return maxLoosePoint;
        return value;
    }

    public static List<Long> getBattleBonus(int userLevel) {
        RandomRewardEntity[] randomReward = config.getRandomReward(userLevel);
        int rand = new Random().nextInt(100);
        for (RandomRewardEntity reward : randomReward) {
            if (rand < reward.percent) return new ArrayList<>(reward.bonus);
        }
        return new ArrayList<>();
    }

    public static List<Long> getSeasonReward(int rank) {
        if (rank == 0) return config.seasonReward[config.seasonReward.length - 1].bonus;

        for (RewardEntity reward : config.seasonReward) {
            if (reward.inRank(rank)) return new ArrayList<>(reward.bonus);
        }
        return null;
    }

    public static List<Long> getDailyReward(int rank) {
        if (rank == 0) return config.dailyReward[config.dailyReward.length - 1].bonus;

        for (RewardEntity reward : config.dailyReward) {
            if (reward.inRank(rank)) return new ArrayList<>(reward.bonus);
        }
        return null;
    }

    public static boolean firstDaySeason() {
        long timePassed = System.currentTimeMillis() - config.startMillisecond;
        long numberEvent = timePassed / config.numberEventDayInMillisecond;
        long startEvent = config.startMillisecond + numberEvent * config.numberEventDayInMillisecond;
        return System.currentTimeMillis() > startEvent && System.currentTimeMillis() < startEvent + DateTime.DAY_MILLI_SECOND;
    }

    public static boolean lastDaySeason() {
        long timePassed = System.currentTimeMillis() - config.startMillisecond;
        long numberEvent = timePassed / config.numberEventDayInMillisecond + 1;
        long endEvent = config.startMillisecond + numberEvent * config.numberEventDayInMillisecond;
        return System.currentTimeMillis() < endEvent && System.currentTimeMillis() > endEvent - DateTime.DAY_MILLI_SECOND;
    }

    public static int getEventId() {
        return (int) ((System.currentTimeMillis() - config.startMillisecond) / config.numberEventDayInMillisecond);
    }

    public static int getLastEventId() {
        return getEventId() - 1;
    }

    public static long getCountdown() {
        long timePassed = System.currentTimeMillis() - config.startMillisecond;
        long numberEvent = timePassed / config.numberEventDayInMillisecond + 1;
        long endEvent = config.startMillisecond + numberEvent * config.numberEventDayInMillisecond;
        return (endEvent - System.currentTimeMillis()) / 1000;
    }

    public static Pbmethod.PbUser.Builder pbUserInfoForBot(UserArenaCrystalEntity userArena) {
        ResBotCrystalEntity resBotEntity = ResBot.getBotCrystal(userArena.getResBotId());
        Pbmethod.PbUser.Builder pbUser = Pbmethod.PbUser.newBuilder();
        pbUser.setId(userArena.getUserId());
        pbUser.setName(resBotEntity.getName());
        int monsterId = GsonUtil.strToListInt(ResBot.getBotCrystal(userArena.getResBotId()).getTeam()).get(0);
        ResMonsterEntity monster = ResMonster.getMonster(monsterId);
        ResHeroEntity resHero = ResHero.getHero(monster.getHeroLinkNew());
        pbUser.addAllAvatar(Arrays.asList(0, resHero.getAvatar(), 3, 3));
        pbUser.setUserInfo(Pbmethod.CommonVector.newBuilder().addALong(3).build());
        pbUser.setClanInfo(Pbmethod.CommonVector.newBuilder().addAString("").addALong(0).addALong(0)
                .addALong(ClanMonitor.getClanAvatar(0)).build());
        pbUser.setPower(userArena.getPower());
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        pbUser.setLevel(30);
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());
        return pbUser;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();
    }

    public class DataConfig {
        public int maxRank, feeTicket;
        public RewardEntity[] dailyReward, seasonReward;
        public RandomRewardEntity[] randomReward30, randomReward50, randomReward70, randomReward999;
        public String strStartDate;
        public int numberEventDay;
        public long startMillisecond, numberEventDayInMillisecond;
        public List<QuickBattleConfig> quickBattle;

        private RandomRewardEntity[] getRandomReward(int level) {
            if (level <= 30) return randomReward30;
            if (level <= 50) return randomReward50;
            if (level <= 70) return randomReward70;
            return randomReward999;
        }

        private void init() {
            init(randomReward30);
            init(randomReward50);
            init(randomReward70);
            init(randomReward999);
            try {
                startMillisecond = DateTime.get_yyyyMMdd().parse(strStartDate).getTime() - DateTime.HOUR_MILLI_SECOND * 2; // Chỉnh lại thời gian chốt sự kiện về 22H tối
                numberEventDayInMillisecond = numberEventDay * DateTime.DAY_MILLI_SECOND;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (quickBattle == null || quickBattle.isEmpty()) {
                quickBattle = new ArrayList<>();
                Telegram.sendNotify("Error config_arenaCrystal thiếu key quickBattle");
            }
        }

        void init(RandomRewardEntity[] aReward) {
            for (int i = 1; i < aReward.length; i++) {
                aReward[i].percent += aReward[i - 1].percent;
            }
        }
    }

    public static class QuickBattleConfig {
        public int addPoint;
        public int[] pointRange;

        public boolean isInRange(int currentPoint) {
            int startPoint = pointRange[0];
            int endPoint = pointRange[1];
            return currentPoint >= startPoint && (endPoint == -1 || currentPoint <= endPoint);
        }
    }
}
