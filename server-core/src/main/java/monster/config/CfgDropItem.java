package monster.config;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserCampaignEntity;
import monster.dao.mapping.UserDropEventEntity;
import monster.dao.mapping.main.ConfigEventDropEntity;
import monster.service.user.Bonus;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgDropItem {
    public static DataConfig config;
    public static List<ConfigEventDropEntity> aEventDrop = new ArrayList<>();

    public static Map<Integer, Integer> testEventLoots(int numberLoop) {
        Map<Integer, Integer> mItem = new HashMap<>();
        List<ConfigEventDropEntity> activeEvent = aEventDrop.stream()
                .filter(event -> event.getStartDate().getTime() < System.currentTimeMillis() && System.currentTimeMillis() < event.getEndDate().getTime() + DateTime.DAY_MILLI_SECOND)
                .collect(Collectors.toList());
        if (!activeEvent.isEmpty()) {
            Random rand = new Random();
            activeEvent.forEach(event -> {
                if (event.getItemId() == 0) { // normal config
                    int numberDrop = 0;
                    for (int i = 0; i < numberLoop; i++) {
                        if (rand.nextFloat() * 100 < event.getRate()) numberDrop++;
                    }
                    if (!mItem.containsKey(event.getItemId())) mItem.put(event.getItemId(), 0);
                    mItem.put(event.getItemId(), mItem.get(event.getItemId()) + numberDrop);
                } else { // super config -> follow by min, max rate
                    UserDropEventEntity userDropEntity = UserDropEventEntity.builder().k(String.valueOf(event.getId())).build();
                    DropInfo dropInfo = config.getDropInfo(event.getItemId());
                    if (dropInfo != null) {
                        int numberDrop = 0;
                        for (int i = 0; i < numberLoop; i++) {
                            if (rand.nextFloat() < dropInfo.rate) numberDrop++;
                        }
                        if (userDropEntity != null) {
                            int newNumberDrop = userDropEntity.getNumberDrop() + numberDrop;
                            int newNumberCheck = userDropEntity.getNumberCheck() + numberLoop;
                            float minDrop = newNumberCheck * dropInfo.min, maxDrop = newNumberCheck * dropInfo.max;

                            if (newNumberDrop > maxDrop) { // fix to max drop
                                numberDrop = maxDrop > userDropEntity.getNumberDrop() ? (int) (maxDrop - userDropEntity.getNumberDrop()) : 0;
                            } else if (newNumberDrop < minDrop) {
                                numberDrop = minDrop > userDropEntity.getNumberDrop() ? (int) (minDrop - userDropEntity.getNumberDrop()) : 0;
                            }

                            if (!mItem.containsKey(event.getItemId())) mItem.put(event.getItemId(), 0);
                            mItem.put(event.getItemId(), mItem.get(event.getItemId()) + numberDrop);
                        }
                    }
                }
            });
        }
        return mItem;
    }

    public static boolean checkEventLoots(UserCampaignEntity uCampaign, int numberLoop) {
        List<ConfigEventDropEntity> activeEvent = aEventDrop.stream()
                .filter(event -> event.getStartDate().getTime() < System.currentTimeMillis() && System.currentTimeMillis() < event.getEndDate().getTime())
                .collect(Collectors.toList());

        if (!activeEvent.isEmpty()) {
            Random rand = new Random();
            activeEvent.forEach(event -> {
                if (event.getItemId() == 0) { // normal config
                    int numberDrop = 0;
                    for (int i = 0; i < numberLoop; i++) {
                        if (rand.nextFloat() * 100 < event.getRate()) numberDrop++;
                    }
                    uCampaign.addEventDrop(event.getId(), numberDrop);
                } else { // super config -> follow by min, max rate
                    UserDropEventEntity userDropEntity = dbGetUserDropEntity(uCampaign.getUserId(), String.valueOf(event.getId()));
                    DropInfo dropInfo = config.getDropInfo(event.getItemId());
                    if (dropInfo != null) {
                        int numberDrop = 0;
                        for (int i = 0; i < numberLoop; i++) {
                            if (rand.nextFloat() < dropInfo.rate) numberDrop++;
                        }
                        if (userDropEntity != null) {
                            int newNumberDrop = userDropEntity.getNumberDrop() + numberDrop;
                            int newNumberCheck = userDropEntity.getNumberCheck() + numberLoop;
                            float minDrop = newNumberCheck * dropInfo.min, maxDrop = newNumberCheck * dropInfo.max;

                            if (newNumberDrop > maxDrop) { // fix to max drop
                                numberDrop = maxDrop > userDropEntity.getNumberDrop() ? (int) (maxDrop - userDropEntity.getNumberDrop()) : 0;
                            } else if (newNumberDrop < minDrop) {
                                numberDrop = minDrop > userDropEntity.getNumberDrop() ? (int) (minDrop - userDropEntity.getNumberDrop()) : 0;
                            }

                            if (numberDrop > 0) uCampaign.addEventDrop(event.getId(), numberDrop);
                            userDropEntity.update(newNumberCheck, userDropEntity.getNumberDrop() + numberDrop);
                        }
                    }
                }
            });
            return true;
        }

        return false;
    }


    public static String addEventDrop(int eventId, int numberDrop) {
        JsonArray arr = new JsonArray();
        for (int i = 0; i < arr.size(); i += 2) {
            if (arr.get(i).getAsInt() == eventId) {
                arr.set(i + 1, new JsonPrimitive(arr.get(i + 1).getAsInt() + numberDrop));
                return arr.toString();
            }
        }
        arr.add(eventId);
        arr.add(numberDrop);
        return arr.toString();
    }

    public static List<Long> getEventDrop(int eventId, int number) {
        ConfigEventDropEntity event = aEventDrop.stream().filter(eventDrop -> eventDrop.getId() == eventId).findFirst().orElse(null);
        if (event != null) {
            if (event.getItemId() == 0) {
                List<Long> aLong = new Gson().fromJson(event.getBonus(), new TypeToken<ArrayList<Long>>() {
                }.getType());
                aLong.set(aLong.size() - 1, aLong.get(aLong.size() - 1) * Math.min(number, event.getMaxNumberDrop()));
                return aLong;
            } else return Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_USED_ITEM, event.getItemId(), number);
        }
        return new ArrayList<>();
    }

    public static UserDropEventEntity dbGetUserDropEntity(int userId, String key) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserDropEventEntity> aCampaign = session.createNativeQuery(String.format("select * from user_drop_event where user_id=%s and k='%s'", userId, key), UserDropEventEntity.class).getResultList();
            if (aCampaign.isEmpty()) {
                UserDropEventEntity userDropEntity = UserDropEventEntity.builder().userId(userId).k(key).build();
                session.getTransaction().begin();
                session.persist(userDropEntity);
                session.getTransaction().commit();
                return userDropEntity;
            }
            return aCampaign.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public static String reloadConfig() {
        if (CfgServer.isTestServer())
            aEventDrop = DBJPA.getList(CfgServer.DB_MAIN + "config_event_drop_dev", ConfigEventDropEntity.class);
        else
            aEventDrop = DBJPA.getList(CfgServer.DB_MAIN + "config_event_drop", ConfigEventDropEntity.class);

        return new Gson().toJson(aEventDrop);
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.aItem.forEach(DropInfo::init);

        reloadConfig();
    }

    public class DataConfig {
        public int numberCheckPerDay;
        public List<DropInfo> aItem;

        public DropInfo getDropInfo(int itemId) {
            return aItem.stream().filter(item -> item.aId.contains(itemId)).findFirst().orElse(null);
        }

    }

    public class DropInfo {
        public List<Integer> aId;
        public float min, max;
        public float rate;

        public void init() {
            rate = (min + max) / 2;
        }
    }

}
