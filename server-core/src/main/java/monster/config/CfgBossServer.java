package monster.config;


import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Getter;
import monster.dao.mapping.UserBossServerEntity;
import monster.dao.mapping.main.ResBossServerEntity;
import monster.dao.mapping.main.ResBossServerPowerEntity;
import monster.object.MyUser;
import monster.service.user.Bonus;

import jakarta.persistence.EntityManager;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Getter
public class CfgBossServer {
    public static DataConfig config;
    private static List<Map<Integer, List<Long>>> rankBonus = new ArrayList<>();
    private static final int[] listRank = {1, 2, 3, 4, 5, 10, 20, 50, 100};
    public static String str100, str0, str1, strNone;
    public static List<ResBossServerEntity> listResBoss = new ArrayList<>();
    public static Map<Integer, Integer> mPower = new HashMap<>();
    public static final int POSITION_BOSS = 4;

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();

        listResBoss = DBJPA.getList(CfgServer.DB_MAIN + "res_boss_server", ResBossServerEntity.class);
        if (listResBoss == null || listResBoss.isEmpty()) throw new NullPointerException();

        List<ResBossServerPowerEntity> listResBossPower = DBJPA.getList(CfgServer.DB_MAIN + "res_boss_server_power", ResBossServerPowerEntity.class);
        if (listResBossPower == null || listResBossPower.isEmpty()) throw new NullPointerException();

        for (ResBossServerPowerEntity resPower : listResBossPower) {
            mPower.put(resPower.getServerId(), resPower.getScale());
        }

//        System.out.println("CfgServer.serverId: " + CfgServer.serverId);
//        System.out.println("mPower.containsKey(CfgServer.serverId) " + mPower.containsKey(CfgServer.serverId));
//        if (!mPower.containsKey(CfgServer.serverId)) {
//            EntityManager session = null;
//            try {
//                session = DBJPA.getEntityManager();
//                ResBossServerPowerEntity resPower = new ResBossServerPowerEntity();
//                session.getTransaction().begin();
//                session.persist(resPower);
//                session.getTransaction().commit();
//                mPower.put(CfgServer.serverId,resPower.getScale());
//            } catch (Exception ex) {
//                Logs.error(GUtil.exToString(ex));
//            } finally {
//                DBJPA.closeSession(session);
//            }
//        }
//        System.out.println("CfgServer.serverId: " + CfgServer.serverId);

        rankBonus.clear();
        for (int i = 0; i < config.numberBoss; i++) {
            rankBonus.add(new HashMap<>());
            rankBonus.get(i).put(1, listResBoss.get(i).getBonusRank().rank1);
            rankBonus.get(i).put(2, listResBoss.get(i).getBonusRank().rank2);
            rankBonus.get(i).put(3, listResBoss.get(i).getBonusRank().rank3);
            rankBonus.get(i).put(4, listResBoss.get(i).getBonusRank().rank4);
            rankBonus.get(i).put(5, listResBoss.get(i).getBonusRank().rank5);
            rankBonus.get(i).put(10, listResBoss.get(i).getBonusRank().rank10);
            rankBonus.get(i).put(20, listResBoss.get(i).getBonusRank().rank20);
            rankBonus.get(i).put(50, listResBoss.get(i).getBonusRank().rank50);
            rankBonus.get(i).put(100, listResBoss.get(i).getBonusRank().rank100);
            rankBonus.get(i).put(101, listResBoss.get(i).getBonusRank().rankDefault);
        }

        List<Integer> a100 = new ArrayList<>();
        List<Integer> a0 = new ArrayList<>();
        List<Integer> a1 = new ArrayList<>();
        List<Integer> aNone = new ArrayList<>();
        for (int i = 0; i < config.numberBoss; i++) {
            a100.add(100);
            a0.add(0);
            a1.add(1);
            aNone.add(-1);
        }
        str100 = a100.toString().replace(" ", "");
        str0 = a0.toString().replace(" ", "");
        str1 = a1.toString().replace(" ", "");
        strNone = aNone.toString().replace(" ", "");
    }

    @Getter
    public class DataConfig {
        private int baseMaxTurn;
        private List<Integer> rate;
        private int feeStart, feeStep;
        private VipBuyTurn vipBuyTurn;
        private List<Integer> numberHeroGerma;
        private List<Integer> percentBonus;
        private int numberBoss, numberTop, levelRequireHero;
        private int numAttackedToScale;
        private int basePower;

        public void init() {
            Collections.sort(numberHeroGerma);
            Collections.sort(percentBonus);
        }
    }

    private class VipBuyTurn {
        int[] vip, maxTurn;
    }

    public class RankBonus {
        List<Long> rank1, rank2, rank3, rank4, rank5, rank10, rank20, rank50, rank100, rankDefault;
    }

    public class DamageBonus {
        List<Long> listDamage;
        List<List<List<Long>>> listRandomBonus;
    }

    //region Cache UserBossServer
    private static Map<Integer, LoadingCache<Integer, UserBossServerEntity>> mCache = new HashMap<>();

    private static synchronized LoadingCache<Integer, UserBossServerEntity> getCache(int serverId) {
        if (!mCache.containsKey(serverId)) {
            mCache.put(serverId, CacheBuilder.newBuilder().maximumSize(3000).expireAfterAccess(1, TimeUnit.HOURS).build(new CacheLoader<Integer, UserBossServerEntity>() {
                @Override
                public UserBossServerEntity load(Integer userId) throws Exception {
                    EntityManager session = null;
                    try {
                        session = DBJPA.getEntityManager();
                        List<UserBossServerEntity> aBoss = session.createNativeQuery(
                                "select * from user_boss_server where user_id=" + userId + " and server_id=" + serverId, UserBossServerEntity.class).getResultList();
                        if (aBoss.isEmpty()) {
                            UserBossServerEntity userBoss = new UserBossServerEntity(userId, serverId);
                            session.getTransaction().begin();
                            session.persist(userBoss);
                            session.getTransaction().commit();
                            return userBoss;
                        }
                        return aBoss.get(0);
                    } catch (Exception ex) {
                        Logs.error(GUtil.exToString(ex));
                    } finally {
                        DBJPA.closeSession(session);
                    }
                    return null;
                }
            }));
        }
        return mCache.get(serverId);
    }

    public static UserBossServerEntity getUserBoss(int serverId, int userId) {
        try {
            return getCache(serverId).get(userId);
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }


    public static void clearCache(int serverId) {
        getCache(serverId).invalidateAll();
    }
    //endregion

    public static List<Long> getURankBonus(int bossIndex, int uRank, int selfDie, int numberHeroGerma) {
        List<Long> aLong = new ArrayList<>();
//        Map<Integer, List<Long>> map = selfDie == 1 ? rankBonusBuster.get(bossIndex) : rankBonus;
        Map<Integer, List<Long>> map = rankBonus.get(bossIndex);
        List<Integer> aNumber = config.getNumberHeroGerma();
        List<Integer> aPercent = config.getPercentBonus();

        int percentPlus = 0;
        for (int i = aNumber.size() - 1; i >= 0; i--) {
            if (numberHeroGerma >= aNumber.get(i)) {
                percentPlus = aPercent.get(i);
                break;
            }
        }

        if (uRank == -1) {
            return aLong;
        }

        if (uRank == 1) {
            aLong.addAll(afterPercentPlus(map.get(1), percentPlus));
            return aLong;
        }

        if (uRank > listRank[listRank.length - 1] || uRank == 0) {
            aLong.addAll(afterPercentPlus(map.get(101), percentPlus));
            return aLong;
        }

        for (int i = 1; i < listRank.length; i++) {
            if (uRank <= listRank[i]) {
                aLong.addAll(afterPercentPlus(map.get(listRank[i]), percentPlus));
                return aLong;
            }
        }

        return aLong;
    }

    public static String getDefaultHeroSummoned() {
        List<List<Integer>> aSummoned = new ArrayList<>();
        for (int i = 0; i < config.numberBoss; i++) {
            aSummoned.add(new ArrayList<>());
        }

        return StringHelper.toDBString(aSummoned);
    }

    public static List<Long> afterPercentPlus(List<Long> aBonus, int percentPlus) {
        List<Long> aLong = new ArrayList<>(aBonus);
//        if (aBonus.size() == 2){
//            aLong.set(1, aLong.get(1) * (100 + percentPlus) / 100);
//        }else{
//            aLong.set(3, aLong.get(3) * (100 + percentPlus) / 100);
//        }

        return aLong;
    }

    public static List<List<Long>> getAllRankBonus(int bossIndex) {
        List<List<Long>> aLLong = new ArrayList<>();
        Map<Integer, List<Long>> map = rankBonus.get(bossIndex);
//        for (Map.Entry<Integer, List<Long>> entry : map.entrySet()) {
//            aLLong.add(new ArrayList<>(entry.getValue()));
//        }
        aLLong.add(new ArrayList<>(map.get(1)));
        aLLong.add(new ArrayList<>(map.get(2)));
        aLLong.add(new ArrayList<>(map.get(3)));
        aLLong.add(new ArrayList<>(map.get(4)));
        aLLong.add(new ArrayList<>(map.get(5)));
        aLLong.add(new ArrayList<>(map.get(10)));
        aLLong.add(new ArrayList<>(map.get(20)));
        aLLong.add(new ArrayList<>(map.get(50)));
        aLLong.add(new ArrayList<>(map.get(100)));
        aLLong.add(new ArrayList<>(map.get(101)));

        return aLLong;
    }

    public static List<Long> getRandomBonus(int bossIndex, long damage) {
        ResBossServerEntity resBossServer = listResBoss.get(bossIndex);
        for (int i = resBossServer.getBonusDamage().listDamage.size() - 1; i >= 0; i--) {
            if (damage < resBossServer.getBonusDamage().listDamage.get(i)) continue;

            List<List<Long>> randomBonus = resBossServer.getBonusDamage().listRandomBonus.get(i);
            Random rd = new Random();
            int point = rd.nextInt(100000) + 1;
            int sum = config.rate.get(0);
            for (int j = 1; j < config.rate.size(); j++) {
                if (point <= sum) return randomBonus.get(j - 1);
                sum += config.rate.get(j);
                if (sum >= 100000) return randomBonus.get(randomBonus.size() - 1);
            }
            break;
        }

        return new ArrayList<>();
    }

//    public static List<Long> getURankBonus(AHandler handler, List<List<Integer>> top100) {
//        List<Long> aLong = new ArrayList<>();
//        int userId = handler.getMUser().getUser().getId();
//        UserBossServerEntity uBoss = getUBoss(handler);
//        int[] rank = new int[3];
//        for (List<Integer> aTop : top100) {
//            for (int i = 0; i < aTop.size(); i++) {
//                if (i == userId) {
//                    aLong.addAll()
//                }
//            }
//        }
//
//        List<Long> aLong = new ArrayList<>();
//
//        if (uRank == 1) {
//            aLong.addAll(rankBonus.get(0));
//            return aLong;
//        }
//        if (uRank > listRank[listRank.length - 1]) {
//            aLong.addAll(rankBonus.get(rankBonus.size() - 1));
//            return aLong;
//        }
//        for (int i = 1; i < listRank.length; i++) {
//            if (uRank <= listRank[i]) aLong.addAll(rankBonus.get(i));
//        }
//
//        return aLong;
//    }

    public static String sqlUpdateUBoss(UserBossServerEntity uBoss, int numberTurn, int newNumberTurnFree, long damage,
                                        int bossIndex, int serverId, int userId, String heroIds, long newMaxDamage, String newTeam) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = "update user_boss_server set number_turn=" + numberTurn + ", number_turn_free=" + newNumberTurnFree + ", last_time='" + df.format(new Date());
        sql += "' , heroes_attacked = '" + heroIds + "'";

        switch (bossIndex) {
            case 0:
//                if (uBoss.getLastRank(bossIndex) < 0) sql += ", last_rank1 = 0";
                sql += ", total_damage1=total_damage1 +" + damage + ", max_damage1=" + newMaxDamage + ", team1='" + newTeam + "'";
                break;
            case 1:
//                if (uBoss.getLastRank(bossIndex) < 0) sql += ", last_rank2 = 0";
                sql += ", total_damage2=total_damage2 +" + damage + ", max_damage2=" + newMaxDamage + ", team2='" + newTeam + "'";
                break;
            case 2:
//                if (uBoss.getLastRank(bossIndex) < 0) sql += ", last_rank3 = 0";
                sql += ", total_damage3=total_damage3 +" + damage + ", max_damage3=" + newMaxDamage + ", team3='" + newTeam + "'";
                break;
            case 3:
//                if (uBoss.getLastRank(bossIndex) < 0) sql += ", last_rank4 = 0";
                sql += ", total_damage4=total_damage4 +" + damage + ", max_damage4=" + newMaxDamage + ", team4='" + newTeam + "'";
                break;
            case 4:
//                if (uBoss.getLastRank(bossIndex) < 0) sql += ", last_rank5 = 0";
                sql += ", total_damage5=total_damage5 +" + damage + ", max_damage5=" + newMaxDamage + ", team5='" + newTeam + "'";
                break;
        }
        sql += " where server_id=" + serverId + " and user_id =" + userId;

        return sql;
    }

    public static int getMaxNumberBuy(MyUser mUser) {
        int vip = mUser.getUser().getVip();
        for (int i = 0; i < config.vipBuyTurn.maxTurn.length; i++) {
            if (vip == config.vipBuyTurn.vip[i]) return config.vipBuyTurn.maxTurn[i];
        }

        return 0;
    }

    public static int getMaxTurn(MyUser mUser) {
        return config.baseMaxTurn + getMaxNumberBuy(mUser);
    }

    public static List<Long> getFeeBuyTurn(UserBossServerEntity uBoss, int numberBuy) {
//        return Bonus.viewGem( uBoss.getNumberBuyDaily() * config.feeStep + config.feeStart);
        return Bonus.viewGem(config.feeStart * numberBuy);
    }

    public static Date getStartDate() {
//        System.out.println("CfgBossInterServer.getStartDate(): " + CfgBossInterServer.getStartDate());
        return DateTime.getDateOffset(CfgBossInterServer.getStartDate(), 7);
    }
}
