package monster.config;

import com.google.gson.Gson;
import monster.object.MyUser;
import monster.object.UserInt;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgOnlineBonus {
    public static DataConfig config;

    public static List<Long> getBonus(MyUser mUser) {
        List<Long> aLong = new ArrayList<>();
        UserInt uInt = mUser.getUData().getUInt();
        int time = uInt.getValue(UserInt.ONLINE_TIME);
        int bonusIndex = uInt.getValue(UserInt.ONLINE_RECEIVE);
        if (bonusIndex >= config.time.length) return new ArrayList<>();

        int timeRequired = config.time[bonusIndex];
        int timePass = (int) (System.currentTimeMillis() / 1000 - time);

        aLong.add((long) (timePass > timeRequired ? 0 : timeRequired - timePass));
//        aLong.add(0L);
        aLong.add((long) bonusIndex);
        for (List<Long> bonus : config.bonus) {
            aLong.addAll(bonus);
        }
        return aLong;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public int[] time;
        public List<List<Long>> bonus;
    }

}
