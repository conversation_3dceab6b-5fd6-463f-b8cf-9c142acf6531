package monster.config;


import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Getter;
import monster.dao.mapping.BossInterServerEntity;
import monster.dao.mapping.UserBossInterServerEntity;
import monster.dao.mapping.main.ResBossInterServerEntity;
import monster.dao.mapping.main.ResClusterEntity;
import monster.object.MyUser;
import monster.object.Rank2Controller;
import monster.service.resource.ResCluster;
import monster.service.user.Bonus;
import jakarta.persistence.*;

import java.text.SimpleDateFormat;
import java.util.*;

@Getter
public class CfgBossInterServer {
    public static DataConfig config;
    private static List<Map<Integer, List<Long>>> rankBonus = new ArrayList<>();
    private static final int[] listRank = {1, 2, 3, 4, 5, 10, 20, 50, 100};
    public static String str100, str0, str1, strNone;
    private static List<ResBossInterServerEntity> listResBoss = new ArrayList<>();
    public static Map<Integer, Integer> mPower = new HashMap<>();
    public static Map<Integer, Rank2Controller[]> rank2ControllersMapByCluster = new HashMap<>();
    public static final int EVENT_DURATION = 7;
    public static final int POSITION_BOSS = 4;

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();

        listResBoss = DBJPA.getList(CfgServer.DB_MAIN + "res_boss_inter_server", ResBossInterServerEntity.class);
        if (listResBoss == null || listResBoss.isEmpty()) throw new NullPointerException();

        rankBonus.clear();
        for (int i = 0; i < config.numberBoss; i++) {
            rankBonus.add(new HashMap<>());
            rankBonus.get(i).put(1, listResBoss.get(i).getBonusRank().rank1);
            rankBonus.get(i).put(2, listResBoss.get(i).getBonusRank().rank2);
            rankBonus.get(i).put(3, listResBoss.get(i).getBonusRank().rank3);
            rankBonus.get(i).put(4, listResBoss.get(i).getBonusRank().rank4);
            rankBonus.get(i).put(5, listResBoss.get(i).getBonusRank().rank5);
            rankBonus.get(i).put(10, listResBoss.get(i).getBonusRank().rank10);
            rankBonus.get(i).put(20, listResBoss.get(i).getBonusRank().rank20);
            rankBonus.get(i).put(50, listResBoss.get(i).getBonusRank().rank50);
            rankBonus.get(i).put(100, listResBoss.get(i).getBonusRank().rank100);
            rankBonus.get(i).put(101, listResBoss.get(i).getBonusRank().rankDefault);
        }

        List<Integer> a100 = new ArrayList<>();
        List<Integer> a0 = new ArrayList<>();
        List<Integer> a1 = new ArrayList<>();
        List<Integer> aNone = new ArrayList<>();
        for (int i = 0; i < config.numberBoss; i++) {
            a100.add(100);
            a0.add(0);
            a1.add(1);
            aNone.add(-1);
        }
        str100 = a100.toString().replace(" ", "");
        str0 = a0.toString().replace(" ", "");
        str1 = a1.toString().replace(" ", "");
        strNone = aNone.toString().replace(" ", "");
    }

    @Getter
    public class DataConfig {
        private int baseMaxTurn;
        private List<Integer> rate;
        private int feeStart, feeStep;
        private VipBuyTurn vipBuyTurn;
        private List<Integer> numberHeroGerma;
        private List<Integer> percentBonus;
        private int numberBoss, numberTop, levelRequireHero;
        private int numAttackedToScale;
        private int numberDayClose;
        private int numberDayOpen;
        private int basePower;

        public void init() {
            Collections.sort(numberHeroGerma);
            Collections.sort(percentBonus);
        }
    }

    private class VipBuyTurn {
        int[] vip, maxTurn;
    }

    public class RankBonus {
        List<Long> rank1, rank2, rank3, rank4, rank5, rank10, rank20, rank50, rank100, rankDefault;
    }

    public class DamageBonus {
        List<Long> listDamage;
        List<List<List<Long>>> listRandomBonus;
    }
    //endregion

    //region Boss
    public static Date getStartCountDate() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, 2025);
        cal.set(Calendar.HOUR_OF_DAY, 9);
        cal.set(Calendar.MONTH, Calendar.APRIL);
        cal.set(Calendar.DAY_OF_MONTH, 8);
        cal.set(Calendar.MINUTE, 55);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static int getEventId() {
        Date now = new Date();
        if (now.before(getStartCountDate())) return -1;

        return (int) (DateTime.numberDayPassed(getStartCountDate(), now) / EVENT_DURATION / 2) + 2;
    }

    public static void initBoss() {
        EntityManager session = null;
        List<BossInterServerEntity> listBossInterServer = new ArrayList<>();
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            List<ResClusterEntity> listResCluster = DBJPA.getQueryList("select * from dson_main.res_cluster where function_id = " + ResCluster.FUNCTION_BOSS_INTER_SERVER, ResClusterEntity.class);
            for (ResClusterEntity resCluster : listResCluster) {
                BossInterServerEntity bossInterServer;
                List<BossInterServerEntity> listBoss = session.createNativeQuery("select * from dson.boss_inter_server where event_id = " + CfgBossInterServer.getEventId() + " and cluster_id = " + resCluster.getCluster(), BossInterServerEntity.class).getResultList();
                if (!listBoss.isEmpty()) bossInterServer = listBoss.get(0);
                else {
                    bossInterServer = new BossInterServerEntity(getEventId(), resCluster.getCluster());
                    session.persist(bossInterServer);
                }
                if (bossInterServer == null) return;
                listBossInterServer.add(bossInterServer);
            }
            session.getTransaction().commit();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        //Qua ngày thì random monster mới
        listBossInterServer.stream().filter(BossInterServerEntity::canRandomNewDay).forEach(bossInterServer -> {
            List<Integer> aNewHeroKey = bossInterServer.getNewRandomHero();
            bossInterServer.dbUpdateNewDay(aNewHeroKey);
        });
    }

    public static List<ResBossInterServerEntity> getListResBoss() {
        List<ResBossInterServerEntity> resBosses = new ArrayList<>();
        for (int i = 0; i < config.getNumberBoss(); i++) {
            resBosses.add(listResBoss.get(i));
        }

        return resBosses;
    }

    public static List<Integer> getListResBossId() {
        List<Integer> listResBossId = new ArrayList<>();
        for (int i = 0; i < config.getNumberBoss(); i++) {
            listResBossId.add(listResBoss.get(i).getId());
        }

        return listResBossId;
    }

    public static void initRankController(int eventId, int clusterId, int userId) {
        if (rank2ControllersMapByCluster.get(clusterId) == null) {
            Rank2Controller[] rank2Controllers = new Rank2Controller[config.numberBoss];
            rank2ControllersMapByCluster.put(clusterId, rank2Controllers);
        }

        String keyCacheRank = "rank:boss_inter_server_11";
        int numberTop = CfgBossInterServer.config.getNumberTop();
        for (int bossIndex = 0; bossIndex < CfgBossInterServer.config.numberBoss; bossIndex++) {
            String redisKey = CfgServer.isTestServer() && userId != 702338 ?
                    String.format("test:%s:%s:%s:%s", keyCacheRank, eventId, clusterId, bossIndex) :
                    String.format("%s:%s:%s:%s", keyCacheRank, eventId, clusterId, bossIndex);
            String damageKey = "total_damage" + (bossIndex + 1);
            String sql = String.format("select user_id, " + damageKey + " from user_boss_inter_server where event_id=" + eventId + " and cluster_id = " + clusterId + " and " + damageKey + " > 0 order by " + damageKey + " desc limit 0,%s", numberTop);
            Rank2Controller rank2Controller = Rank2Controller.builder().numberTop(numberTop).key(redisKey).defaultPoint(0)
                    .sql(sql).build();
            rank2Controller.init();
            rank2ControllersMapByCluster.get(clusterId)[bossIndex] = rank2Controller;
        }
    }

    public static void updateUserRank(int bossIndex, UserBossInterServerEntity uBoss, long totalDamage) {
        if (uBoss.getServerId() < 4) return;

        rank2ControllersMapByCluster.get(uBoss.getClusterId())[bossIndex].updatePoint(uBoss.getUserId(), totalDamage);
    }

    public static int getUserRank(int bossIndex, UserBossInterServerEntity uBoss) {
        int rank = rank2ControllersMapByCluster.get(uBoss.getClusterId())[bossIndex].getRank(uBoss.getUserId(), Rank2Controller.bossInterServer);
        return rank > CfgBossServer.config.getNumberTop() && uBoss.getDamage(bossIndex) <= 0 ? -1 : rank;
    }
    //endregion

    public static List<Long> getURankBonus(int bossIndex, int uRank, int numberHeroGerma) {
        List<Long> aLong = new ArrayList<>();
        //        Map<Integer, List<Long>> map = selfDie == 1 ? rankBonusBuster.get(bossIndex) : rankBonus;
        Map<Integer, List<Long>> map = rankBonus.get(bossIndex);
        List<Integer> aNumber = config.getNumberHeroGerma();
        List<Integer> aPercent = config.getPercentBonus();

        int percentPlus = 0;
        for (int i = aNumber.size() - 1; i >= 0; i--) {
            if (numberHeroGerma >= aNumber.get(i)) {
                percentPlus = aPercent.get(i);
                break;
            }
        }

        if (uRank == -1) {
            return aLong;
        }

        if (uRank == 1) {
            aLong.addAll(afterPercentPlus(map.get(1), percentPlus));
            return aLong;
        }

        if (uRank > listRank[listRank.length - 1] || uRank == 0) {
            aLong.addAll(afterPercentPlus(map.get(101), percentPlus));
            return aLong;
        }

        for (int i = 1; i < listRank.length; i++) {
            if (uRank <= listRank[i]) {
                aLong.addAll(afterPercentPlus(map.get(listRank[i]), percentPlus));
                return aLong;
            }
        }

        return aLong;
    }

    public static String getDefaultHeroSummoned() {
        List<List<Integer>> aSummoned = new ArrayList<>();
        for (int i = 0; i < config.numberBoss; i++) {
            aSummoned.add(new ArrayList<>());
        }

        return StringHelper.toDBString(aSummoned);
    }

    public static List<Long> afterPercentPlus(List<Long> aBonus, int percentPlus) {
        List<Long> aLong = new ArrayList<>(aBonus);
        // int index = 0;
        // while (index < aLong.size()){
        //     if(aLong.get(index) == 2){
        //         aLong.set(index + 1, aLong.get(index + 1) * (100 + percentPlus) / 100);
        //         index +=2 ;
        //     }else if(aLong.get(index) == 3, a){
        //         aLong.set(index + 3, aLong.get(index + 3) * (100 + percentPlus) / 100);
        //         index +=4;
        //     }
        // }

        return aLong;
    }

    public static List<List<Long>> getAllRankBonus(int bossIndex) {
        List<List<Long>> aLLong = new ArrayList<>();
        Map<Integer, List<Long>> map = rankBonus.get(bossIndex);
        //        for (Map.Entry<Integer, List<Long>> entry : map.entrySet()) {
        //            aLLong.add(new ArrayList<>(entry.getValue()));
        //        }
        aLLong.add(new ArrayList<>(map.get(1)));
        aLLong.add(new ArrayList<>(map.get(2)));
        aLLong.add(new ArrayList<>(map.get(3)));
        aLLong.add(new ArrayList<>(map.get(4)));
        aLLong.add(new ArrayList<>(map.get(5)));
        aLLong.add(new ArrayList<>(map.get(10)));
        aLLong.add(new ArrayList<>(map.get(20)));
        aLLong.add(new ArrayList<>(map.get(50)));
        aLLong.add(new ArrayList<>(map.get(100)));
        aLLong.add(new ArrayList<>(map.get(101)));

        return aLLong;
    }

    public static List<Long> getRandomBonus(int bossIndex, long damage) {
        ResBossInterServerEntity resBossInterServer = listResBoss.get(bossIndex);
        for (int i = resBossInterServer.getBonusDamage().listDamage.size() - 1; i >= 0; i--) {
            if (damage < resBossInterServer.getBonusDamage().listDamage.get(i)) continue;

            List<List<Long>> randomBonus = resBossInterServer.getBonusDamage().listRandomBonus.get(i);
            Random rd = new Random();
            int point = rd.nextInt(100000) + 1;
            int sum = config.rate.get(0);
            for (int j = 1; j < config.rate.size(); j++) {
                if (point <= sum) return randomBonus.get(j - 1);
                sum += config.rate.get(j);
                if (sum >= 100000) return randomBonus.get(randomBonus.size() - 1);
            }
            break;
        }

        return new ArrayList<>();
    }

    public static String sqlUpdateUBoss(int numberTurn, int newNumberTurnFree, long damage, int bossIndex, int userId, String heroIds, long newMaxDamage, int clusterId, String newTeam) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sql = "update user_boss_inter_server set number_turn=" + numberTurn + ", number_turn_free=" + newNumberTurnFree + ", last_time='" + df.format(new Date());
        sql += "' , heroes_attacked = '" + heroIds + "'";

        switch (bossIndex) {
            case 0:
                sql += ", total_damage1=total_damage1 +" + damage + ", max_damage1=" + newMaxDamage + ", team1='" + newTeam + "'";
                break;
            case 1:
                sql += ", total_damage2=total_damage2 +" + damage + ", max_damage2=" + newMaxDamage + ", team2='" + newTeam + "'";
                break;
            case 2:
                sql += ", total_damage3=total_damage3 +" + damage + ", max_damage3=" + newMaxDamage + ", team3='" + newTeam + "'";
                break;
            case 3:
                sql += ", total_damage4=total_damage4 +" + damage + ", max_damage4=" + newMaxDamage + ", team4='" + newTeam + "'";
                break;
            case 4:
                sql += ", total_damage5=total_damage5 +" + damage + ", max_damage5=" + newMaxDamage + ", team5='" + newTeam + "'";
                break;
        }
        sql += " where user_id =" + userId + " and event_id = " + getEventId() + " and cluster_id = " + clusterId;

        return sql;
    }

    public static int getMaxNumberBuy(MyUser mUser) {
        int vip = mUser.getUser().getVip();
        for (int i = 0; i < config.vipBuyTurn.maxTurn.length; i++) {
            if (vip == config.vipBuyTurn.vip[i]) return config.vipBuyTurn.maxTurn[i];
        }

        return 0;
    }

    private static boolean updateUBoss(String... sqls) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            for (String sql : sqls) {
                session.createNativeQuery(sql).executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }

    public static Date getStartDate() {
        Date startDate = DateTime.getDateOffset(getStartCountDate(), (getEventId() - 2) * EVENT_DURATION * 2);
        return startDate;
    }

    public static Date getEndAttackDate() {
        Date endAttackDate = DateTime.getDateOffset(getStartDate(), EVENT_DURATION - 2);
        return endAttackDate;
    }

    public static Date getEndDate() {
        Date endDate = DateTime.getMilliSecondOffset(DateTime.getDateOffset(getStartDate(), EVENT_DURATION), -DateTime.MIN_MILLI_SECOND);
        return endDate;
    }

    public static List<Long> getFeeBuyTurn(int numberBuy) {
        //        return Bonus.viewGem( uBoss.getNumberBuyDaily() * config.feeStep + config.feeStart);
        return Bonus.viewGem(config.feeStart * numberBuy);
    }
}
