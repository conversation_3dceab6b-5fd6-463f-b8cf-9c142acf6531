package monster.config.penum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum BattleType {
    NONE(0, "battlelogs"),
    MODE_RAID(1),
    MODE_CAMPAIGN(2),
    MODE_TOWER1(3),
    MODE_STORY(4),
    MODE_ASPEN_DUNGEON(5, 11), // hu<PERSON>n luy<PERSON>, dinh thự kì bí
    MODE_GUILD_BOSS(6, 3),
    MODE_TOWER2(8),
    MODE_BOSS_SERVER(9, 4),
    MODE_CRYSTALCROWN(13, true, 14),
    MODE_TRIALOFCHAMPION(14, true, 14),
    MODE_ALLSTARSLEAGUE(15, true, "/home/<USER>/data/battlelogs/corrida", 14),
    MODE_PRACTICE(16),
    MODE_CHALLENGE(17, 4), // tàu vô tận
    MODE_CLOUD_VILLAGE(18),
    <PERSON><PERSON><PERSON>_MAZE(19),
    M<PERSON><PERSON>_RAID_RAIN_VILLAGE(20),
    <PERSON>OD<PERSON>_RAID_SAND_VILLAGE(21),
    <PERSON><PERSON><PERSON>_RAID_MISTINESS_VILLAGE(22),
    MODE_FRIEND_BOSS(23),
    MODE_HELP_FRIEND_FIGHT_BOSS(24),
    MODE_FRIEND_ATTACK(25, true, 2), // Khiêu chiến bạn bè
    MODE_PEAK_OF_TIME(26),
    MURIM_CHALLENGE(27, 12),
    MODE_BATTLE_TEST_RELIC(28),
    MODE_WAR_CLAN(29, true, "/home/<USER>/data/battlelogs/warclan", 14),
    EVENT_BOSS_SIGMA(30),
    MODE_BOSS_INTER_SERVER(31),
    MODE_FISHING_PEARL_ROB(32),
    BOSS_SILENT_RING(33),
    MODE_FISHING_PEARL_MINING(34),
    MODE_STORY_MONSTER(35, 16),
    MODE_STORY_BOSS(36, 4),
    MODE_TERRITORY(37), // background theo factionId Nước 8 | lửa 7 | gió 16 | tối 3 | ánh sáng 5
    MODE_BOSS_NEW_HERO(38),
    MODE_ABYSS(39),
    MODE_REPLAY(99),
    MODE_QUICK_BONUS(1001), // fake for redRibbon event
    MODE_STORY_MISSION(100),

    ARENA_SERVER(40),
    ARENA3_SERVER(41),
    MODE_THREE_STAR_ARENA(42),
    //    WAR_CLAN(1, "/home/<USER>/data/battlelogs/warclan"),
    ;

    public int value, background;
    public boolean isPVP = false;
    public String logPath;

    BattleType(int value) {
        this.value = value;
        this.logPath = "battlelogs";
    }

    BattleType(int value, int background) {
        this.value = value;
        this.background = background;
        this.logPath = "battlelogs";
    }

    BattleType(int value, boolean isPVP, int background) {
        this.value = value;
        this.isPVP = isPVP;
        this.background = background;
        this.logPath = "battlelogs";
    }

    BattleType(int value, boolean isPVP, String logPath, int background) {
        this.value = value;
        this.isPVP = isPVP;
        this.logPath = logPath;
        this.background = background;
    }

    BattleType(int value, String logPath) {
        this.value = value;
        this.logPath = logPath;
    }

    public boolean isDisarmEnable() {
        return !disarmDisableMode.contains(this);
    }

    public static List<BattleType> disarmDisableMode = Arrays.asList(BattleType.MODE_BOSS_SERVER); // , BattleType.CLAN_FIRE, BattleType.CLAN_RAID
    public static final List<BattleType> listCheckHeroTest = Arrays.asList(MODE_GUILD_BOSS, MODE_STORY_BOSS, MODE_CAMPAIGN, MODE_STORY, MODE_CRYSTALCROWN, MODE_TRIALOFCHAMPION,
            MODE_ALLSTARSLEAGUE, MODE_TERRITORY, MODE_ASPEN_DUNGEON, MODE_CHALLENGE, MODE_PEAK_OF_TIME, MODE_FRIEND_ATTACK);
    // lookup
    static Map<Integer, BattleType> lookup = new HashMap<>();

    static {
        for (BattleType battleType : values()) {
            lookup.put(battleType.value, battleType);
        }
    }

    public static BattleType get(int value) {
        return lookup.get(value);
    }

}
