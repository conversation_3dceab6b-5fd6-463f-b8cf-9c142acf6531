package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum Status {
    BLOCK_CHAT(1),
    BLOCK_LOGIN(2),

    MISSION_NONE(0),
    MISSION_FINISH(1),
    MISSION_RECEIVED(2);
    //
    public int value;

    Status(int value) {
        this.value = value;
    }

    public static Map<Integer, Status> missionStatus = new HashMap<>();

    static {
        for (Status value : values()) {
            if (value.name().contains("MISSION")) {
                missionStatus.put(value.value, value);
            }
        }
    }

    public static Status getMissionStatus(int value) {
        return missionStatus.get(value);
    }

}
