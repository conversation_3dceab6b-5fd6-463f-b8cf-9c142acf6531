package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum MegaBoxType {
    NULL(0),
    CAMPAIGN_LEVEL(1),
    HAS_VIP(2),
    DAY(3),
    TOWER1(4),
    TOWER2(5);

    public int value;

    MegaBoxType(int value) {
        this.value = value;
    }

    // lookup
    static Map<Integer, MegaBoxType> lookup = new HashMap<>();

    static {
        for (MegaBoxType itemType : values()) {
            lookup.put(itemType.value, itemType);
        }
    }

    public static MegaBoxType get(int type) {
        return lookup.get(type);
    }
}
