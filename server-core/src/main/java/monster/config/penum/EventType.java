package monster.config.penum;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.game.challenge.entity.ResEventType;
import monster.game.challenge.service.ChallengeNoteService;
import monster.game.challenge.service.ChallengeService;
import monster.object.MyUser;
import monster.server.config.Guice;
import monster.service.battle.common.config.HeroType;
import monster.service.monitor.EventMonitor;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum EventType {
    E_POINT(43, true),
    KIM_NGUYEN_BAO(1000),
    XU(1001),
    EXP(1002),
    HEROIC_SUMMON(1003), // xong
    PROPHET_SUMMON(1006), // xong
    SUPER_CASINO(1113), // xong
    FUSE_5STAR(1008), // xong
    FUSE_6STAR(1009), // xong
    TAVERN_4STAR(1010), // xong
    TAVERN_5STAR(1011), // xong
    TAVERN_6STAR(1012), // xong
    TAVERN_7STAR(1013), // xong
    GET_HERO_WATER(1014), // xong
    GET_HERO_LUNISOLAR(1015), // xong
    GET_HERO_LAND(1016), // xong
    GET_HERO_FIRE(1017), // xong
    GET_HERO_WIND(1018), // xong
    GET_HERO_LINGTNING(1019), // xong
    FUSE_9STAR(1020), // xong
    FUSE_10STAR(1021), // xong
    USER_LEVEL_UP(1022), // xong
    TOPUP_125GEM(1023), // Không cần gửi
    TOPUP_250GEM(1024), // Không cần gửi
    TOPUP_500GEM(1025), // Không cần gửi
    TOPUP_750GEM(1026), // Không cần gửi
    TOPUP_1250GEM(1027), // Không cần gửi
    TOPUP_2500GEM(1028), // Không cần gửi
    TOPUP_5000GEM(1029), // Không cần gửi
    TOPUP_12500GEM(1030), // Không cần gửi
    BOSS_BROKEN_SPACE(1031), // xong
    VIP_EXP(1033), // xong
    CAMPAIGN_LEVEL(2210), // xong -> level hiện tại
    TOWER_OBLIVION(1036), // xong -> level hiện tại
    CELESTIAL_KILL_ISLAND(1037), // xong
    CELESTIAL_BUILD_ISLAND(1038), // xong
    FORGE_ITEM(1039), // xong
    MAKE_FRIEND(2211), // xong -> level hiện tại
    UPGRADE_SKILL_CLAN(1042), // xong
    CELESTIAL_BUY_DAFFODIL(1043), // xong
    BUY_ATTACK_BROKEN_SPACE(1044), // xong
    BUY_SHOP_ALTAR(1045), // xong
    BUY_SHOP_CASINO(1046), // xong
    BUY_SHOP_CLAN(1047), // xong
    BUY_SHOP_ARENA(1048), // xong
    FUSE_7STAR(1049), // xong
    PROPHET_REPLACE(1050), // xong
    RUONG_THAN_BI(1052), // xong
    HUYET_TE_3_SAO(1053), //
    GET_HERO_5STAR(1054), //
    GET_HERO_4STAR(1055), //
    //
    BRAVE_TRIAL_DAY(1059),
    BRAVE_TRIAL_NIGHT(1060),
    //
    GEM_MINUS(2001),
    GEM_PLUS(2002),
    NUBMER_ATK_TOWER_ZERO(2006),
    FUSE_8STAR(2009),
    //
    TOWER2_LEVEL(2037),
    // Gói trang bị đặc thù
    SPECIAL_ITEM_CLASS_WARRIOR_LEVEL(2038),
    SPECIAL_ITEM_CLASS_CLASS_MAGE_LEVEL(2039),
    SPECIAL_ITEM_CLASS_RANGER_LEVEL(2040),
    SPECIAL_ITEM_CLASS_ASSASSIN_LEVEL(2041),
    SPECIAL_ITEM_CLASS_PRIEST_LEVEL(2042),
    //
    SPECIAL_ITEM_RANK_S(2061),
    SPECIAL_ITEM_RANK_SS(2062),

    GET_HERO_E1(2044),
    GET_HERO_E2(2045),
    GET_HERO_E3(2046),
    GET_HERO_E4(2047),
    GET_HERO_E5(2048),
    // Nhiệm vụ sổ tay đại hải trình
    COMPLETE_DAILY_MISSION(2103),
    GIVE_FRIEND_HEART(2104),
    MIDAS(2112),
    CAMPAIGN_LOOT(2107),
    BUY_MARKETPLACE(2108),
    HERO_ALTAR(2109),
    CAMPAIGN_ATTACK(2110),
    CAMPAIGN_STAGE(2111),
    EVENT_RAID(2106),
    WAR_CLAN_JOIN(2113),
    CLAN_CHECK_IN(2114),
    TOWER1_ATTACK(2116),
    TOWER2_ATTACK(2117),
    BROKEN_SPACE_ATTACK(2118),
    LABYRINTH_ATTACK_LAST_BOSS(2119),
    CLAN_MILL_ORDER_5STAR(2120),
    YELLOW_STAR(2121, true),
    QUICK_BONUS_CAMPAIGN(2122),
    ARENA_CRYSTAL_ATTACK_WIN(2123),
    ARENA_CRYSTAL_ATTACK(2124),
    ARENA_TRIAL_ATTACK_WIN(2125),
    ARENA_CRYSTAL_POINT(2126),
    LABYRINTH_EASY_LEVEL(2127),
    HERO_LEVEL_100(2128),
    HERO_LEVEL_140(2129),
    HERO_LEVEL_200(2130),
    HERO_STAR_6(2131),
    HERO_STAR_7(2132),
    HERO_STAR_9(2133),
    TAVERN_COMPLETE(2134),
    TOP_SUMMON(2135),
    TOP_USER_POWER(2136),
    RUBY_ADD(2137),
    FIRST_HERO_6STAR(2138),
    FIRST_SUMMON_SSR(2139),
    FIRST_HERO_LEVEL_80(2140),
    EVENT_POINT_ARENA1(2141),
    EVENT_POINT_ARENA2(2142),
    TOP_USER_POWER_6(2143),
    //2144 điểm ự kiện rương báu
    EVENT_SIGMA_TOTAL(2145),
    HERO_STAR_8(2146),
    LABYRINTH_COMPLETE(2147),
    UPGRADE_TREASURE(2148),

    HERO_FACTION_HOA_UP_STAR(2149), // fire
    HERO_FACTION_PHONG_UP_STAR(2150), // wind
    HERO_FACTION_NUOC_UP_STAR(2151), // water
    HERO_FACTION_QUY_UP_STAR(2152), // Lunisolar

    GET_HERO5_STAR_HEROIC(2153),
    GET_HERO5_STAR_PROPHET(2154),
    GET_SSR_HERO5_STAR_HEROIC(2155),
    GET_SSR_HERO5_STAR_PROPHET(2156),

    // Chess master
    CHESS_MASTER_POINT(2157),
    CHESS_MASTER_MAX_POINT(2158),
    CHESS_MASTER_PAWN(2159),
    CHESS_MASTER_ROOK(2160),
    CHESS_MASTER_BISHOP(2161),
    CHESS_MASTER_KNIGHT(2162),
    CHESS_MASTER_QUEEN(2163),
    CHESS_MASTER_KING(2164),

    EXP_TRANGPHUC(5002, true),
    EXP_LABYRINTH(5001, true),
    EXP_TOWER1(5000, true),
    EXP_ARENA(5003),
    //
    EXP_PASS_EXP_FISHING(5005),
    EXP_PASS_EXP_FISHING_PEARL(5006),
    EXP_PASS_EXP_HUYET_QUY(5007),
    EXP_PASS_EXP_KY_NGO(5008),
    EXP_PASS_EXP_THU_THAP_NHANH(5009),
    EXP_EVENT_SINH_NHAT(5010),
    EXP_EVENT_S_PASS(5011),
    //
    EVENT_VOID_STONE(200019),
    //
    TIME_DAY_IN_WEEK(1),

    TIME_MINUTE_IN_DAY(1),
    EVENT_VOID_SKILL(10000),

    // Nhiệm vụ sổ tay chú thuật
    INDENTURE_LEVEL(2165),
    ROYAL_PALACE_SUM_LEVEL(2166),
    HERO_STAR_10(2174),
    HERO_STAR_11(2175),
    HERO_STAR_12(2176),
    HERO_STAR_13(2177),
    HERO_STAR_14(2178),
    HERO_STAR_15(2179),
    PET_LEVEL(2167),
    USER_POWER(2181),
    USER_LEVEL(2182),
    HERO_STAR_5(2183),
    TREASURE_LEVEL_2(2184),
    ITEM_XANH_LAM_1SAO(2185),
    ITEM_TIM_1SAO(2186),
    ITEM_VANG_1SAO(2187),
    ITEM_VANG_2SAO(2188),
    ITEM_VANG_3SAO(2189),
    ITEM_VANG_4SAO(2190),
    ITEM_DO_1SAO(2191),
    TREASURE_LEVEL_3(2192),
    TREASURE_LEVEL_4(2193),
    TREASURE_LEVEL_5(2194),
    TREASURE_LEVEL_6(2195),
    TREASURE_LEVEL_7(2196),
    BOSS_STORY_ATTACK(2197),
    DAILY_MISSION(2198),
    WEEKLY_MISSION(2199),

    TERRITORY_SWEEP(2200),
    STORY_DAILY_MONSTER_KILL(2201),
    STORY_DAILY_CHEST_RECEIVE(2202),
    MONTHLY_CARD_SMALL(2203),
    MONTHLY_CARD_BIG(2204),
    LABYRINTH_LEVEL(2207),
    NHIEMVU_TRUTA(2208),
    NHIEMVU_TRUTA_SS(2209),

    STORY_LEVEL(2036),
    BUY_SHOP(2212),
    BOSS_CLAN_ATTACK(2213), // xong
    ASPEN_DUNGEON_LEVEL(2214), // Dinh thự kì bí
    TERRITORY_COUNT_LEVEL(2215),
    SUMMON_LIMIT2(2216), // Chiêu mộ tinh tú
    CASINO(2217), // xong
    GET_HERO_5STAR_NOT_FUSE(2218),

    // Nhiệm vụ này chỉ chạy trong game - ko chạy với a trường
    HAS_HERO_LEVEL_10(2219),
    HAS_HERO_LEVEL_20(2220),
    HAS_HERO_LEVEL_30(2221),
    HAS_HERO_LEVEL_40(2222),
    HAS_HERO_LEVEL_50(2223),
    HAS_HERO_LEVEL_60(2224),
    HAS_HERO_LEVEL_70(2225),
    HAS_HERO_LEVEL_80(2226),
    HAS_HERO_LEVEL_90(2227),
    HAS_HERO_LEVEL_100(2228),
    HAS_HERO_LEVEL_110(2229),
    HAS_HERO_LEVEL_120(2230),
    HAS_HERO_LEVEL_130(2231),
    HAS_HERO_LEVEL_140(2232),
    HAS_HERO_LEVEL_150(2233),
    HAS_HERO_LEVEL_160(2234),
    HAS_HERO_LEVEL_170(2235),
    HAS_HERO_LEVEL_180(2236),
    HAS_HERO_LEVEL_190(2237),
    HAS_HERO_LEVEL_200(2238),

    HERO_STAR_16(2246),
    HERO_STAR_17(2247),
    HERO_STAR_18(2248),
    HERO_STAR_19(2249),
    HERO_STAR_20(2250),

    PET_OWN_ID_1(2251),
    PET_OWN_ID_2(2252),
    PET_OWN_ID_3(2253),
    PET_OWN_ID_4(2254),
    PET_OWN_ID_5(2255),
    PET_OWN_ID_6(2256),
    PET_OWN_ID_7(2257),
    PET_OWN_ID_8(2258),

    // sự kiện gói hạn giờ theo sao tướng
    EVENT_PACKAGE_HERO_STAR(2259), // 2259_star(xx)_heroId(yyy) tối ưu thì gửi 1 lần
    EVENT_NEW_HERO_SUMMON(2260),
    EVENT_NEW_HERO_BOSS(2261),
    EVENT_NEW_HERO_BOSS_DAMAGE(2262),

    PET_UNLOCK(2263),
    PET_UP_TO_2STAR(2264),
    PET_UP_TO_3STAR(2265),
    PET_UP_TO_4STAR(2266),
    //
    ROYAL_PALACE_LEVEL_LUA(2267),
    ROYAL_PALACE_LEVEL_GIO(2268),
    ROYAL_PALACE_LEVEL_NUOC(2269),
    ROYAL_PALACE_LEVEL_ANHSANG(2270),
    ROYAL_PALACE_LEVEL_BONGTOI(2271),
    //
    GEM_PASS(2272),
    GEM_SUMMON_POINT(2273),
    GEM_SUMMON_NUMBER(2274),
    //
    TOWER2(2275),

    // nhiệm vụ mèo
    CAT_EAT(2276), // done
    CAT_HELP_FRIEND(2277),
    CAT_ACCELERATION(2278), // done
    CAT_CLEANSE(2279), // done
    CAT_TRAINING(2280), // done
    // Mèo 1
    CAT_1_PURIFYING_ATTRIBUTES(2281), // done: thuộc tính tẩy luyện mèo 1 đạt y dòng
    CAT_1_WITH_RARE_ATTRIBUTES(2282), // thuộc tính mèo thuộc tính hiếm
    CAT_1_ATTACHED_EXTREME_ATTRIBUTES(2283), //thuộc tính mèo thuộc tính cực
    CAT_1_REACHES_STAR(2284),
    // MÈO 2
    CAT_2_PURIFYING_ATTRIBUTES(2285), // done: thuộc tính mèo đạt y dòng
    CAT_2_WITH_RARE_ATTRIBUTES(2286), // thuộc tính mèo thuộc tính hiếm
    CAT_2_ATTACHED_EXTREME_ATTRIBUTES(2287), //thuộc tính mèo thuộc tính cực
    CAT_2_REACHES_STAR(2288),
    // MÈO 3
    CAT_3_PURIFYING_ATTRIBUTES(2289), // done: thuộc tính mèo đạt y dòng
    CAT_3_WITH_RARE_ATTRIBUTES(2290), // thuộc tính mèo thuộc tính hiếm
    CAT_3_ATTACHED_EXTREME_ATTRIBUTES(2291), //thuộc tính mèo thuộc tính cực
    CAT_3_REACHES_STAR(2292),
    // MÈO 4
    CAT_4_PURIFYING_ATTRIBUTES(2293),
    CAT_4_WITH_RARE_ATTRIBUTES(2294),
    CAT_4_ATTACHED_EXTREME_ATTRIBUTES(2295),
    CAT_4_REACHES_STAR(2296),
    // MÈO 5
    CAT_5_PURIFYING_ATTRIBUTES(2297),
    CAT_5_WITH_RARE_ATTRIBUTES(2298),
    CAT_5_ATTACHED_EXTREME_ATTRIBUTES(2299),
    CAT_5_REACHES_STAR(2300),
    // MÈO 6
    CAT_6_PURIFYING_ATTRIBUTES(2301),
    CAT_6_WITH_RARE_ATTRIBUTES(2302),
    CAT_6_ATTACHED_EXTREME_ATTRIBUTES(2303),
    CAT_6_REACHES_STAR(2304),
    // MÈO 7
    CAT_7_PURIFYING_ATTRIBUTES(2305),
    CAT_7_WITH_RARE_ATTRIBUTES(2306),
    CAT_7_ATTACHED_EXTREME_ATTRIBUTES(2307),
    CAT_7_REACHES_STAR(2308),
    // MÈO 8
    CAT_8_PURIFYING_ATTRIBUTES(2309),
    CAT_8_WITH_RARE_ATTRIBUTES(2310),
    CAT_8_ATTACHED_EXTREME_ATTRIBUTES(2311),
    CAT_8_REACHES_STAR(2312),

    // MINIGAME
    // ANIMAL_CHESS
    ANIMAL_CHESS_PLAY(2313),
    ANIMAL_CHESS_VOI(2314),
    ANIMAL_CHESS_SUTU(2315),
    ANIMAL_CHESS_HO(2316),
    ANIMAL_CHESS_CAO(2317),
    ANIMAL_CHESS_SOI(2318),
    ANIMAL_CHESS_CHO(2319),
    ANIMAL_CHESS_MEO(2320),
    ANIMAL_CHESS_CHUOT(2321),

    //DICE
    DICE_FINISH_ROUND(2322),
    //Exp pass Nuôi mèo
    EXP_PASS_CAT_RAISING(2323),
    //
    ARENA_SERVER_REGISTER(12188),
    ARENA_SERVER_BET(12189),
    ARENA_SERVER_BET_SUCCESS(12190),
    // Đấu trường tam tinh
    ARENA_TEAM_ATTACK(12206),
    ARENA_TEAM_BET(12212),
    //
    ARENA3_SERVER_REGISTER(12208),
    ARENA3_SERVER_BET(12209),
    ARENA3_SERVER_BET_SUCCESS(12210),

    ;

    public static Date startTime;

    public int id;
    public boolean inEvent, inChallenge, inNote;
    public boolean inEventPriority;
    public boolean maxPoint = false;
    public int clientToGo, maxPerDay;

    EventType(int id) {
        this.id = id;
    }

    EventType(int id, int clientToGo) {
        this.id = id;
        this.clientToGo = clientToGo;
    }

    EventType(int id, boolean... inEventPriority) {
        this.id = id;
        if (inEventPriority.length > 0) this.inEventPriority = inEventPriority[0];
    }

    static {
        try {
            startTime = DateTime.get_yyyyMMdd().parse("20181201");
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public void addEventMaxPerDay(MyUser mUser, long... number) {
        long value = number.length > 0 ? number[0] : 1;
        if (maxPerDay > 0) {
            String key = DateTime.getTimeKey(new Date()) + "event_type_" + mUser.getUser().getId() + "_" + name();
            long curValue = JCache.getInstance().getLongValue(key);
            if (curValue == -1) curValue = 0;
            value = Math.min(maxPerDay - curValue, value);
            if (value <= 0) return;
            JCache.getInstance().setValue(key, String.valueOf(value + curValue), (int) DateTime.DAY_SECOND);
        }
        if (value > 0) addEvent(mUser, value);
    }

    public void addEvent(MyUser mUser, long... number) {
        long value = number.length > 0 ? number[0] : 1;
        if (inEventPriority)
            EventMonitor.getInstance().addLeftDropItem(mUser.getUser().getId(), this, value);
        else
            EventMonitor.getInstance().addDropItem(mUser.getUser().getId(), this, value);

        if (inChallenge) {
            Guice.getInstance(ChallengeService.class).addDataValue(mUser, this, value);
        }
        if (inNote) {
            Guice.getInstance(ChallengeNoteService.class).addDataValue(mUser, this, value);
        }
    }

    public void addStarEvent(int userId, int star, long... number) {
        long value = number.length > 0 ? number[0] : 1;
        if (value > 0) {
            if (inEventPriority) {
                EventMonitor.getInstance().addLeftDropItem(userId, this, number.length > 0 ? number[0] : 1);
                EventMonitor.getInstance().addLeftDropItem(userId, List.of((long) (this.id * 1000 + star), number.length > 0 ? number[0] : 1));
            } else {
                EventMonitor.getInstance().addDropItem(userId, this, number.length > 0 ? number[0] : 1);
                EventMonitor.getInstance().addDropItem(userId, this.id * 1000 + star, number.length > 0 ? number[0] : 1);
            }
        }
    }

    public static EventType getHeroFactionEvent(HeroType faction) {
        return mHeroFactionToEvent.get(faction);
    }

    static Map<HeroType, EventType> mHeroFactionToEvent = new HashMap<HeroType, EventType>() {{
        put(HeroType.FACTION_FIRE, GET_HERO_FIRE);
        put(HeroType.FACTION_WIND, GET_HERO_WIND);
        //        put(HeroType.FACTION_LIGHTNING, GET_HERO_LINGTNING);
        //        put(HeroType.FACTION_LAND, GET_HERO_LAND);
        //        put(HeroType.FACTION_LUNISOLAR, GET_HERO_LUNISOLAR);
        put(HeroType.FACTION_WATER, GET_HERO_WATER);
    }};

    // lookup
    static Map<Integer, EventType> lookup = new HashMap<>();

    static {
        Map<Integer, ResEventType> map = (Map<Integer, ResEventType>) DBJPA.getList(CfgServer.DB_MAIN + "res_event_type", ResEventType.class)
                .stream().collect(Collectors.toMap(ResEventType::getId, Function.identity()));
        for (EventType eventType : values()) {
            lookup.put(eventType.id, eventType);
            ResEventType resEventType = map.get(eventType.id);
            if (resEventType != null) {
                eventType.inEvent = resEventType.getInEvent() == 1;
                eventType.inChallenge = resEventType.getInChallenge() == 1;
                eventType.inNote = resEventType.getInNote() == 1;
                eventType.inEventPriority = resEventType.getInEventPriority() == 1;
                eventType.clientToGo = resEventType.getGotoField();//ClientToGo.get(resEventType.getGotoField());
                eventType.maxPoint = resEventType.getMaxPoint() == 1;
                eventType.maxPerDay = resEventType.getMaxPerDay();
            }
        }
    }

    public static EventType get(int id) {
        return lookup.get(id);
    }

    public String getTitle(Lang lang) {
        return lang.get("challenge2_quest_type_title_" + id);
    }

    public String getDescription(Lang lang, Object value) {
        String outputText = lang.get("challenge2_quest_type_" + id);
        if (outputText.contains("%s")) {
            return outputText.formatted(value);
        }
        return outputText;
    }
}
