package monster.config.penum;

import monster.config.lang.Lang;

public enum ClanPosition {
    NOT_MEMBER(-1, ""),
    <PERSON><PERSON><PERSON>(0, "clan_member"),
    <PERSON><PERSON><PERSON>(1, "clan_elder"),
    CO_LEADER(2, "clan_co_leader"),
    LEADER(3, "clan_leader");

    public int value;
    public String keyLang;

    ClanPosition(int value, String keyLang) {
        this.value = value;
        this.keyLang = keyLang;
    }

    public static String getKey(int position) {
        for (ClanPosition clanPosition : values()) {
            if (clanPosition.value == position) {
                return clanPosition.keyLang;
            }
        }
        return "";
    }

    public static String getName(Lang lang, int position) {
        for (ClanPosition clanPosition : values()) {
            if (clanPosition.value == position) {
                return lang.get(clanPosition.keyLang);
            }
        }
        return "";
    }

    public static boolean isLeader(int position) {
        return LEADER.value == position;
    }
}
