package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum FreedomStatusType {
    UNKNOW(0),
    TEAM(1),
    BUFF(2),
    ATTACK(3),
    ;

    public int value;

    FreedomStatusType(int value) {
        this.value = value;
    }

    // lookup
    static Map<Integer, FreedomStatusType> lookup = new HashMap<>();

    static {
        for (FreedomStatusType itemType : values()) {
            lookup.put(itemType.value, itemType);
        }
    }

    public static FreedomStatusType get(int type) {
        return lookup.get(type);
    }
}
