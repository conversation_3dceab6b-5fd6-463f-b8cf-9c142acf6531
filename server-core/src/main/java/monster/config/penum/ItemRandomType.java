package monster.config.penum;

import monster.service.resource.ResItem;
import monster.service.user.Bonus;

import java.util.ArrayList;
import java.util.List;

public enum ItemRandomType {

    // used item
    CLAN_BOSS_BOX(1),
    CLAN_BOSS_YELLOW_BOX(2),
    CLAN_BOSS_RED_BOX(3),
    ;

    public int id;

    ItemRandomType(int id) {
        this.id = id;
    }

    public List<Long> getRandom(int... numbers) {
        int number = numbers.length == 0 ? 1 : numbers[0];
        if (ResItem.mItemRandom.containsKey(id)) {
            List<Long> values = new ArrayList<>();
            for (int i = 0; i < number; i++) {
                values = Bonus.merge(values, ResItem.mItemRandom.get(id).getRandom());
            }
            return values;
        }
        return new ArrayList<>();
    }
}