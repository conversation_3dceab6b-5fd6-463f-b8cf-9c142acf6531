package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum ModePlayType {
    NONE(0, 1),
    EVENT_RAID(1, 1),
    CAMPAIGN(2, 1),
    TOWER_OBLIVION(3, 1),
    TOWER_2(4, 2),
    ASPEN_DUNGEON(5, 1),
    GUILD_BOSS(6, 1),
    FRIEND_BOSS(7, 1),
    BROKEN_SPACE(8, 1),
    BOSS_SERVER(9, 1),
    SEALED_LAND(10, 1),
    ISLAND_BOSS(11, 1),
    ISLAND_CREEP(12, 1),
    CRYSTALCROWN(13, 1),
    TRIALOFCHAMPOION(14, 2),
    ALLSTARSLEAGUE(15, 3),
    POT(26, 1),
    TERRITORY(27, 1),
    ;

    public int id, numberTeam;

    ModePlayType(int id, int numberTeam) {
        this.id = id;
        this.numberTeam = numberTeam;
    }

    // lookup
    static Map<Integer, ModePlayType> lookup = new HashMap<>();

    static {
        for (ModePlayType mode : values())
            lookup.put(mode.id, mode);
    }

    public static ModePlayType get(int id) {
        return lookup.get(id);
    }
}
