package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum ChatType {
    NONE(0),
    CLAN(1),
    CLAN_RECRUIT(2),
    ;

    public int value;

    ChatType(int value) {
        this.value = value;
    }


    //region Lookup
    static Map<Integer, ChatType> lookUp = new HashMap<>();

    static {
        for (ChatType chatType : values())
            lookUp.put(chatType.value, chatType);
    }

    public static ChatType get(int value) {
        return lookUp.get(value);
    }
    //endregion
}