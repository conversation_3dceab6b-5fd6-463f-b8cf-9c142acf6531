package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum MarketType {
    MARKETPLACE(1, "marketplace"),
    CASINO(2, "shop_casino"),
    ALTAR(3, "shop_altar"),
    GUILD(4, "shop_guild"),
    ARENA_TRIAL(5, "shop_arena"),
    BRAVE_TRIAL(6, "shop_trial"),
    VO_DAU(7, "shop_vo_dau"),
    HUYET_THACH_TUONG(8, "shop_tuong"),
    BAO_VAT(9, "shop_bao_vat"),
    ME_CUNG(10, "shop_me_cung"),
    SHOP_HT(11, "shop_ht"),
    SHOP_GUILD_NEW(12, "shop_guild"),
    SHOP_LABYRINTH(13, "shop_labyrinth"),
    SHOP_CRY(14, 0, "shop_cry"),
    SHOP_LABYRINTH_THANBI(15, "shop_thanbi"),
    SHOP_FISHING(16, "shop_fish"),
    SHOP_WEEK(17, "shop_week"),
    SHOP_CAT(18, "shop_cat"),
    SHOP_ARENA_SERVER(18, "shop_arena_server"),
    ;

    public static final int SHOP_TYPE_REFRESH = 1;
    public static final int SHOP_TYPE_STOCK = 2;
    public static final int SHOP_TYPE_UNLIMITED = 3;
    public static final int SHOP_TYPE_REFRESH_DAILY = 4;
    public static final int SHOP_SEQUENCE = 5;
    public static final int SHOP_TYPE_REFRESH_MONTH = 6;
    public static final int SHOP_TYPE_LABYRINTH_THANBI = 7;
    //
    public static final int ITEM_AVAILABLE = 1;
    public static final int ITEM_OUT_OF_STOCK = 0;

    public int id, type, showNumber;
    public int version;
    public long timeRefresh;
    public String key;
    public boolean enable = false;
    public boolean fileData;

    MarketType(int id, String key) {
        this.id = id;
        this.key = key;
    }

    MarketType(int id, int type, String key) {
        this.id = id;
        this.type = type;
        this.key = key;
    }

    public void setType(int type) {
        this.type = type;
        if (type != SHOP_TYPE_UNLIMITED) fileData = true;
    }

    public static int maxId = 0;

    // lookup
    static Map<Integer, MarketType> lookup = new HashMap<>();

    static {
        for (MarketType market : values()) {
            lookup.put(market.id, market);
            maxId = Math.max(maxId, market.id);
        }

    }

    public static MarketType get(int id) {
        return lookup.get(id);
    }

}