package monster.config.penum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum LabyrinthMapType {

    PLAYER(0),
    BOSS(1),
    ITEM_BONUS(2),
    FOUNTAIN(3), // hồi 50%s máu
    SHOP(4),
    NORMAL_MONSTER(5), // monster, drop rare & epic relics
    ELITE_MONSTER(6), // monster, drop epic & leg relics
    HERO_REVIVE(7);

    public int id;

    LabyrinthMapType(int id) {
        this.id = id;
    }

    public static List<LabyrinthMapType> atkMap = Arrays.asList(BOSS, ELITE_MONSTER, NORMAL_MONSTER);

    public static boolean isAtkType(int type) {
        return atkMap.contains(get(type));
    }

    //region lookup
    static Map<Integer, LabyrinthMapType> lookup = new HashMap<>();

    static {
        for (LabyrinthMapType type : values()) {
            lookup.put(type.id, type);
        }
    }

    public static LabyrinthMapType get(int value) {
        return lookup.get(value);
    }
    //endregion
}
