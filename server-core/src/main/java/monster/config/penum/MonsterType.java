package monster.config.penum;

public enum MonsterType {
    CAMPAIGN(1), // done
    TOWER_OBLIVION(2), // done
    ASPEN_DUNGEON(3),
    GUILD_BOSS(4), // done
    ISLAND_CREEP(5),
    ISLAND_BOSS(6),
    EVENT_RAID(7),
    FRIEND_BOSS(8),
    BROKEN_SPACE(9),
    SEAL_LAND(10),
    TOWER2(11),
    BOSS_SERVER(12),
    BOSS_TOWER1(13),
    ARENA_BOT(14),
    <PERSON>OT(15),
    BOSS_INTER_SERVER(16),
    PEARL_MINE(17), // done
    TERRITORY(18), // done
    STORY_MISSION(19),
    ;

    public int value;

    MonsterType(int value) {
        this.value = value;
    }

}
