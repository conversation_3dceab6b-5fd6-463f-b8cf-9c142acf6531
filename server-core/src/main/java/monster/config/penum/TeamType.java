package monster.config.penum;

import java.util.HashMap;
import java.util.Map;

public enum TeamType {
    CAMPAIGN(0, true),
    FRIEND(1, false),
    CELESTIAL(2, false),
    BOSS_CLAN(3, true),
    ARENA_CRYSTAL_DEF(4, true),
    ARENA_TRIAL_DEF(5, true),
    ARENA_SWAP_DEF(6, true),
    ARENA_CRYSTAL_ATK(7, true),
    TOWER1_ATTACK(8, true),
    STORY_ATTACK(9, true),

    FISHING_PEARL(10, true),
    TOWER2_ATTACK(11, true),

    ARENA_SERVER_DEF(12, true),
    ARENA_THREE_STAR_DEF(13, true),
    ARENA3_SERVER_DEF(14, true)
    ;

    public int value;
    public boolean save;

    TeamType(int value, boolean save) {
        this.value = value;
        this.save = save;
    }

    //region Lookup
    static Map<Integer, TeamType> lookUp = new HashMap<>();

    static {
        for (TeamType type : values()) lookUp.put(type.value, type);
    }

    public static TeamType get(int value) {
        return lookUp.get(value);
    }
    //endregion
}