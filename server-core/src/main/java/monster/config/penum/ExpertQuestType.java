package monster.config.penum;

import monster.config.lang.Lang;
import monster.service.battle.common.config.HeroType;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;

import java.util.HashMap;
import java.util.Map;

public enum ExpertQuestType {

    WIN(1),
    ENTER(2),
    ACTIONS_ACTIVE(3),
    ACTIONS_NORMAL(4),
    BEST_DMG(5, true),
    TOTAL_DMG(6),
    HEAL(7),
    INCOMING_DMG(8),
    COUNTER_ATTACK(9),
    CC(10),
    DEBUF(11),
    BUF(12),
    UPGRADE_STAR(13, true),
    ADVENTURE_RANK_ABOVE5(14),


    HERO_LEVEL(5001, "[C<PERSON>ờng hóa] Nâng tới cấp %s", "[Level Up] Up level to level %s"),
    WIN_ARENA_CRYSTAL(5002, "[<PERSON><PERSON><PERSON> đấu] <PERSON><PERSON><PERSON><PERSON> chiến và thắng %s lần", "[League of Pirates] Fight and win %s times"),
    WIN_ARENA_TRIAL(5003, "[<PERSON><PERSON><PERSON> đấu] <PERSON><PERSON><PERSON><PERSON> chiến và thắng %s lần", "[League of MarineFord] Fight and win %s times"),
    WIN_ARENA(5004, "[Chiến trường] Khiêu chiến và thắng %s lần", "[Arena] Fight and win %s times"),

    DAMAGE_MAX_IN_ARENA(5005, "[Chiến trường] Khiêu chiến và sát thương cao nhất hơn %s điểm", "[Arena] Fight and deal damage over %s points"),
    DAMAGE_IN_ARENA_CRYSTAL(5006, "[Đơn đấu] Khiêu chiến và sát thương được %s điểm", "[League of Pirates] Fight and deal damage over %s points"),
    DAMAGE_IN_ARENA_TRIAL(5007, "[Liên đấu] Khiêu chiến và sát thương được %s điểm", "[League of MarineFord] Fight and deal damage over %s points"),
    DAMAGE_IN_ARENA(5008, "[Chiến trường] Khiêu chiến và sát thương được %s điểm", "[Arena] Fight and deal damage over %s points"),

    HEAL_MAX_IN_ARENA(5009, "[Chiến trường] Khiêu chiến và hồi phục cao nhất hơn %s máu", "[Arena] Fight and heal over HP over %s points"),
    HEAL_IN_ARENA_CRYSTAL(50010, "[Đơn đấu] Khiêu chiến và hồi phục được %s máu", "[League of Pirates] Fight and heal over HP over %s points"),
    HEAL_IN_ARENA_TRIAL(50011, "[Liên đấu] Khiêu chiến và hồi phục được %s máu", "[League of MarineFord] Fight and heal over HP over %s points"),
    HEAL_IN_ARENA(50012, "[Chiến trường] Khiêu chiến và hồi phục được %s máu", "[Arena] Fight and heal over HP over %s points"),

    DEFENSE_IN_ARENA_CRYSTAL(50013, "[Đơn đấu] Khiêu chiến và chống chịu được %s sát thương", "[League of Pirates] Fight and take damage over %s points"),
    DEFENSE_IN_ARENA_TRIAL(50014, "[Liên đấu] Khiêu chiến và chống chịu %s sát thương", "[League of MarineFord] Fight and take damage over %s points "),
    DEFENSE_IN_ARENA(50015, "[Chiến trường] Khiêu chiến và chống chịu %s sát thương", "[Arena] Fight and take damage over %s points"),

    // --------
    ARENA_ONE_KILL(16, "[Chiến trường] Khiêu chiến và đạt %s lần One Kill", "[Arena] Fight and get 1 Kill %s time"),
    ARENA_DOUBLE_KILL(17, "[Chiến trường] Khiêu chiến và đạt %s lần Double Kill", "[Arena] Fight and get 2 Kill %s time"),
    ARENA_TRIPLE_KILL(18, "[Chiến trường] Khiêu chiến và đạt %s lần Triple Kill", "[Arena] Fight and get 3 Kill %s time"),
    ARENA_QUADRA_KILL(19, "[Chiến trường] Khiêu chiến và đạt %s lần Quadra Kill", "[Arena] Fight and get 4 Kill %s time"),
    ARENA_PNETA_KILL(20, "[Chiến trường] Khiêu chiến và đạt %s lần Penta Kill", "[Arena] Fight and get 5 Kill %s time"),
    ARENA_HEXA_KILL(21, "[Chiến trường] Khiêu chiến và đạt %s lần Hexa Kill", "[Arena] Fight and get 6 Kill %s time"),

    TAVERN_4_STAR(22, "[Tổng bộ] Dùng %s hoàn thành %s lần nhiệm vụ 4 sao", "[Marine Headquarters] Using %s complete 4-star quest %s times"),
    TAVERN_5_STAR(23, "[Tổng bộ] Dùng %s hoàn thành %s lần nhiệm vụ 5 sao", "[Marine Headquarters] Using %s complete 5-star quest %s times"),
    TAVERN_6_STAR(24, "[Tổng bộ] Dùng %s hoàn thành %s lần nhiệm vụ 6 sao", "[Marine Headquarters] Using %s complete 6-star quest %s times"),
    WIN_CELESTIAL(25, "[Đảo trời] Tiêu diệt %s lần Boss đảo trời", "[Sky Island] Kill Boss %s times"),

    ARENA_TRIAL_NORMAL_ATTACK(26, "[Liên đấu] Đánh thường %s lần", "[League of MarineFord] Use normal attack %s times"),
    ARENA_NORMAL_ATTACK(27, "[Chiến trường] Đánh thường %s lần", "[Arena] Use normal attack %s times"),
    ARENA_TRIAL_ACTIVE_SKILL(28, "[Liên đấu] Xuất nộ %s lần", "[League of MarineFord] Use active skill %s times"),
    ARENA_ACTIVE_SKILL(29, "[Chiến trường] Xuất nộ %s lần", "[Arena] Use active skill %s times"),
    ARENA_TRIAL_COUNTER_ATTACK(30, "[Liên đấu] Phản công %s lần", "[League of MarineFord] Use counter attack %s times"),
    ARENA_COUNTER_ATTACK(31, "[Chiến trường] Phản công %s lần", "[Arena] Use counter attack %s times"),

    // chưa làm
    ARENA_TRIAL_WIN_TOGETHER_HERO(32, "[Liên đấu] Đi cùng %s và chiến thắng %s lần.", "[League of MarineFord] Teamup with %s and win %s times"),
    ARENA_WIN_TOGETHER_HERO(33, "[Chiến trường] Đi cùng %s và chiến thắng %s lần.", "[Arena] Teamup with %s and win %s times"),
    ARENA_TRIAL_WIN_TOGETHER_CLASS(34, "[Liên đấu] Cùng thuyền viên nghề %s và chiến thắng %s lần.", "[League of MarineFord Teamup with %s hero and win %s times"),
    ARENA_WIN_TOGETHER_CLASS(35, "[Chiến trường] Cùng thuyền viên nghề %s và chiến thắng %s lần.", "[Arena] Teamup with %s hero and win %s times"),
    ARENA_TRIAL_WIN_TOGETHER_FACTION(36, "[Liên đấu] Cùng thuyền viên Hệ %s và chiến thắng %s lần.", "[League of MarineFord] Teamup with %s hero and win %s times"),
    ARENA_WIN_TOGETHER_FACTION(37, "[Chiến trường] Cùng thuyền viên Hệ %s và chiến thắng %s lần.", "[Arena] Teamup with %s hero and win %s times"),
    ARENA_TRIAL_WIN_TOGETHER_PET(38, "[Liên đấu] Cùng chiến hạm %s chiến thắng %s lần.", "[League of MarineFord] Teamup with %s warship and win %s times"),
    ARENA_WIN_TOGETHER_PET(39, "[Chiến trường] Cùng chiến hạm %s chiến thắng %s lần.", "[Arena] Teamup with %s warship and win %s times"),

    BOSS_SERVER_NUMBER_ATTACK(40, "[Boss] Đánh boss % lần", "[Boss] Fight %s times"),
    BOSS_SERVER_DEFENSE(41, "[Boss] Khiêu chiến và chống chịu được %s sát thương ", "[Boss] Fight and take damage over %s points"),
    BOSS_SERVER_HEAL(42, "[Boss] Khiêu chiến và hồi phục được %s máu", "[Boss] Fight and heal over HP over %s points"),
    BOSS_SERVER_DAMAGE(43, "[Boss] Khiêu chiến và sát thương được %s điểm", "[Boss] Fight and deal damage over %s points"),
    BOSS_SERVER_ACTIVE_SKILL(44, "[Boss] Xuất nộ %s lần", "[Boss] Use active skill %s times"),
    BOSS_SERVER_NORMAL_ATTACK(45, "[Boss] Đánh thường %s lần", "[Boss] Use normal attack %s times"),
    BOSS_SERVER_COUNTER_ATTACK(46, "[Boss] Phản công %s lần ", "[Boss] Use counter attack %s times"),

    // các nhiệm vụ sau chưa làm
    WINNER_WITH_FRIEND(99, "Thuyền viên %s đạt %s lần chiến thắng cùng thuyền viên %s tại đấu trường đơn đấu", "%s wins %s times when teamup with %s in Arena"),
    COMPLETE_ALL(100, "Hoàn thành tất cả nhiệm vụ", "All Quests completed"),
    ;

    public int id;
    public boolean maxValue;
    public String nameEn, nameVi;

    ExpertQuestType(int id, boolean... maxValue) {
        this.id = id;
        this.maxValue = maxValue.length == 0 ? false : maxValue[0];
    }

    ExpertQuestType(int id, String nameVi, String nameEn) {
        this.id = id;
        this.nameEn = nameEn;
        this.nameVi = nameVi;
    }

    public String getName(Lang lang) {
        return lang.get("expert_quest_type_" + id);
    }

    public String getName(Lang lang, int heroId, long value1, long value2) {
        String name = getName(lang);
        if (id == 22 || id == 23 || id == 24) {
            return String.format(name, ResHero.getHero(heroId).getName(), value1);
        } else if (id == 32 || id == 33) {
            return String.format(name, ResHero.getHero((int) value2).getName(), value1);
        } else if (id == 34 || id == 35) {
            return String.format(name, HeroType.getClass((int) value2).getName(lang), value1);
        } else if (id == 36 || id == 37) {
            return String.format(name, HeroType.getFaction((int) value2).getName(lang), value1);
        } else if (id == 38 || id == 39) {
            return String.format(name, ResMonster.getPet((int) value2).getName(lang), value1);
        }
        return String.format(name, value1);
    }

    // lookup
    static Map<Integer, ExpertQuestType> lookup = new HashMap<>();

    static {
        for (ExpertQuestType quest2 : values()) {
            lookup.put(quest2.id, quest2);
        }
    }

    public static ExpertQuestType get(int value) {
        return lookup.get(value);
    }

}
