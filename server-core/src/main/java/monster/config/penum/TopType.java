package monster.config.penum;

import monster.config.CfgHeroRanking;

import java.util.HashMap;
import java.util.Map;

public enum TopType {
    CAMPAIGN(0, "CAMPAIGN",
            "select u.*, d.level as trophy from user u, user_campaign d where u.server=%s and u.id=d.user_id order by d.level desc limit 20",
            "select count(*) number from user_campaign where server_id=%s and LEVEL>=(SELECT LEVEL FROM user_campaign WHERE user_id=%s)",
            "select u.*, d.level as trophy from user u, user_campaign d where u.id=%s and u.id=d.user_id"),
    ARENA_CRYSTAL_CROWN(1, "CRYSTAL_CROWN", ""),
    ASPEN_DUNGEON(2, "ASPEN_DUNGEON",
            "select u.*, d.dungeon_level as trophy from user u, user_data d where u.server=%s and u.id=d.user_id order by d.dungeon_level desc limit 20",
            "select count(*) number from user_data d, user u WHERE d.user_id=u.id AND u.server=%s and dungeon_level>=(SELECT dungeon_level FROM user_data WHERE user_id=%s)",
            "select u.*, d.dungeon_level as trophy from user u, user_data d where u.id=%s and u.id=d.user_id"),
    TOWER_OF_OBLIVION(3, "TOWER_OF_OBLIVION",
            "select u.*, d.level as trophy from user u, user_oblivion d where u.server=%s and u.id=d.user_id order by d.level desc limit 20",
            "select count(*) number from user_oblivion where server_id=%s and LEVEL>=(SELECT LEVEL FROM user_oblivion WHERE user_id=%s)",
            "select u.*, d.level as trophy from user u, user_oblivion d where u.id=%s and u.id=d.user_id"),
    MILL_DONATE(4, "MILL_DONATE",
            "select u.*, d.mill_donate as trophy from user u, user_clan d where u.id=d.user_id and clan=%s order by d.mill_donate desc"),
    TOWER_2(7, "TOWER2_OF_OBLIVION",
            "select u.*, d.level as trophy from user u, user_tower2 d where u.server=%s and u.id=d.user_id order by d.level desc limit 20",
            "select count(*) number from user_tower2 where server_id=%s and LEVEL>=(SELECT LEVEL FROM user_tower2 WHERE user_id=%s)",
            "select u.*, d.level as trophy from user u, user_tower2 d where u.id=%s and u.id=d.user_id"),
    POWER(8, "POWER",
            "SELECT *,power as trophy from user where SERVER=%s  order BY power DESC limit 100",
            "select count(*) number from user where user.server=%s and power>=(SELECT power FROM user WHERE id=%s)",
            "select *, power as trophy from user where id=%s"),
    CAMPAIGN_LEVEL(9, "CAMPAIGN",
            "select u.*, d.level as trophy from user u, user_campaign d where u.server=%s and u.id=d.user_id order by d.level desc limit 100",
            "select count(*) number from user_campaign where server_id=%s and LEVEL>=(SELECT LEVEL FROM user_campaign WHERE user_id=%s)",
            "select u.*, d.level as trophy from user u, user_campaign d where u.id=%s and u.id=d.user_id"),
    TOWER1(10, "TOWER_OF_OBLIVION",
            "select u.*, d.level as trophy from user u, user_oblivion d where u.server=%s and u.id=d.user_id order by d.level desc, d.last_time_attack  limit 100",
            "select count(*) number from user_oblivion where server_id =%s and LEVEL>=(SELECT LEVEL FROM user_oblivion WHERE user_id=%s)",
            "select u.*, d.level as trophy from user u, user_oblivion d where u.id=%s and u.id=d.user_id"),
    ASPEN(11, "ASPEN_DUNGEON",
            "select u.*, d.dungeon_level as trophy from user u, user_data d where u.server=%s and u.id=d.user_id order by d.dungeon_level desc limit 100",
            "select count(*) number from user_data d, user u WHERE d.user_id=u.id AND u.server=%s and dungeon_level>=(SELECT dungeon_level FROM user_data WHERE user_id=%s)",
            "select u.*, d.dungeon_level as trophy from user u, user_data d where u.id=%s and u.id=d.user_id"),
    FACTION_1(12, "FACTION_1",
            "SELECT *,power_f1 as trophy from user where SERVER=%s  order BY power_f1 DESC limit 100",
            "select count(*) number from user where user.server=%s and power_f1>=(SELECT power_f1 FROM user WHERE id=%s)",
            "select *, power_f1 as trophy from user where id=%s"),
    FACTION_2(13, "FACTION_2",
            "SELECT *,power_f2 as trophy from user where SERVER=%s  order BY power_f2 DESC limit 100",
            "select count(*) number from user where user.server=%s and power_f2>=(SELECT power_f2 FROM user WHERE id=%s)",
            "select *, power_f2 as trophy from user where id=%s"),
    FACTION_3(14, "FACTION_3",
            "SELECT *,power_f3 as trophy from user where SERVER=%s  order BY power_f3 DESC limit 100",
            "select count(*) number from user where user.server=%s and power_f3>=(SELECT power_f3 FROM user WHERE id=%s)",
            "select *, power_f3 as trophy from user where id=%s"),
    FACTION_4(15, "FACTION_4",
            "SELECT *,power_f4 as trophy from user where SERVER=%s  order BY power_f4 DESC limit 100",
            "select count(*) number from user where user.server=%s and power_f4>=(SELECT power_f4 FROM user WHERE id=%s)",
            "select *, power_f4 as trophy from user where id=%s"),
    HERO_POWER(16, "HERO_POWER_" + CfgHeroRanking.getEventId(),
            "SELECT u.*,d.power as trophy,d.time from user u, user_hero_ranking d where cluster=%s AND u.id=d.user_id and d.power>0 and event_id=" + CfgHeroRanking.getEventId() + "  order BY d.power DESC, d.time asc LIMIT 200",
            "Select count(*) number from user_hero_ranking d, user u where d.cluster=%s AND d.user_id=u.id and d.event_id=" + CfgHeroRanking.getEventId() + "  and d.power>=(SELECT power FROM user_hero_ranking WHERE user_id=%s)",
            "Select u.*, d.power as trophy ,d.time from user u,user_hero_ranking d WHERE u.id=%s and d.event_id=" + CfgHeroRanking.getEventId() + " and u.id=d.user_id and d.power >0"
    ),
    TOWER2(17, "TOWER2",
            "select u.*, d.level as trophy from user u, user_tower2 d where u.server=%s and u.id=d.user_id order by d.level desc, d.last_time_attack  limit 100",
            "select count(*) number from user_tower2 where server_id =%s and LEVEL>=(SELECT LEVEL FROM user_tower2 WHERE user_id=%s)",
            "select u.*, d.level as trophy from user u, user_tower2 d where u.id=%s and u.id=d.user_id");

    public int value;
    public String name, sql, sqlMyRank, sqlMyInfo;

    TopType(int value, String name, String sql, String sqlMyRank, String sqlMyInfo) {
        this.value = value;
        this.name = name;
        this.sql = sql;
        this.sqlMyRank = sqlMyRank;
        this.sqlMyInfo = sqlMyInfo;
    }

    TopType(int value, String name, String sql) {
        this.value = value;
        this.name = name;
        this.sql = sql;
    }

    //region Lookup
    static Map<Integer, TopType> lookUp = new HashMap<>();

    static {
        for (TopType topType : values())
            lookUp.put(topType.value, topType);
    }

    public static TopType get(int value) {
        return lookUp.get(value);
    }
    //endregion
}