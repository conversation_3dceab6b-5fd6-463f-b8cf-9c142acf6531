package monster.config.penum;

import grep.database.DBJPA;
import grep.helper.DateTime;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import monster.dao.mapping.main.ConfigFunctionEntity;
import monster.game.story.service.StoryService;
import monster.object.MyUser;
import monster.server.AppConfig;
import monster.server.config.Guice;
import monster.service.common.SystemService;

import java.util.*;

public enum FunctionType {
    HERO_EXPERT(1),
    BOSS_SERVER(2),
    DICE(3),
    TOWER2(4),// <PERSON>ô hạn thành khó
    BOSS_SILENT_RING(5),
    BALANCE_ARENA(6),
    PEARL_MINE(7),
    TERRITORY(8),
    CLA<PERSON>(9),
    ARENA_CRYSTAL(10),
    CAMPAIGN_AFK(11),
    ASPEND_DUNGEON(12),
    USER_GEM(13),// <PERSON><PERSON> ngữ
    LABYRINTH(14),
    <PERSON><PERSON><PERSON>_STORY(15),
    <PERSON><PERSON><PERSON>_TRIAL(17),
    <PERSON><PERSON>O_BREATH(18),
    <PERSON><PERSON><PERSON>_SWAP(19),
    <PERSON><PERSON>(22),
    <PERSON>VENTURE(24),
    CASINO_SUPER(27),
    CASINO(28),
    BRAVE_TRIAL_NEW(29),
    ONLINE_BONUS(30), // quà online
    PROPHET_TREE(31),
    HEROIC_SUMMON(32), // chiêu mộ cao cấp
    MARKET(34), // Cửa hàng
    IDLE_BATTLE(35), // chế độ battle ngầm
    DAILY_QUEST(36), // nhiệm vụ hằng ngày
    FORGE_ITEM(37), // rèn trang bị
    HERO_UPGRADE_STAR(38), // nâng sao tướng
    INDENTURE(39), // khế ước
    CAMPAIGN_QUICK_BONUS(40), // nhận quà nhanh trong afk
    BADAGE(41), // huy hiệu (badage)
    CAT(43), // huy hiệu (badage)
    LUCKY_MONEY(44),
    BLOOD_MAGIC(46),
    SWORDSMANSHIP(47),
    ABYSS(48),

    CLAN_BOSS(50),

    ARENA_SERVER(51),// Tính anh đại chiến
    ARENA_TEAM(52),// Đại chiến tam tinh
    ARENA3_SERVER(53),// Vô song đại chiến
    ;

    public int id, clientToGo;

    FunctionType(int id) {
        this.id = id;
    }

    static Map<Integer, ConfigFunctionEntity> mConfigFunction;

    public ConfigFunctionEntity getConfig() {
        if (mConfigFunction == null) {
            mConfigFunction = new HashMap<>();
            synchronized (mConfigFunction) {
                if (mConfigFunction.isEmpty()) {
                    List<ConfigFunctionEntity> aConfig = DBJPA.getList(CfgServer.DB_MAIN + "config_function", ConfigFunctionEntity.class);
                    for (ConfigFunctionEntity config : aConfig) {
                        mConfigFunction.put(config.getId(), config);
                    }
                }
            }
        }
        return mConfigFunction.get(id);
    }

    public long getTimeOpenServer(MyUser mUser) {
        ConfigFunctionEntity configFunction = getConfig();
        if (configFunction != null) {
            if (CfgServer.isTestServer() || configFunction.getIsEnable() == 1) { // mở tính năng
                if (!configFunction.isDayOpenServer(mUser.getUser().getServer())) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(Guice.getInstance(SystemService.class).getDateOpenServer(mUser.getUser().getServer()));
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    calendar.add(Calendar.DATE, configFunction.getDayOpenServer());
                    long second = (calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000;
                    return second;
                }
                return 0;
            }
        }
        return 100000;
    }

    public boolean isEnable() {
        ConfigFunctionEntity configFunction = getConfig();
        return configFunction != null && configFunction.getIsEnable() == 1;
    }

    public boolean isEnable(MyUser mUser, AHandler... handlers) {
        if (AppConfig.cfg.isSubmit()) return true;
        ConfigFunctionEntity configFunction = getConfig();
        if (configFunction == null) {
            if (handlers != null && handlers.length > 0)
                handlers[0].addErrResponse("Chưa config tính năng này trong ConfigFunction id=" + id);
            return false;
        }
        if (CfgServer.isTestServer() || configFunction.getIsEnable() == 1) { // mở tính năng
            if (mUser != null) {
                if (!configFunction.isStoryUnlock(Guice.getInstance(StoryService.class).getUserStory(mUser))) {
                    if (handlers != null && handlers.length > 0)
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.story_required).formatted(configFunction.getChapter(), configFunction.getLevel()) + "(%s)".formatted(id));
                    return false;
                }
                if (!configFunction.isDayOpenServer(mUser.getUser().getServer())) {
                    if (handlers != null && handlers.length > 0) {
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.day_open_server).formatted(DateTime.formatTime(getTimeOpenServer(mUser))) + "(%s)".formatted(id));
                    }
                    return false;
                }
                if (mUser.getUser().getLevel() < configFunction.getUserLevel()) {
                    if (handlers != null && handlers.length > 0)
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.user_function_level_required).formatted(configFunction.getUserLevel()) + "(%s)".formatted(id));
                    return false;
                }
                if (mUser.getUser().getVip() < configFunction.getUserVip()) {
                    if (handlers != null && handlers.length > 0)
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.user_function_vip_required).formatted(configFunction.getUserVip()) + "(%s)".formatted(id));
                    return false;
                }
                if (!configFunction.isCampaignLevel(mUser)) {
                    if (handlers != null && handlers.length > 0)
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.campaign_function_level_required).formatted(configFunction.getCampaignLevel()) + "(%s)".formatted(id));
                    return false;
                }
                if (!configFunction.isShibuyaLevel(mUser)) {
                    if (handlers != null && handlers.length > 0)
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.shibuya_function_level_required).formatted(configFunction.getShibuyaLevel()) + "(%s)".formatted(id));
                    return false;
                }
                if (!configFunction.isTotalTerritoryLevel(mUser)) {
                    if (handlers != null && handlers.length > 0)
                        handlers[0].addErrResponse(mUser.getLang().get(Lang.territory_function_level_required).formatted(configFunction.getTerritoryTotalLevel()) + "(%s)".formatted(id));
                    return false;
                }
                return true;
            }
            return false;
        }
        if (handlers != null && handlers.length > 0) handlers[0].addErrResponse(Lang.user_function_closed);
        return false;
    }

    public boolean isNotify() {
        return getConfig().getIsNotify() == 1;
    }

    static Map<Integer, FunctionType> lookUp = new HashMap<>();

    static {
        for (FunctionType function : values())
            lookUp.put(function.id, function);
    }

    public static FunctionType get(int id) {
        return lookUp.get(id);
    }
}
