package monster.config;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.cache.redis.JCachePubSub;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import monster.object.MyUser;
import monster.object.UserInt;
import monster.protocol.CommonProto;
import monster.server.IAction;

import java.util.Arrays;
import java.util.List;

@SuppressWarnings({"rawtypes", "unchecked"})
public class CfgUser {
    public static final int VIP_BENEFIT_ADVENTURE_INDEX = 0;
    public static final int VIP_BENEFIT_TERRITORY = 1;
    public static final int VIP_BENEFIT_LABYRINTH = 2;
    public static final List<Integer> listVipBenefit = Arrays.asList(VIP_BENEFIT_ADVENTURE_INDEX, VIP_BENEFIT_TERRITORY, VIP_BENEFIT_LABYRINTH);

    public static DataConfig config;

    public static void removeBonusGift(int userId) {
        JCachePubSub.getInstance().lpop("popup:" + userId);
    }

    public static void checkBonusGift(AHandler handler, MyUser mUser) {
        List<String> values = JCachePubSub.getInstance().lrange("popup:" + mUser.getUser().getId(), 0, 0);
        if (!values.isEmpty()) { // "{\"when\":null,\"form\":{\"image\":\"Event/event_42\",\"items\":\"[3,1,16,10,3,1,15,20,9,500]\",\"eventType\":0,\"eventId\":7479},\"expire\":1604486619049}"
            try {
                JsonObject obj = GsonUtil.parseJsonObject(values.get(0));
                if (obj.has("expire") && obj.get("expire").getAsLong() > System.currentTimeMillis()) {
                    //                    Logs.warn(String.format("bonusGift %s -> %s", mUser.getUser().getId(), values.get(0)));
                    handler.addResponse(IAction.SHOPPING_NOTIFY, CommonProto.getErrorMsg(obj.get("form").getAsJsonObject().toString()));
                    if (StringHelper.convertVersion2Int("1.0.1") >= StringHelper.convertVersion2Int(mUser.getVersion())) {
                        removeBonusGift(mUser.getUser().getId());
                    }
                } else {
                    removeBonusGift(mUser.getUser().getId());
                }
            } catch (Exception ex) {
                Logs.error(values + " -> " + GUtil.exToString(ex));
            }
        }
    }

    public static int getVip(int exp) {
        for (int i = 0; i < config.vip.length; i++) {
            if (exp < config.vip[i]) return i;
        }
        return config.vip.length;
    }

    public static int getVipAdventure(int vip) {
        return config.vipBenefit.freeResetAdventure[vip];
    }

    public static int getVipTerritory(int vip) {
        return config.vipBenefit.buyTurnTerritory[vip];
    }

    public static int getVipLabyrinthBuyTurn(int vip) {
        return config.vipBenefit.buyTurnLabyrinth[vip];
    }

    public static float getVipLabyrinthBonus(int vip) {
        return config.vipBenefit.bonusLabyrinth[vip];
    }

    public static boolean isVipFreeReupHero(int vip) {
        return config.vipBenefit.freeReupHero[vip] == 1;
    }

    public static boolean isRegisterAccount(String username) {
        return !(username.matches("fb[0-9]+") || username.matches("dtq_[0-9]+"));
    }

    public static long countdownHandOfMidas(MyUser mUser) {
        int lastReceive = mUser.getUData().getUInt().getValue(UserInt.MIDAS_1_TIME);
        int timePassed = (int) ((System.currentTimeMillis() / 1000) - lastReceive);
        int timeRequired = (int) DateTime.HOUR_SECOND * 8;
        if (timePassed >= timeRequired) return 0;
        return timeRequired - timePassed;
    }

    public static boolean enoughHeroSlot(AHandler handler, MyUser mUser, int number) {
        if (mUser.getResources().heroes.size() + number <= CfgHero.getMaxHeroSlot(mUser)) {
            return true;
        }
        handler.addErrResponse(handler.getLang(Lang.user_max_hero_slot));
        return false;
    }

    public static float getVipMidas(int vip) {
        return 100 + config.vipBenefit.handOfMidas[vip];
    }

    public static int getVipAutoCampaign(int vip) {
        return config.vipBenefit.autoCampaign[vip];
    }

    public static int getMaxAutoTime(int vip) {
        return config.vipBenefit.maxIdleTime[vip];
    }

    public static int getVipTavern(int vip) {
        return 5;
        //        return vip == 0 ? 5 : config.vipBenefit.tavernQuest[vip - 1];
    }

    public static int getNumberBuyEventRaid(int vip) {
        return 0;
        //        return vip == 0 ? 0 : config.vipBenefit.eventRaid[vip - 1];
    }

    public static int getNumberBuyQuickBonus(int vip) {
        //        int length = config.vipBenefit.numberBuyQuickBonus.length;
        //        if (vip >= length)
        //            return config.vipBenefit.numberBuyQuickBonus[length - 1] + 1;
        //        return config.vipBenefit.numberBuyQuickBonus[vip] + 1;
        return 2;
    }

    public static float getGoldFree() {
        return config.vipBenefit.goldMidas[0];
    }

    public static float getGoldX2() {
        return config.vipBenefit.goldMidas[1];
    }

    public static float getGoldX5() {
        return config.vipBenefit.goldMidas[2];
    }

    public static int getVipHeroSlot(int vip) {
        return config.vipBenefit.heroSlot[vip];
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public static List<Long> getVipBonus(int vip) {
        return config.vipBenefit.bonusVip.get(vip);
    }

    public class DataConfig {
        public long[] exp;
        public int[] vip;
        public VipBenefit vipBenefit;
    }

    public class VipBenefit {
        public int[] autoCampaign; //Phần thưởng tiền xu, kinh nghiệm, đá exp khi tự động đánh
        public float[] handOfMidas, goldMidas; // Mua tiền xu tăng thêm
        public int[] heroSlot; //Tăng ô chứa tướng
        public int[] freeResetAdventure; //Số lượt reset miễn phí Nhiệm Vụ Trừ Tà
        public int[] buyTurnTerritory; //Số lần mua lượt quét Lãnh Địa
        public int[] buyTurnLabyrinth; //Số lần mua lượt khiêu chiến Biến Cố Shibuya
        public int[] bonusLabyrinth; //Tăng tài nguyên Shibuya
        public int[] maxIdleTime; //Tăng thời gian treo máy (phút)
        public int[] freeReupHero; //Hoàn trả tướng miễn phí
        public List<List<Long>> bonusVip;
    }

}
