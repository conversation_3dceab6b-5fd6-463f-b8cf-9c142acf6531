package monster.config;

import com.google.gson.Gson;
import grep.helper.NumberUtil;
import lombok.Data;
import monster.config.penum.MaterialType;
import monster.object.MyUser;
import monster.service.battle.common.config.HeroType;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.*;

public class CfgTavern {
    // tavern
    public static final int TYPE_REQ_STAR = 1;
    public static final int TYPE_REQ_FACTION = 2;
    public static final int TYPE_REQ_CLASS = 3;
    public static final int MAX_QUEST = 50;

    public static JSONObject json;
    public static CfgTavern.DataConfig config;

    public static int getRandomIdTavern(MyUser mUser) {
        float range = 0;
        for (int i = 0; i < config.tavernQuestRate.length; i++) {
            range += config.tavernQuestRate[i];
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 0; i < config.tavernQuestRate.length; i++) {
            top += config.tavernQuestRate[i];
            if (random < top) {
                Actions.save(mUser.getUser(), "tavern", "random", "randomValue", random, "star", i + 1, "require", "[25,52,75,90,98,99.6,100]");
                return i;
            }
        }
        return 0;
    }

    public static int getRandomIdTavernNormal() {
        float range = 0;
        for (int i = 0; i < 3; i++) {
            range += config.tavernQuestRate[i];
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 0; i < config.tavernQuestRate.length; i++) {
            top += config.tavernQuestRate[i];
            if (random < top) {
                return i;
            }
        }
        return 0;
    }

    public static int getRandomIdTavernSenior() {
        float range = 0;
        for (int i = 3; i < 7; i++) {
            range += config.tavernQuestRate[i];
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 3; i < config.tavernQuestRate.length; i++) {
            top += config.tavernQuestRate[i];
            if (random < top) {
                return i;
            }
        }
        return 0;
    }

    public static JSONArray getTavernBonusShow(int indexQuest) {
        JSONArray arrBonus = new JSONArray();

        int currIndex = indexQuest;
        CfgTavern.TavernBonusObject bonusObject = null;
        if (currIndex < config.bonusTavern.length)
            bonusObject = config.bonusTavern[currIndex];

        if (bonusObject != null) {
            int rangeIndexBonus = getBonusIndex(currIndex);

            while (arrBonus.size() == 0) {
                switch (rangeIndexBonus) {
                    case 0:
                        if (bonusObject.diamond.length > 0) {
                            arrBonus.addAll(Bonus.view(Bonus.BONUS_GEM, new Random().nextInt(bonusObject.diamond[1] - bonusObject.diamond[0] + 1) + bonusObject.diamond[0]));
                        }
                        break;
                    case 1:
                        if (bonusObject.basicScroll > 0) {
                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.BASIC_SUMMON, bonusObject.basicScroll));
                        }
                        break;
                    case 2:
                        if (bonusObject.heroicScroll > 0) {
                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.HEROIC_SUMMON, bonusObject.heroicScroll));
                        }
                        break;
                    case 3:
                        if (bonusObject.shard.length > 0) {
                            int star = bonusObject.shard[0];
                            int numFragMin = bonusObject.shard[1];
                            int numFragMax = bonusObject.shard[2];
                            if (star == 3)
                                arrBonus.addAll(Bonus.viewGold(NumberUtil.getRandom(numFragMin, numFragMax)));
                            else if (star == 4)
                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.HERO_SHARD_4, new Random().nextInt((numFragMax - numFragMin) + 1) + numFragMin));
                            else if (star == 5)
                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.HERO_SHARD_5, new Random().nextInt((numFragMax - numFragMin) + 1) + numFragMin));
                        }
                        break;
                    case 4:
                        if (bonusObject.arenaCard > 0) {
                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARENA_TICKET, bonusObject.arenaCard));
                        }
                        break;
                    case 5:
                        //                        if (bonusObject.prophetBlessing.length > 0) {
                        //                            int numMin = bonusObject.prophetBlessing[0];
                        //                            int numMax = bonusObject.prophetBlessing[1];
                        //                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.PROPHET_BLESSING, new Random().nextInt((numMax - numMin) + 1) + numMin));
                        //                        }
                        arrBonus.addAll(Bonus.viewGem(200));//
                        break;
                    case 6:
                        if (bonusObject.casinoChip > 0) {
                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.CHIP, bonusObject.casinoChip));
                        }
                        break;
                    case 7:
                        if (bonusObject.artifactFragment.length > 0) {
                            int random = new Random().nextInt(bonusObject.artifactFragment.length / 2);
                            int quality = bonusObject.artifactFragment[random * 2];
                            int number = bonusObject.artifactFragment[(random * 2) + 1];
                            //
                            if (quality == MaterialType.ARTIFACT_SHARD_GREEN.id) {
                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_GREEN, number));
                            } else if (quality == MaterialType.ARTIFACT_SHARD_RED_EXCLUSIVE.id) {
                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_RED_EXCLUSIVE, number));
                            } else if (quality == MaterialType.ARTIFACT_SHARD_RED.id) {
                                arrBonus.addAll(Bonus.viewMaterial(MaterialType.ARTIFACT_SHARD_RED, number));
                            }
                        }
                        break;
                    case 8:
                        if (bonusObject.prophetOrb > 0) {
                            arrBonus.addAll(Bonus.viewMaterial(MaterialType.PROPHET_ORB, bonusObject.prophetOrb));
                        }
                        break;
                }
                rangeIndexBonus = new Random().nextInt(9);
            }
        }

        return arrBonus;
    }

    public static List<Long> getRequireQuest(int indexQuest) {
        List<Long> lstResult = new ArrayList<>();
        Map<Integer, List<Integer>> mapResultRandom = new HashMap<Integer, List<Integer>>();
        // req star
        if (config.requireStar[indexQuest] > 0) {
            lstResult.add((long) TYPE_REQ_STAR);
            lstResult.add((long) config.requireStar[indexQuest]);
        }
        //
        // req class or faction
        if (indexQuest < config.requireClassFaction.length) {
            int numRequire = config.requireClassFaction[indexQuest];
            for (int i = 0; i < numRequire; i++) {
                int randomType = new Random().nextInt(2) + 2;
                if (!mapResultRandom.containsKey(randomType))
                    mapResultRandom.put(randomType, new ArrayList<>());

                if (randomType == TYPE_REQ_FACTION) {// faction
                    List<Integer> lstRequire = mapResultRandom.get(TYPE_REQ_FACTION);
//                    List<HeroType> factions = List.of(HeroType.FACTION_FIRE, HeroType.FACTION_LUNISOLAR, HeroType.FACTION_WATER, HeroType.FACTION_WIND);
                    //                    int rand = NumberUtil.getRandomInList(factions).value;
                    //                    while (lstRequire.contains(rand)) {
                    //                        rand = NumberUtil.getRandomInList(factions).value;
                    //                    }
//                    lstRequire.add(rand);
                    //                    mapResultRandom.put(TYPE_REQ_FACTION, lstRequire);
                } else if (randomType == TYPE_REQ_CLASS) {// class
                    List<Integer> lstRequire = mapResultRandom.get(TYPE_REQ_CLASS);
                    int rand = new Random().nextInt(5) + 1;
                    while (lstRequire.contains(rand)) {
                        rand = new Random().nextInt(5) + 1;
                    }
                    lstRequire.add(rand);
                    mapResultRandom.put(TYPE_REQ_CLASS, lstRequire);
                }
            }

            for (Map.Entry<Integer, List<Integer>> entry : mapResultRandom.entrySet()) {
                for (int value : entry.getValue()) {
                    lstResult.add((long) entry.getKey());
                    lstResult.add((long) value);
                }
            }
        }

        return lstResult;
    }

    private static Map<Integer, List<Integer>> mIndex = new HashMap<Integer, List<Integer>>() {{
        put(0, Arrays.asList(0, 1));
        put(1, Arrays.asList(0, 1, 3));
        put(2, Arrays.asList(0, 1, 3, 4));
        put(3, Arrays.asList(0, 2, 3, 4, 6));
        put(4, Arrays.asList(0, 2, 3, 4, 6));
        put(5, Arrays.asList(0, 3, 5, 7, 9));
        put(6, Arrays.asList(0, 3, 5, 8, 9));
    }};

    private static Map<Integer, List<Integer>> mRate = new HashMap<Integer, List<Integer>>() {{
        put(0, Arrays.asList(50, 100));
        put(1, Arrays.asList(34, 67, 100));
        put(2, Arrays.asList(25, 50, 75, 100));
        put(3, Arrays.asList(25, 40, 60, 80, 100));
        put(4, Arrays.asList(25, 35, 60, 80, 100));
        put(5, Arrays.asList(25, 45, 70, 90, 100));
        put(6, Arrays.asList(30, 60, 80, 90, 100));
    }};

    private static int getBonusIndex(int questStar) {
        List<Integer> aIndex = mIndex.get(questStar);
        List<Integer> aRate = mRate.get(questStar);
        int rand = new Random().nextInt(100);
        for (int i = 0; i < aRate.size(); i++) {
            if (rand < aRate.get(i)) return aIndex.get(i);
        }
        return 0;
    }

    public static void loadConfig(String value) {
        json = JSONObject.fromObject(value);
        config = new Gson().fromJson(value, CfgTavern.DataConfig.class);

        config.init();
    }

    @Data
    public class DataConfig {
        float[] tavernQuestRate;
        public int[] feeSpeedup;
        public long[] timeComplete;
        public int hourRefresh, feeRefresh, feeBasicQuestScroll, feeSeniorQuestScroll;
        public int[] requireNumberHero;
        public int[] requireStar;
        int[] requireClassFaction;
        public int numberQuestPerOnce, maxStore;
        TavernBonusObject[] bonusTavern;

        public void init() {

        }
    }

    public class TavernBonusObject {
        public int[] diamond;
        public int[] shard;
        public int[] prophetBlessing;
        public int[] artifactFragment;
        public int basicScroll, heroicScroll, arenaCard, casinoChip, prophetOrb;
    }
}
