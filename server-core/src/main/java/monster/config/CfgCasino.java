package monster.config;

import com.google.gson.Gson;
import grep.helper.NumberUtil;
import lombok.Data;
import monster.config.penum.MaterialType;
import monster.dao.mapping.main.ResArtifactEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResItemEntity;
import monster.service.monitor.Telegram;
import monster.service.resource.ResArtifact;
import monster.service.resource.ResHero;
import monster.service.resource.ResItem;
import monster.service.user.Bonus;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class CfgCasino {
    public static JSONObject json;
    public static DataConfig config;

    public static Pbmethod.CommonVector.Builder protoRate() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        for (int i = 0; i < config.casinoRate.length; i++) {
            builder.addAString(String.valueOf(config.casinoRate[i]));
        }
        return builder;
    }

    public static int getRandomIndex() {
        float range = 0;
        for (int i = 0; i < config.casinoRate.length; i++) {
            range += config.casinoRate[i];
        }

        float random = new Random().nextFloat() * range;
        float top = 0;
        for (int i = 0; i < config.casinoRate.length; i++) {
            top += config.casinoRate[i];
            if (random < top) {
                return i;
            }
        }
        return 0;
    }

    public static JSONArray getCasinoBonusShow(int level) {
        JSONArray arrBonus = new JSONArray();
        int currLevel = level;
        CasinoBonusObject bonusObject = null;
        if (currLevel > config.casinoBonus.get(config.casinoBonus.size() - 1).levelMax) {
            currLevel = config.casinoBonus.get(config.casinoBonus.size() - 1).levelMax;
        }

        for (int i = 0; i < config.casinoBonus.size(); i++) {
            if (currLevel <= config.casinoBonus.get(i).levelMax) {
                bonusObject = config.casinoBonus.get(i);
                break;
            }
        }

        if (bonusObject != null) {
            float range = 0;
            int indexBonus = 0;
            for (int i = 0; i < bonusObject.chance.size(); i++) {
                range += bonusObject.chance.get(i);
            }

            float random = new Random().nextFloat() * range;
            float top = 0;
            for (int i = 0; i < bonusObject.chance.size(); i++) {
                top += bonusObject.chance.get(i);
                if (random < top) {
                    indexBonus = i;
                    break;
                }
            }
            //set bonus

            if (indexBonus < bonusObject.gold.size()) { //gold
                arrBonus.add(Bonus.view(Bonus.BONUS_GOLD, bonusObject.gold.get(indexBonus)));
            }
            if (indexBonus < bonusObject.spirit.size()) { //spirit
                arrBonus.add(Bonus.viewMaterial(MaterialType.SPIRIT, bonusObject.spirit.get(indexBonus)));
            }
            if (indexBonus < bonusObject.magic_dust.size()) { //magic dust
                arrBonus.add(Bonus.viewMaterial(MaterialType.MAGIC_DUST, bonusObject.magic_dust.get(indexBonus)));
            }
            if (indexBonus < bonusObject.promo_stone.size()) { // promo stone
                arrBonus.add(Bonus.viewMaterial(MaterialType.PROMOTION_STONE, bonusObject.promo_stone.get(indexBonus)));
            }
            if (bonusObject.artifact.size() > 0) { // artifact
                List<ResArtifactEntity> lstArtifact = ResArtifact.getListArtifactByQuality(bonusObject.artifact.get(0));
                if (lstArtifact.size() > 0) {
                    arrBonus.add(Bonus.view(Bonus.BONUS_ARTIFACT, lstArtifact.get(new Random().nextInt(lstArtifact.size())).getId(), bonusObject.artifact.get(1), 1));
                }
            }
            if (bonusObject.equipment1.size() > 0) { // equipment
                List<ResItemEntity> lstItem = ResItem.getListItemByColorAndStar(bonusObject.equipment1.get(0), bonusObject.equipment1.get(1));
                if (lstItem.size() > 0) {
                    arrBonus.add(Bonus.view(Bonus.BONUS_ITEM, lstItem.get(new Random().nextInt(lstItem.size())).getId(), 1));
                }
            }
            if (bonusObject.equipment2.size() > 0) { // equipment
                List<ResItemEntity> lstItem = ResItem.getListItemByColorAndStar(bonusObject.equipment2.get(0), bonusObject.equipment2.get(1));
                if (lstItem.size() > 0) {
                    arrBonus.add(Bonus.view(Bonus.BONUS_ITEM, lstItem.get(new Random().nextInt(lstItem.size())).getId(), 1));
                }
            }
            if (bonusObject.hero_shard1.size() > 0) { // hero shard
                int number = bonusObject.hero_shard1.get(0);
                int star = bonusObject.hero_shard1.get(1);
                int type = bonusObject.hero_shard1.get(2);
                if (type == MaterialType.HERO_SHARD_4.id) { // he bat ky
                    arrBonus.add(Bonus.viewMaterial(
                            NumberUtil.getRandomInList(List.of(MaterialType.HERO_SHARD_4ABYSS, MaterialType.HERO_SHARD_4FOREST, MaterialType.HERO_SHARD_4DARK)),
                            30));
                } else if (type == 0) { // tuong bat ky
                    ResHeroEntity heroRandom = ResHero.getRandomHeroSameStar(star);
                    if (heroRandom != null) {
                        arrBonus.add(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_HERO_SHARD, heroRandom.getId(), 30));
                    }
                } else {
                    List<ResHeroEntity> arrResult = new ArrayList<>();
                    for (int i = 2; i < bonusObject.hero_shard1.size(); i++) {
                        arrResult.add(ResHero.getHero(bonusObject.hero_shard1.get(i)));
                    }
                    ResHeroEntity heroRandom = arrResult.get(new Random().nextInt(arrResult.size()));
                    if (heroRandom != null) {
                        arrBonus.add(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_HERO_SHARD, heroRandom.getId(), 30));
                    }
                }
            }
            if (bonusObject.hero_shard2.size() > 0) { // hero shard
                int number = bonusObject.hero_shard2.get(0);
                int star = bonusObject.hero_shard2.get(1);
                int type = bonusObject.hero_shard2.get(2);

                if (type == MaterialType.HERO_SHARD_4.id) {
                    arrBonus.add(Bonus.viewMaterial(
                            NumberUtil.getRandomInList(List.of(MaterialType.HERO_SHARD_4ABYSS, MaterialType.HERO_SHARD_4FOREST, MaterialType.HERO_SHARD_4DARK)),
                            number));
                } else if (type == 0) {
                    ResHeroEntity heroRandom = ResHero.getRandomHeroSameStar(star);
                    if (heroRandom != null) {
                        arrBonus.add(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_HERO_SHARD, heroRandom.getId(), number));
                    }
                } else {
                    List<ResHeroEntity> arrResult = new ArrayList<>();
                    for (int i = 2; i < bonusObject.hero_shard2.size(); i++) {
                        arrResult.add(ResHero.getHero(bonusObject.hero_shard2.get(i)));
                    }
                    ResHeroEntity heroRandom = arrResult.get(new Random().nextInt(arrResult.size()));
                    if (heroRandom != null) { // Luôn là mảnh 5 sao
                        arrBonus.add(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_ARTIFACT_HERO_SHARD_STAR + 5, heroRandom.getId(), 50));
                    } else {
                        Telegram.sendNotify("casino heroShard2 error = " + bonusObject.hero_shard2.toString());
                    }
                }
            }
        }

        return arrBonus;
    }

    public static void loadConfig(String value) {
        json = JSONObject.fromObject(value);
        config = new Gson().fromJson(value, DataConfig.class);

        config.init();
    }

    @Data
    public class DataConfig {
        public int timeFreeRefresh;
        public float[] casinoRate;
        public int[] feeRefresh;
        public int[] feeRotate;
        public List<CasinoBonusObject> casinoBonus;
        public int priceChip;

        public void init() {

        }
    }

    public class CasinoBonusObject {
        public int levelMax;
        public List<Integer> chance;
        public List<Integer> gold;
        public List<Integer> spirit;
        public List<Integer> magic_dust;
        public List<Integer> promo_stone;
        public List<Integer> artifact;
        public List<Integer> equipment1;
        public List<Integer> equipment2;
        public List<Integer> hero_shard1;
        public List<Integer> hero_shard2;
    }
}