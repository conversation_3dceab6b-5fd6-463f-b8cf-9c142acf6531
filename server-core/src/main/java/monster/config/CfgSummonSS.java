package monster.config;

import com.google.gson.Gson;
import monster.config.penum.SummonType;
import monster.service.battle.common.config.HeroType;

public class CfgSummonSS {

    public static DataConfig config;

    public static int getOrbSummonType(HeroType faction, int number) {
        switch (faction) {
            case FACTION_FIRE:
                return config.orbAbyss.getSummonType(number);
            case FACTION_WIND:
                return config.orbForest.getSummonType(number);
//            case FACTION_LAND:
//                return config.orbFortress.getSummonType(number);
//            case FACTION_LIGHTNING:
//                return config.orbShadow.getSummonType(number);
            case FACTION_WATER:
                return config.orbDark.getSummonType(number);
        }
        return -1;
    }

    public static int getOrbNoSS100SummonType(HeroType faction) {
        switch (faction) {
            case FACTION_FIRE:
                return SummonType.NO_SS_100_ABYSS.value;
            case FACTION_WIND:
                return SummonType.NO_SS_100_FOREST.value;
//            case FACTION_LAND:
//                return SummonType.NO_SS_100_FORTRESS.value;
//            case FACTION_LIGHTNING:
//                return SummonType.NO_SS_100_SHADOW.value;
        }
        return -1;
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
    }

    public class DataConfig {
        public SummonData orbAbyss, orbShadow, orbForest, orbFortress, orbDark;
        public SummonData orbDl, heroicGod, ssl1, ssl2, shardGod;
    }

    /**
     * Mapping các mốc quay với id SummonRate
     */
    public class SummonData {
        int[] milestone, summonId;

        /**
         * @param number lần quay thứ mấy
         * @return id SummonRate
         */
        public int getSummonType(int number) {
            int size = milestone.length;
            int summonIndex = size - 1;
            for (int i = 0; i < milestone.length; i++) {
                if (number <= milestone[i]) {
                    summonIndex = i;
                    break;
                }
            }
            return summonId[summonIndex];
        }
    }
}