package monster.config;

import com.google.gson.Gson;
import monster.config.penum.EventType;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.object.MyUser;
import monster.service.battle.common.config.HeroType;
import monster.service.monitor.EventMonitor;
import monster.service.resource.ResHero;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import java.util.*;

public class CfgSpecialItem {
    public static DataConfig config;

    private static Map<HeroType, MaterialType> upgradedMaterial = new HashMap<HeroType, MaterialType>() {{
        put(HeroType.CLASS_MAGE, MaterialType.BADGE_CLASS_MAGE);
        put(HeroType.CLASS_WARRIOR, MaterialType.BADGE_CLASS_WARRIOR);
        put(HeroType.CLASS_ASSASSIN, MaterialType.BADGE_CLASS_ASSASSIN);
        put(HeroType.CLASS_RANGER, MaterialType.BADGE_CLASS_RANGE);
        put(HeroType.CLASS_PRIEST, MaterialType.BADGE_CLASS_PRIEST);
        //        put(HeroType.FACTION_LAND, MaterialType.BADGE_FACTION_FORTRESS);
        put(HeroType.FACTION_FIRE, MaterialType.BADGE_FACTION_ABYSS);
        put(HeroType.FACTION_WIND, MaterialType.BADGE_FACTION_FOREST);
        //        put(HeroType.FACTION_LIGHTNING, MaterialType.BADGE_FACTION_SHADOW);
        put(HeroType.FACTION_WATER, MaterialType.BADGE_FACTION_DARK);
        //        put(HeroType.FACTION_LUNISOLAR, MaterialType.BADGE_FACTION_LIGHT);
    }};

    private static Map<HeroType, EventType> eventLevel = new HashMap<HeroType, EventType>() {{
        put(HeroType.CLASS_MAGE, EventType.SPECIAL_ITEM_CLASS_CLASS_MAGE_LEVEL);
        put(HeroType.CLASS_WARRIOR, EventType.SPECIAL_ITEM_CLASS_WARRIOR_LEVEL);
        put(HeroType.CLASS_ASSASSIN, EventType.SPECIAL_ITEM_CLASS_ASSASSIN_LEVEL);
        put(HeroType.CLASS_RANGER, EventType.SPECIAL_ITEM_CLASS_RANGER_LEVEL);
        put(HeroType.CLASS_PRIEST, EventType.SPECIAL_ITEM_CLASS_PRIEST_LEVEL);
    }};

    public static List<Long> getListInfo(int[] levelEnhance, int[] tierUpgrade) {
        //Declare
        List<Long> listInfo = new ArrayList<>();
        int levelEnhanceAtt = Math.min(levelEnhance[0], getMaxEnhance());
        int tierUpgradeAtt = Math.min(tierUpgrade[0], getMaxUpgrade());
        int levelEnhanceHp = Math.min(levelEnhance[1], getMaxEnhance());
        int tierUpgradeHp = Math.min(tierUpgrade[1], getMaxUpgrade());

        //Item Attack
        listInfo.add((long) levelEnhanceAtt);
        listInfo.add(config.enhance.attack[levelEnhanceAtt]);
        listInfo.add((long) tierUpgradeAtt);
        listInfo.add(config.upgrade.attack[tierUpgradeAtt]);

        //Item Hp
        listInfo.add((long) levelEnhanceHp);
        listInfo.add(config.enhance.hp[levelEnhanceHp]);
        listInfo.add((long) tierUpgradeHp);
        listInfo.add(config.upgrade.hp[tierUpgradeHp]);

        //next info without level
        if (levelEnhanceAtt >= getMaxEnhance()) listInfo.add(0L);
        else listInfo.add(config.enhance.attack[levelEnhanceAtt + 1]);

        if (tierUpgradeAtt >= getMaxUpgrade()) listInfo.add(0L);
        else listInfo.add(config.upgrade.attack[tierUpgradeAtt + 1]);

        if (levelEnhanceHp >= getMaxEnhance()) listInfo.add(0L);
        else listInfo.add(config.enhance.hp[levelEnhanceHp + 1]);

        if (tierUpgradeHp >= getMaxUpgrade()) listInfo.add(0L);
        else listInfo.add(config.upgrade.hp[tierUpgradeHp + 1]);

        return listInfo;
    }

    //    public static List<Long> getFee(UserHeroEntity uHero, int typeSpecialItem, boolean isEnhance) {
    //        List<Long> feeInfo = new ArrayList<>();
    //        if (isEnhance) {
    //            feeInfo.addAll(getEnhancePrice(uHero, typeSpecialItem));
    //            return feeInfo;
    //        } else {
    //            feeInfo.addAll(getUpgradePrice(uHero, typeSpecialItem));
    //            return feeInfo;
    //        }
    //    }

    public static int getMaxEnhance() {
        return config.enhance.level.length - 1;
    }

    public static int getMaxUpgrade() {
        return config.upgrade.tier.length - 1;
    }

    public static Pbmethod.CommonVector getMaxEnhanceUpgrade() {
        Pbmethod.CommonVector.Builder aBuilder = Pbmethod.CommonVector.newBuilder();
        aBuilder.addALong(getMaxEnhance());
        aBuilder.addALong(getMaxUpgrade());

        return aBuilder.build();
    }

    public static String getDefaultSpecialItem() {
        return "[1,1,1,1]";
    }

    public static Pbmethod.ListCommonVector.Builder getAllInfoForClient(UserHeroEntity uHero) {
        //Declare
        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();
        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(getListInfo(uHero.getLevelEnhance(), uHero.getTierUpgrade())).build());

        for (int i = 0; i <= 1; i++) {
            //Nếu max enhance
            if (uHero.getLevelEnhance()[i] == getMaxEnhance()) {
                aBuilder.addAVector(getKeyPriceForClient(uHero, true, i).build().getAVector(0));
            } else {
                aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(getEnhancePrice(uHero, i, 1)).build());
            }

            //Nếu max upgrade
            if (uHero.getTierUpgrade()[i] == getMaxUpgrade()) {
                aBuilder.addAVector(getKeyPriceForClient(uHero, false, i).build().getAVector(0));
                aBuilder.addAVector(getKeyPriceForClient(uHero, false, i).build().getAVector(1));
            } else {
                aBuilder.addAVector(getUpgradePriceForClient(uHero, i).getAVector(0));
                aBuilder.addAVector(getUpgradePriceForClient(uHero, i).getAVector(1));
            }

        }

        return aBuilder;
    }

    //    public static Pbmethod.ListCommonVector.Builder getNextInfoForClient(UserHeroEntity uHero, int typeSpecialItem) {
    //        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();
    //        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(getNextInfo(uHero, typeSpecialItem)));
    //
    //        return aBuilder;
    //    }

    public static Pbmethod.ListCommonVector.Builder getNextFeeForClient(UserHeroEntity uHero, int typeSpecialItem, boolean isEnhance) {
        //Declare
        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();

        //Enhance
        if (isEnhance) {
            //Nếu max
            if (uHero.getLevelEnhance()[typeSpecialItem] == getMaxEnhance()) {
                return getKeyPriceForClient(uHero, true, typeSpecialItem);
            }

            aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(getEnhancePrice(uHero, typeSpecialItem, 1)));
            return aBuilder;
        }

        //Upgrade
        //Nếu max
        if (uHero.getTierUpgrade()[typeSpecialItem] == getMaxUpgrade()) {
            return getKeyPriceForClient(uHero, false, typeSpecialItem);
        }

        aBuilder.addAVector(getUpgradePriceForClient(uHero, typeSpecialItem).getAVector(0));
        aBuilder.addAVector(getUpgradePriceForClient(uHero, typeSpecialItem).getAVector(1));

        return aBuilder;
    }

    public static List<Long> getNextInfo(UserHeroEntity uHero, int typeSpecialItem, boolean isEnhance, int number) {
        //Declare
        List<Long> listInfo = new ArrayList<>();
        int levelEnhance = uHero.getLevelEnhance()[typeSpecialItem];
        int tierUpgrade = uHero.getTierUpgrade()[typeSpecialItem];
        if (typeSpecialItem == 0) {
            if (isEnhance)
                listInfo.add(config.enhance.attack[Math.min(CfgSpecialItem.getMaxEnhance(), levelEnhance + number)]);
            else listInfo.add(config.upgrade.attack[Math.min(CfgSpecialItem.getMaxUpgrade(), tierUpgrade + number)]);
        } else {
            if (isEnhance)
                listInfo.add(config.enhance.hp[Math.min(CfgSpecialItem.getMaxEnhance(), levelEnhance + number)]);
            else listInfo.add(config.upgrade.hp[Math.min(CfgSpecialItem.getMaxUpgrade(), tierUpgrade + number)]);
        }

        return listInfo;
    }

    public static List<Long> getInfoAfterReup(UserHeroEntity uHero, int typeSpecialItem) {
        //Declare
        List<Long> listInfo = new ArrayList<>();
        int levelEnhance = uHero.getLevelEnhance()[typeSpecialItem];
        int tierUpgrade = uHero.getTierUpgrade()[typeSpecialItem];

        if (typeSpecialItem == 0) {
            listInfo.add(config.enhance.attack[levelEnhance]);
            listInfo.add(config.upgrade.attack[tierUpgrade]);
        } else {
            listInfo.add(config.enhance.hp[levelEnhance]);
            listInfo.add(config.upgrade.hp[tierUpgrade]);
        }

        return listInfo;
    }

    //    public static List<Long> getFeeAfterReup(UserHeroEntity uHero, int typeSpecialItem, boolean isEnhance) {
    //        List<Long> feeAterReup = new ArrayList<>();
    //        if (isEnhance) {
    //            feeAterReup.addAll(getEnhancePrice(uHero, typeSpecialItem));
    //            return feeAterReup;
    //        } else {
    //            feeAterReup.addAll(getUpgradePrice(uHero, typeSpecialItem));
    //            return feeAterReup;
    //        }
    //    }

    public static Pbmethod.ListCommonVector getPreviewReupForClient(UserHeroEntity uHero, int typeSpecialItem) {
        //Declare
        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();

        //aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(CfgSpecialItem.getBonusReupFee(uHero)).build());
        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(CfgSpecialItem.getBonusUsedMaterial(uHero, typeSpecialItem)).build());

        return aBuilder.build();
    }

    //    public static Pbmethod.ListCommonVector.Builder getAllInfoAfterReupForClient(UserHeroEntity uHero, int typeSpecialItem) {
    //        //Declare
    //        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();
    //
    //        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(getInfoAfterReup(uHero, typeSpecialItem)));
    //        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(getEnhancePrice(uHero, typeSpecialItem)));
    //        aBuilder.addAVector(getUpgradePriceForClient(uHero, typeSpecialItem).getAVector(0));
    //        aBuilder.addAVector(getUpgradePriceForClient(uHero, typeSpecialItem).getAVector(1));
    //
    //        return aBuilder;
    //    }

    public static List<Long> getEnhancePrice(UserHeroEntity uHero, int typeSpecialItem, int numberEnhance) {
        //Declare
        List<Long> aLong = new ArrayList<>();
        int level = uHero.getLevelEnhance()[typeSpecialItem];
        for (int i = 0; i < numberEnhance; i++) {
            int newLevel = level + i;
            if (newLevel >= 250)
                aLong = Bonus.merge(aLong, Bonus.view(Bonus.BONUS_MATERIAL, 1, 2092, config.enhance.enhanceStone[newLevel]));
            else
                aLong = Bonus.merge(aLong, Bonus.viewMaterial(MaterialType.ENHANCE_STONE, config.enhance.enhanceStone[newLevel]));
            aLong = Bonus.merge(aLong, Bonus.viewGold(config.enhance.gold[newLevel]));
        }
        return aLong;
    }

    public static Pbmethod.ListCommonVector.Builder getKeyPriceForClient(UserHeroEntity uHero, boolean isEnhance, int typeSpecialItem) {
        //Declare
        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();
        ResHeroEntity resHero = ResHero.getHero(uHero.getHeroId());
        List<Long> aLong = new ArrayList<>();
        if (isEnhance) {
            aLong.add((long) MaterialType.ENHANCE_STONE.id);
            aLong.add(1001L);

            aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(aLong));
        } else {
            if (typeSpecialItem == 0) {
                aLong.add((long) upgradedMaterial.get(resHero.getHeroClass()).id);
            } else {
                if (resHero.getRank() == 5) aLong.add((long) MaterialType.BADGE_RANK_5.id);
                else aLong.add((long) MaterialType.BADGE_RANK_6.id);
            }
            aLong.add((long) upgradedMaterial.get(resHero.getHeroFaction()).id);

            aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(aLong));
            aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(1001L));
        }


        return aBuilder;
    }

    public static List<Long> getUpgradePrice(UserHeroEntity uHero, int typeSpecialItem, int number) {
        if (true) return new ArrayList<>();
        //Declare
        ResHeroEntity resHero = ResHero.getHero(uHero.getHeroId());
        List<Long> aLong = new ArrayList<>();
        int tierUpgrade = uHero.getTierUpgrade()[typeSpecialItem];
        for (int i = 0; i < number; i++) {
            int newTierUpgrade = tierUpgrade + i;
            List<Long> currentLevel = new ArrayList<>();
            if (typeSpecialItem == 0) {
                currentLevel.addAll(Bonus.viewMaterial(upgradedMaterial.get(resHero.getHeroClass()), config.upgrade.badgeClass[newTierUpgrade]));
            } else {
                if (resHero.getRank() == 5)
                    currentLevel.addAll(Bonus.viewMaterial(MaterialType.BADGE_RANK_5, config.upgrade.badgeRank[newTierUpgrade]));
                else
                    currentLevel.addAll(Bonus.viewMaterial(MaterialType.BADGE_RANK_6, config.upgrade.badgeRank[newTierUpgrade]));
            }
            currentLevel.addAll(Bonus.viewMaterial(upgradedMaterial.get(resHero.getHeroFaction()), config.upgrade.badgeFaction[newTierUpgrade]));
            currentLevel.addAll(Bonus.viewGold(config.upgrade.gold[newTierUpgrade]));
            aLong = Bonus.merge(aLong, currentLevel);
        }
        return aLong;
    }

    private static Pbmethod.ListCommonVector getUpgradePriceForClient(UserHeroEntity uHero, int typeSpecialItem) {
        //Declare
        ResHeroEntity resHero = ResHero.getHero(uHero.getHeroId());
        Pbmethod.ListCommonVector.Builder aBuilder = Pbmethod.ListCommonVector.newBuilder();
        List<Long> aLong = new ArrayList<>();
        int tierUpgrade = uHero.getTierUpgrade()[typeSpecialItem];

        if (typeSpecialItem == 0) {
            aLong.addAll(Bonus.viewMaterial(upgradedMaterial.get(resHero.getHeroClass()), config.upgrade.badgeClass[tierUpgrade]));
        } else {
            if (resHero.getRank() == 5)
                aLong.addAll(Bonus.viewMaterial(MaterialType.BADGE_RANK_5, config.upgrade.badgeRank[tierUpgrade]));
            else
                aLong.addAll(Bonus.viewMaterial(MaterialType.BADGE_RANK_6, config.upgrade.badgeRank[tierUpgrade]));
        }
        aLong.addAll(Bonus.viewMaterial(upgradedMaterial.get(resHero.getHeroFaction()), config.upgrade.badgeFaction[tierUpgrade]));

        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(aLong).build());
        aLong.clear();

        //aLong.addAll(Bonus.viewMaterial(MaterialType.MAGIC_DUST, config.upgrade.magicDust[tierUpgrade]));
        aLong.addAll(Bonus.viewGold(config.upgrade.gold[tierUpgrade]));
        aBuilder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(aLong).build());

        return aBuilder.build();
    }

    private static List<Long> getUsedMaterial(UserHeroEntity uHero, int typeSpecialItem) {
        //Declare
        int levelEnhance = uHero.getLevelEnhance()[typeSpecialItem];
        int tierUpgrade = uHero.getTierUpgrade()[typeSpecialItem];
        Long[] usedMaterial = {0L, 0L, 0L, 0L, 0L, 0L, 0L}; // thêm 1 nguyên liệu mới

        //Enhance
        for (int i = 0; i < Math.min(250, levelEnhance); i++) {
            usedMaterial[0] += config.enhance.enhanceStone[i];
            usedMaterial[5] += config.enhance.gold[i];
        }
        for (int i = 250; i < levelEnhance; i++) {
            usedMaterial[5] += config.enhance.gold[i];
            usedMaterial[6] += config.enhance.enhanceStone[i];
        }

        //Upgrade
        for (int i = 0; i < tierUpgrade; i++) {
            usedMaterial[1] += config.upgrade.badgeClass[i];
            usedMaterial[2] += config.upgrade.badgeRank[i];
            usedMaterial[3] += config.upgrade.badgeFaction[i];
            //usedMaterial[4] += config.upgrade.magicDust[i];
            usedMaterial[5] += config.upgrade.gold[i];
        }

        return Arrays.asList(usedMaterial);
        //      return Arrays.stream(usedMaterial).collect(Collectors.toList());
    }

    public static List<Long> getBonusUsedMaterial(List<UserHeroEntity> listHero) {
        List<Long> aLong = new ArrayList<>();
        for (UserHeroEntity uHero : listHero) {
            aLong.addAll(Bonus.merge(getBonusUsedMaterial(uHero, 0), getBonusUsedMaterial(uHero, 1)));
        }

        return aLong;
    }

    public static List<Long> getBonusUsedMaterial(UserHeroEntity uHero) {
        return Bonus.merge(getBonusUsedMaterial(uHero, 0), getBonusUsedMaterial(uHero, 1));
    }

    public static List<Long> getBonusUsedMaterial(UserHeroEntity uHero, int typeSpecialItem) {
        //Declare
        ResHeroEntity resHero = ResHero.getHero(uHero.getHeroId());
        List<Long> aLong = new ArrayList<>();
        List<Long> usedMaterial = getUsedMaterial(uHero, typeSpecialItem);

        if (usedMaterial.get(0) > 0) aLong.addAll(Bonus.viewMaterial(MaterialType.ENHANCE_STONE, usedMaterial.get(0)));
        if (usedMaterial.get(6) > 0) aLong.addAll(Bonus.view(Bonus.BONUS_MATERIAL, 1, 2092, usedMaterial.get(6)));
        if (typeSpecialItem == 0) {
            if (usedMaterial.get(1) > 0) aLong.addAll(Bonus.viewMaterial(upgradedMaterial.get(resHero.getHeroClass()), usedMaterial.get(1)));
        } else {
            if (resHero.getRank() == 5) {
                if (usedMaterial.get(2) > 0) aLong.addAll(Bonus.viewMaterial(MaterialType.BADGE_RANK_5, usedMaterial.get(2)));
            } else {
                if (usedMaterial.get(2) > 0) aLong.addAll(Bonus.viewMaterial(MaterialType.BADGE_RANK_6, usedMaterial.get(2)));
            }
        }
        if (usedMaterial.get(3) > 0) aLong.addAll(Bonus.viewMaterial(upgradedMaterial.get(resHero.getHeroFaction()), usedMaterial.get(3)));
        //aLong.addAll(Bonus.viewMaterial(MaterialType.MAGIC_DUST, usedMaterial.get(4)));
        aLong.addAll(Bonus.viewGold(usedMaterial.get(5)));

        return aLong;
    }

    public static long getFeeReup(UserHeroEntity uHero) {
        if ((uHero.getLevelEnhance()[0] + uHero.getLevelEnhance()[1]) != 0) { //check unlock
            return config.feeReup;
        } else {
            return 0;
        }

    }

    public static List<Long> getBonusReupFee(UserHeroEntity uHero) {
        List<Long> aLong = new ArrayList<>();
        //        int count = 0;
        //        if ((uHero.getLevelEnhance()[0] + uHero.getLevelEnhance()[1]) != 0) { //check unlock
        //            if (uHero.getLevelEnhance()[0] > 0 || uHero.getTierUpgrade()[0] > 0) count++;
        //            if (uHero.getLevelEnhance()[1] > 0 || uHero.getTierUpgrade()[1] > 0) count++;
        //            aLong.addAll(Bonus.viewMaterial(MaterialType.FEE_REUP_SPECIAL_ITEM, config.feeReup * count));
        //        } else {
        //            aLong.addAll(Bonus.viewMaterial(MaterialType.FEE_REUP_SPECIAL_ITEM, 0));
        //        }
        //        return aLong;
        aLong.addAll(Bonus.viewMaterial(MaterialType.FEE_REUP_SPECIAL_ITEM, 0));
        return aLong;
    }

    public static List<Long> getLock(UserHeroEntity uHero, int typeSpecialItem) {
        List<Long> aLong = new ArrayList<>();

        if (uHero.getLevel() < config.lock.heroLevel[1]) {
            aLong.add(config.lock.heroLevel[1]);
            aLong.add(config.lock.lockLevelEnhance[0]);
            aLong.add(config.lock.lockLevelEnhance[1]);
            aLong.add(config.lock.lockTierUpgrade[0]);

            return aLong;
        }

        if (uHero.getLevel() >= config.lock.heroLevel[config.lock.heroLevel.length - 1]) {
            aLong.add(config.lock.heroLevel[config.lock.heroLevel.length - 1]);
            aLong.add(config.lock.lockLevelEnhance[config.lock.heroLevel.length - 1]);
            for (int j = 1; j < config.lock.heroLevel.length; j++) {
                if (uHero.getLevelEnhance()[typeSpecialItem] == config.lock.lockLevelEnhance[config.lock.heroLevel.length - 1]) {
                    aLong.add(config.lock.lockLevelEnhance[config.lock.heroLevel.length - 1]);
                    aLong.add(config.lock.lockTierUpgrade[config.lock.heroLevel.length - 1]);
                    break;
                }
                if (uHero.getLevelEnhance()[typeSpecialItem] < config.lock.lockLevelEnhance[j]) {
                    aLong.add(config.lock.lockLevelEnhance[j]);
                    aLong.add(config.lock.lockTierUpgrade[j - 1]);
                    break;
                }
            }

            return aLong;
        }

        for (int i = 1; i < config.lock.heroLevel.length - 1; i++) {
            if (uHero.getLevel() < config.lock.heroLevel[i + 1]) {
                aLong.add(config.lock.heroLevel[i + 1]);
                aLong.add(config.lock.lockLevelEnhance[i]);
                for (int j = 1; j < config.lock.heroLevel.length; j++) {
                    if (uHero.getLevelEnhance()[typeSpecialItem] == config.lock.lockLevelEnhance[config.lock.heroLevel.length - 1]) {
                        aLong.add(config.lock.lockLevelEnhance[j]);
                        aLong.add(config.lock.lockTierUpgrade[j]);
                        break;
                    }
                    if (uHero.getLevelEnhance()[typeSpecialItem] < config.lock.lockLevelEnhance[j]) {
                        aLong.add(config.lock.lockLevelEnhance[j]);
                        aLong.add(config.lock.lockTierUpgrade[j - 1]);
                        break;
                    }
                }
            }
        }

        return aLong;
    }

    public static void loadEventLevel5(MyUser mUser, int heroId) {
        UserHeroEntity uHero = mUser.getResources().getHero(heroId);
        ResHeroEntity resHero = ResHero.getHero(uHero.getHeroId());
        int[] arrLevelEnhance = uHero.getLevelEnhance();
        //Event
        for (int i = 0; i < 1; i++) {
            EventMonitor.getInstance().addDropItem(mUser.getUser().getId(), eventLevel.get(resHero.getHeroClass()), arrLevelEnhance[i]);
        }
        if (resHero.getRank() == 5)
            EventMonitor.getInstance().addDropItem(mUser.getUser().getId(), EventType.SPECIAL_ITEM_RANK_S, arrLevelEnhance[0]);
        if (resHero.getRank() == 6)
            EventMonitor.getInstance().addDropItem(mUser.getUser().getId(), EventType.SPECIAL_ITEM_RANK_SS, arrLevelEnhance[0]);
    }

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, CfgSpecialItem.DataConfig.class);
        config.init();
    }

    public class DataConfig {
        public EnhanceObject enhance;
        public UpgradeObject upgrade;
        public LockObject lock;
        public long feeReup;
        public int heroLevelRequest;

        public void init() {
        }
    }

    public class LockObject {
        public long[] heroLevel, lockLevelEnhance, lockTierUpgrade;
    }

    public class EnhanceObject {
        public long[] level, enhanceStone, gold, attack, hp;
    }

    public class UpgradeObject {
        public long[] tier, badgeClass, badgeRank, badgeFaction, /**
         * magicDust,
         */
        gold, attack, hp;
    }
}
