package monster.config;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.LogicUtil;
import lombok.Getter;
import monster.dao.mapping.main.ResRelicEntity;
import monster.game.pot.*;

import java.util.*;

public class CfgPot {
    public static final int EVENT_UPDATE = 0;
    public static final int EVENT_CHAT = 1;
    public static final int EVENT_UPDATE_LOCATION = 4;
    public static final int EVENT_UPDATE_LOCATION_BY_SWITCH = 5;

    public static final int STATE_INVISIBLE = 0;
    public static final int STATE_VISIBLE = 1;
    public static final int STATE_DESTROYED = 2;
    public static final int STATE_LOCKED = 3;
    public static final int STATE_UNLOCKED = 4;
    public static final int STATE_OPENED = 5;
    public static final int STATE_RIGHT_ANSWER = 6;
    public static final int STATE_WRONG_ANSWER = 7;
    public static final String OBJECT_MONSTER = "monster";
    public static final String OBJECT_CHEST = "chest";
    public static final String OBJECT_RELIC = "relic";
    public static final String OBJECT_DOOR = "door";
    public static final String OBJECT_HEALING_BEAN = "healingRice";
    public static final String OBJECT_BLOCK = "block1x1";
    public static final String OBJECT_EVENT_LISTENER = "eventListener";
    public static final String OBJECT_MULTI_EVENT_LISTENER = "multiEventListener";
    public static final String OBJECT_POISON_POTION = "poisonPotion";
    public static final String OBJECT_NPC = "NPC";
    public static final String OBJECT_REVIVE_JAR = "reviveJar";
    public static final String OBJECT_SUPPORT_STATION = "supportStation";
    public static final String OBJECT_PORTAL = "portal";
    public static final String OBJECT_ROCK = "blockMoveable";
    public static final String OBJECT_CHEST_MONSTER = "chestMonster";
    public static final String OBJECT_CHALLENGE_MONSTER = "challengeMonster";
    public static final String OBJECT_SWITCH = "switch";
    public static final String OBJECT_TORCH = "torch";
    public static final String OBJECT_NPC_GIFT = "NPCgift";
    public static final String OBJECT_SLEEK_GROUND = "sleekGround";

    public static DataConfig config;
    public static Map<Integer, ResPotMapEntity> mPotMap = new HashMap<>();
    public static Map<Integer, ResNewHeroEventMapEntity> mNewHeroEventMap = new HashMap<>();
    public static Map<Integer, List<ResPotChestBossEntity>> listResPotChestBossMapById = new HashMap<>();
    public static Map<Integer, List<Long>> mBonus = new HashMap<>();
    private static Map<Integer, List<ResRelicEntity>> mRelicByRank = new HashMap<>();
    private static Map<Integer, ResRelicEntity> mRelic = new HashMap<>();
    private static List<ResPotMonsterFormation> listResPotMonsterFormation = new ArrayList<>();

    public static void loadConfig(String strJson) {
        config = new Gson().fromJson(strJson, DataConfig.class);
        config.init();

        String mapTable = CfgServer.isTestServer() ? "res_pot_map_test" : "res_pot_map";
        String strQuery = "select * from " + CfgServer.DB_MAIN + mapTable + (CfgServer.isTestServer() ? "" : " where `is_release` = 1");
        List<ResPotMapEntity> aResMap = DBJPA.getQueryList(strQuery, ResPotMapEntity.class);
        if (aResMap == null) throw new NullPointerException();
        mPotMap.clear();
        for (ResPotMapEntity resMap : aResMap) {
            //            if (CfgServer.isRealServer() && mPotMap.size() >= 10) break;
            mPotMap.put(resMap.getId(), resMap);
        }

        //        List<ResPotMapEntity> aResMapTest = DBJPA.getList(CfgServer.DB_MAIN + mapTable, ResPotMapEntity.class);
        //        if (aResMapTest == null) throw new NullPointerException();
        //        mPotMapTest.clear();
        //        for (ResPotMapEntity resMap : aResMapTest) {
        //            if (mPotMapTest.size() >= 8) break;
        //            mPotMapTest.put(resMap.getId(), resMap);
        //        }

        List<ResNewHeroEventMapEntity> aResEventMap = DBJPA.getList(CfgServer.DB_MAIN + "res_new_hero_event_map", ResNewHeroEventMapEntity.class);
        if (aResEventMap == null) throw new NullPointerException();
        mNewHeroEventMap.clear();
        for (ResNewHeroEventMapEntity resMap : aResEventMap) {
            mNewHeroEventMap.put(resMap.getMapId(), resMap);
        }

        List<ResPotChestBossEntity> listResPotChestBoss = DBJPA.getList(CfgServer.DB_MAIN + "res_pot_chest_boss", ResPotChestBossEntity.class);
        if (listResPotChestBoss == null) throw new NullPointerException();
        listResPotChestBossMapById.clear();
        for (ResPotChestBossEntity resPotChestBoss : listResPotChestBoss) {
            if (!listResPotChestBossMapById.containsKey(resPotChestBoss.getId()))
                listResPotChestBossMapById.put(resPotChestBoss.getId(), new ArrayList<>());
            listResPotChestBossMapById.get(resPotChestBoss.getId()).add(resPotChestBoss);
        }

        listResPotMonsterFormation = DBJPA.getList(CfgServer.DB_MAIN + "res_pot_monster_formation", ResPotMonsterFormation.class);
        if (listResPotMonsterFormation == null) throw new NullPointerException();

        mRelicByRank.clear();
        List<ResRelicEntity> aRelic = DBJPA.getList(CfgServer.DB_MAIN + "res_relic", Arrays.asList("finish", 1, "pot", 1), "", ResRelicEntity.class);
        for (ResRelicEntity relic : aRelic) {
            relic.init();
            if (!mRelicByRank.containsKey(relic.getRrank())) mRelicByRank.put(relic.getRrank(), new ArrayList<>());
            mRelicByRank.get(relic.getRrank()).add(relic);
            mRelic.put(relic.getId(), relic);
        }

    }

    @Getter
    public class DataConfig {
        private int numberMap;
        private RankMonster rankMonster;
        private List<Integer> cloneHeroIds;

        public void init() {


        }
    }

    @Getter
    public class RankMonster {
        private List<Integer> rank, xPower;
    }

    public static JsonArray getMap(int mapId, int mapMode) {
        ResPotMapEntity resMap = mPotMap.get(mapId);
        return GsonUtil.parseJsonArray(resMap.getStrData());
    }

    public static int getXPower(int monsterRank) {
        //        List<Integer> aRank = config.getRankMonster().rank;
        //        List<Integer> aXPower = config.getRankMonster().xPower;
        //        for (int i = 0; i < aRank.size(); i++) {
        //            if (aRank.get(i) == monsterRank) return aXPower.get(i);
        //        }

        return 0;
    }

    public static List<Integer> getRandomRelic(List<Integer> lstRelicRandomByRank, List<Integer> lstRelicId) {
        List<Integer> listRelicId = new ArrayList<>(lstRelicId);
        for (int rank : lstRelicRandomByRank) {
            int randomRelicId = LogicUtil.getRandom(mRelicByRank.get(rank)).getId();
            int count = 0;
            while (listRelicId.contains(randomRelicId) && count < 10000) {
                randomRelicId = LogicUtil.getRandom(mRelicByRank.get(rank)).getId();
                count++;
            }
            listRelicId.add(randomRelicId);
        }

        return listRelicId;
    }

    public static ResRelicEntity getRelic(int relicId) {
        return mRelic.get(relicId);
    }

    public static Map<Integer, PotAbstractObject> getSuperObjectFromJsonMappedById(JsonArray values) {
        Map<Integer, PotAbstractObject> mObject = new HashMap<>();
        for (int i = 0; i < values.size(); i++) {
            JsonObject obj = values.get(i).getAsJsonObject();
            String type = obj.get("type").getAsString();
            int objId = obj.get("id").getAsInt();
            switch (type) {
                case OBJECT_DOOR -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotDoorObject.class));
                case OBJECT_BLOCK -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotBlockObject.class));
                case OBJECT_MONSTER -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotMonsterObject.class));
                case OBJECT_CHEST -> {
                    PotChestObject chestObject = new Gson().fromJson(obj.toString(), PotChestObject.class);
                    mObject.put(objId, chestObject);
                }
                case OBJECT_RELIC -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotRelicObject.class));
                case OBJECT_HEALING_BEAN ->
                        mObject.put(objId, new Gson().fromJson(obj.toString(), PotHealingBeanObject.class));
                case OBJECT_EVENT_LISTENER ->
                        mObject.put(objId, new Gson().fromJson(obj.toString(), PotEventListenerObject.class));
                case OBJECT_POISON_POTION ->
                        mObject.put(objId, new Gson().fromJson(obj.toString(), PotPoisonPotionObject.class));
                case OBJECT_NPC -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotNPCObject.class));
                case OBJECT_REVIVE_JAR ->
                        mObject.put(objId, new Gson().fromJson(obj.toString(), PotReviveJarObject.class));
                case OBJECT_SUPPORT_STATION ->
                        mObject.put(objId, new Gson().fromJson(obj.toString(), PotSupportStationObject.class));
                case OBJECT_PORTAL -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotPortalObject.class));
                case OBJECT_ROCK -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotRockObject.class));
                case OBJECT_CHEST_MONSTER -> {
                    PotChestMonsterObject chestMonsterObject = new Gson().fromJson(obj.toString(), PotChestMonsterObject.class);
                    mObject.put(objId, chestMonsterObject);
                }
                case OBJECT_CHALLENGE_MONSTER -> {
                    PotChallengeMonsterObject challengeMonster = new Gson().fromJson(obj.toString(), PotChallengeMonsterObject.class);
                    mObject.put(objId, challengeMonster);
                }
                case OBJECT_SWITCH -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotSwitchObject.class));
                case OBJECT_TORCH -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotTorchObject.class));
                case OBJECT_NPC_GIFT -> mObject.put(objId, new Gson().fromJson(obj.toString(), PotNPCGiftObject.class));
                case OBJECT_MULTI_EVENT_LISTENER -> {
                    PotMultiEventListenerObject potMultiEventListenerObject = new Gson().fromJson(obj.toString(), PotMultiEventListenerObject.class);
                    mObject.put(objId, potMultiEventListenerObject);
                }
                case OBJECT_SLEEK_GROUND -> {
                    PotSleekGroundObject potSleekGroundObject = new Gson().fromJson(obj.toString(), PotSleekGroundObject.class);
                    mObject.put(objId, potSleekGroundObject);
                }
            }
        }

        return mObject;
    }

    public static String toJson(Map<Integer, PotAbstractObject> objectsMapById) {
        StringBuilder json = new StringBuilder("[");
        int i = 1;
        for (PotAbstractObject obj : objectsMapById.values()) {
            if (obj == null) continue;
            String str = i != objectsMapById.values().size() ? "," : "";
            if (obj.getType().equals(OBJECT_MONSTER)) {
                PotMonsterObject potMonsterObject = (PotMonsterObject) obj;
                json.append(potMonsterObject.toJson()).append(str);
            } else if (obj.getType().equals(OBJECT_CHALLENGE_MONSTER)) {
                PotChallengeMonsterObject potChallengeMonsterObject = (PotChallengeMonsterObject) obj;
                json.append(potChallengeMonsterObject.toJson()).append(str);
            } else if (obj.getType().equals(OBJECT_CHEST_MONSTER)) {
                PotChestMonsterObject potChestMonsterObject = (PotChestMonsterObject) obj;
                json.append(potChestMonsterObject.toJson()).append(str);
            } else json.append(obj.toJson()).append(str);
            i++;
        }
        json.append("]");

        return json.toString();
    }

    public static ResPotMonsterFormation getResPotMonsterFormation(int mapId, int level) {
        return listResPotMonsterFormation.stream()
                .filter(potMonsterFormation -> potMonsterFormation.getMapId() == mapId && potMonsterFormation.getLevel() == level).findFirst().orElse(null);
    }
}
