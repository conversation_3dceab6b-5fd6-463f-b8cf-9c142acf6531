package monster.task;

import org.quartz.*;

import static org.quartz.CronScheduleBuilder.dailyAtHourAndMinute;
import static org.quartz.SimpleScheduleBuilder.simpleSchedule;

public class QuartzUtil {

    private static final String groupName = "grepgame";

    public static JobDetail getJob(Class clazz, String name) {
        return JobBuilder.newJob(clazz)
                .withIdentity(name, groupName)
                .build();
    }

    public static Trigger getTriggerSecond(String name, int... seconds) {
        return TriggerBuilder.newTrigger()
                .withIdentity(name, groupName)
                .startNow().withSchedule(simpleSchedule().withIntervalInSeconds(seconds.length == 0 ? 1 : seconds[0]).repeatForever())
                .build();
    }

    public static Trigger getTriggerMinute(String name, int minute) {
        return TriggerBuilder.newTrigger()
                .withIdentity(name, groupName)
                .startNow().withSchedule(simpleSchedule().withIntervalInMinutes(minute).repeatForever())
                .build();
    }

    private static ScheduleBuilder<SimpleTrigger> getScheduleBuilder() {
        return simpleSchedule().repeatForever().withIntervalInSeconds(1);
    }

    public static Trigger getTriggerDaily(String name, int hour, int minute) {
        return TriggerBuilder.newTrigger()
                .withIdentity(name, groupName)
                .startNow().withSchedule(dailyAtHourAndMinute(hour, minute))
                .build();

    }
}
