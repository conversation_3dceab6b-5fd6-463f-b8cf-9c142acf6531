package monster.task.worker;

import grep.database.DBJPA;
import grep.database.DBJPA;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;

import java.util.List;

@DisallowConcurrentExecution
public class CleanMaterialProcess extends JobCounter implements Job {

    public static void main(String[] args) {
    }

    @Override
    protected void executeJob() {
        getLogger().warn("CleanMaterialProcess");
        List removeList = getListRemove();
        if (removeList != null && !removeList.isEmpty()) {
            for (Object obj : removeList) {
                Integer materialId = (Integer) obj;
                getLogger().warn("CleanMaterialProcess id=" + materialId);
                String sql = "delete from dson.user_material where type_id=1 and material_id=" + materialId;
                DBJPA.rawSQL(sql);
                DBJPA.rawSQL("update dson_main.task_remove_item set task_status=1, date_finish=now() where material_id=" + materialId);
            }
        }
    }

    private List getListRemove() {
        String sql = "select material_id from dson_main.task_remove_item where task_status=0";
        return DBJPA.getList(sql);
    }
}
