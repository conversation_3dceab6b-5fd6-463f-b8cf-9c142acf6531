package monster.task.worker;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import grep.log.slib_Logger;
import jakarta.persistence.EntityManager;
import monster.dao.mapping.UserMailEntity;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.List;
import java.util.stream.Collectors;

@DisallowConcurrentExecution
public class MailCleaner extends JobCounter implements Job {

    @Override
    public void executeJob() {
        int counter = 0;
        try {
            List<UserMailEntity> mails = getDeleteMail();
            while (mails != null && !mails.isEmpty()) {
                counter += mails.size();
                if (counter % 1000 == 0) getLogger().warn("MailCleaner continue=" + counter);
                for (UserMailEntity mail : mails) {
                    logRemove(mail);
                }
                String ids = mails.stream().map(mail -> String.valueOf(mail.getId())).collect(Collectors.joining(","));
                DBJPA.rawSQL("delete from dson.user_mail where id in (" + ids + ")");
                mails = getDeleteMail();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(ex);
        }
        if (counter > 0) getLogger().warn("MailCleaner summary=" + counter);
    }

    private void logRemove(UserMailEntity uMail) {
        LoggerFactory.getLogger("MAIL").info(new Gson().toJson(uMail));
    }

    private List<UserMailEntity> getDeleteMail() {
        try {
            EntityManager session = null;
            try {
                session = DBJPA.getEntityManager();
                return session.createNativeQuery("select * from dson.user_mail where receive=2 limit 50", UserMailEntity.class).getResultList();
            } catch (Exception ex) {
                ex.printStackTrace();
                Logs.error(GUtil.exToString(ex));
            } finally {
                DBJPA.closeSession(session);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static Logger getLogger() {
        return slib_Logger.root();
    }

}
