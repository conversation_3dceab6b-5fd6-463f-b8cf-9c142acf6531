package monster.task.worker;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.dao.mapping.UserMailEntity;
import monster.task.dbcache.MailCreatorCache;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;

import jakarta.persistence.EntityManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.Set;

@DisallowConcurrentExecution
public class MailCreator extends JobCounter implements Job {

    private static final int numberPop = 20;

    @Override
    public void executeJob() {
        int counter = 0, numberUpdate = 0;
        try {
            Set<String> values = getQueue();
            while (values != null && !values.isEmpty()) {
                counter++;
                if (counter % 1000 == 0) getLogger().warn("mailCreator continue=" + counter);
                numberUpdate += values.size();
                if (!dbUpdate(values)) {
                    for (String value : values) {
                        Logs.getMailLogger().error(value);
                    }
                } else {
                    for (String value : values) {
                        Logs.getMailLogger().info(value);
                    }
                }
                values = getQueue();
            }
        } catch (Exception ex) {
            Logs.error("executeJob", ex);
        }
        if (counter > 0)
            getLogger().warn(String.format("mailCreator summary count=%s, update=%s", counter, numberUpdate));
    }

    private Set<String> getQueue() {
        return JCache.getInstance().spop(MailCreatorCache.queueKey, numberPop);
    }

    private boolean dbUpdate(Set<String> values) {
        EntityManager em = null;
        try {
            em = DBJPA.getEntityManager();

            em.getTransaction().begin();

            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
            for (String value : values) {
                UserMailEntity uMail = gson.fromJson(value, UserMailEntity.class);
                em.persist(uMail);
            }

//            Connection connection = (Connection) em.getDelegate();
//
//            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
//            PreparedStatement statement = connection.prepareStatement("insert into user_mail" +
//                    "(user_id, sender_id, title, message, title_key, message_key, bonus, mail_idx, date_created, origin) " +
//                    "values(?,?,?,?,?,?,?,?,?,?)");
//            for (String value : values) {
//                UserMailEntity uMail = gson.fromJson(value, UserMailEntity.class);
//                statement.setInt(1, uMail.getUserId());
//                statement.setInt(2, uMail.getSenderId());
//                statement.setString(3, uMail.getTitle());
//                statement.setString(4, uMail.getMessage());
//                statement.setString(5, uMail.getTitleKey());
//                statement.setString(6, uMail.getMessageKey());
//                statement.setString(7, uMail.getBonus());
//                statement.setString(8, DateTime.getDateyyyyMMdd());
//                statement.setTimestamp(9, new Timestamp(uMail.getDateCreated().getTime()));
//                statement.setString(10, uMail.getOrigin());
//                statement.addBatch();
//            }
//            statement.executeBatch();

            em.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error("dbUpdate", ex);
        } finally {
            DBJPA.closeSession(em);
        }
        return false;
    }

}
