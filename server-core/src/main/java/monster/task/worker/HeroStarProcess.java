package monster.task.worker;

import grep.database.DBJPA;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.task.dbcache.HeroStarCache;
import org.quartz.DisallowConcurrentExecution;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@DisallowConcurrentExecution
public class HeroStarProcess extends JobCounter {

    private static final int numberPop = 20;

    @Override
    public void executeJob() {
        int counter = 0, numberUpdate = 0;
        try {
            Set<String> values = getQueue();
            while (values != null && !values.isEmpty()) {
                counter++;
                if (counter % 1000 == 0) getLogger().warn("heroStarProcess continue=" + counter);
                numberUpdate += values.size();
                if (!dbUpdate(values)) {
                    for (String value : values) {
                        getFailLogger().info(value);
                    }
                }
                values = getQueue();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
        if (counter > 0)
            getLogger().warn(String.format("heroStar summary count=%s, update=%s", counter, numberUpdate));
    }

    private Set<String> getQueue() {
        return JCache.getInstance().spop(HeroStarCache.queueKey, numberPop);
    }

    private boolean dbUpdate(Set<String> values) {
        String sql = "INSERT INTO dson.user_hero_star (user_id, hero_id, star, number) VALUES %s ON DUPLICATE KEY UPDATE number = number+1";
        List<String> sqlData = new ArrayList<>();
        for (String value : values) {
            String[] tmp = value.split("_");
            sqlData.add(String.format("(%s,%s,%s,1)", tmp[0], tmp[1], tmp[2]));
        }
        sql = String.format(sql, sqlData.stream().collect(Collectors.joining(",")));
        if (DBJPA.rawSQL(sql)) {
            return true;
        }
        return false;
    }

}
