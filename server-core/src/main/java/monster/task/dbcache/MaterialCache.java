package monster.task.dbcache;

import grep.database.DBJPA;
import grep.database.DBJPA;
import lombok.NoArgsConstructor;
import monster.cache.redis.IRedis;
import monster.cache.redis.JCache;
import monster.dao.mapping.UserMaterialEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
public class MaterialCache {
    static final boolean useRedisCache = false;
    static final MaterialCache INSTANCE = new MaterialCache();

    public static MaterialCache getInstance() {
        return INSTANCE;
    }

    public static final String key = "user_material", queueKey = "queue_user_material";

    private String getKey(int userId) {
        return String.format("%s:%s", this.key, userId);
    }

    public List<UserMaterialEntity> getMaterials(int userId) {
        if (useRedisCache) {
            String key = getKey(userId);
            Map<String, String> map = JCache.getInstance().hgetAll(key);
            if (map != null && map.isEmpty()) { // redis is ok and no data
                List<UserMaterialEntity> materials = DBJPA.getList("user_material", Arrays.asList("user_id", userId), "", UserMaterialEntity.class);
                for (UserMaterialEntity material : materials) {
                    map.put(material.getKey(), String.valueOf(material.getNumber()));
                }
                JCache.getInstance().hset(key, map, IRedis.EXPIRE_1D * 2);
                return materials;
            } else if (map != null && !map.isEmpty()) {
                JCache.getInstance().hset(key, map, IRedis.EXPIRE_1D * 2);
                List<UserMaterialEntity> materials = new ArrayList<>();
                map.forEach((k, v) -> {
                    String[] tmp = k.split("_");
                    materials.add(new UserMaterialEntity(userId, Integer.parseInt(tmp[0]), Integer.parseInt(tmp[1]), Long.parseLong(v)));
                });
                return materials;
            }
        } else {
            return DBJPA.getList("user_material", Arrays.asList("user_id", userId), "", UserMaterialEntity.class);
        }
        return null;
    }

    public boolean insertValue(UserMaterialEntity uMaterial) {
        if (useRedisCache) {
            JCache.getInstance().hset(getKey(uMaterial.getUserId()), uMaterial.getKey(), String.valueOf(uMaterial.getNumber()));
            return JCache.getInstance().sadd(queueKey, toValue(uMaterial)) >= 0;
        }
        return DBJPA.save(uMaterial);
    }

    public boolean addValue(UserMaterialEntity uMaterial, long value) {
        if (useRedisCache) {
            String key = getKey(uMaterial.getUserId());
            if (JCache.getInstance().hset(key, uMaterial.getKey(), String.valueOf(uMaterial.getNumber() + value)) >= 0) {
                uMaterial.add(value);
                JCache.getInstance().sadd(queueKey, toValue(uMaterial));
                return true;
            }
            return false;
        }
        if (DBJPA.updateNumber("user_material", Arrays.asList("number", value), Arrays.asList("user_id", uMaterial.getUserId(),
                "type_id", uMaterial.getTypeId(), "material_id", uMaterial.getMaterialId()))) {
            uMaterial.add(value);
            return true;
        }
        return false;
    }

    public String toValue(UserMaterialEntity uMaterial) {
        return String.format("%s_%s_%s_%s", uMaterial.getUserId(), uMaterial.getTypeId(), uMaterial.getMaterialId(), uMaterial.getNumber());
    }

}