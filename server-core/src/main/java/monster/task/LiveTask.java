package monster.task;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.*;
import grep.log.Logs;
import monster.config.*;
import monster.dao.mapping.ConfigHeroReleaseEntity;
import monster.dao.mapping.main.*;
import monster.object.BattleTeam;
import monster.service.battle.common.config.BattleEffectType;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.IMath;

import monster.service.monitor.Telegram;
import monster.service.resource.ResBot;
import monster.service.resource.ResHero;

import jakarta.persistence.EntityManager;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class LiveTask {
    private final String NAME_KEY_ABYSS = "res_abyss_quest_name_";
    public static final String TABLE_QUEST = "res_abyss_quest";
    public static final String TABLE_FORMATION = "res_abyss_formation";

    public String doTask(List<String> args) {
        if (args.isEmpty()) return "no taskId";
        String taskId = args.get(0);
        args.remove(0);
        switch (taskId) {
            case "thvd":
                return thvd();
            case "heroStar":
                return reloadHeroStar();
            case "genAbyss":
                return genAbyss();
            case "addBotArenaSwap":
                return addBotArenaSwap();
            case "dropEvent":
                return CfgDropItem.reloadConfig();
            case "test":
                return CfgArenaSwap.getAvailableCluster();
            case "test1":
                return CfgBonus.isX2() + "";
        }

        return "Nothing to do";
    }

    public String reloadHeroStar() {
        String value = DBJPA.getUniqueColumn(CfgServer.DB_MAIN + "config_api", Arrays.asList("k", "grepgame:server_list"), "v");
        if (value != null) {
            try {
                SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                Calendar ca = Calendar.getInstance();
                ca.add(Calendar.MINUTE, 1);
                JsonArray arr = GsonUtil.parseJsonArray(value);
                Map<Integer, Integer> map = new HashMap<>();
                Map<Integer, Integer> realMap = new HashMap<>();
                for (int i = 0; i < arr.size(); i++) {
                    JsonObject obj = arr.get(i).getAsJsonObject();
                    int serverId = obj.get("id").getAsInt();
//                    int maxStar = obj.get("star").getAsInt();
                    map.put(serverId, 15);
                    if (!obj.has("open")) {
                        realMap.put(serverId, 15);
                    } else {
                        Date date = sfd.parse(obj.get("open").getAsString());
                        if (ca.getTime().after(date) && !realMap.containsKey(serverId)) {
                            realMap.put(serverId, getRealMaxStar(date, new Date()));
                            CfgHero.dateOpenServer.put(serverId, DateTime.getStartOfDay(date));
                        }
//                        map.put(serverId, maxStar);
                    }
                }
                { // set value
                    map.forEach((k, v) -> CfgHero.mMaxStar.put(k, v));
                    realMap.forEach((k, v) -> CfgHero.mRealMaxStar.put(k, v));
                    Object[] obj = CfgHero.mMaxStar.keySet().toArray();
                    for (Object o : obj) {
                        if (!map.containsKey(o)) CfgHero.mMaxStar.remove(o);
                    }
                    Object[] obj1 = CfgHero.mRealMaxStar.keySet().toArray();
                    for (Object o : obj1) {
                        if (!realMap.containsKey(o)) CfgHero.mRealMaxStar.remove(o);
                    }
                }
                return new Gson().toJson(CfgHero.mMaxStar);
            } catch (Exception ex) {
                ex.printStackTrace();
                String errMsg = GUtil.exToString(ex);
                Logs.error(errMsg);
                return errMsg;
            }
        }
        return "none";
    }

    public String genAbyss() {
//        Date now = new Date();
//        if (now.after(CfgAbyss.getStartDate()) && now.before(CfgAbyss.getCloseDate())) {
//            return "Success";
//        }
//
//        if (DateTime.getDayDiff(now, CfgAbyss.getNextStartDate()) != 1) {
//            return "Success";
//        }
        //Gen quest
        Map<Integer, ResAbyssQuestTypeEntity> mQuestType = new HashMap<>();
        Map<Integer, ConfigHeroReleaseEntity> mHeroRelease = new HashMap<>();
        List<ConfigHeroReleaseEntity> heroRelease = DBJPA.getList(CfgServer.DB_MAIN + "config_hero_release", ConfigHeroReleaseEntity.class);
        for (ConfigHeroReleaseEntity obj : heroRelease) {
            mHeroRelease.put(obj.getHeroId(), obj);
        }
        {
            List<ResAbyssQuestTypeEntity> aQuestType = DBJPA.getList(CfgServer.DB_MAIN + "res_abyss_quest_type", ResAbyssQuestTypeEntity.class);
            if (aQuestType == null) return "Loi gen quest 1";
            List<ResAbyssQuestEntity> aOldQuest = DBJPA.getList(CfgServer.DB_MAIN + TABLE_QUEST, ResAbyssQuestEntity.class);
            if (aOldQuest == null) return "Loi gen quest 2";
            for (ResAbyssQuestTypeEntity questType : aQuestType) {
                mQuestType.put(questType.getTypeId(), questType);
            }

            int newNumberKey = 1;
            Map<Integer, List<Integer>> mOldIndexOfTypeData = new HashMap<>();
            List<ResAbyssQuestEntity> listOldHeroQuest = new ArrayList<>();
            for (ResAbyssQuestEntity oldQuest : aOldQuest) {
                ResAbyssQuestTypeEntity resAbyssQuestType = mQuestType.get(oldQuest.getTypeId());
                if (!resAbyssQuestType.isSpecial() && resAbyssQuestType.getStyle().contains(CfgAbyss.STYLE_HERO_KEY)) {
                    listOldHeroQuest.add(oldQuest);
                    continue;
                }

                String nameKey = oldQuest.getNameKey();
                newNumberKey = Integer.parseInt(nameKey.substring(NAME_KEY_ABYSS.length())) + 1;
                if (!mOldIndexOfTypeData.containsKey(oldQuest.getTypeId())) {
                    List<Integer> indexOfTypeData = new ArrayList<>(Collections.singletonList(oldQuest.getTypeDataIndex()));
                    mOldIndexOfTypeData.put(oldQuest.getTypeId(), indexOfTypeData);
                } else {
                    List<Integer> oldIndexOfTypeData = mOldIndexOfTypeData.get(oldQuest.getTypeId());
                    oldIndexOfTypeData.add(oldQuest.getTypeDataIndex());
                }
            }
            //Clear listOldHeroQuest
            {
                List<Integer> listOldQuestId = listOldHeroQuest.stream().map(ResAbyssQuestEntity::getQuestId).collect(Collectors.toList());
                if (!listOldQuestId.isEmpty()) {
                    EntityManager session = null;
                    try {
                        session = DBJPA.getEntityManager();
                        session.getTransaction().begin();
                        String sql = "delete from dson_main." + TABLE_QUEST + " where quest_id in " + StringHelper.toDBList(listOldQuestId);
                        session.createNativeQuery(sql).executeUpdate();
                        session.getTransaction().commit();
                    } catch (Exception ex) {
                        Logs.error(GUtil.exToString(ex));
                    } finally {
                        DBJPA.closeSession(session);
                    }
                }
            }

            List<ResHeroEntity> listResHero = DBJPA.getQueryList("select * from dson_main.res_hero where `rank` = 5", ResHeroEntity.class);
            List<Integer> listHeroKeyRelease = listResHero.stream().filter(resHero -> {
                ConfigHeroReleaseEntity configHeroRelease = mHeroRelease.get(resHero.getId());
                return configHeroRelease != null && configHeroRelease.isRelease();
            }).map(ResHeroEntity::getId).collect(Collectors.toList());
            if (listHeroKeyRelease.isEmpty()) {
                Telegram.sendNotify("Tự động gen vực thẳm thất bại, không có hero release");
                return "Tự động gen vực thẳm thất bại, không có hero release";
            }
            List<ResAbyssQuestEntity> aNewResQuest = new ArrayList<>();
            List<ResAbyssQuestEntity> aNewResHeroQuest = new ArrayList<>();
            for (ResAbyssQuestTypeEntity questType : aQuestType) {
                if (questType.isSpecial() && getCountQuest(aOldQuest, questType.getTypeId()) == 0) {
                    String newNameKey = NAME_KEY_ABYSS + newNumberKey;
                    aNewResQuest.add(new ResAbyssQuestEntity(questType.getTypeId(), newNameKey, questType.getTitle(), questType.getListMechanic(), questType.getListDifficult(), questType.getLevel(), questType.getMainId(), "[]"));
                    newNumberKey++;
                    continue;
                }

                if (questType.isSpecial()) continue;

                if (questType.getStyle().contains(CfgAbyss.STYLE_HERO_KEY)) {
                    int numberHero = questType.getTypeData().get(0).get(0);
                    int numberQuest = questType.getTypeData().get(0).get(1);

                    for (int i = 0; i < numberQuest; i++) {
                        String title = questType.getTitle();
                        List<Integer> listRandomHeroKey = LogicUtil.getRandomDistinct(listHeroKeyRelease, numberHero);
                        if (listRandomHeroKey.isEmpty()) {
                            Telegram.sendNotify("Tự động gen vực thẳm thất bại, không đủ " + numberHero + " hero release cho quest type id = " + questType.getTypeId());
                            return "Tự động gen vực thẳm thất bại, không đủ " + numberHero + " hero release cho quest type id = " + questType.getTypeId();
                        }
                        String questData = StringHelper.toDBString(listRandomHeroKey);
                        aNewResHeroQuest.add(new ResAbyssQuestEntity(questType.getTypeId(), "", title, i, questType.getListMechanic(), questType.getListDifficult(), questType.getLevel(), questType.getMainId(), questData));
                    }
                } else {
                    List<Integer> oldIndexOfTypeData = mOldIndexOfTypeData.get(questType.getTypeId());
                    List<List<Integer>> typeData = questType.getTypeData();
                    for (int i = 0; i < typeData.size(); i++) {
                        String newNameKey = NAME_KEY_ABYSS + newNumberKey;
                        if (oldIndexOfTypeData != null && oldIndexOfTypeData.contains(i))
                            continue; // ở index này đã gen rồi thì bỏ qua
//                System.out.println("questType: " + StringHelper.toDBString(questType));
                        String title = getTitleForQuest(questType, i);
                        aNewResQuest.add(new ResAbyssQuestEntity(questType.getTypeId(), newNameKey, title, i, questType.getListMechanic(), questType.getListDifficult(), questType.getLevel(), questType.getMainId(), StringHelper.toDBString(questType.getTypeData().get(i))));
                        newNumberKey++;
                    }
                }

            }


            dbSaveNewHeroQuest(aNewResHeroQuest);
            dbSaveNewQuestAndReloadConfig(aNewResQuest);
        }
        //Clear formation
        {
            EntityManager session = null;
            try {
                session = DBJPA.getEntityManager();
                session.getTransaction().begin();
                String sql = "delete from dson_main." + TABLE_FORMATION;
                session.createNativeQuery(sql).executeUpdate();
                session.getTransaction().commit();
            } catch (Exception ex) {
                Logs.error(GUtil.exToString(ex));
            } finally {
                DBJPA.closeSession(session);
            }
        }
        //Gen formation
        {
            String value = DBJPA.getUniqueColumn(CfgServer.DB_MAIN + "config_api", Arrays.asList("k", "server_list"), "v");
            if (value != null) {
                Map<Integer, ResAbyssMechanicEntity> resAbyssMechanicMapById = new HashMap<>();
                List<ResAbyssMechanicEntity> listAbyssMechanic = DBJPA.getList(CfgServer.DB_MAIN + "res_abyss_mechanic", ResAbyssMechanicEntity.class);
                if (listAbyssMechanic == null) return "Loi gen quest 1";
                listAbyssMechanic.forEach(resAbyssMechanic -> resAbyssMechanicMapById.put(resAbyssMechanic.getId(), resAbyssMechanic));

                List<ResAbyssHeroTypeEntity> listResHeroType = DBJPA.getList(CfgServer.DB_MAIN + "res_abyss_hero_type", ResAbyssHeroTypeEntity.class);
                if (listResHeroType == null || listResHeroType.isEmpty()) throw new NullPointerException();
                Map<String, List<ResAbyssHeroTypeEntity>> listResAbyssHeroTypeMapByType = new HashMap<>();
                for (ResAbyssHeroTypeEntity resHeroType : listResHeroType) {
                    resHeroType.getListType().forEach(type -> {
                        if (!listResAbyssHeroTypeMapByType.containsKey(type))
                            listResAbyssHeroTypeMapByType.put(type, new ArrayList<>());
                        listResAbyssHeroTypeMapByType.get(type).add(resHeroType);
                    });
                }

                List<ResAbyssQuestEntity> aResAbyssQuest = DBJPA.getList(CfgServer.DB_MAIN + TABLE_QUEST, ResAbyssQuestEntity.class);
                if (aResAbyssQuest == null || aResAbyssQuest.isEmpty()) throw new NullPointerException();
                Map<Integer, List<ResAbyssQuestEntity>> listResAbyssQuestMapByLevel = new HashMap<>();
                for (ResAbyssQuestEntity resAbyssQuest : aResAbyssQuest) {
                    if (!listResAbyssQuestMapByLevel.containsKey(resAbyssQuest.getLevel()))
                        listResAbyssQuestMapByLevel.put(resAbyssQuest.getLevel(), new ArrayList<>());
                    listResAbyssQuestMapByLevel.get(resAbyssQuest.getLevel()).add(resAbyssQuest);
                }

                ConfigEntity cfg = (ConfigEntity) DBJPA.getUnique("dson_main.config", ConfigEntity.class, "k", "config_abyss");
                CfgAbyss.DataConfig config = new Gson().fromJson(cfg.getV(), CfgAbyss.DataConfig.class);

                JsonArray arr = GsonUtil.parseJsonArray(value);
                for (int i = 0; i < arr.size(); i++) {
                    JsonObject obj = arr.get(i).getAsJsonObject();
                    int serverId = obj.get("id").getAsInt();
//                    int dayOpenServer = Guice.getInstance(SystemService.class).getNumberDayServerOpen(serverId);
                    int dayOpenServer = 1;
                    List<ResAbyssFormationEntity> listAbyssFormation = new ArrayList<>();
                    for (int positionIndex = 0; positionIndex < config.getNumberPosition(); positionIndex++) {
                        for (int difficult = 1; difficult <= 3; difficult++) {
                            CfgAbyss.DifficultData difficultData = config.getDifficultData(difficult);
                            List<Integer> listMechanic = difficultData.mechanic.get(positionIndex);
                            Collections.shuffle(listMechanic);
                            ResAbyssFormationEntity abyssFormation = null;
                            for (int mechanicId : listMechanic) {
                                List<Integer> listQuestId = new ArrayList<>();
                                for (int questLevel = 1; questLevel <= 3; questLevel++) {
                                    List<ResAbyssQuestEntity> listResAbyssQuest = new ArrayList<>();
                                    for (ResAbyssQuestEntity resAbyssQuest : listResAbyssQuestMapByLevel.get(questLevel)) {
                                        boolean isContained = resAbyssQuest.getListDifficult().contains(difficult);
                                        ResAbyssQuestTypeEntity resAbyssQuestType = mQuestType.get(resAbyssQuest.getTypeId());
                                        boolean isHeroNotRelease = false;
                                        if (!resAbyssQuestType.isSpecial() && resAbyssQuestType.getStyle().contains(CfgAbyss.STYLE_HERO_KEY)) {
                                            for (int conditionIndex = 0; conditionIndex < resAbyssQuestType.getStyle().size(); conditionIndex++) {
                                                int style = resAbyssQuestType.getStyle().get(conditionIndex);
                                                if (style != CfgAbyss.STYLE_HERO_KEY) continue;

                                                int heroKey = resAbyssQuest.getDataQuest().get(conditionIndex);
                                                ConfigHeroReleaseEntity configHeroRelease = mHeroRelease.get(heroKey);
//                                                boolean isRelease = configHeroRelease != null && configHeroRelease.isRelease() && dayOpenServer >= configHeroRelease.getDayOpenServer();
                                                boolean isRelease = configHeroRelease != null && configHeroRelease.isRelease();
                                                if (!isRelease) isHeroNotRelease = true;
                                            }
                                        }
                                        if (isContained && !isHeroNotRelease) {
                                            listResAbyssQuest.add(resAbyssQuest);
                                        }
                                    }
                                    if (listResAbyssQuest.isEmpty()) {
                                        Telegram.sendNotify("Tự động gen vực thẳm thất bại, không có nhiệm vụ độ khó " + difficult + ", level " + questLevel);
                                        return "Tự động gen vực thẳm thất bại, không có nhiệm vụ độ khó " + difficult + ", level " + questLevel;
                                    }
                                    listQuestId.add(LogicUtil.getRandom(listResAbyssQuest).getQuestId());
                                }

                                int totalGrownPoint = 0;
                                int minGrownPoint = difficultData.getMinGrownPoint().get(positionIndex), maxGrownPoint = difficultData.getMaxGrownPoint().get(positionIndex);
                                List<ResAbyssHeroTypeEntity> listResAbyssHeroType = new ArrayList<>();
                                for (int loopCount = 0; loopCount < 10000; loopCount++) {
                                    listResAbyssHeroType.clear();
                                    ResAbyssHeroTypeEntity heroTypeOnSlot1 = null, heroTypeOnSlot2 = null, heroTypeOnSlot3 = null, heroTypeOnSlot4 = null, heroTypeOnSlot5 = null, heroTypeOnSlot6 = null;
                                    ResAbyssMechanicEntity resAbyssMechanic = resAbyssMechanicMapById.get(mechanicId);

                                    heroTypeOnSlot1 = LogicUtil.getRandom(listResAbyssHeroTypeMapByType.get(resAbyssMechanic.getSlot1()));
                                    if (heroTypeOnSlot1 == null) {
                                        continue;
                                    }
                                    listResAbyssHeroType.add(heroTypeOnSlot1);
                                    int loopCount1 = 0;
                                    do {
                                        loopCount1++;
                                        heroTypeOnSlot2 = LogicUtil.getRandom(listResAbyssHeroTypeMapByType.get(resAbyssMechanic.getSlot2()));
                                        if (heroTypeOnSlot2 == null) break;

                                        boolean isContainHeroKey = false;
                                        for (ResAbyssHeroTypeEntity resAbyssHeroType : listResAbyssHeroType) {
                                            if (resAbyssHeroType.getHeroKey() == heroTypeOnSlot2.getHeroKey()) {
                                                isContainHeroKey = true;
                                                break;
                                            }
                                        }
                                        if (!isContainHeroKey) {
                                            break;
                                        } else {
                                            heroTypeOnSlot2 = null;
                                        }
                                    } while (loopCount1 < 100000);

                                    if (heroTypeOnSlot2 == null) {
                                        continue;
                                    }
                                    listResAbyssHeroType.add(heroTypeOnSlot2);

                                    loopCount1 = 0;
                                    do {
                                        loopCount1++;
                                        heroTypeOnSlot3 = LogicUtil.getRandom(listResAbyssHeroTypeMapByType.get(resAbyssMechanic.getSlot3()));

                                        if (heroTypeOnSlot3 == null) break;
                                        boolean isContainHeroKey = false;
                                        for (ResAbyssHeroTypeEntity resAbyssHeroType : listResAbyssHeroType) {
                                            if (resAbyssHeroType.getHeroKey() == heroTypeOnSlot3.getHeroKey()) {
                                                isContainHeroKey = true;
                                                break;
                                            }
                                        }
                                        if (!isContainHeroKey) {
                                            break;
                                        } else {
                                            heroTypeOnSlot3 = null;
                                        }
                                    } while (loopCount1 < 100000);
                                    if (heroTypeOnSlot3 == null) continue;
                                    listResAbyssHeroType.add(heroTypeOnSlot3);

                                    loopCount1 = 0;
                                    do {
                                        loopCount1++;
                                        heroTypeOnSlot4 = LogicUtil.getRandom(listResAbyssHeroTypeMapByType.get(resAbyssMechanic.getSlot4()));

                                        if (heroTypeOnSlot4 == null) break;
                                        boolean isContainHeroKey = false;
                                        for (ResAbyssHeroTypeEntity resAbyssHeroType : listResAbyssHeroType) {
                                            if (resAbyssHeroType.getHeroKey() == heroTypeOnSlot4.getHeroKey()) {
                                                isContainHeroKey = true;
                                                break;
                                            }
                                        }
                                        if (!isContainHeroKey) {
                                            break;
                                        } else {
                                            heroTypeOnSlot4 = null;
                                        }
                                    } while (loopCount1 < 100000);

                                    if (heroTypeOnSlot4 == null) continue;
                                    listResAbyssHeroType.add(heroTypeOnSlot4);

                                    loopCount1 = 0;
                                    do {
                                        loopCount1++;
                                        heroTypeOnSlot5 = LogicUtil.getRandom(listResAbyssHeroTypeMapByType.get(resAbyssMechanic.getSlot5()));

                                        if (heroTypeOnSlot5 == null) break;
                                        boolean isContainHeroKey = false;
                                        for (ResAbyssHeroTypeEntity resAbyssHeroType : listResAbyssHeroType) {
                                            if (resAbyssHeroType.getHeroKey() == heroTypeOnSlot5.getHeroKey()) {
                                                isContainHeroKey = true;
                                                break;
                                            }
                                        }
                                        if (!isContainHeroKey) {
                                            break;
                                        } else {
                                            heroTypeOnSlot5 = null;
                                        }
                                    } while (loopCount1 < 100000);
                                    if (heroTypeOnSlot5 == null) continue;
                                    listResAbyssHeroType.add(heroTypeOnSlot5);

                                    loopCount1 = 0;
                                    do {
                                        loopCount1++;
                                        heroTypeOnSlot6 = LogicUtil.getRandom(listResAbyssHeroTypeMapByType.get(resAbyssMechanic.getSlot6()));

                                        if (heroTypeOnSlot6 == null) break;
                                        boolean isContainHeroKey = false;
                                        for (ResAbyssHeroTypeEntity resAbyssHeroType : listResAbyssHeroType) {
                                            if (resAbyssHeroType.getHeroKey() == heroTypeOnSlot6.getHeroKey()) {
                                                isContainHeroKey = true;
                                                break;
                                            }
                                        }
                                        if (!isContainHeroKey) {
                                            break;
                                        } else {
                                            heroTypeOnSlot6 = null;
                                        }
                                    } while (loopCount1 < 100000);
                                    if (heroTypeOnSlot6 == null) continue;
                                    listResAbyssHeroType.add(heroTypeOnSlot6);

                                    totalGrownPoint = heroTypeOnSlot1.getGrownPoint() +
                                            heroTypeOnSlot2.getGrownPoint() +
                                            heroTypeOnSlot3.getGrownPoint() +
                                            heroTypeOnSlot4.getGrownPoint() +
                                            heroTypeOnSlot5.getGrownPoint() +
                                            heroTypeOnSlot6.getGrownPoint();
                                    if (loopCount >= 9999) {
                                        System.out.println("totalGrownPoint: " + totalGrownPoint);
                                        System.out.println("minGrownPoint: " + minGrownPoint);
                                        System.out.println("maxGrownPoint: " + maxGrownPoint);
                                    }

                                    if (totalGrownPoint >= minGrownPoint && totalGrownPoint <= maxGrownPoint) {
                                        abyssFormation = ResAbyssFormationEntity.builder()
                                                .positionIndex(positionIndex)
                                                .difficult(difficult)
                                                .server(serverId)
                                                .mechanicId(mechanicId)
                                                .slot1(listResAbyssHeroType.get(0).getId())
                                                .slot2(listResAbyssHeroType.get(1).getId())
                                                .slot3(listResAbyssHeroType.get(2).getId())
                                                .slot4(listResAbyssHeroType.get(3).getId())
                                                .slot5(listResAbyssHeroType.get(4).getId())
                                                .slot6(listResAbyssHeroType.get(5).getId())
                                                .totalGrownPoint(totalGrownPoint)
                                                .quest(StringHelper.toDBString(listQuestId))
                                                .build();
                                        break;
                                    }
                                }

                                if (abyssFormation != null) {
                                    listAbyssFormation.add(abyssFormation);
                                    break;
                                }
                            }
                            if (abyssFormation == null) {
                                return String.format("Khong tim thay doi hinh cho position: %s, difficult: %s, server: %s", positionIndex, difficult, serverId);
                            }
                        }
                    }

                    dbSaveNewAbyssFormation(listAbyssFormation);
                }
            }
        }
        Telegram.sendNotify("Tự động gen vực thẳm thàng công");
        return "Success";
    }

    public static String addBotArenaSwap() {
        String value = DBJPA.getUniqueColumn(CfgServer.DB_MAIN + "config_api", Arrays.asList("k", "grepgame:server_list"), "v");
        if (value == null) return "none";

        int eventId = CfgArenaSwap.getEventId();
        if (eventId % 2 == 0) return "none";

        JsonArray arr = GsonUtil.parseJsonArray(value);
        for (int i = 0; i < arr.size(); i++) {
            JsonObject obj = arr.get(i).getAsJsonObject();
            int serverId = obj.get("id").getAsInt();
            StringBuffer buffer = new StringBuffer();
            int userIdForBot = -serverId * ResBot.mBotSwap.size();
            int cluster = CfgArenaSwap.getCluster(serverId);

            for (ResBotSwapEntity resBotSwap : ResBot.mBotSwap.values()) {
                HeroInfoEntity[] botHeroInfo = ResBot.getHeroInfoForBotSwap(resBotSwap);
                long power = Stream.of(botHeroInfo).filter(Objects::nonNull).mapToLong(hero -> IMath.getPower(hero.point)).sum();
                BattleTeam botTeam = BattleTeam.builder().aHero(botHeroInfo).aPet(null).build();
                buffer.append("," + String.format("(%s, %s, %s, %s, %s, '%s', %s, %s)",
                        userIdForBot, eventId, serverId, resBotSwap.getRank(), cluster, StringHelper.toDBString(botTeam), power, resBotSwap.getId()));
                userIdForBot--;

            }
            if (buffer.length() == 0) return "none";

            String sqlInsert = "insert into dson.user_arena_swap (user_id,event_id,server_id,user_rank,cluster,def_team,power,res_bot_id) values "
                    + buffer.substring(1);

            DBJPA.rawSQL(sqlInsert);

        }

        return "none";
    }

    private String thvd() {
        ConfigEntity config = (ConfigEntity) DBJPA.getUnique("dson_main.config_api", ConfigEntity.class, "k", "server_list");

        JsonArray arr = GsonUtil.parseJsonArray(config.getV());
        List<Integer> aServer = new ArrayList<>();
        for (int i = 0; i < arr.size(); i++) {
            aServer.add(arr.get(i).getAsJsonObject().get("id").getAsInt());
        }

        Collections.sort(aServer);
        List<List<Integer>> aCluster = new ArrayList<>();
        aCluster.add(Arrays.asList(1, 2, 3));
        aCluster.add(new ArrayList<>());
        for (int i = 0; i < aServer.size(); i++) {
            List<Integer> cluster = aCluster.get(aCluster.size() - 1);
            if (cluster.size() >= 8) {
                aCluster.add(new ArrayList<>());
                cluster = aCluster.get(aCluster.size() - 1);
            }
            cluster.add(aServer.get(i));
        }
        for (int i = aCluster.size() - 1; i >= 1; i--) {
            if (aCluster.get(i).size() < 4) aCluster.remove(i);
        }

        ConfigEntity swapConfig = (ConfigEntity) DBJPA.getUnique("dson_main.config", ConfigEntity.class, "k", "config_arenaSwap");
        JsonObject arrSwap = GsonUtil.parseJsonObject(swapConfig.getV());
        arrSwap.add("cluster", GsonUtil.parseJsonArray(aCluster.toString()));

        DBJPA.update("dson_main.config", Arrays.asList("v", arrSwap.toString()), Arrays.asList("k", "config_arenaSwap"));

        Map<Integer, Integer> mCluster = new HashMap<>();
        for (int i = 0; i < aCluster.size(); i++) {
            for (Integer value : aCluster.get(i)) {
                mCluster.put(value, i + 1);
            }
        }
        CfgArenaSwap.mCluster = mCluster;
        Logs.warn(aCluster.toString());
        return aCluster.toString();
    }

    private int getRealMaxStar(Date openDate, Date curDate) {
        for (int i = CfgHero.config.openStar.dayOpened.size() - 1; i >= 0; i--) {
            if (DateTime.getDayDiff(openDate, curDate) >= CfgHero.config.openStar.dayOpened.get(i))
                return CfgHero.config.openStar.star.get(i);
        }
        return CfgHero.config.openStar.star.get(0);
    }

    private int getCountQuest(List<ResAbyssQuestEntity> aOldQuest, int questTypeId) {
        int count = 0;
        for (ResAbyssQuestEntity oldQuest : aOldQuest) {
            if (oldQuest.getTypeId() == questTypeId) count++;
        }

        return count;
    }

    private String getTitleForQuest(ResAbyssQuestTypeEntity questType, int index) {
        List<List<Integer>> typeData = questType.getTypeData();
        List<Integer> singleData = typeData.get(index);
        List<Integer> aStyle = questType.getStyle();
        List<String> aString = new ArrayList<>();

        for (int i = 0; i < aStyle.size(); i++) {
            switch (aStyle.get(i)) {
                case CfgAbyss.STYLE_NUMBER:
                    aString.add(singleData.get(i).toString());
                    break;
                case CfgAbyss.STYLE_FACTION:
                    aString.add(HeroType.getFaction(singleData.get(i)).nameVi);
                    break;
                case CfgAbyss.STYLE_CLASS:
                    aString.add(HeroType.getClass(singleData.get(i)).nameVi);
                    break;
                case CfgAbyss.STYLE_HERO_KEY:
                    ResHeroEntity resHero = ResHero.getHero(singleData.get(i));
                    aString.add(resHero.getName());
                    break;
                case CfgAbyss.STYLE_EFFECT:
                    BattleEffectType battleEffectType = BattleEffectType.get(singleData.get(i));
                    aString.add(battleEffectType.name);
                    break;
                default:
                    aString.add("NA");
                    break;
            }
        }

        return String.format(questType.getTitle(), aString.toArray());
    }

    private boolean dbSaveNewHeroQuest(List<ResAbyssQuestEntity> aNewResHeroQuest) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            if (!aNewResHeroQuest.isEmpty()) {
                String sql = "insert into dson_main." + TABLE_QUEST + " (type_id,name_key,title,type_data_index,list_mechanic,level,list_difficult,main_id,quest_data) values ";
                for (int i = 0; i < aNewResHeroQuest.size(); i++) {
                    ResAbyssQuestEntity newResQuest = aNewResHeroQuest.get(i);
                    if (i == 0) {
                        sql += "(" + newResQuest.getTypeId() + ",'" + newResQuest.getNameKey() + "','" + newResQuest.getTitle() + "'," + newResQuest.getTypeDataIndex() +
                                ",'" + StringHelper.toDBString(newResQuest.getListMechanic()) + "'," + newResQuest.getLevel() + ",'" + StringHelper.toDBString(newResQuest.getListDifficult()) + "', " + newResQuest.getMainId() + ", '" + newResQuest.getQuestData() + "') ";
                        continue;
                    }
                    sql += ", (" + newResQuest.getTypeId() + ",'" + newResQuest.getNameKey() + "','" + newResQuest.getTitle() + "'," + newResQuest.getTypeDataIndex() +
                            ",'" + StringHelper.toDBString(newResQuest.getListMechanic()) + "'," + newResQuest.getLevel() + ",'" + StringHelper.toDBString(newResQuest.getListDifficult()) + "', " + newResQuest.getMainId() + ", '" + newResQuest.getQuestData() + "') ";
                }

                session.createNativeQuery(sql).executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
            return false;
        } finally {
            DBJPA.closeSession(session);
        }
    }

    private boolean dbSaveNewQuestAndReloadConfig(List<ResAbyssQuestEntity> aNewResQuest) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            if (!aNewResQuest.isEmpty()) {
                String sql = "insert into dson_main." + TABLE_QUEST + " (type_id,name_key,title,type_data_index,list_mechanic,level,list_difficult,main_id,quest_data) values ";
                String sql2 = "insert into dson_main.config_res_language (k,vi) values ";
                for (int i = 0; i < aNewResQuest.size(); i++) {
                    ResAbyssQuestEntity newResQuest = aNewResQuest.get(i);
                    if (i == 0) {
                        sql += "(" + newResQuest.getTypeId() + ",'" + newResQuest.getNameKey() + "','" + newResQuest.getTitle() + "'," + newResQuest.getTypeDataIndex() +
                                ",'" + StringHelper.toDBString(newResQuest.getListMechanic()) + "'," + newResQuest.getLevel() + ",'" + StringHelper.toDBString(newResQuest.getListDifficult()) + "', " + newResQuest.getMainId() + ", '" + newResQuest.getQuestData() + "') ";
                        sql2 += "('" + newResQuest.getNameKey() + "','" + newResQuest.getTitle() + "') ";
                        continue;
                    }
                    sql += ", (" + newResQuest.getTypeId() + ",'" + newResQuest.getNameKey() + "','" + newResQuest.getTitle() + "'," + newResQuest.getTypeDataIndex() +
                            ",'" + StringHelper.toDBString(newResQuest.getListMechanic()) + "'," + newResQuest.getLevel() + ",'" + StringHelper.toDBString(newResQuest.getListDifficult()) + "', " + newResQuest.getMainId() + ", '" + newResQuest.getQuestData() + "') ";
                    sql2 += ", ('" + newResQuest.getNameKey() + "','" + newResQuest.getTitle() + "') ";
                }

                session.createNativeQuery(sql).executeUpdate();
                session.createNativeQuery(sql2).executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
            return false;
        } finally {
            DBJPA.closeSession(session);
        }
    }

    private boolean dbSaveNewAbyssFormation(List<ResAbyssFormationEntity> listResAbyssFormation) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            if (!listResAbyssFormation.isEmpty()) {
                String sql = "insert into dson_main." + TABLE_FORMATION + " (position_index,difficult,server,mechanic_id,slot1,slot2,slot3,slot4,slot5,slot6,total_grown_point,quest) values ";
                for (int i = 0; i < listResAbyssFormation.size(); i++) {
                    ResAbyssFormationEntity resAbyssFormation = listResAbyssFormation.get(i);
                    if (i == 0) {
                        sql += "(" + resAbyssFormation.getPositionIndex() +
                                "," + resAbyssFormation.getDifficult() +
                                "," + resAbyssFormation.getServer() +
                                "," + resAbyssFormation.getMechanicId() +
                                "," + resAbyssFormation.getSlot1() +
                                "," + resAbyssFormation.getSlot2() +
                                "," + resAbyssFormation.getSlot3() +
                                "," + resAbyssFormation.getSlot4() +
                                "," + resAbyssFormation.getSlot5() +
                                "," + resAbyssFormation.getSlot6() +
                                "," + resAbyssFormation.getTotalGrownPoint() +
                                ",'" + StringHelper.toDBString(resAbyssFormation.getListQuestId()) + "'" +
                                ") ";
                        continue;
                    }
                    sql += ", (" + resAbyssFormation.getPositionIndex() +
                            "," + resAbyssFormation.getDifficult() +
                            "," + resAbyssFormation.getServer() +
                            "," + resAbyssFormation.getMechanicId() +
                            "," + resAbyssFormation.getSlot1() +
                            "," + resAbyssFormation.getSlot2() +
                            "," + resAbyssFormation.getSlot3() +
                            "," + resAbyssFormation.getSlot4() +
                            "," + resAbyssFormation.getSlot5() +
                            "," + resAbyssFormation.getSlot6() +
                            "," + resAbyssFormation.getTotalGrownPoint() +
                            ",'" + StringHelper.toDBString(resAbyssFormation.getListQuestId()) + "'" +
                            ") ";
                }

                session.createNativeQuery(sql).executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
            return false;
        } finally {
            DBJPA.closeSession(session);
        }
    }
}
