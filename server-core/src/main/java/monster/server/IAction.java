package monster.server;

import java.util.Arrays;
import java.util.List;

public class IAction {

    // Special
    public static final int ADMIN_ACTION = 10000;
    public static final int HERO_HANDLER_TEXT = 9000;

    // Common Action 1-100
    public static final int MSG_TOAST = 1;
    public static final int MSG_POPUP = 2;
    public static final int MSG_SLIDE = 3;
    public static final int LIST_ACTION = 4;
    public static final int UPDATE_MONEY = 5;
    public static final int USER_ALL_INFO = 6;
    public static final int SHOPPING_NOTIFY = 7;
    public static final int LOGIN_REQUIRE = 8;
    public static final int LEVEL_BONUS = 9;
    public static final int CLIENT_LOG = 10;
    public static final int PING_PONG = 11;
    public static final int CLIENT_CACHE = 12;
    public static final int CLIENT_GET_CACHE = 13;
    public static final int LOG_DEVICE_INFO = 14;
    public static final int GET_DEFAULT_DEVICE_SETTING = 15;
    public static final int CLIENT_PACK = 16;
    public static final int LIVESTREAM_PLAYER_INO = 89;
    public static final int LIVESTREAM_PLAYER_BATTLE = 90;
    //    public static final int BATTLE_REPLAY_WITH_MONGO = 91;
    public static final int CONFIRM_BONUS_GIFT = 92;
    public static final int SIMULATE_BATTLE = 95;
    public static final int HELP_VALUE = 96;
    public static final int BATTLE_REPLAY = 97;
    public static final int BATTLE_HISTORY_REPLAY = 585;
    public static final int NEW_USER_REPLAY = 596;
    public static final int TEST_BATTLE_REPLAY = 93;
    public static final int TEST_BATTLE = 94;
    public static final int TEST_BATTLE_WITH_RELIC = 88;

    // User Action 100++
    public static final int LOGIN = 100;
    public static final int LOGIN_FAIL = 101;
    public static final int GLOBAL_CHAT = 102;
    public static final int GLOBAL_CHAT_LIST = 103;
    public static final int CLAN_CHAT = 104;
    public static final int CLAN_CHAT_LIST = 105;
    public static final int CHAT_HIDDEN_VIP = 106;
    public static final int CLAN_RECRUIT = 107;
    public static final int CLAN_RECRUIT_LIST = 108;
    public static final int SHARE_BATTLE = 109;
    public static final int LOCAL_NOTIFICATION = 130;
    public static final int SERVER_CHAT = 280;
    public static final int SERVER_CHAT_LIST = 281;

    public static final int UPDATE_NAME = 110;
    public static final int AVATAR_LIST = 111;
    public static final int AVATAR_CHOOSE = 112;
    public static final int HERO_AVATAR_LIST = 1143;
    public static final int HERO_AVATAR_CHOOSE = 1144;
    public static final int USER_CONFIG = 113;
    public static final int USER_NOTIFY = 114;
    public static final int FEED_BACK = 115;
    public static final int UPDATE_BONUS = 116;
    public static final int ONLINE_BONUS_STATUS = 117;
    public static final int ONLINE_BONUS_RECEIVE = 118;
    public static final int USER_TEAM_INFO = 119;
    public static final int USER_UPDATE_NOTIFY = 126;
    public static final int UPDATE_NUMBER_HERO_SLOT = 127;
    public static final int AVATAR_UPDATE = 129;
    public static final int UPDATE_NAME_FREE = 133;
    public static final int AVATAR_FRAME_LIST = 134;
    public static final int AVATAR_FRAME_CHOOSE = 135;
    public static final int AVATAR_FIGURE_CHOOSE = 136;
    public static final int MODE_PLAY = 279;
    public static final int QUESTION_INFO = 275;
    public static final int QUESTION_ANSWER = 276;
    public static final int QUESTION_SELECT_BONUS = 277;

    //
    public static final int TRAINING_STATUS = 274;

    // market
    public static final int MARKET_STATUS = 120;
    public static final int MARKET_BUY = 121;
    public static final int MARKET_REFRESH = 122;
    public static final int MARKET_IAP_VERIFY = 123;
    public static final int TOP_PLAYER = 124;
    public static final int TOP_PLAYER_ME = 125;
    public static final int MARKET_STATUS_DETAIL = 128;
    public static final int SHOP_CRY_STATUS = 131;
    public static final int SHOP_CRY_BUY = 132;

    // Hero Action
    public static final int HERO_LIST = 200;
    public static final int HERO_EQUIP_ITEM = 201;
    public static final int HERO_EQUIP_ARTIFACT = 202;
    public static final int HERO_EQUIP_AUTO = 203;
    public static final int HERO_LEVEL_UP = 204;
    public static final int HERO_LEVEL_UP_NEW = 470;
    public static final int HERO_LEVEL_UP_MAX = 471;
    public static final int HERO_EXCLUSIVE_SKILL_LEVEL_UP = 283;
    public static final int HERO_SKIN_EQUIP = 472;
    public static final int HERO_TIER_UP = 205;
    public static final int HERO_AWAKEN = 206;
    public static final int ENABLE_OPEN_E = 249;
    public static final int HERO_UPDATE_INFO = 207;
    public static final int HERO_REMOVE_ITEM = 208;
    public static final int HERO_ALTAR = 209;
    public static final int HERO_ALTAR_SETTING = 199;
    public static final int HERO_BUY_SLOT = 234;
    public static final int HERO_ALTAR_PREVIEW = 237;
    public static final int HERO_POINT_TIER_UP = 239;
    public static final int HERO_LOCK_UNLOCK = 240;
    public static final int HERO_REPLACE = 241;
    public static final int HERO_DECAY = 242;
    public static final int HERO_CHOOSE_ESKILL = 243;
    public static final int HERO_REPLACE_INFO = 244;
    public static final int REUP_HERO = 245;
    public static final int REUP_HERO_PREVIEW = 246;
    public static final int HERO_UNLOCK_ALL = 247;
    public static final int HERO_LEGEND_UPGRADE = 248;

    // hero treasure
    public static final int HERO_TREASURE_UNLOCK = 231;
    public static final int HERO_TREASURE_UPGRADE = 232;
    public static final int HERO_TREASURE_CONVERT = 235;
    public static final int HERO_TREASURE_INFO = 236;
    public static final int HERO_TREASURE_SAVE_CONVERT = 238;

    // hero special item
    public static final int SPECIAL_ITEM_UNLOCK = 264;
    public static final int SPECIAL_ITEM_ENHANCE = 265;
    public static final int SPECIAL_ITEM_UPGRADE = 266;
    public static final int SPECIAL_ITEM_INFO = 267;
    public static final int SPECIAL_ITEM_REUP = 268;

    // Summon Circle
    public static final int SUMMON_CIRCLE_STATUS = 210;
    public static final int SUMMON_CIRCLE_SUMMON = 211;
    public static final int SUMMON_CIRCLE_ENERGY = 233;
    public static final int SUMMON_GET_BONUS = 814;

    // Prophet Tree
    public static final int PROPHET_TREE_SUMMON_STATUS = 380;
    public static final int PROPHET_TREE_SUMMON_POINT_REWARD = 381;
    public static final int PROPHET_TREE_SUMMON_FACTION_UNLOCK = 382;
    public static final int PROPHET_TREE_SUMMON = 212;
    public static final int PROPHET_REPLACE_HERO = 213;
    public static final int PROPHET_CANCEL_NEW_HERO = 214;
    public static final int PROPHET_REPLACE_STATUS = 215;


    //Event Summon Limit
    public static final int SUMMON_LIMIT_INFO = 292;
    public static final int SUMMON_LIMIT_CHOOSE = 293;
    public static final int SUMMON_LIMIT_SUMMON = 294;
    public static final int EVENT_LIST = 312;
    public static final int EVENT_SUMMON_2_INFO = 313;
    public static final int EVENT_SUMMON_2_CHOOSE = 314;
    public static final int EVENT_SUMMON_2_SUMMON = 315;
    public static final int EVENT_SUMMON_3_INFO = 1160;
    public static final int EVENT_SUMMON_3_CHOOSE = 1161;
    public static final int EVENT_SUMMON_3_SUMMON = 1162;
    public static final int EVENT_SUMMON_3_SUMMON_GEM = 1163;

    // Event top hero
    public static final int HERO_RANKING_TOP_STATUS = 291;
    public static final int HERO_RANKING_TOP = 296;
    public static final int HERO_RANKING_INFO = 297;
    public static final int HERO_RANKING_LOCAL_REWARD = 298;
    public static final int HERO_RANKING_GLOBAL_INFO = 295;
    public static final int HERO_RANKING_GLOBAL_REWARD = 299;

    public static final int EVENT_CANDY_BREAK_INFO = 1012;
    public static final int EVENT_CANDY_BREAK_XEP = 1013;
    public static final int EVENT_CANDY_BREAK_XEP_NHANH = 1014;
    public static final int EVENT_CANDY_BREAK_END_MAP = 1015;


    // Event sushi
    public static final int EVENT_SUSHI_INFO_USER_PARTY = 1005;
    public static final int EVENT_SUSHI_INFO_CLAN_USER = 1006;
    public static final int EVENT_SUSHI_INFO_HISTORY_DEDICATION_SERVER = 1007;
    public static final int EVENT_SUSHI_BONUS_HOT_POT = 1008;
    public static final int EVENT_SUSHI_CONTRIBUTE_FOOD = 1009;
    public static final int EVENT_SUSHI_CONTRIBUTE_EXALTED = 1010;
    public static final int EVENT_SUSHI_LEVEL_REWARD_HOT_POT = 1011;

    // Creation Circle
    public static final int CREATION_CIRCLE = 230;


    // Tower of Oblivion
    public static final int TOWER1_STATUS = 216;
    public static final int TOWER1_ATTACK = 217;
    public static final int TOWER1_TOP = 218;
    public static final int TOWER1_BUY = 257;
    public static final int TOWER1_HISTORY = 258;
    public static final int TOWER1_SWEEP = 290;
    public static final int TOWER1_TEST = 760;
    public static final int TOWER1_ATTACK_TEST = 762;

    // Story
    public static final int STORY_STATUS = 580;
    public static final int STORY_MONSTER_INFO = 582;
    public static final int STORY_ATTACK = 583;
    public static final int STORY_WIN_HISTORY = 584;
    public static final int STORY_DAILY_CHEST = 586;
    public static final int STORY_DAILY_MONSTER_ATTACK = 587;
    public static final int STORY_CHAPTER_BONUS = 588;
    public static final int STORY_MINI_GAME_WIN = 589;

    // Story boss
    public static final int STORY_BOSS_STATUS = 590;
    public static final int STORY_BOSS_TOP = 591;
    public static final int STORY_BOSS_ATTACK = 592;
    public static final int STORY_BOSS_REWARD = 593;
    public static final int STORY_BOSS_TOP3 = 594;
    public static final int STORY_BOSS_DAMAGE_BONUS = 595;

    // Mail
    public static final int MAIL_LIST = 219;
    public static final int MAIL_RECEIVE = 220;
    public static final int MAIL_DELETE = 221;

    // Tavern
    public static final int TAVERN_STATUS = 222;
    public static final int TAVERN_USE_ITEM = 223;
    public static final int TAVERN_REFRESH = 224;
    public static final int TAVERN_SPEED_UP = 225;
    public static final int TAVERN_LOCK_UNLOCK = 226;
    public static final int TAVERN_START = 227;
    public static final int TAVERN_RECEIVE = 228;
    public static final int TAVERN_CANCEL = 229;

    // Item Action
    public static final int ITEM_LIST = 250;
    public static final int MATERIAL_LIST = 251;
    public static final int FORGE_ITEM = 252;
    public static final int FORGE_LIST_ITEM = 278;
    public static final int ARTIFACT_LIST = 253;
    public static final int USED_MATERIAL = 254;
    public static final int UPGRADE_ARTIFACT = 255;
    public static final int SELL_ITEM = 256;
    public static final int SELL_ARTIFACT = 259;
    public static final int SELL_USED_ITEM = 260;
    public static final int ITEM_SPECIAL_LIST = 269;
    public static final int ITEM_MEGABOX_LIST = 286;
    public static final int RESONANCE_ITEM_INFO = 971;
    public static final int FORGE_SET_CLASS_ITEM = 972;
    public static final int FORGE_SET_HERO_ITEM = 973;
    public static final int REPLACE_SET_CLASS_ITEM = 974;
    public static final int REPLACE_SET_HERO_ITEM = 975;
    public static final int REPLACE_ITEM = 976;
    public static final int CONFIRM_REPLACE_ITEM = 977;
    public static final int DISASSEMBLE = 978;

    // Hero Expert Action
    public static final int EXPERT_STATUS = 270;
    public static final int EXPERT_HERO_DETAIL = 271;
    public static final int EXPERT_POINT_DETAIL = 272;
    public static final int EXPERT_RECEIVE = 273;

    // Campaign Action
    public static final int CAMPAIGN_STATUS = 300;
    public static final int CAMPAIGN_EQUIP_TEAM = 301;
    public static final int CAMPAIGN_GET_AWARDS = 302;
    public static final int CAMPAIGN_GET_LOOTS = 303;
    public static final int CAMPAIGN_BATTLE = 304;
    public static final int CAMPAIGN_TOP = 305;
    public static final int CAMPAIGN_SELECT_MAP_AUTO = 306;
    public static final int CAMPAIGN_GET_QUICK_BONUS = 352;
    public static final int CAMPAIGN_BATTLE_TEST = 353;
    public static final int CAMPAIGN_QUICK_BONUS_STATUS = 354;
    public static final int CAMPAIGN_QUICK_BONUS_BUY_PACK = 357;
    public static final int CAMPAIGN_LIST_HISTORY = 1146;
    public static final int CAMPAIGN_SWEEP = 1147;
    public static final int CAMPAIGN_SWEEP_RESULT = 1148;

    // Daily Quest2
    public static final int QUEST2_STATUS = 261;
    public static final int QUEST2_RECEIVE = 262;
    public static final int QUESST2_DONE = 263;

    // Daily Quest
    public static final int WEEKLY_QUEST_STATUS = 850;
    public static final int WEEKLY_QUEST_RECEIVE = 851;
    public static final int QUEST_STATUS = 852;
    public static final int DAILY_QUEST_STATUS = 307;
    public static final int DAILY_QUEST_RECEIVE = 308;
    public static final int CHALLENGE_STATUS = 309;
    public static final int CHALLENGE_RECEIVE = 310;

    // Event Raid
    public static final int EVENTRAID_STATUS = 316;
    public static final int EVENTRAID_BATTLE = 317;
    public static final int EVENTRAID_BUY_TURN = 318;
    public static final int EVENTRAID_BATTLE_WITH_NUMBER = 349;
    public static final int EVENTRAID_NOTIFY = 355;

    // Casino
    public static final int CASINO_STATUS = 319;
    public static final int CASINO_REFRESH = 320;
    public static final int CASINO_ROTATE = 321;
    public static final int CASINO_RECORD = 322;
    public static final int CASINO_BUY_CHIP = 323;
    public static final int CASINO_SUPER_STATUS = 344;
    public static final int CASINO_SUPER_REFRESH = 324;
    public static final int CASINO_SUPER_ROTATE = 325;
    public static final int CASINO_SUPER_RECORD = 326;

    // Arena Crystal // don dau
    public static final int ARENA_TOP_STATUS = 345;
    public static final int ARENA_TOP_DETAIL = 346;
    public static final int ARENA_STATUS = 327;
    public static final int CRYSTAL_STATUS = 328;
    public static final int CRYSTAL_RECORD = 329;
    public static final int CRYSTAL_REVENGE = 330;
    public static final int CRYSTAL_SAVE_DEFENSE_TEAM = 331;
    public static final int CRYSTAL_BUY_TICKET = 332;
    public static final int CRYSTAL_FIND_OPPONENT = 333;
    public static final int CRYSTAL_BATTLE = 334;
    public static final int CRYSTAL_BATTLE_RESULT = 335;
    public static final int CRYSTAL_TOP_USER = 340;
    public static final int CRYSTAL_LAST_SEASON = 347;
    public static final int ARENA_NOTIFY = 356;

    // Arena Trial // lien dau
    public static final int TRIAL_STATUS = 336;
    public static final int TRIAL_RECORD = 337;
    public static final int TRIAL_REVENGE = 338;
    public static final int TRIAL_SAVE_DEFENSE_TEAM = 339;
    public static final int TRIAL_FIND_OPPONENT = 341;
    public static final int TRIAL_BATTLE = 342;
    public static final int TRIAL_TOP_USER = 343;
    public static final int TRIAL_LAST_SEASON = 348;


    // Arena SWAP // thach dau - dai chien lien sv
    public static final int SWAP_STATUS = 550;
    public static final int SWAP_RECORD = 551;
    public static final int SWAP_SAVE_DEFENSE_TEAM = 552;
    public static final int SWAP_FIND_OPPONENT = 553;
    public static final int SWAP_BATTLE = 554;
    public static final int SWAP_TOP_RANK = 555;
    public static final int SWAP_HAS_RECORD = 556;
    public static final int SWAP_LAST_SEASON = 557;
    public static final int SWAP_FINAL = 558;
    public static final int SWAP_FINAL_RESULT = 559;
    public static final int SWAP_BUY_TICKET = 560;
    public static final int SWAP_TOP_POINT = 561;
    public static final int SWAP_BET = 562;
    public static final int SWAP_BET_HISTORY = 563;
    public static final int SWAP_FINAL_NEW = 564;

    // Hand of midas
    public static final int MIDAS_STATUS = 350;
    public static final int MIDAS_RECEIVE = 351;

    // Pet
    public static final int PET_LIST = 360;
    public static final int PET_UNLOCK = 361;
    public static final int PET_LEVEL_UP = 362;
    public static final int PET_EVOLVE = 363; // tien hoa
    public static final int PET_PASSIVE_UP = 364;
    public static final int PET_RESET = 365;
    public static final int PET_RUNE_LEVEL_UP = 366;

    // Badge
    public static final int BADGE_INFO = 370;
    public static final int BADGE_LIST = 371;
    public static final int BADGE_EQUIP = 372;
    public static final int BADGE_UP_STAR = 373;
    // Clan action
    public static final int CLAN_CREATE = 400;
    public static final int CLAN_APPLICATION_LIST = 401;
    public static final int CLAN_REQ = 402;
    public static final int CLAN_ANSWER_REQ = 403;
    public static final int CLAN_MEMBER_LIST = 404;
    public static final int CLAN_INFO = 405;
    public static final int CLAN_KICK_MEMBER = 406;
    public static final int CLAN_LEAVE = 407;
    public static final int CLAN_FINDING = 408;
    public static final int CLAN_TOP = 409;
    public static final int CLAN_SET_POSITION = 410;
    public static final int CLAN_USER_UPDATE_STATE = 427;
    public static final int CLAN_MAIL_TO_MEMBER = 428;
    public static final int CLAN_CHANGE_NAME = 429;

    // Celestial island
    public static final int CELESTIAL_STATUS = 411;
    public static final int CELESTIAL_TOWER_UPGRADE = 412;
    public static final int CELESTIAL_MAKE_CONSTRUCTION = 413;
    public static final int CELESTIAL_DISMANTLE_MINE_FIELD = 414;
    public static final int CELESTIAL_BUILDING_UPGRADE = 415;
    public static final int CELESTIAL_GET_MINE_FIELD = 416;
    public static final int CELESTIAL_ISLAND_STATUS = 417;
    public static final int CELESTIAL_BOSS_STATUS = 418;
    public static final int CELESTIAL_ATTACK = 419;
    public static final int CELESTIAL_AUTO_ATTACK = 420;
    public static final int CELESTIAL_SMASH = 421;
    public static final int CELESTIAL_BUY_DAFFODIL = 422;
    public static final int CELESTIAL_USE_ASSIST = 423;
    public static final int CELESTIAL_TECH_UPGRADE = 424;
    public static final int CELESTIAL_MINE_UPDATE = 425;
    public static final int GET_DAFFODIL = 426;
    // Friend
    public static final int FRIEND_STATUS = 430;
    public static final int FRIEND_LIST = 431;
    public static final int FRIEND_LIST_REQ = 432;
    public static final int FRIEND_RECOMMENDED = 433;
    public static final int FRIEND_APPLY = 434;
    public static final int FRIEND_REP_APPLY = 435;
    public static final int FRIEND_DELETE = 436;
    public static final int FRIEND_SEND_MAIL = 437;
    public static final int FRIEND_FIGHT_FRIEND = 438;
    public static final int FRIEND_SCOUT_STATUS = 439;
    public static final int FRIEND_SEND_FRIENDSHIP = 440;
    public static final int FRIEND_RECEIVED_FRIENDSHIP = 441;
    public static final int FRIEND_HELP_FIGHT_BOSS = 442;
    public static final int FRIEND_FIGHT_BOSS = 443;
    public static final int FRIEND_GET_SCOUT = 444;
    public static final int FRIEND_SEND_AND_RECEIVE = 445;
    public static final int FRIEND_SMASH = 446;
    public static final int SMASH_HELP_FIGHT_BOSS = 447;
    public static final int FRIEND_LIST_NEW = 448;

    // Clan Skill
    public static final int CLAN_SKILL_STATUS = 450;
    public static final int CLAN_SKILL_UPGRADE = 451;
    public static final int CLAN_SKILL_RESET = 452;
    public static final int CLAN_SKILL_RESET_FEE = 467;

    public static final int CLAN_SKILL2_STATUS = 468;
    public static final int CLAN_SKILL2_UPGRADE = 469;

    // Clan Raid
    public static final int CLAN_BOSS_STATUS = 480;
    public static final int CLAN_BOSS_TOP_DAMAGE = 481;
    public static final int CLAN_BOSS_ATTACK = 482;
    public static final int CLAN_BOSS_RECEIVE_RED_BOX = 483;
    public static final int CLAN_BOSS_RECEIVE_YELLOW_BOX = 484;
    public static final int CLAN_RAID_HISTORY = 465;

    // Clan Mill
    public static final int CLAN_MILL_ORDER_STATUS = 456;
    public static final int CLAN_MILL_ORDER_UPGRADE = 457;
    public static final int CLAN_MILL_ORDER_PROCESS = 458;
    public static final int CLAN_MILL_ORDER_GET = 459;
    public static final int CLAN_MILL_STATUS = 460;
    public static final int CLAN_MILL_DONATE = 461;
    public static final int CLAN_MILL_DONATE_RANKING = 462;
    public static final int CLAN_CHECK_IN = 463;
    public static final int CLAN_MILL_ORDER_BONUS = 464;

    // clan
    public static final int CLAN_CHANGE_AVATAR_AND_STATUS = 466;

    // Aspen Dungeon
    public static final int ASPEN_STATUS = 500;
    public static final int ASPEN_REQUIRED_SELECT_HERO = 501;
    public static final int ASPEN_SELECT_HERO = 502;
    public static final int ASPEN_FIRST_BONUS = 503;
    public static final int ASPEN_BATTLE = 504;
    public static final int ASPEN_BUY_IN_BATTLE = 505;
    public static final int ASPEN_MERCHANT_LIST = 506;
    public static final int ASPEN_MERCHANT_BUY = 507;
    public static final int ASPEN_RANK = 508;
    public static final int ASPEN_CLOSED = 509;
    public static final int ASPEN_USED_PORTION = 510;
    public static final int ASPEN_NOTIFY = 664;

    // Pray for fire
    public static final int PRAY_FIRE = 511;
    public static final int PRAY_FIRE_TOP = 512;
    public static final int PRAY_FIRE_BONUS = 513;
    public static final int PRAY_FIRE_BATTLE = 514;
    public static final int PRAY_FIRE_SIGNUP = 515;

    // Event service support Truong
    public static final int BROKEN_SPACE = 600;
    public static final int BROKEN_BUY = 601;
    public static final int BROKEN_SPACE_NEW = 602;

    // Sự kiện quay huy hiệu
    public static final int EVENT_BADGE_STATUS = 603;
    public static final int EVENT_BADGE_SUMMON = 604;

    // Seal land island
    public static final int SEALLAND_STATUS = 650;
    public static final int SEALLAND_SMASH = 651;
    public static final int SEALLAND_BATTLE = 652;
    public static final int SEALLAND_BUY_ATTACK = 653;
    public static final int SEALLAND_REWARD = 654;
    public static final int SEALLAND_NOTIFY = 663;

    // Brave Trial new
    public static final int BRAVETRIAL2_COUNTDOWN = 655;
    public static final int BRAVETRIAL2_STATUS = 656;
    public static final int BRAVETRIAL2_BUY_BADGE = 657;
    public static final int BRAVETRIAL2_ATTACK = 658;
    public static final int BRAVETRIAL2_REVIVE_HERO = 659;
    public static final int BRAVETRIAL2_HEAL_HERO = 660;
    public static final int BRAVETRIAL2_BUFF_TEAM = 661;
    public static final int BRAVETRIAL2_BUY_BOX = 662;

    // Labyrinth
    public static final int LABYRINTH_STATUS = 610;
    public static final int LABYRINTH_ATTACK = 611;
    public static final int LABYRINTH_CHOOSE_SUPPORT = 612;
    public static final int LABYRINTH_CHOOSE_RELIC = 613;
    public static final int LABYRINTH_FOUNTAIN = 614;
    public static final int LABYRINTH_UPSTAIR = 615;
    public static final int LABYRINTH_HERO_RES = 616;
    public static final int LABYRINTH_REVIVE_HERO = 617;
    public static final int LABYRINTH_TEXT = 618;
    public static final int LABYRINTH_SELECT_STAGE = 619;
    public static final int LABYRINTH_MOVE_BONUS = 620;
    public static final int LABYRINTH_MOVE_MARKET = 621;
    public static final int LABYRINTH_BUY_TURN = 622;
    public static final int LABYRINTH_SUPER_STATUS = 623;
    public static final int LABYRINTH_SWEEP_ALL = 624;

    // ranking
    public static final int RANKING_STATUS = 1019;
    public static final int RANKING_INFO = 1020;
    public static final int RANKING_INFO_DETAIL = 1021;
    public static final int RANKING_STATE = 1022;
    public static final int RANKING_RECEIVE = 1023;

    // ************* WarClan ***********
    public static final int WAR_CLAN_STATUS = 2100;
    public static final int WAR_CLAN_REGISTER = 2101;
    public static final int WAR_CLAN_GET_TEAM = 2102;
    public static final int WAR_CLAN_MEMBER = 2103;
    public static final int WAR_CLAN_CHOOSE_MEMBER = 2104;
    public static final int WAR_CLAN_ATTACK = 2105;
    public static final int WAR_CLAN_ACTIVITY = 2106;
    public static final int WAR_CLAN_HISTORY = 2107;
    public static final int WAR_CLAN_BONUS = 2108;

    public static final int ADMIN_CHAT = 700;
    public static final int ADMIN_CHAT_LIST = 701;

    // Album collection
    public static final int ALBUM_STATUS = 710;
    public static final int ALBUM_GET_FRAGMENT_IMAGE = 711;

    //Boss server
    public static final int BOSS_SERVER_STATUS = 720;
    public static final int BOSS_SERVER_STATUS_DETAIL = 724;
    public static final int BOSS_SERVER_TOP = 721;
    public static final int BOSS_SERVER_ATTACK = 722;

    //Dice
    public static final int DICE_STATUS = 725;
    public static final int DICE_GO = 726;
    public static final int DICE_OPEN_CARD = 727;
    public static final int DICE_RECEIVE_REWARD = 728;
    public static final int DICE_VIEW_STAR_REWARD = 729;

    //Interior
    public static final int INTERIOR_LIST_HERO = 731;
    public static final int INTERIOR_SAVE = 737;
    public static final int INTERIOR_HERO_SET = 738;
    public static final int INTERIOR_LIST_ITEM = 739;
    public static final int INTERIOR_CREATE = 740;
    public static final int INTERIOR_RECYCLE = 741;
    public static final int INTERIOR_WISHLIST = 742;
    public static final int INTERIOR_QUEST_STATUS = 743;
    public static final int INTERIOR_QUEST_RECEIVE = 745;
    public static final int INTERIOR_CHOOSE_ITEM = 744;
    public static final int INTERIOR_GALLERY = 746;
    public static final int INTERIOR_AUTO_SET_ITEM = 747;
    public static final int INTERIOR_UNLOCK_BOAT = 748;

    // Hero void
    public static final int VOID_STATUS = 750;
    public static final int VOID_UPGRADE = 751;
    public static final int VOID_UPGRADE_SKILL = 752;
    public static final int VOID_RESET = 753;

    //Event Impel Down
    public static final int EVENT_IMPEL_DOWN_STATUS = 780;
    public static final int EVENT_IMPEL_DOWN_FLOOR_STATUS = 781;
    public static final int EVENT_IMPEL_DOWN_UNLOCK = 782;
    public static final int EVENT_IMPEL_DOWN_GET_REWARD = 787;

    //Fishman Island
    public static final int FISHMAN_ISLAND_STATUS = 790;
    public static final int CANDY_RAIN = 791;
    public static final int MOUSE_MASHING = 792;
    public static final int ZORO_CHALLENGE_FIND_OPP = 793;
    public static final int ZORO_CHALLENGE_FIGHT = 794;

    //Abyss
    public static final int ABYSS_STATUS = 812;
    public static final int ABYSS_BUY_TURN = 813;
    public static final int ABYSS_STATUS_DIFFICULT = 795;
    public static final int ABYSS_STATUS_DETAIL = 798;
    public static final int ABYSS_ATTACK = 796;
    public static final int ABYSS_CHOOSE_REWARD = 797;
    public static final int ABYSS_CLAIM_QUEST = 799;
    public static final int ABYSS_CLAIM_REWARD = 808;

    // Event Sanji
    public static final int EVENT_SANJI_STATUS = 800;
    public static final int EVENT_SANJI_SEND_DISH = 801;
    public static final int EVENT_SANJI_RANK = 802;
    public static final int EVENT_SANJI_ACCUMULATE_STATUS = 803;
    public static final int EVENT_SANJI_ACCUMULATE_RECEIVE = 804;
    public static final int EVENT_SANJI_CHEF = 805;
    public static final int EVENT_SANJI_CHEF_BUY = 806;
    public static final int EVENT_SANJI_BUY_DISH = 807;

    // creation circle ssCREATION_CIRCLE_SS
    public static final int CREATION_CIRCLE_SS_STATUS = 810;
    public static final int CREATION_CIRCLE_SS = 811;

    //
    public static final int HERO_STAR_NOT_AWAKEN_MESSAGE = 816;

    //Peak of time
    public static final int POT_STATUS = 833;
    public static final int POT_STATUS_MAP = 834;
    public static final int POT_STATUS_HERO = 835;
    public static final int POT_INTERACT_UPDATE = 830;
    public static final int POT_ATTACK_MONSTER = 831;
    public static final int POT_OPEN_CHEST = 832;
    public static final int POT_LIST_RELIC = 836;
    public static final int POT_CHOOSE_RELIC = 837;
    public static final int POT_UPLOAD_DATA = 838;
    public static final int POT_HEALING_BEAN = 839;
    public static final int POT_POISON_POTION = 840;
    public static final int POT_NPC = 841;
    public static final int POT_REVIVE = 842;
    public static final int POT_CHOOSE_SUPPORT = 843;
    public static final int POT_ROCK = 844;
    public static final int POT_SWITCH = 845;
    public static final int POT_TORCH = 846;
    public static final int POT_NPC_GIFT = 847;
    public static final int ZEN_MATCH_STATUS = 853;
    public static final int ZEN_MATCH_FINISH = 854;
    public static final int ZEN_MATCH_BUY = 855;
    public static final int ZEN_MATCH_RECEIVE = 856;

    //Boss inter server
    public static final int BOSS_INTER_SERVER_STATUS = 848;
    public static final int BOSS_INTER_SERVER_STATUS_DETAIL = 849;
    public static final int BOSS_INTER_SERVER_TOP = 857;
    public static final int BOSS_INTER_SERVER_ATTACK = 858;
    public static final int BOSS_INTER_SERVER_BUY_TURN = 859;
    public static final int BOSS_FUNCTION_STATUS = 860;

    //Saving dog
    public static final int EVENT_SAVING_DOG_STATUS = 861;
    public static final int EVENT_SAVING_DOG_UPDATE = 862;
    public static final int EVENT_SAVING_DOG_RECEIVE = 863;

    //Mini Game
    public static final int MINI_GAME_STATUS = 864;
    public static final int MINI_GAME_UPDATE = 865;
    public static final int MINI_GAME_RECEIVE = 866;

    // Tower 2
    public static final int TOWER2_STATUS = 870;
    public static final int TOWER2_ATTACK = 871;
    public static final int TOWER2_TOP = 872;
    public static final int TOWER2_BUY = 873;
    public static final int TOWER2_HISTORY = 874;
    public static final int TOWER2_DAILY_BONUS = 875;

    //Sự kiện tướng mới
    //    public static final int NEW_HERO_EVENT_POT_STATUS = 1054;
    //    public static final int NEW_HERO_EVENT_POT_STATUS_MAP = 1055;
    //    public static final int NEW_HERO_EVENT_POT_ATTACK_MONSTER = 1056;
    //    public static final int ATK_VERIFY_NEW_HERO_EVENT = 1057;
    //    public static final int NEW_HERO_EVENT_UPLOAD_DATA = 1058;
    //    public static final int NEW_HERO_EVENT_OPEN_CHEST = 1059;

    // gem handler
    public static final int GEM_LIST = 900;
    public static final int GEM_EQUIP = 901;
    public static final int GEM_AUTO_EQUIP = 902;
    public static final int GEM_UN_EQUIP = 903;
    public static final int GEM_UPDATE_NEW = 904;
    public static final int GEM_SELL = 905;
    public static final int GEM_INFO = 906;

    //Vành đai tĩnh lặng
    public static final int BOSS_SILENT_RING_GET_LIST_HERO_CHECK_IN = 928;
    public static final int BOSS_SILENT_RING_CHECK_IN = 929;
    public static final int BOSS_SILENT_RING_STATUS = 930;
    public static final int BOSS_SILENT_RING_GET_REWARD_LAST_SEASON = 931;
    public static final int BOSS_SILENT_RING_ATTACK = 932;
    public static final int BOSS_SILENT_CHALLENGE_GET_LIST = 933;
    public static final int BOSS_SILENT_CHALLENGE_COLLECT = 934;
    public static final int BOSS_SILENT_GET_RECORDS = 935;
    public static final int BOSS_SILENT_PLAY_RECORD = 936;
    public static final int BOSS_SILENT_GET_DAMAGE_BOSS = 937;
    public static final int BOSS_SILENT_GET_RANK = 938;
    public static final int BOSS_SILENT_GET_DEMON_BLOOD_BY_RANK = 991;

    public static final int GEM_SUMMON = 907;
    public static final int MURIN_CHALLENGE = 908;
    public static final int EVENT_NEW_HERO_BOSS_ATTACK = 909;
    public static final int ACTIVITY_SPECIAL_STATUS = 996;
    public static final int UPDATE_VIP_POINT = 997;
    public static final int EVENT_BOSS_SIMGA = 998;

    public static final int IAP_PACKAGE_BONUS = 999;
    //region Monitor service 10000
    public static final int INDEX = 10000;
    public static final int PING = 10001;
    public static final int RELOAD_CONFIG = 10002;
    public static final int PING_IDLE = 10003;
    public static final int UPDATE_BATTLE_RESULT = 10004;
    public static final int SEND_MSG_ALL = 10005;
    public static final int SEND_MSG_USER = 10006;
    public static final int ADMIN_KICK_PLAYER = 10007;
    public static final int SERVER_STATUS = 10008;
    //endregion

    // Idle service
    public static final int IDLE_STATUS = 1001;
    public static final int IDLE_START = 1002;
    public static final int IDLE_STOP_AND_REWARD = 1003;

    // freedom arena
    public static final int FREEDOM_ARENA_STATUS = 1031;
    public static final int FREEDOM_ARENA_TEAM = 1032;
    public static final int FREEDOM_ARENA_RESET = 1033;
    public static final int FREEDOM_ARENA_CONFIRM_TEAM = 1034;
    public static final int FREEDOM_ARENA_CONFIRM_BUFF = 1035;
    public static final int FREEDOM_ARENA_FIND_ENEMY = 1036;
    public static final int FREEDOM_ARENA_ATTACK = 1037;
    public static final int FREEDOM_ARENA_GET_REWARD = 1038;

    // lucky money
    public static final int LUCKY_MONEY_LIST_USER = 1050;
    public static final int LUCKY_MONEY_GIVE_OUT = 1051;
    public static final int LUCKY_MONEY_LIST_SYSTEM = 1052;
    public static final int LUCKY_MONEY_DETAIL = 1053;
    public static final int LUCKY_MONEY_DIARY = 1054;
    public static final int LUCKY_MONEY_RECEIVE = 1055;

    // Battle Verify -> 2000 + BattleType
    public static final int ATK_VERIFY_MODE_RAID = 5001;
    public static final int ATK_VERIFY_MODE_CAMPAIGN = 5002;
    public static final int ATK_VERIFY_MODE_TOWER_TOWER1 = 5003;
    public static final int ATK_VERIFY_MODE_STORY = 5004;
    public static final int ATK_VERIFY_MODE_ASPEN_DUNGEON = 5005;
    public static final int ATK_VERIFY_MODE_GUILD_BOSS = 5006;
    public static final int ATK_VERIFY_MODE_GUILD_BOSS_FIRE = 5007;
    public static final int ATK_VERIFY_MODE_TOWER2 = 5008;
    public static final int ATK_VERIFY_MODE_BOSS_SERVER = 5009;
    public static final int ATK_VERIFY_MODE_CRYSTALCROWN = 5013;
    public static final int ATK_VERIFY_MODE_TRIALOFCHAMPION = 5014;
    public static final int ATK_VERIFY_MODE_ALLSTARSLEAGUE = 5015;
    public static final int ATK_VERIFY_MODE_PRACTICE = 5016;
    public static final int ATK_VERIFY_MODE_CHALLENGE = 5017;

    public static final int ATK_VERIFY_MODE_CLOUD_VILLAGE = 5018;
    public static final int ATK_VERIFY_MODE_MAZE = 5019;
    public static final int ATK_VERIFY_MODE_RAID_RAIN_VILLAGE = 5020;
    public static final int ATK_VERIFY_MODE_RAID_SAND_VILLAGE = 5021;
    public static final int ATK_VERIFY_MODE_RAID_MISTINESS_VILLAGE = 5022;
    public static final int ATK_VERIFY_MODE_FRIEND_BOSS = 5023;
    public static final int ATK_VERIFY_MODE_HELP_FRIEND_FIGHT_BOSS = 5024;
    public static final int ATK_VERIFY_MODE_FRIEND_ATTACK = 5025;
    public static final int ATK_VERIFY_MODE_PEAK_OF_TIME = 5026;
    public static final int ATK_VERIFY_MURIM_CHALLENGE = 5027;
    public static final int ATK_VERIFY_WAR_CLAN = 5029;
    public static final int ATK_VERIFY_EVENT_BOSS_SIGMA = 5030;
    public static final int ATK_VERIFY_BOSS_INTER_SERVER = 5031;
    public static final int ATK_VERIFY_FISHING_PEARL_ROB = 5032;
    public static final int ATK_VERIFY_FISHING_PEARL_MINING = 5034;
    public static final int ATK_VERIFY_SILENT_RING = 5033;
    public static final int ATK_VERIFY_STORY_MONSTER = 5035;
    public static final int ATK_VERIFY_STORY_BOSS = 5036;
    public static final int ATK_VERIFY_MODE_TERRITORY = 5037;
    public static final int ATK_VERIFY_EVENT_BOSS_NEW_HERO = 5038;
    public static final int ATK_VERIFY_ABYSS = 5039;
    public static final int ATK_VERIFY_MODE_REPLAY = 5099;
    public static final int ATK_VERIFY_MODE_STORY_MISSION = 5060;
    public static final int ATK_VERIFY_ARENA_SERVER = 5061;
    public static final int ATK_VERIFY_THREE_STAR = 5062;
    public static final int ATK_VERIFY_ARENA3_SERVER = 5063;

    //Battle fake
    public static final int BATTLE_FAKE_STATUS = 11000;
    public static final int BATTLE_FAKE_ATTACK = 11001;
    public static final int BATTLE_FAKE_INFO_AVG = 11002;
    public static final int BATTLE_FAKE_INFO_BATTLE = 11003;
    public static final int BATTLE_FAKE_INFO_BATTLE_DETAIL = 11004;
    public static final int BATTLE_FAKE_HISTORY = 11005;
    public static final int BATTLE_FAKE_HISTORY2 = 11012;
    public static final int BATTLE_FAKE_ATTACK2 = 11013;
    public static final int BATTLE_FAKE_INFO_AVG2 = 11014;
    public static final int BATTLE_FAKE_INFO_BATTLE2 = 11015;
    public static final int BATTLE_FAKE_INFO_BATTLE_DETAIL2 = 11016;
    public static final int BATTLE_FAKE_ADD_GROUP = 11006;
    public static final int BATTLE_FAKE_REPLAY = 11007;
    public static final int BATTLE_TOWER_LOG = 11008;
    public static final int AUTO_BATTLE_TOWER = 11009;
    public static final int AUTO_BATTLE_TOWER_RESULT = 11011;
    // test
    public static final int TEST_TU = 11010;
    // tool đánh ải
    public static final int TOOL_1_CREATE_RESMONSTER = 11020;
    public static final int TOOL_2_STATUS = 11021;
    public static final int TOOL_2_UPDATE = 11022;
    public static final int TOOL_3_STATUS = 11023;
    public static final int TOOL_3_UPDATE = 11024;
    public static final int TOOL_LOGIN = 11025;
    public static final int TOOL_4_STATUS = 11026;
    public static final int TOOL_4_UPDATE = 11027;

    //
    // math puzzles

    public static final int MATH_PUZZLES_STATUS = 915;
    public static final int MATH_PUZZLES_SUBMIT = 916;

    public static final int MATH_CHALLENGE_STATUS = 917;

    public static final int MATH_CHALLENGE_RECEIVE = 918;

    public static final int MATH_SHOP_STATUS = 919;
    public static final int MATH_SHOP_CLAIM = 920;
    public static final int MATH_BUY_ITEM = 921;
    public static final int CHALLENGE2_STATUS = 1120;
    public static final int CHALLENGE2_RECEIVE = 1121;
    // update t10/2024
    public static final int NOTE_LIST = 1122;
    public static final int NOTE_RECEIVE = 1123;
    public static final int NOTE_RECEIVE_SPECIAL = 1124;
    public static final int NOTE_STATUS_PROGRESS = 1125;
    public static final int NOTE_BEING_COMPLETE = 1126;

    // Fishing Man
    public static final int FISHING_LEVEL_STATUS = 1100;
    public static final int FISHING_LEVEL_UP = 1101;
    public static final int FISHING_GEAR_UPGRADE = 947;
    public static final int FISHING_SELECT_SHIP = 948;
    public static final int FISHING_CHEST_BONUS = 949;
    public static final int FISHING_STATUS = 950;
    public static final int FISHING_USER_SHIP = 951;
    public static final int FISHING_USER_FISH = 952;
    public static final int FISHING_USER_AQUARIUM = 953;
    public static final int FISHING_USER_BAIT = 954;
    public static final int FISHING_USER_ISLAND = 955;
    public static final int FISHING_CHANGE_ISLAND = 957;
    public static final int FISHING_LET_GO = 958;
    public static final int FISHING_FISH_TO_AQUARIUM = 959;
    public static final int FISHING_SELL_FISH = 960;
    public static final int FISHING_BUY_AQUARIUM = 961;
    public static final int FISHING_UNLOCK_AQUARIUM_SLOT = 962;
    public static final int FISHING_LAB = 963;
    public static final int FISHING_LAB_REWARD = 964;
    public static final int FISHING_PEARL_STATUS = 965;
    public static final int FISHING_PEARL_MINE_LIST_DETAIL = 979;
    public static final int FISHING_PEARL_MINE_LIST = 966;
    public static final int FISHING_PEARL_MINING = 967;
    public static final int FISHING_PEARL_ROB = 968;
    public static final int FISHING_PEARL_ROB_HISTORY = 969;
    public static final int FISHING_PEARL_LIST_REVENGE = 970;
    public static final int FISHING_ENERGY_STATUS = 1102;
    public static final int FISHING_BUY_FISH_SLOT = 1103;

    //Balance Arena
    public static final int BALANCE_ARENA_STATUS = 980;
    public static final int BALANCE_ARENA_HERO_INFO = 981;
    public static final int BALANCE_ARENA_PET_INFO = 982;
    public static final int BALANCE_ARENA_LIST_FIRST_PICK = 983;
    public static final int BALANCE_ARENA_CHOOSE_HERO = 984;
    public static final int BALANCE_ARENA_EDIT_FORMATION = 985;
    public static final int BALANCE_ARENA_ATTACK = 986;
    public static final int BALANCE_ARENA_LIST_TO_CHOOSE = 987;
    public static final int BALANCE_ARENA_ROLL = 988;
    public static final int BALANCE_ARENA_GET_RANK = 989;
    public static final int BALANCE_ARENA_OPP_USER_INFO = 990;

    // chess master
    public static final int CHESS_MASTER_STATUS = 939;
    public static final int CHESS_MASTER_START_MATCH = 940;
    public static final int CHESS_MASTER_BOARD_INFO = 941;
    public static final int CHESS_MASTER_MOVE = 942;
    public static final int CHESS_MASTER_REVIVE = 943;
    public static final int CHESS_MASTER_ENDGAME = 944;
    public static final int CHESS_MASTER_UPGRADE_STATUS = 945;
    public static final int CHESS_MASTER_UPGRADE = 946;

    //
    public static final int HERO_BREATH_INFO = 1040;
    public static final int HERO_BREATH_LEVEL_UP = 1041;
    public static final int HERO_BREATH_CHOOSE_SKILL = 1042;
    public static final int HERO_BREATH_SKILL_LIST = 1043;
    public static final int HERO_BREATH_POINT_INFO = 1044;

    // Adventure
    public static final int ADVENTURE_STATUS = 1045;
    public static final int ADVENTURE_REFRESH = 1046;
    public static final int ADVENTURE_SPEED_UP = 1047;
    public static final int ADVENTURE_START = 1048;
    public static final int ADVENTURE_RECEIVE = 1049;
    public static final int ADVENTURE_AUTO_FILL = 1060;

    // Animal chess
    public static final int ANIMAL_CHESS_START = 1061;
    public static final int ANIMAL_CHESS_END_GAME = 1062;

    // Indenture
    public static final int INDENTURE_INFO = 2000;
    public static final int INDENTURE_OPEN = 2001;
    public static final int INDENTURE_INSTALLATION_HERO = 2002;
    public static final int INDENTURE_REMOVE_HERO = 2003;
    public static final int INDENTURE_SKIP_TIME = 2004;
    public static final int INDENTURE_LEVEL_UP = 2005;
    public static final int INDENTURE_INFO_SLOT_LOCKED = 2006;

    //Swordsmanship
    public static final int SWORDSMANSHIP_FUNCTION_INFO = 1149;
    public static final int SWORDSMANSHIP_BOOK_INFO = 1150;
    public static final int SWORDSMANSHIP_STATUS = 1151;
    public static final int SWORDSMANSHIP_COMBINE_SHARD = 1152;
    public static final int SWORDSMANSHIP_UPGRADE = 1153;
    public static final int SWORDSMANSHIP_REBORN = 1154;
    public static final int SWORDSMANSHIP_GROWTH = 1155;
    public static final int SWORDSMANSHIP_UPGRADE_TIER = 1156;
    public static final int SWORDSMANSHIP_UPGRADE_STAR = 1157;
    public static final int SWORDSMANSHIP_SUMMON = 1158;
    public static final int SWORDSMANSHIP_SUBMIT = 1159;
    public static final int SWORDSMANSHIP_RESET = 1127;

    // Chat riêng
    public static final int USER_CHAT_LIST_ROOM_CHAT = 1131;
    public static final int USER_CHAT_CONTENT = 1132;
    public static final int USER_CHAT_CHAT = 1133;
    public static final int USER_CHAT_REPORT = 1134;
    public static final int USER_CHAT_FRIEND_ONLY = 1135;

    // Lãnh địa
    public static final int TERRITORY_STATUS = 1136;
    public static final int TERRITORY_STATUS_DETAIL = 1137;
    public static final int TERRITORY_BUY_TURN = 1138;
    public static final int TERRITORY_ATTACK = 1139;
    public static final int TERRITORY_RECEIVE_REWARD = 1140;
    public static final int TERRITORY_SWEEP = 1141;
    public static final int TERRITORY_SWEEP_ALL = 1142;
    public static final int TERRITORY_LIST_HISTORY = 1145;

    // arena team
    public static final int ARENA_TEAM_STATUS = 11070;
    public static final int ARENA_TEAM_DEF_TEAM = 11071;
    public static final int ARENA_TEAM_CREATE = 11072;
    public static final int ARENA_TEAM_SETTING = 11073;
    public static final int ARENA_TEAM_INFO = 11074;

    // arena team member
    public static final int ARENA_TEAM_REQUEST_WAIT = 11075;
    public static final int ARENA_TEAM_REQUEST = 11076;
    public static final int ARENA_TEAM_APPROVE_REQUEST = 11077;
    public static final int ARENA_TEAM_INVITE = 11078;
    public static final int ARENA_TEAM_LEAVE = 11079;
    public static final int ARENA_TEAM_LIST_INVITE_ME = 11080;
    public static final int ARENA_TEAM_ANSWER_INVITE_ME = 11081;
    public static final int ARENA_TEAM_LIST_INVITE_FRIEND = 11082;
    public static final int ARENA_TEAM_LIST_INVITE_CLAN = 11083;
    public static final int ARENA_TEAM_MEMBER_LIST = 11084;
    public static final int ARENA_TEAM_FIND = 11085;
    public static final int ARENA_TEAM_FIND_RANDOM = 11086;
    public static final int ARENA_TEAM_SETTING_AUTO_MERGE = 11087;
    public static final int ARENA_TEAM_SORTING = 11088;

    // arena team battle
    public static final int ARENA_TEAM_TOP3 = 1089;
    public static final int ARENA_TEAM_CHAT = 1090;
    public static final int ARENA_TEAM_CHAT_LIST = 1091;
    public static final int ARENA_TEAM_LIST_OPP = 1092;
    public static final int ARENA_TEAM_ATTACK = 1093;

    public static final int ARENA_TEAM_TOP_SEASON = 1094;
    public static final int ARENA_TEAM_SEASON_REWARD = 1095;

    // arena team betting
    public static final int ARENA_TEAM_BET_LIST = 11200;
    public static final int ARENA_TEAM_BET = 11201;
    public static final int ARENA_TEAM_BET_LAST_SEASON = 11202;
    // arena team Vinh Danh
    public static final int ARENA_TEAM_TOP_VINH_DANH = 11203;
    public static final int ARENA_TEAM_LIKE_TOP_VINH_DANH = 11204;
    public static final int ARENA_TEAM_LIKE_ALL_TOP = 11205;

    // vòng đăng ký
    public static final int ARENA_SERVER_STATUS = 11160;
    public static final int ARENA_SERVER_DEF_TEAM = 11161;
    public static final int ARENA_SERVER_STATUS_REGISTER = 11162;
    public static final int ARENA_SERVER_SCHEDULE = 11163;
    public static final int ARENA_SERVER_TOP_HISTORY = 11164;
    public static final int ARENA_SERVER_TOP_HISTORY_LIKE = 11165;
    public static final int ARENA_SERVER_ROUND_BONUS = 11166;

    // loại + bảng
    public static final int ARENA_SERVER_ROUND_STATUS = 11167;
    public static final int ARENA_SERVER_ROUND_RANKING = 11168;
    public static final int ARENA_SERVER_ROUND_RANKING_DETAIL = 11169;
    public static final int ARENA_SERVER_ROUND_OPP = 11170;
    public static final int ARENA_SERVER_ROUND_ATTACK = 11171;
    public static final int ARENA_SERVER_ROUND_HISTORY = 11172;
    public static final int ARENA_SERVER_DEF_TEAM2 = 11173;

    // chung kết
    public static final int ARENA_SERVER_FINAL_STATUS = 11174;
    public static final int ARENA_SERVER_FINAL_BET = 11175;
    public static final int ARENA_SERVER_FINAL_BET_HISTORY = 11176;
    public static final int ARENA_SERVER_FINAL_BET_REWARD = 11177;

    // vòng đăng ký
    public static final int ARENA3_SKILL_STATUS = 11730;
    public static final int ARENA3_SKILL_LEVEL_UP = 11731;
    public static final int ARENA3_SKILL_CHOOSE = 11732;

    public static final int ARENA3_SERVER_STATUS = 11860;
    public static final int ARENA3_SERVER_DEF_TEAM = 11861;
    public static final int ARENA3_SERVER_STATUS_REGISTER = 11862;
    public static final int ARENA3_SERVER_SCHEDULE = 11863;
    public static final int ARENA3_SERVER_TOP_HISTORY = 11864;
    public static final int ARENA3_SERVER_TOP_HISTORY_LIKE = 11865;
    public static final int ARENA3_SERVER_ROUND_BONUS = 11866;

    // loại + bảng
    public static final int ARENA3_SERVER_ROUND_STATUS = 11867;
    public static final int ARENA3_SERVER_ROUND_RANKING = 11868;
    public static final int ARENA3_SERVER_ROUND_RANKING_DETAIL = 11869;
    public static final int ARENA3_SERVER_ROUND_OPP = 11870;
    public static final int ARENA3_SERVER_ROUND_ATTACK = 11871;
    public static final int ARENA3_SERVER_ROUND_HISTORY = 11872;
    public static final int ARENA3_SERVER_DEF_TEAM2 = 11873;
    // chung kết
    public static final int ARENA3_SERVER_FINAL_STATUS = 11874;
    public static final int ARENA3_SERVER_FINAL_BET = 11875;
    public static final int ARENA3_SERVER_FINAL_BET_HISTORY = 11876;
    public static final int ARENA3_SERVER_FINAL_BET_REWARD = 11877;

    // Boundary
    public static final int USER_ROYAL_PALACE_STATUS = 2010;
    public static final int USER_ROYAL_PALACE_UPGRADE_LEVEL = 2011;
    public static final int USER_ROYAL_PALACE_STATUS_SKILL_FACTION = 2012;
    public static final int USER_ROYAL_PALACE_SKILL_LEVEL_UPGRADE = 2013;

    // Backup runes
    public static final int BACK_UP_RUNES_STATUS = 1724;
    public static final int BACK_UP_RUNES_UPDATE = 1725;
    public static final int BACK_UP_RUNES_SELECT = 1726;

    // hero test
    public static final int RESET_HERO_TEST = 2014;

    // story mission
    public static final int STORY_MISSION_STATUS = 2015;
    public static final int STORY_MISSION_BONUS_ALL_STORY_OF_TYPE = 2016;
    public static final int STORY_MISSION_RECEIVE_BONUS_STORY_FINISH = 2017;
    public static final int STORY_MISSION_BONUS_STORY_TYPE = 2018;
    public static final int STORY_MISSION_RECEIVE_BONUS_STORY_TYPE = 2019;
    public static final int STORY_MISSION_ALL_HERO_SSR = 2020;
    public static final int STORY_MISSION_ATTACK = 2021;


    // raise cat
    // 1. Info mèo
    public static final int RAISE_CAT_STATUS_CAT_USER = 2023;
    public static final int RAISE_CAT_UNLOCK_CAT = 2024;
    public static final int RAISE_CAT_CAT_EAT = 2025;
    public static final int RAISE_CAT_SKIP_TIME_CAT_EAT = 2026;
    public static final int RAISE_CAT_RECEIVE_CAT_EAT_DONE = 2027;
    public static final int RAISE_CAT_LOG_ACTIVITY = 2038;

    // 2. Star mèo
    public static final int RAISE_CAT_LIST_INFO_CAT = 2028;
    public static final int RAISE_CAT_LIST_INFO_CAT_NEXT_STAR = 2029;
    public static final int RAISE_CAT_UP_STAR = 2030;
    public static final int RAISE_CAT_TAYLUYEN = 2031;
    public static final int RAISE_CAT_TAYLUYEN_ACCEPT = 2032;
    public static final int RAISE_CAT_BOIDUONG = 2033;
    public static final int RAISE_CAT_BOIDUONG_ACCEPT = 2034;

    // 3. Nhiệm vụ để có mèo ^^
    public static final int RAISE_CAT_LIST_MISSION_BY_ID_CAT = 2035;
    public static final int RAISE_CAT_RECEIVE_MISSION_BY_ID_CAT = 2036;
    public static final int RAISE_CAT_BEING_COMPLETE_MISSION_BY_ID_CAT = 2037;
    public static final int RAISE_CAT_STATUS_MISSION_DAILY = 2049;
    public static final int RAISE_CAT_RECEIVE_MISSION_DAILY = 2050;


    // 4. thông tin bạn bè của user
    public static final int RAISE_CAT_STATUS_CAT_FRIEND = 2042;
    public static final int RAISE_CAT_HELP_CAT_FRIEND = 2043;
    public static final int RAISE_CAT_STATUS_MATERIAL = 2044;

    // Blood Magic
    public static final int BLOOD_MAGIC_STATUS_BLOOD_MAGIC = 2045;
    public static final int BLOOD_MAGIC_UNLOCK_SKILL_IN_GROUP = 2046;
    public static final int BLOOD_MAGIC_UNLOCK_GROUP_FLOOR = 2047;
    public static final int BLOOD_MAGIC_STATUS_STATUS_POINT_SKILL = 2048;


    public static final List<Integer> allowMultiAction = Arrays.asList(USER_CONFIG, USER_NOTIFY, UPDATE_BONUS, CELESTIAL_MINE_UPDATE, SWAP_RECORD, CRYSTAL_RECORD, HERO_LIST,
            ITEM_LIST, MATERIAL_LIST, GLOBAL_CHAT_LIST, VOID_STATUS, CAMPAIGN_STATUS, USER_ALL_INFO, LUCKY_MONEY_LIST_SYSTEM, NEW_USER_REPLAY);
}
