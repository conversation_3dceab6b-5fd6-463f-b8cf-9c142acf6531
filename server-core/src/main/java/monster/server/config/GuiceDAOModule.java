package monster.server.config;

import com.google.inject.AbstractModule;
import com.google.inject.Singleton;
import monster.dao.*;
import monster.game.arena.dao.ArenaServerDAO;
import monster.game.arena.dao.ArenaServerJobDAO;
import monster.game.arena.dao.ArenaTeamDAO;
import monster.game.bloodmagic.dao.UserBloodMagicDao;
import monster.game.challenge.dao.ChallengeDAO;
import monster.game.challenge.dao.ChallengeNoteDAO;
import monster.game.chat.dao.LuckyMoneyDAO;
import monster.game.clan.dao.ClanDAO;
import monster.game.fishing.dao.FishingDAO;
import monster.game.fishing.dao.FishingPearlDAO;
import monster.game.hero.dao.HeroExpertDAO;
import monster.game.indenture.dao.IndentureDAO;
import monster.game.raisecat.dao.InfoUserCatDAO;
import monster.game.ranking.dao.HeroRankingSeaDAO;
import monster.game.ranking.dao.RankingDAO;
import monster.game.royalpalace.dao.UserRoyalPalaceDAO;
import monster.game.story.dao.StoryBossDAO;
import monster.game.story.dao.StoryDAO;
import monster.game.summon.dao.SummonDAO;
import monster.game.sushi.dao.SushiEventDAO;
import monster.game.truongevent.dao.CandyBreakDAO;
import monster.game.user.dao.AchievementDAO;
import monster.game.user.dao.BadgeDAO;
import monster.game.user.service.dao.FriendDAO;

public class GuiceDAOModule extends AbstractModule {

    @Override
    protected void configure() {
        bind(ClanDAO.class).in(Singleton.class);
        bind(GemDAO.class).in(Singleton.class);
        bind(HeroDAO.class).in(Singleton.class);
        bind(WarningDAO.class).in(Singleton.class);
        bind(SystemDAO.class).in(Singleton.class);
        bind(UserTeamDAO.class).in(Singleton.class);
        bind(UserDAO.class).in(Singleton.class);
        bind(FeatureTestDAO.class).in(Singleton.class);
        bind(MarketDAO.class).in(Singleton.class);
        bind(IdleDAO.class).in(Singleton.class);
        bind(LabyrinthDAO.class).in(Singleton.class);
        bind(FishingDAO.class).in(Singleton.class);
        bind(FishingPearlDAO.class).in(Singleton.class);
        bind(BadgeDAO.class).in(Singleton.class);
        bind(HeroRankingSeaDAO.class).in(Singleton.class);
        bind(SushiEventDAO.class).in(Singleton.class);
        bind(AchievementDAO.class).in(Singleton.class);
        bind(FriendDAO.class).in(Singleton.class);
        bind(CandyBreakDAO.class).in(Singleton.class);
        bind(StoryDAO.class).in(Singleton.class);
        bind(StoryBossDAO.class).in(Singleton.class);
        bind(SummonDAO.class).in(Singleton.class);
        bind(IndentureDAO.class).in(Singleton.class);
        bind(LuckyMoneyDAO.class).in(Singleton.class);
        bind(UserRoyalPalaceDAO.class).in(Singleton.class);
        bind(ChallengeDAO.class).in(Singleton.class);
        bind(ChallengeNoteDAO.class).in(Singleton.class);
        bind(RankingDAO.class).in(Singleton.class);
        bind(HeroTestDAO.class).in(Singleton.class);
        bind(InfoUserCatDAO.class).in(Singleton.class);
        bind(UserBloodMagicDao.class).in(Singleton.class);
        bind(HeroExpertDAO.class).in(Singleton.class);

        // arena
        bind(ArenaServerDAO.class).in(Singleton.class);
        bind(ArenaTeamDAO.class).in(Singleton.class);
        bind(ArenaServerJobDAO.class).in(Singleton.class);

    }
}
