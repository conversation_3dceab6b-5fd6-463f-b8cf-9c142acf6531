package monster.server;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.Filer;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.dao.mapping.main.ConfigEntity;
import monster.dao.mapping.main.GameServerStatusEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppConfig {
    public static GameServerStatusEntity gameServer;
    public static Map<Integer, GameServerStatusEntity> mGameServer = new HashMap<>();
    public static DataConfig cfg;
    public static final int SERVER_BATTLE_FAKE = 2;

    public static boolean isMainnet() {
        return cfg != null && cfg.mainnet;
    }

    public static boolean isLocalTest() {
        return cfg.name.equals("LocalTest");
    }

    public static boolean isServerRealTest() {
        return cfg.isRealTest;
    }

    public static int getAdminPort() {
        return cfg.backdoor;
    }

    public static void setDbConfig() {
        ConfigEntity config = DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", cfg.keyConfig);
        JsonObject obj = GsonUtil.parseJsonObject(config.getV());
        cfg.mongodb = obj.get("mongodb").getAsString();
        cfg.prefixIp = obj.get("prefixIp").getAsString();
        cfg.redis = new Gson().fromJson(obj.get("redis").toString(), RedisConfig.class);
        cfg.memcached = GsonUtil.strToListString(obj.get("memcached").toString());
    }

    public static String getServerUrl(int serverId) {
        GameServerStatusEntity gameServer = mGameServer.get(serverId);
        return gameServer == null ? "" : gameServer.getConnectUrl();
    }

    public static DataConfig load(String filename) {
        String data = Filer.readFile(filename);
        if (StringHelper.isEmpty(data)) {
            Logs.error("Couldn't load json config file");
        }
        cfg = new Gson().fromJson(data, DataConfig.class);
        if (StringHelper.isEmpty(cfg.keyConfig)) cfg.keyConfig = "server_config";
        else cfg.keyConfig = "server_config_" + cfg.keyConfig;
        return cfg;
    }

    public class DataConfig {
        public String name, keyConfig, prefixIp;
        public boolean battleFake;
        public boolean isRealTest;
        public boolean mainnet = false;
        public TelegramConfig telegram;
        public int backdoor;
        public DBConfig db;
        public String localIp;

        public String mongodb;
        public RedisConfig redis;
        public List<String> memcached;
        private Boolean debug, submit;
        public AsanaConfig asana;

        public boolean isDebug() {
            return debug != null && debug;
        }

        public boolean isSubmit() {
            return submit != null && submit;
        }
    }

    public class DBConfig {
        public String mysqlUsername, mysqlPassword;
        public String entity1, entity2;
    }

    public class TelegramConfig {
        public String redisChannel, prefix;
        public String botId, chatId;
        public String chatId2, chatPrivate, chatGroupGd;
    }

    public class RedisConfig {
        public String pubSubHost, gameHost, redis3, redisEvent;
    }

    public class AsanaConfig {
        public String pat, projectId, workspaceId;
    }
}
