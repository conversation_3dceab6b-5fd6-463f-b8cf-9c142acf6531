package monster.object;

import grep.database.DBJPA;
import lombok.Builder;
import lombok.Data;
import monster.cache.redis.IRedis;
import monster.cache.redis.JCache;
import monster.config.CfgArenaCrystal;
import redis.clients.jedis.resps.Tuple;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0
 * @date 2018/12/24
 * @description
 * @since 1.0
 * <p>
 * quản lý rank - xử lý vấn đề nhiều server cùng access bảng rank
 * sử dụng sorted set của redis
 * </p>
 */
@Builder
@Data
public class Rank2Controller {
    private int serverId, numberTop;
    private int defaultPoint;
    private String key, sql;

    public static final int arenaCrystal = 1;
    public static final int bossInterServer = 2;

    /**
     * Nhiều server cũng gọi hàm init nên chỉ được phép 1 server thực hiện
     * Tại sao mỗi lần reset lại phải tạo lại key 1 lần ?
     *
     * @return
     */
    public boolean init() {
        String lockKey = "locked:" + key;
        boolean acquireLock = getRedis().distributedLocked.acquireLock(lockKey);
        if (acquireLock) {
            IRedis iRedis = getRedis();
            if (!iRedis.exists(key)) {
                Map<String, Double> scoreMembers = new HashMap<>();
                List aUser = DBJPA.getList(sql);
                for (int i = 0; i < aUser.size(); i++) {
                    Object[] values = (Object[]) aUser.get(i);
                    scoreMembers.put(values[0].toString(), Double.parseDouble(values[1].toString()));
                }
                if (scoreMembers.isEmpty()) return true;
                return iRedis.zadd(key, scoreMembers);
            }
            return true;
        }
        return false;
    }

    public int getRank(int userId, int type) {
        Long rank = JCache.getInstance().zrevrank(key, String.valueOf(userId));
        switch (type) {
            case arenaCrystal:
                return rank != null && rank >= 0 ? rank.intValue() + 1 : CfgArenaCrystal.config.maxRank + 1;
            case bossInterServer:
                return rank != null && rank >= 0 ? rank.intValue() + 1 : -1;
        }

        return -1;
    }

    public int getPoint(int userId, int... defaultPoints) {
        int defaultPoint = defaultPoints.length == 0 ? this.defaultPoint : defaultPoints[0];
        Double score = getRedis().zscore(key, String.valueOf(userId));
        if (score == null) {
            if (updatePoint(userId, defaultPoint))
                return defaultPoint;
        } else if (score.intValue() == -1000) score = null;
        return score.intValue();
    }

    public List<Integer> getPoints(int... userIds) {
        // convert userId to string
        String[] strUserIds = new String[userIds.length];
        for (int i = 0; i < userIds.length; i++) {
            strUserIds[i] = String.valueOf(userIds[i]);
        }
        List<Double> scores = getRedis().zmscore(key, strUserIds);
        // convert scores to int
        List<Integer> points = new java.util.ArrayList<>();
        for (Double score : scores) {
            points.add(score.intValue());
        }
        return points;
    }

    public boolean addPoints(Object... values) {
        Map<String, Double> scoreMembers = new HashMap<>();
        for (int i = 0; i < values.length; i += 2) {
            if ((Integer) values[i] > 0)
                scoreMembers.put(values[i].toString(), Double.parseDouble(values[i + 1].toString()));
        }
        return getRedis().zincrby(key, scoreMembers);
    }

    public boolean updatePoint(Object... values) {
        Map<String, Double> scoreMembers = new HashMap<>();
        for (int i = 0; i < values.length; i += 2) {
            scoreMembers.put(values[i].toString(), Double.parseDouble(values[i + 1].toString()));
        }
        return getRedis().zadd(key, scoreMembers);
    }

    public List<Tuple> getTop() {
        return getRedis().zrevrangeWithScores(key, 0, numberTop - 1);
    }

    public void reset() {
        JCache.getInstance().removeValue(key);
    }

    public boolean hasRank(String mKey) {
        return getRedis().getValue(mKey) != null;
    }

    public JCache getRedis() {
        return JCache.getInstance();
    }
}
