package monster.object;

import com.google.gson.Gson;
import grep.helper.ListUtil;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import monster.config.penum.BattleType;
import monster.controller.AHandler;
import monster.dao.mapping.UserHeroCloneEntity;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.UserPetCloneEntity;
import monster.dao.mapping.UserPetEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.game.herotest.entity.UserHeroTestEntity;
import monster.game.storymission.entity.ResHeroStoryMissionEntity;
import monster.game.user.service.UserService;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResHero;
import monster.service.resource.ResMonster;
import monster.service.resource.ResStoryMission;
import protocol.Pbmethod;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
@Getter
public class BattleTeam {
    HeroInfoEntity[] aHero;
    UserPetEntity[] aPet;
    HeroInfoEntity heroBreath;
    List<Integer> aRelics;
    int numberWin;
    @Setter
    long powerShow = 0;

    public boolean isOk() {
        if (aHero == null) return false;
        HeroInfoEntity hero = Stream.of(aHero).filter(Objects::nonNull).findFirst().orElse(null);
        return hero != null;
    }

    public HeroInfoEntity getHeroBreath() {
        if (heroBreath == null) heroBreath = new HeroInfoEntity();
        return heroBreath;
    }

    public List<Integer> getARelics() {
        if (aRelics == null) aRelics = new ArrayList<>();
        return aRelics;
    }

    public List<Long> getHeroIds() {
        List<Long> heroIds = new ArrayList<>();
        for (int index = 0; index < aHero.length / 6; index++) {
            for (int i = 0; i < 6; i++) {
                if (aHero[index * 6 + i] != null) {
                    heroIds.add(aHero[index * 6 + i].id);
                }
            }
        }
        return heroIds;
    }

    public static BattleTeam getInstance(MyUser mUser, List<Long> heroIds) {
        int number = heroIds.size() / BattleConfig.TEAM_INPUT;
        for (int i = 0; i < number; i++) {
            List<Integer> heroKeys = mUser.getResources().getHeroKey(heroIds.subList(BattleConfig.TEAM_INPUT * i, BattleConfig.TEAM_INPUT * (i + 1)));
            if (ListUtil.hasDuplicateValueInteger(heroKeys)) {
                return null;
            }
        }
        if (heroIds.size() == BattleConfig.TEAM_INPUT || heroIds.size() == BattleConfig.TEAM_INPUT * 3 || heroIds.size() == BattleConfig.TEAM_INPUT * 2) {
            HeroInfoEntity[] aHero = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE * number];
            UserPetEntity[] aPet = new UserPetEntity[number];
            for (int index = 0; index < number; index++) {
                int petIndex = index * BattleConfig.TEAM_INPUT + BattleConfig.TEAM_HERO_SIZE;
                aPet[index] = mUser.getResources().getPet(heroIds.get(petIndex).intValue());
                List<Long> aTeam = heroIds.subList(index * BattleConfig.TEAM_INPUT, index * BattleConfig.TEAM_INPUT + BattleConfig.TEAM_HERO_SIZE);
                UserHeroEntity[] tmpHero = mUser.getResources().getTeam(aTeam);
                for (int i = 0; i < tmpHero.length; i++) {
                    if (tmpHero[i] != null) {
                        aHero[index * BattleConfig.TEAM_HERO_SIZE + i] = tmpHero[i].toHeroInfo(1, i % BattleConfig.TEAM_HERO_SIZE);
                    }
                }

                // them hero hero support vao team
                for (int i = 0; i < aTeam.size(); i++) {
                    long idHero = aTeam.get(i);
                    if (idHero >= 0) continue;

                    //Tướng dùng thử
                    UserHeroTestEntity userHeroTest = mUser.getResources().getHeroTest(Math.abs(idHero));
                    if (userHeroTest != null)
                        aHero[index * BattleConfig.TEAM_HERO_SIZE + i] = userHeroTest.toHeroInfo(1, i % BattleConfig.TEAM_HERO_SIZE, mUser);
                }
            }
            HeroInfoEntity heroBreath = Guice.getInstance(UserService.class).getHeroBreath(mUser.getUser().getId());
            return BattleTeam.builder().aHero(aHero).aPet(aPet).heroBreath(heroBreath).build();
        }
        return BattleTeam.builder().aHero(null).aPet(null).heroBreath(null).build();
    }

    public static BattleTeam getInstanceNew(MyUser mUser, List<Long> heroIds) {
        int number = heroIds.size() / BattleConfig.TEAM_INPUT;
        for (int i = 0; i < number; i++) {
            List<Integer> heroKeys = mUser.getResources().getHeroKey(heroIds.subList(BattleConfig.TEAM_INPUT * i, BattleConfig.TEAM_INPUT * (i + 1)));
            if (ListUtil.hasDuplicateValueInteger(heroKeys)) {
                return null;
            }
        }
        if (heroIds.size() == BattleConfig.TEAM_INPUT || heroIds.size() == BattleConfig.TEAM_INPUT * 3 || heroIds.size() == BattleConfig.TEAM_INPUT * 2) {
            HeroInfoEntity[] aHero = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE * number];
            UserPetEntity[] aPet = new UserPetEntity[number];
            for (int index = 0; index < number; index++) {
                int petIndex = index * BattleConfig.TEAM_INPUT + BattleConfig.TEAM_HERO_SIZE;
                aPet[index] = mUser.getResources().getPet(heroIds.get(petIndex).intValue());
                List<Long> aTeam = heroIds.subList(index * BattleConfig.TEAM_INPUT, index * BattleConfig.TEAM_INPUT + BattleConfig.TEAM_HERO_SIZE);
                UserHeroEntity[] tmpHero = mUser.getResources().getTeam(aTeam);
                for (int i = 0; i < tmpHero.length; i++) {
                    if (tmpHero[i] != null)
                        aHero[index * BattleConfig.TEAM_HERO_SIZE + i] = tmpHero[i].toHeroInfo(1, i % BattleConfig.TEAM_HERO_SIZE);
                }
                // them hero hero support vao team
                for (int i = 0; i < aTeam.size(); i++) {
                    long idHero = aTeam.get(i);
                    if (idHero >= 0) continue;

                    //Tướng dùng cho tutorial
                    if (Math.abs(idHero) < 10000) {
                        ResMonsterEntity rMonster = ResMonster.getMonster((int) Math.abs(aTeam.get(i))); // đang để số âm để phân biệt hero của user hay vay mượn
                        aHero[index * BattleConfig.TEAM_HERO_SIZE + i] = rMonster.toHeroInfo(1, i % BattleConfig.TEAM_HERO_SIZE);
                        continue;
                    }

                    //Tướng dùng thử
                    UserHeroTestEntity userHeroTest = mUser.getResources().getHeroTest(Math.abs(idHero));
                    if (userHeroTest != null)
                        aHero[index * BattleConfig.TEAM_HERO_SIZE + i] = userHeroTest.toHeroInfo(1, i % BattleConfig.TEAM_HERO_SIZE, mUser);
                }
            }
            HeroInfoEntity heroBreath = Guice.getInstance(UserService.class).getHeroBreath(mUser.getUser().getId());
            return BattleTeam.builder().aHero(aHero).aPet(aPet).heroBreath(heroBreath).build();
        }
        return BattleTeam.builder().aHero(null).aPet(null).heroBreath(null).build();
    }

    public static BattleTeam getInstanceClone(MyUser mUser, List<Integer> heroIds, int positionIndex) {
        if (heroIds.size() == BattleConfig.TEAM_INPUT || heroIds.size() == BattleConfig.TEAM_INPUT * 3 || heroIds.size() == BattleConfig.TEAM_INPUT * 2) {
            int number = 1;
            HeroInfoEntity[] aHero = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE * number];
            UserPetEntity[] aPet = new UserPetEntity[number];
            List<UserHeroCloneEntity> allHeroClone = mUser.getCache().getAllHeroClone(null, mUser);
            List<UserHeroCloneEntity> allHeroCloneById = allHeroClone.stream().filter(heroClone -> heroIds.contains(heroClone.getId()) && heroClone.getPositionIndex() == positionIndex).collect(Collectors.toList());
            int index = 0;

            UserPetCloneEntity petClone = mUser.getCache().getPetClone(null, mUser, positionIndex);
            if (petClone != null) aPet[index] = petClone.toUserPet();
            for (int i = 0; i < allHeroCloneById.size(); i++) {
                if (allHeroCloneById.get(i) != null){
                    HeroInfoEntity heroInfo = allHeroCloneById.get(i).toHeroInfo(2, i % BattleConfig.TEAM_HERO_SIZE);
                    aHero[index * BattleConfig.TEAM_HERO_SIZE + i] = heroInfo;
                }
            }

            HeroInfoEntity heroBreath = Guice.getInstance(UserService.class).getHeroBreath(mUser.getUser().getId());
            return BattleTeam.builder().aHero(aHero).aPet(aPet).heroBreath(heroBreath).build();
        }
        return BattleTeam.builder().aHero(null).aPet(null).heroBreath(null).build();
    }

    public static BattleTeam getInstanceStoryMission(MyUser mUser, List<Long> heroIds, int level, int star, int exclusiveSkillLevel) {
        int number = heroIds.size() / BattleConfig.TEAM_INPUT;
        if (heroIds.size() == BattleConfig.TEAM_INPUT || heroIds.size() == BattleConfig.TEAM_INPUT * 3 || heroIds.size() == BattleConfig.TEAM_INPUT * 2) {
            HeroInfoEntity[] aHero = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE * number];
            UserPetEntity[] aPet = new UserPetEntity[number];
            for (int teamIndex = 0; teamIndex < number; teamIndex++) {
                int petIndex = teamIndex * BattleConfig.TEAM_INPUT + BattleConfig.TEAM_HERO_SIZE;
                aPet[teamIndex] = mUser.getResources().getPet(heroIds.get(petIndex).intValue());
                UserHeroEntity[] tmpHero = addHeroStoryMission(mUser, teamIndex, heroIds, level, star, exclusiveSkillLevel);
                for (int i = 0; i < tmpHero.length; i++) {
                    if (tmpHero[i] != null) {
                        aHero[teamIndex * BattleConfig.TEAM_HERO_SIZE + i] = tmpHero[i].toHeroStoryMission(1, i % BattleConfig.TEAM_HERO_SIZE);
                    }
                }
            }
            return BattleTeam.builder().aHero(aHero).aPet(aPet).build();
        }
        return BattleTeam.builder().aHero(null).aPet(null).heroBreath(null).build();
    }

    private static UserHeroEntity[] addHeroStoryMission(MyUser mUser, int teamIndex, List<Long> heroIds, int level, int star, int exclusiveSkillLevel) {
        UserHeroEntity[] team = new UserHeroEntity[BattleConfig.TEAM_HERO_SIZE];
        for (int i = teamIndex * BattleConfig.TEAM_INPUT; i < (teamIndex + 1) * BattleConfig.TEAM_INPUT - 1; i++) {
            if (heroIds.get(i) != 0) {
                UserHeroEntity userHero = new UserHeroEntity();
                userHero.setId(heroIds.get(i));
                userHero.setUserId(mUser.getUser().getId());
                userHero.setHeroId((int) (heroIds.get(i) - 1000000));
                userHero.setLevel(level);
                userHero.setStar(star);
                userHero.setExclusiveSkillLevel(exclusiveSkillLevel);
                userHero.calculatePointHero(null);
                team[i] = userHero;
            } else {
                break;
            }
        }

        return team;
    }

    public void recovery() {
        for (HeroInfoEntity hero : aHero) {
            if (hero != null) {
                hero.point.setStartHp(-1);
                hero.point.setPercentHp(-1);
                hero.point.setMaxHp(-1);
                hero.point.setStartHpPercent(-1);
                hero.point.setCurrentValue(Point.REVIVED, 0);
                hero.point.setCurrentValue(Point.HP, hero.point.getValue(Point.CALCULATED_VALUES_INDEX, Point.HP));
            }
        }
    }

    public void reducePowerStar(int maxStar) {
        HeroInfoEntity tmpHero = null;
        for (int i = 0; i < aHero.length; i++) {
            if (aHero[i] != null && tmpHero == null) tmpHero = aHero[i];

            if (aHero[i] != null && aHero[i].star > maxStar) {
                aHero[i] = null;
            }
        }
        HeroInfoEntity testHero = Arrays.stream(aHero).filter(Objects::nonNull).findFirst().orElse(null);
        if (testHero == null) {
            aHero[0] = tmpHero;
        }
    }

    public void removePet() {
        if (aPet != null) {
            for (int i = 0; i < aPet.length; i++) {
                aPet[i] = null;
            }
        }
    }

    /**
     * percent > 0 -> giam
     * percent < 0 -> tang
     *
     * @param percent
     */
    public void reducePowerPercent(int percent) {
        addPowerPercent(percent, false);
    }

    public void increasePowerPercent(int percent) {
        addPowerPercent(percent, true);
    }

    private void addPowerPercent(int percent, boolean isIncrease) {
        percent = isIncrease ? Math.abs(percent) : -Math.abs(percent);
        for (int i = 0; i < aHero.length; i++) {
            if (aHero[i] != null) {
                long newHp = aHero[i].point.getCurrentHP() * (100 + percent) / 100;
                long newAtk = aHero[i].point.getCurrentAttack() * (100 + percent) / 100;
                aHero[i].point.setCurrentValue(Point.HP, newHp);
                aHero[i].point.setCalculatedValue(Point.HP, newHp);
                aHero[i].point.setCurrentValue(Point.ATTACK, newAtk);
                aHero[i].point.setCalculatedValue(Point.ATTACK, newAtk);
            }
        }
    }

    //    public void reducePower(long power) {
    //        List<Long> aPower = new ArrayList<>();
    //        List<Integer> aIndex = new ArrayList<>();
    //        List<Integer> aStar = new ArrayList<>();
    //        for (int i = 0; i < aHero.length; i++) {
    //            if (aHero[i] != null) {
    //                aPower.add(IMath.getPower(aHero[i].point));
    //                aIndex.add(i);
    //                aStar.add(aHero[i].star);
    //            }
    //        }
    //        while (aIndex.size() > 1) {
    //            if (aPower.stream().collect(Collectors.summingLong(Long::longValue)) > power) {
    //                Integer maxStar = aStar.stream().max(Integer::compareTo).get();
    //                int index = aStar.indexOf(maxStar);
    //                aHero[aIndex.get(index)] = null;
    //                aPower.remove(index);
    //                aIndex.remove(index);
    //                aStar.remove(index);
    //            } else break;
    //        }
    //    }

    public int getHighestStar() {
        return Stream.of(aHero).filter(Objects::nonNull).mapToInt(hero -> hero.star).max().getAsInt();
    }

    public void fakedOdd() {
        int rand = new Random().nextInt(2);
        for (HeroInfoEntity hero : aHero) {
            if (hero != null) {
                hero.point.setCurrentValue(Point.ATTACK, hero.point.getCurrentAttack() + rand);
                hero.point.setCurrentValue(Point.ARMOR, hero.point.getCurrentArmor() + rand);
            }
        }
    }

    public static void toPbUserProto(protocol.Pbmethod.PbUser.Builder pbUser, BattleTeam team) {
        if (team != null && team.getAPet() != null) {
            for (int index = 0; index < team.getAPet().length; index++) {
                if (team.getAPet()[index] != null)
                    pbUser.addTeamPet(team.getAPet()[index].getPetId()).addTeamPet(team.getAPet()[index].getTier());
            }
        }
        if (team != null) {
            for (HeroInfoEntity heroEntity : team.getAHero()) {
                if (heroEntity != null) {
                    pbUser.addTeam(heroEntity.protoTeamHero());
                } else pbUser.addTeam(protocol.Pbmethod.PbTeamHeroInfo.newBuilder());
            }
            pbUser.setPower(team.getPower());
        } else {
            for (int index = 0; index < 6; index++)
                pbUser.addTeam(protocol.Pbmethod.PbTeamHeroInfo.newBuilder());
        }
    }

    // aura team
    // pet
    // specialItem
    public long getPower(int... teamIndexes) {
        int numberTeam = aHero.length / BattleConfig.TEAM_HERO_SIZE;
        int fromIndex = 0, toIndex = numberTeam - 1;
        if (teamIndexes.length > 0) {
            fromIndex = teamIndexes[0];
            toIndex = teamIndexes[0];
        }

        long power = 0;
        for (int k = fromIndex; k <= toIndex; k++) {
            List<HeroInfoEntity> heroClone = new ArrayList<>();
            int index = k * BattleConfig.TEAM_HERO_SIZE;
            for (int j = index; j < index + BattleConfig.TEAM_HERO_SIZE; j++) {
                HeroInfoEntity hero = aHero[j];
                if (hero != null) heroClone.add(hero.cloneHero());
            }
            // aura
            //Team thủ trong pve ko tính buff formation
            float[] cfgBonusAtk = new float[]{0, 10, 15, 20, 25};
            float[] cfgBonusHp = new float[]{0, 10, 15, 20, 25};
            float totalBonusHp = 0, totalBonusAtk = 0;
            int type = getTypeBonusFaction(heroClone);
            totalBonusHp += cfgBonusHp[type];
            totalBonusAtk += cfgBonusAtk[type];

            for (HeroInfoEntity hero : heroClone) {
                if (hero.team == 0) continue;
                hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.HP, (int) (hero.point.getCurrentValue(Point.HP) * totalBonusHp / 100));
                hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.ATTACK, (int) (hero.point.getCurrentValue(Point.ATTACK) * totalBonusAtk / 100));
                hero.point.setCalculatedValue();
            }

            //pet
            HeroBattleEntity pet = aPet != null && aPet[k] != null ? aPet[k].toHeroBattle(1) : null;
            if (pet != null) {
                for (int j = 0; j < heroClone.size(); j++) {
                    HeroInfoEntity hero = heroClone.get(j);
                    for (int pointIndex = 0; pointIndex < 13; pointIndex++) {
                        if (pointIndex == Point.ARMOR)
                            hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, pointIndex, hero.point.getCurrentValue(pointIndex) * pet.point.getCurrentValue(pointIndex) / 1000);
                        else
                            hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, pointIndex, pet.point.getValue(Point.CURRENT_VALUES_INDEX, pointIndex));
                    }
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.ATTACK,
                            hero.point.getCurrentValue(Point.ATTACK) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_ATTACK));
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.HP,
                            hero.point.getCurrentValue(Point.HP) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_HP));
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.SPEED,
                            hero.point.getCurrentValue(Point.SPEED) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_SPEED));
                }
            }

            for (int j = 0; j < heroClone.size(); j++) {
                HeroInfoEntity hero = heroClone.get(j);
                hero.point.setCalculatedValue();

                if (hero.point.getMaxHp() <= 0)
                    hero.point.setMaxHp(hero.point.getCalculatedValue(Point.HP));
                else hero.point.setCalculatedValue(Point.HP, hero.point.getMaxHp());


                if (hero.point.getStartHp() >= 0 && hero.point.getStartHp() < hero.point.getValue(Point.CALCULATED_VALUES_INDEX, Point.HP)) {
                    hero.point.setCurrentValue(Point.HP, hero.point.getStartHp());
                } else if (hero.point.getStartHpPercent() >= 0) {
                    BigDecimal bigDecimal = new BigDecimal(hero.point.getCurrentValue(Point.HP));
                    bigDecimal = bigDecimal.multiply(BigDecimal.valueOf(hero.point.getStartHpPercent())).divide(BigDecimal.valueOf(100));
                    long newHp = bigDecimal.longValue();
                    if (hero.point.getStartHpPercent() > 0 && newHp == 0) newHp = 1;
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.HP, newHp);
                } else hero.point.setStartHp(hero.point.getValue(Point.CURRENT_VALUES_INDEX, Point.HP));
            }
            power += heroClone.stream().mapToLong(hero -> IMath.getPower(hero.point)).sum();
        }
        return power;
    }

    private static int getTypeBonusFaction(List<HeroInfoEntity> team) {
        Map<Integer, Integer> typeMapByNumber = new HashMap<>();
        typeMapByNumber.put(6, 4);
        typeMapByNumber.put(5, 3);
        typeMapByNumber.put(4, 2);
        typeMapByNumber.put(3, 1);

        int[] countFaction = new int[7];
        int number = 0;
        team.stream().filter(heroInfo -> heroInfo.faction != null).forEach(hero -> countFaction[hero.faction.value]++);
        boolean onlyDarkLight = true;
        for (int index = 0; index < countFaction.length - 1; index++) {
            if (!List.of(HeroType.FACTION_DARK.value, HeroType.FACTION_LIGHT.value).contains(index) && countFaction[index] > 0) {
                onlyDarkLight = false;
                break;
            }
        }
        if (onlyDarkLight)
            number = countFaction[HeroType.FACTION_DARK.value] + countFaction[HeroType.FACTION_LIGHT.value];
        else {
            for (int index = 0; index < countFaction.length - 1; index++) {
                if (List.of(HeroType.FACTION_DARK.value, HeroType.FACTION_LIGHT.value).contains(index)) continue;
                number = Math.max(number, countFaction[index] + countFaction[HeroType.FACTION_DARK.value] + countFaction[HeroType.FACTION_LIGHT.value]);
            }
        }

        return typeMapByNumber.containsKey(number) ? typeMapByNumber.get(number) : 0;
    }

    public long getPowerTest(AHandler handler) {
        int numberTeam = aHero.length / BattleConfig.TEAM_HERO_SIZE;
        long power = 0;
        for (int k = 0; k < numberTeam; k++) {
            List<HeroInfoEntity> heroClone = new ArrayList<>();
            int index = k * BattleConfig.TEAM_HERO_SIZE;
            for (int j = index; j < index + BattleConfig.TEAM_HERO_SIZE; j++) {
                HeroInfoEntity hero = aHero[j];
                if (hero != null) {
                    HeroInfoEntity clone = hero.cloneHero();
                    heroClone.add(clone);
                }
            }
            // aura
            float[] cfgBonusAtk = new float[]{0f, 10f, 15f, 15f, 20f, 25f};
            float[] cfgBonusHp = new float[]{0, 10, 15, 20, 20, 25};
            float totalBonusHp = 0, totalBonusAtk = 0;
            int type = getTypeBonusFaction(heroClone);
            totalBonusHp += cfgBonusHp[type];
            totalBonusAtk += cfgBonusAtk[type];

            for (HeroInfoEntity hero : heroClone) {
                if (hero.team == 0) continue;
                hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.HP, (int) (hero.point.getBaseValue(Point.HP) * totalBonusHp / 100));
                hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.ATTACK, (int) (hero.point.getBaseValue(Point.ATTACK) * totalBonusAtk / 100));
                hero.point.setCalculatedValue();
                handler.addResponse(-99, CommonProto.getErrorMsg("ID: " + hero.id + "Aura Point:" + hero.point));
            }

            //pet
            HeroBattleEntity pet = aPet[k] != null ? aPet[k].toHeroBattle(1) : null;
            if (pet != null) {
                for (int j = 0; j < heroClone.size(); j++) {
                    HeroInfoEntity hero = heroClone.get(j);
                    for (int pointIndex = 0; pointIndex < 13; pointIndex++) {
                        if (pointIndex == Point.ARMOR)
                            hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, pointIndex, hero.point.getCurrentValue(pointIndex) * pet.point.getCurrentValue(pointIndex) / 1000);
                        else
                            hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, pointIndex, pet.point.getValue(Point.CURRENT_VALUES_INDEX, pointIndex));
                    }
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.ATTACK,
                            hero.point.getCurrentValue(Point.ATTACK) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_ATTACK));
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.HP,
                            hero.point.getCurrentValue(Point.HP) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_HP));
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.SPEED,
                            hero.point.getCurrentValue(Point.SPEED) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_SPEED));
                    handler.addResponse(-99, CommonProto.getErrorMsg("ID: " + hero.id + "Pet Point:" + hero.point));
                }
            }
            handler.addResponse(-99, CommonProto.getErrorMsg("Power sau khi cộng special item = " + power));
            for (int j = 0; j < heroClone.size(); j++) {
                HeroInfoEntity hero = heroClone.get(j);
                hero.point.setCalculatedValue();

                if (hero.point.getMaxHp() <= 0)
                    hero.point.setMaxHp(hero.point.getCalculatedValue(Point.HP));
                else hero.point.setCalculatedValue(Point.HP, hero.point.getMaxHp());


                if (hero.point.getStartHp() >= 0 && hero.point.getStartHp() < hero.point.getValue(Point.CALCULATED_VALUES_INDEX, Point.HP)) {
                    hero.point.setCurrentValue(Point.HP, hero.point.getStartHp());
                } else if (hero.point.getStartHpPercent() >= 0) {
                    BigDecimal bigDecimal = new BigDecimal(hero.point.getCurrentValue(Point.HP));
                    bigDecimal = bigDecimal.multiply(BigDecimal.valueOf(hero.point.getStartHpPercent())).divide(BigDecimal.valueOf(100));
                    long newHp = bigDecimal.longValue();
                    if (hero.point.getStartHpPercent() > 0 && newHp == 0) newHp = 1;
                    hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.HP, newHp);
                } else hero.point.setStartHp(hero.point.getValue(Point.CURRENT_VALUES_INDEX, Point.HP));
            }
            power += heroClone.stream().mapToLong(hero -> IMath.getPower(hero.point)).sum();
            heroClone.forEach(hero -> {
                handler.addResponse(-99, CommonProto.getErrorMsg("HERO ID: " + hero.id + "  --- POWER= " + IMath.getPower(hero.point)));
            });
            handler.addResponse(-99, CommonProto.getErrorMsg("Tổng Power TEAM = " + power));
        }
        handler.addResponse(-99, CommonProto.getErrorMsg("Tổng Power CÁC TEAM = " + power));
        return power;
    }

    public List<List<Integer>> getHeroInfo() {
        List<List<Integer>> heroIds = new ArrayList<>();
        for (int index = 0; index < aHero.length / 6; index++) {
            List<Integer> tmp = new ArrayList<>();
            for (int i = 0; i < 6; i++) {
                if (aHero[index * 6 + i] != null) {
                    tmp.add(aHero[index * 6 + i].heroId);
                    tmp.add(aHero[index * 6 + i].star);
                    tmp.add(aHero[index * 6 + i].level);
                }
            }
            heroIds.add(tmp);
        }
        return heroIds;
    }

    public List<List<Integer>> getPetInfo() {
        List<List<Integer>> petIds = new ArrayList<>();
        for (int index = 0; index < aPet.length; index++) {
            List<Integer> tmp = new ArrayList<>();
            if (aPet[index] != null) {
                tmp.add(aPet[index].getPetId());
                tmp.add(aPet[index].getLevel());
            }
            petIds.add(tmp);
        }
        return petIds;
    }

    public boolean isEmpty() {
        int numberTeam = aHero.length / 6;
        boolean isEmpty = true;
        for (int index = 1; index <= numberTeam; index++) {
            for (int i = 0; i < index * 6; i++) {
                if (aHero[i] != null) {
                    isEmpty = false;
                    break;
                }
            }
        }
        return isEmpty;
    }

    public String getHeroNames() {
        String value = "";
        for (HeroInfoEntity hero : aHero) {
            if (hero != null && hero.heroId > 0) {
                value += "," + (ResHero.getHero(hero.heroId) != null ? ResHero.getHero(hero.heroId).getName() : "");
            }
        }
        return value.length() > 0 ? value.substring(1) : value;
    }

    public List<Integer> getHeroKeys() {
        List<Integer> keys = new ArrayList<>();
        for (int index = 0; index < aHero.length / 6; index++) {
            for (int i = 0; i < 6; i++) {
                HeroInfoEntity hero = aHero[index * 6 + i];
                if (hero != null && hero.heroId > 0) keys.add(hero.heroId);
                else keys.add(0);
            }
            if (aPet.length > index && aPet[index] != null) keys.add(aPet[index].getPetId());
            else keys.add(0);
        }
        return keys;
    }

    public int getAvatar() {
        for (HeroInfoEntity hero : aHero) {
            if (hero != null) {
                ResHeroEntity resHero = ResHero.getHero(hero.heroId);
                if (resHero != null) return resHero.getAvatar();
            }
        }
        return 1;
    }

    public Pbmethod.PbListHero.Builder toProto() {
        Pbmethod.PbListHero.Builder builder = Pbmethod.PbListHero.newBuilder();
        for (HeroInfoEntity hero : aHero) {
            if (hero == null) builder.addAHero(Pbmethod.PbHero.newBuilder());
            else builder.addAHero(hero.toProto());
        }
        return builder;
    }


    public String toString() {
        return new Gson().toJson(this);
    }
}
