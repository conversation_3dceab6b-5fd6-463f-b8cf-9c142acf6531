package monster.object;

import monster.config.penum.EventType;
import monster.service.monitor.EventMonitor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class EventPreparedData {

    private List<Long> dropItem = new ArrayList<>();
    public List<Integer> heroKey5Star = new ArrayList<>();

    public EventPreparedData addDropItem(EventType eventType, long... values) {
        long value = eventType.id, number = 1;
        if (values.length > 0) number = values[0];
        int index = 0;
        while (index < dropItem.size()) {
            if (dropItem.get(index) == value) {
                dropItem.set(index + 1, dropItem.get(index + 1) + number);
                break;
            }
            index += 2;
        }
        if (index >= dropItem.size()) { // không tìm thấy dữ liệu
            dropItem.add(value);
            dropItem.add(number);
        }
        return this;
    }

    public void send(int userId, String detailAction) {
        detailAction = detailAction.toLowerCase();
        for (int i = 0; i < dropItem.size(); i += 2) {
            EventMonitor.getInstance().addDropItem(userId, Arrays.asList(dropItem.get(i), dropItem.get(i + 1)));
        }
        if (!heroKey5Star.isEmpty()) {
            if (Arrays.asList("heroic_summon", "summon_energy", "heroic_gem_summon").contains(detailAction)) {
                EventMonitor.getInstance().addHeroicSummon(userId, heroKey5Star);
            } else if (Arrays.asList("prophet_summon").contains(detailAction)) {
                EventMonitor.getInstance().addProphetSummon(userId, heroKey5Star);
            }
        }
    }


    //    public static void checkHeroEvent(int userId, String detailAction, int hereKey) {
//        if (detailAction.equals("prophet_summon") || detailAction.equals("basic_summon")
//                || detailAction.equals("heroic_summon") || detailAction.equals("heroic_gem_summon")
//                || detailAction.equals("casino_rotate") || detailAction.equals("casino_super_rotate")
//                || detailAction.equals("summon_energy")) {
//            ResHeroEntity resHero = ResHero.getHero(hereKey);
//            if (resHero != null && resHero.getRank() == 6) { // rank ss
//                EventMonitor.getInstance().addDropItem(userId, EventType.GET_SS_HERO, 1);
//                if ((detailAction.equals("prophet_summon") || detailAction.equals("heroic_summon")) && (resHero.getHeroFaction() == HeroType.FACTION_LUNISOLAR)) {
//                    EventMonitor.getInstance().addDropItem(userId, EventType.GET_RARE_SS_HERO, 1);
//                }
//            }
//        }
//    }

}
