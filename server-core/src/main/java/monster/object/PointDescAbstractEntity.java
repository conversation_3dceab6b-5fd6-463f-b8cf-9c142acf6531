package monster.object;

import com.google.gson.Gson;
import monster.dao.mapping.main.ResHeroEntity;
import monster.service.resource.ResHero;

public abstract class PointDescAbstractEntity {
    public int clazz, faction;
    public int hero_avatar;
    public Integer[] point;

    public PointDescAbstractEntity(int clazz, int faction, Integer[] point) {
        this.clazz = clazz;
        this.faction = faction;
        this.point = point;
    }

    public PointDescAbstractEntity(Integer[] point) {
        this.clazz = 0;
        this.faction = 0;
        this.point = point;
    }


    public String toString() {
        return new Gson().toJson(this);
    }

    public boolean isBuff(int heroKey) {
        return isHeroAvatarOk(heroKey) && isClazzFactionOk(heroKey);
    }

    public boolean isHeroAvatarOk(int heroKey) {
        if (this.hero_avatar == 0) return true;
        ResHeroEntity resHero = ResHero.getHero(heroKey);
        return resHero.getAvatar() == this.hero_avatar;
    }

    public boolean isClazzFactionOk(int heroKey) {
        if (this.clazz == 0 && this.faction == 0) return true;
        ResHeroEntity resHero = ResHero.getHero(heroKey);
        if (resHero.getHeroClass().value == this.clazz && this.faction == 0) return true;
        if (resHero.getHeroFaction().value == this.faction && this.clazz == 0) return true;
        if (resHero.getHeroClass().value == this.clazz && resHero.getHeroFaction().value == this.faction) return true;
        return false;
    }

//    public boolean isBuff(ResUserHeroSimulatorEntity simulHero) {
//        if (this.clazz == 0 && this.faction == 0) return true;
//        ResHeroEntity resHero = ResHero.getHero(simulHero.getHeroKey());
//        if (resHero.getHeroClass().value == this.clazz && this.faction == 0) return true;
//        if (resHero.getHeroFaction().value == this.faction && this.clazz == 0) return true;
//        if (resHero.getHeroClass().value == this.clazz && resHero.getHeroFaction().value == this.faction) return true;
//        return false;
//    }
}
