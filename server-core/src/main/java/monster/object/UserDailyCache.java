package monster.object;

import grep.helper.DateTime;
import lombok.Data;
import monster.cache.redis.JCache;

/**
 * Một vài data của người chơi được cache theo ngày
 * qua ngày sẽ clear
 */
@Data
public class UserDailyCache {

    private int curDate;
    private boolean redisLogin = false;
    private int numberDailyQuest = 0;

    public UserDailyCache() {
        this.curDate = Integer.parseInt(DateTime.getDateyyyyMMdd());
    }

    public void updateCacheDailyQuest(int userId) {
        String kCache = "DailyQuest:" + DateTime.getDateyyyyMMdd();
        long value = JCache.getInstance().hset(kCache, String.valueOf(userId), String.valueOf(numberDailyQuest));
        if (value > 0) {
            JCache.getInstance().expire(kCache, DateTime.DAY_SECOND);
        }
    }

    public void setNumberDailyQuest(int numberDailyQuest) {
        this.numberDailyQuest = numberDailyQuest;
    }

}
