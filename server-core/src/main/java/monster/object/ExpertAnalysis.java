package monster.object;

import grep.log.Logs;
import monster.game.hero.entity.UserHeroExpertEntity;
import monster.service.battle.dependence.entity.SimulateHero;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExpertAnalysis {

    public Map<Integer, ExpertHeroData> mData = new HashMap<>();
    public boolean isWin;
    public int arenaType;

    public ExpertAnalysis(boolean isWin, int arenaType) {
        this.isWin = isWin;
        this.arenaType = arenaType;
    }

    public void addHero(SimulateHero simulateHero, List<Integer> teammates, int petId) {
        if (simulateHero.id > 0) {
            if (!mData.containsKey(simulateHero.heroId)) mData.put(simulateHero.heroId, new ExpertHeroData(simulateHero.heroId));
            ExpertHeroData heroData = mData.get(simulateHero.heroId);
            heroData.pets.add(petId);
            if (teammates != null) heroData.teammates.addAll(teammates);
            heroData.add(simulateHero);
        }
    }

    public class ExpertHeroData {
        public int heroId;
        public int number; // Số tướng trong trận
        public List<Long> logDamage = new ArrayList<>(), logDef = new ArrayList<>(), logHeal = new ArrayList<>();
        public List<Integer> numberKill = new ArrayList<>();
        public List<Integer> cntNormalAtk = new ArrayList<>();
        public List<Integer> cntSkillAtk = new ArrayList<>();
        public List<Integer> cntCounterAtk = new ArrayList<>();
        public List<Integer> pets = new ArrayList<>();
        public List<Integer> teammates = new ArrayList<>();

        public ExpertHeroData(int heroId) {
            this.heroId = heroId;
        }

        private void add(SimulateHero simulateHero) {
            logDamage.add(simulateHero.heroAnalysis.logDamage);
            logDef.add(simulateHero.heroAnalysis.logDef);
            logHeal.add(simulateHero.heroAnalysis.logHeal);
            numberKill.add(simulateHero.numberKill);
            cntNormalAtk.add(simulateHero.heroAnalysis.cntNormalAttack);
            cntSkillAtk.add(simulateHero.heroAnalysis.cntActiveSkill);
            cntCounterAtk.add(simulateHero.heroAnalysis.cntCounterAttack);
            number++;
        }

        public long getMaxDamage() {
            return logDamage.stream().mapToLong(Long::longValue).max().getAsLong();
        }

        public long getTotalDamage() {
            return logDamage.stream().mapToLong(Long::longValue).sum();
        }

        public long getMaxHeal() {
            return logHeal.stream().mapToLong(Long::longValue).max().getAsLong();
        }

        public long getTotalHeal() {
            return logHeal.stream().mapToLong(Long::longValue).sum();
        }

        public long getMaxDef() {
            return logDef.stream().mapToLong(Long::longValue).max().getAsLong();
        }

        public long getTotalDef() {
            return logDef.stream().mapToLong(Long::longValue).sum();
        }

        public int getMaxKill() {
            return numberKill.stream().mapToInt(Integer::intValue).max().getAsInt();
        }

        public int getTotalNormalAttack() {
            return cntNormalAtk.stream().mapToInt(Integer::intValue).sum();
        }

        public int getTotalSkillAttack() {
            return cntSkillAtk.stream().mapToInt(Integer::intValue).sum();
        }

        public int getTotalCounterAttack() {
            return cntCounterAtk.stream().mapToInt(Integer::intValue).sum();
        }

        public int getTotalCounterAttack() {
            return cntCounterAtk.stream().mapToInt(Integer::intValue).sum();
        }
    }

}
