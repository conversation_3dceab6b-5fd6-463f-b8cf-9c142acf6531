package monster.object;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import lombok.Data;
import monster.cache.CacheStore;
import monster.cache.CacheStoreBeans;

import monster.config.CfgHero;
import monster.game.hero.config.ConfigHeroExpert;
import monster.config.CfgSpecialItem;
import monster.config.lang.Lang;
import monster.config.penum.BattleType;
import monster.config.penum.MaterialType;
import monster.config.penum.NewUserActionType;
import monster.controller.AHandler;
import monster.dao.mapping.UserHeroEntity;
import monster.protocol.CommonProto;
import monster.server.IAction;
import monster.service.user.Actions;
import monster.service.user.BonusBuilder;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class UserSync {

    private boolean checkValidHero(AHandler handler, UserHeroEntity uHero) {
        if (uHero == null || uHero.getLevel() == 0) {
            handler.addErrResponse(handler.getLang(Lang.hero_not_own));
            return false;
        }
        if (CfgHero.isLinhChi(uHero.getHeroId())) {
            handler.addErrResponse(handler.getLang(Lang.hero_tin_soldier));
            return false;
        }
        return true;
    }

    public synchronized void heroLevelUpMax(AHandler handler, byte[] requestData) {
        MyUser mUser = handler.getMUser();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        long idHero = aLong.get(0);
        int toLevel = aLong.get(1).intValue();
        long gold = aLong.get(2), spirit = aLong.get(3), promotion = aLong.get(4);
        // luôn cần gold nên gold ko được == 0
        if (toLevel < 0 || gold <= 0 || spirit < 0 || promotion < 0) {
            handler.addErrResponse(handler.getLang(Lang.err_params) + "(0)");
            return;
        }

        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (!checkValidHero(handler, uHero)) return;

        // kiểm tra tier với level client gửi lên có hợp lệ ko
        if (CfgHero.getMaxLevel(mUser.getUser().getLevel(), uHero) < toLevel) {
            handler.addErrResponse(Lang.err_hero_max_level.formatted(CfgHero.config.referToUserLevel));
            return;
        }
        // kiêm tra tài nguyên client gửi lên có hợp lệ không
        long tmpGold = 0, tmpSpirit = 0, tmpPromotion = 0;
        for (int i = uHero.getLevel(); i < toLevel; i++) {
            tmpGold += CfgHero.mHeroLevelUp.get(i).getGold();
            tmpSpirit += CfgHero.mHeroLevelUp.get(i).getSpirit();
            tmpPromotion += CfgHero.mHeroLevelUp.get(i).getStone();
        }
        if (tmpGold != gold || tmpSpirit != spirit || tmpPromotion != promotion) {
            handler.addErrResponse(handler.getLang(Lang.err_params) + "(2)");
            return;
        }
        // kiểm tra tài nguyên của user có đủ không
        BonusBuilder bonusBuilder = BonusBuilder.newInstance()
                .addGold(-tmpGold)
                .addMaterial(MaterialType.SPIRIT, -tmpSpirit)
                .addMaterial(MaterialType.PROMOTION_STONE, -tmpPromotion);
        var serviceResult = bonusBuilder.process(mUser, "hero_level_up");
        if (serviceResult.success) {
            if (DBJPA.updateNumber("user_hero",
                    Arrays.asList("level", toLevel - uHero.getLevel()),
                    Arrays.asList("id", idHero))) {
                uHero.setLevel(toLevel);
                uHero.calculatePointHero(mUser);
                //Notify special item
                List<Long> notify = new ArrayList<>();
                for (int i = 0; i <= 1; i++) {
                    notify.add(isNotify(uHero, i, true, handler));
                    notify.add(isNotify(uHero, i, false, handler));
                }
                Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
                builder.addAVector(handler.getCommonVector(uHero.getLevel()));
                builder.addAVector(handler.getCommonVector(serviceResult.data));
                //                builder.addAVector(handler.getCommonVector(notify));
                handler.addResponse(builder.build());
                Actions.save(mUser.getUser(), "hero", "level", "id", uHero.getId(), "key", uHero.getHeroId(), "level", uHero.getLevel());
                ConfigHeroExpert.checkUpdateLevel(mUser, uHero.getHeroId(), uHero.getLevel());
                NewUserActionType.HERO_UPGRADE.log(mUser);
            } else {
                handler.addErrResponse();
                BonusBuilder.newInstance()
                        .addGold(tmpGold)
                        .addMaterial(MaterialType.SPIRIT, tmpSpirit)
                        .addMaterial(MaterialType.PROMOTION_STONE, tmpPromotion).process(mUser, "hero_level_up_fail");
            }
        } else serviceResult.writeResponse(handler);
    }

    public synchronized void heroLevelUpNew(AHandler handler, byte[] requestData) {
        MyUser mUser = handler.getMUser();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        long idHero = aLong.get(0);
        if (idHero < 0) {
            handler.addErrResponse(handler.getLang(Lang.hero_test_not_yet_owned));
            return;
        }
        int numberLevelUp = aLong.size() > 1 ? aLong.get(1).intValue() : 1;
        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (!checkValidHero(handler, uHero)) return;

        int maxLevel = CfgHero.getMaxLevel(mUser.getUser().getLevel(), uHero);
        if (uHero.getLevel() >= maxLevel) {
            handler.addErrResponse(Lang.err_hero_max_level.formatted(CfgHero.config.referToUserLevel));
            return;
        }
        int maxLevelUp = numberLevelUp + uHero.getLevel() >= maxLevel ? maxLevel - uHero.getLevel() : numberLevelUp;
        int gold = 0, spirit = 0, stones = 0;
        for (int i = 0; i < maxLevelUp; i++) {
            long requiredGold = CfgHero.mHeroLevelUp.get(uHero.getLevel() + i).getGold();
            long requiredSpirit = CfgHero.mHeroLevelUp.get(uHero.getLevel() + i).getSpirit();
            long requiredStones = CfgHero.mHeroLevelUp.get(uHero.getLevel() + i).getStone();
            if (mUser.getUser().getGold() >= gold + requiredGold
                    && mUser.getResources().getMaterial(MaterialType.SPIRIT).getNumber() >= spirit + requiredSpirit
                    && mUser.getResources().getMaterial(MaterialType.PROMOTION_STONE).getNumber() >= stones + requiredStones) {
                gold += requiredGold;
                spirit += requiredSpirit;
                stones += requiredStones;
                numberLevelUp = i + 1;
            } else numberLevelUp = i;
        }
        if (numberLevelUp == 0) {
            handler.addErrResponse(handler.getLang(Lang.err_not_enough_material));
            return;
        }
        BonusBuilder bonusBuilder = BonusBuilder.newInstance()
                .addGold(-gold)
                .addMaterial(MaterialType.SPIRIT, -spirit)
                .addMaterial(MaterialType.PROMOTION_STONE, -stones);
        var serviceResult = bonusBuilder.process(mUser, "hero_level_up");
        if (serviceResult.success) {
            if (DBJPA.updateNumber("user_hero", Arrays.asList("level", numberLevelUp), Arrays.asList("id", idHero))) {
                uHero.setLevel(uHero.getLevel() + numberLevelUp);
                uHero.calculatePointHero(mUser);

                //Notify special item
                List<Long> notify = new ArrayList<>();
                for (int i = 0; i <= 1; i++) {
                    notify.add(isNotify(uHero, i, true, handler));
                    notify.add(isNotify(uHero, i, false, handler));
                }
                //                aResult.addAll(notify);
                //                aResult.add(0, (long) uHero.getTier());
                //                aResult.add(0, (long) uHero.getLevel());
                handler.addResponse(Pbmethod.CommonVector.newBuilder().addALong(uHero.getLevel())
                        .addAllALong(serviceResult.data).build());
                Actions.save(mUser.getUser(), "hero", "level", "id", uHero.getId(), "key", uHero.getHeroId(), "level", uHero.getLevel());
                ConfigHeroExpert.checkUpdateLevel(mUser, uHero.getHeroId(), uHero.getLevel());
                NewUserActionType.HERO_UPGRADE.log(mUser);
            } else {
                handler.addErrResponse();
                BonusBuilder.newInstance()
                        .addGold(gold)
                        .addMaterial(MaterialType.SPIRIT, spirit)
                        .addMaterial(MaterialType.PROMOTION_STONE, stones).process(mUser, "hero_level_up_fail");
            }
        } else serviceResult.writeResponse(handler);
    }

    public synchronized void heroLevelUp(AHandler handler, byte[] requestData) {
        MyUser mUser = handler.getMUser();
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        long idHero = aLong.get(0);
        int numberLevelUp = aLong.size() > 1 ? aLong.get(1).intValue() : 1;
        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (!checkValidHero(handler, uHero)) return;

        int maxLevel = CfgHero.getMaxLevel(mUser.getUser().getLevel(), uHero);
        if (uHero.getLevel() >= maxLevel) {
            handler.addErrResponse(Lang.err_hero_max_level.formatted(CfgHero.config.referToUserLevel));
            return;
        }
        if (numberLevelUp + uHero.getLevel() >= maxLevel)
            numberLevelUp = maxLevel - uHero.getLevel();

        BonusBuilder bonusBuilder = BonusBuilder.newInstance().addUsedMaterial(
                CfgHero.getLevelUpResource(uHero.getLevel(), uHero.getLevel() + numberLevelUp)
        );
        var serviceResult = bonusBuilder.process(mUser, "hero_level_up");
        if (serviceResult.success) {
            if (DBJPA.updateNumber("user_hero", Arrays.asList("level", numberLevelUp), Arrays.asList("id", idHero))) {
                uHero.setLevel(uHero.getLevel() + numberLevelUp);
                uHero.calculatePointHero(mUser);
                handler.addResponse(Pbmethod.CommonVector.newBuilder()
                        .addALong(uHero.getLevel()).addAllALong(serviceResult.data)
                        .build());
                Actions.save(mUser.getUser(), "hero", "level", "id", uHero.getId(), "key", uHero.getHeroId(), "level", uHero.getLevel());
                ConfigHeroExpert.checkUpdateLevel(mUser, uHero.getHeroId(), uHero.getLevel());
                NewUserActionType.HERO_UPGRADE.log(mUser);
            } else {
                handler.addErrResponse();
                BonusBuilder.newInstance().add(CfgHero.getLevelUpResource(uHero.getLevel(), uHero.getLevel() + numberLevelUp)).process(mUser, "hero_level_up_fail");
            }
        } else serviceResult.writeResponse(handler);
    }

    private long isNotify(UserHeroEntity uHero, int typeSpecialItem, boolean isEnhance, AHandler handler) {
        MyUser mUser = handler.getMUser();
        JsonArray arrConvertPrice;
        List<Long> lock = CfgSpecialItem.getLock(uHero, typeSpecialItem);
        if (isEnhance) {
            if (uHero.getLevelEnhance()[typeSpecialItem] == CfgSpecialItem.getMaxEnhance()) return 0; // Nếu max
            if (uHero.getLevelEnhance()[typeSpecialItem] == lock.get(1)) return 0; // Nếu max theo lock
            arrConvertPrice = GsonUtil.parseFromListLong(CfgSpecialItem.getEnhancePrice(uHero, typeSpecialItem, 1));
        } else {
            if (uHero.getTierUpgrade()[typeSpecialItem] == CfgSpecialItem.getMaxUpgrade()) return 0; // Nếu max
            if (uHero.getTierUpgrade()[typeSpecialItem] == lock.get(3)) {
                return 0; // Nếu max theo lock
            }
            arrConvertPrice = GsonUtil.parseFromListLong(CfgSpecialItem.getUpgradePrice(uHero, typeSpecialItem, 1));
        }
        if (mUser.checkPrice(handler, arrConvertPrice, true)) return 1;
        else return 0;
    }

    public synchronized boolean quickAttackButtonClick(AHandler handler, BattleType type) {
        String k = "%s_%s".formatted(handler.getUser().getId(), type.value);
        CacheStore<String> cacheStore = CacheStoreBeans.getSecond(String.class, 3);
        if (cacheStore.get(k) != null) {
            cacheStore.add(k, k);
            return true;
        }
        handler.addResponse(IAction.UPDATE_BONUS, Pbmethod.CommonVector.newBuilder().build());
        return false;
    }
}
