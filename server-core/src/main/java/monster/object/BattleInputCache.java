package monster.object;

import grep.helper.Filer;
import grep.helper.GZip;
import grep.helper.StringHelper;
import grep.log.Logs;
import lombok.Builder;
import monster.config.penum.BattleType;
import monster.controller.AHandler;
import monster.service.battle.dependence.BattleResultEntityNew;
import protocol.Pbmethod;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Builder
public class BattleInputCache {
    public long battleId;
    public List<Long> values;
    public BattleResultEntityNew battleResult;
    //    public BattleResultEntity oldBattleResult;
    public BattleTeam team, defTeam;
    public byte[] data, outputBytes;
    @Builder.Default
    public List<List<Long>> output = new ArrayList<>();
    @Builder.Default
    public List<List<String>> outputStr = new ArrayList<>();
    public BattleType battleType;
    public long highestAtkPoint;
    @Builder.Default
    public long systemTime = System.currentTimeMillis();

    public BattleInputCache addOutput(List<Long> data) {
        output.add(data);
        return this;
    }

    public BattleInputCache addOutput(int index, List<Long> data) {
        output.add(index, data);
        return this;
    }

    public Pbmethod.ListCommonVector toProto() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < output.size(); i++) {
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            cmm.addAllALong(output.get(i));
            if (outputStr.size() > i) cmm.addAllAString(outputStr.get(i));
            builder.addAVector(cmm);
        }
        return saveOutput(builder.build());
    }

    public void writeResponse(AHandler handler) {
        handler.addResponse(toProto());
    }

    public Pbmethod.ListCommonVector saveOutput(Pbmethod.ListCommonVector listCommonVector) {
        try {
            String filename = battleResult.logPath + ".output";
            if (!new File(filename).exists()) {
                this.outputBytes = GZip.compress(listCommonVector.toByteArray());
                if (battleResult != null && !StringHelper.isEmpty(battleResult.logPath)) {
                    Filer.saveBinFile(filename, outputBytes);
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return listCommonVector;
    }

    public BattleInputCache saveOutputEarly() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < output.size(); i++) {
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            cmm.addAllALong(output.get(i));
            if (outputStr.size() > i) cmm.addAllAString(outputStr.get(i));
            builder.addAVector(cmm);
        }
        saveOutput(builder.build());
        return this;
    }

    public void checkVerifyTime(MyUser mUser, BattleType battleType, int level) {
        long timeVerify = System.currentTimeMillis() - systemTime;
        if (timeVerify < 3000) { // verify quá nhanh logs lại
            //            Telegram.sendPrivate(String.format("verifyTime userId=%s battleType=%s level=%s", mUser.getUser().getId(), battleType.value, level));
        }
    }
}
