package monster.object;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.helper.StringHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by vieth_000 on 2/22/2017.
 */
public class UserInt {
    public static final int NUMBER_VALUE = 50;
    //
    public static final int MEDAL_USED = 0;
    public static final int SUMMON_BASIC = 1;
    public static final int SUMMON_HEROIC = 2;
    public static final int SUMMON_ENERGY = 3;
    public static final int REFRESH_TAVERN = 4;
    public static final int ONLINE_TIME = 5;
    public static final int ONLINE_RECEIVE = 6;
    public static final int MIDAS_1_TIME = 7;
    public static final int MIDAS_DAY = 8;
    public static final int MIDAS_2 = 9;
    public static final int MIDAS_3 = 10;
    public static final int HIDDEN_VIP = 11;
    public static final int AWARD_ARENA_CRYSTAL_SEASON = 12;
    public static final int EVENT_MAKE_FRIEND = 13;
    public static final int AWARD_ARENA_TRIAL = 14;
    public static final int AWARD_ARENA_SWAP = 15;
    public static final int BROKEN_SPACE_ID = 16;
    public static final int BROKEN_SPACE_BUY_NUMBER = 17;
    public static final int ALBUM_NUMBER_OPEN_FRAGMENT = 18;
    public static final int TAVERN_TUTORIAL = 19;
    public static final int SHARE_BATTLE_0 = 20;
    public static final int SHARE_BATTLE_1 = 21;
    public static final int SHARE_BATTLE_2 = 22;
    public static final int DATE_KEY = 23;
    public static final int ALTAR_SETTING = 24;
    public static final int GUARANTEED_SUMMON_HEROIC = 25;
    public static final int BONUS_SUMMON_HEROIC = 26;
    public static final int BONUS_SUMMON_1 = 27; //1 đã nhận , 0 là chưa nhận
    public static final int BONUS_SUMMON_2 = 28;
    public static final int BONUS_SUMMON_3 = 29;
    public static final int LABYRINTH_MAX_ROW = 32; // lần đầu nhận bonus 2 summon
    public static final int GUARANTEED_SUMMON_HEROIC_4_STAR = 33; // đảm bảo summon 4 sao

    public static final int FIRST_HERO_6STAR = 35; // Lần đầu nâng cấp 1 tướng lên 6 sao
    public static final int FIRST_SUMMON_SSR = 36; // Chiêu mộ được tướng SSR đầu tiên trong tính năng Chiêu mộ
    public static final int FIRST_HERO_LEVEL_80 = 37; // lần đầu nâng cấp có tướng level > 80
    public static final int BONUS_SUMMON_4 = 38;
    public static final int BONUS_SUMMON_5 = 39;
    public static final int GUARANTEED_SUMMON_BASIC = 40;
    public static final int FIRST_SUMMON_250 = 41;
    public static final int LEVEL_INDENTURE_MAX = 42;
    public static final int BLOOD_RESONANCE_SLOT = 43; // Slot cộng hưởng huyết trú thuật

    //    public static final int SIGMA_MAX_DAMAGE = 38; // sigma

    //
    public List<Integer> aInt;

    public UserInt(String dataInt) {
        if (StringHelper.isEmpty(dataInt)) dataInt = "[]";
        aInt = new Gson().fromJson(dataInt, new TypeToken<ArrayList<Integer>>() {
        }.getType());
        while (aInt.size() < NUMBER_VALUE) {
            aInt.add(0);
        }
    }

    public void addValue(int index, int value) {
        aInt.set(index, aInt.get(index) + value);
    }

    public int getValue(int index) {
        return aInt.get(index);
    }

    public UserInt setValue(int index, int value) {
        aInt.set(index, value);
        return this;
    }

    public String toString() {
        return new Gson().toJson(this);
    }

    public boolean setAndUpdateDB(int userId, int index, int value) {
        setValue(index, value);
        return update(userId);
    }

    public boolean update(long userId) {
        return DBJPA.update("user_data", Arrays.asList("data_int", StringHelper.toDBString(aInt)), Arrays.asList("user_id",
                String.valueOf(userId)));
    }

}
