package monster.object;

import grep.helper.DateTime;

import java.util.ArrayList;
import java.util.List;

public class ScoutBossObject {
    public List<Integer> idBoss;
    public List<Integer> levelBoss;
    public List<Long> currHp;
    public List<Long> hp;
    public List<Long> revived;
    public List<Long> bonus;
    public long power;
    public long bornTime;

    public ScoutBossObject() {
        this.idBoss = new ArrayList<>();
        this.levelBoss = new ArrayList<>();
        this.currHp = new ArrayList<>();
        this.hp = new ArrayList<>();
        this.bonus = new ArrayList<>();
        this.revived = new ArrayList<>();
        this.bornTime = System.currentTimeMillis();
    }

    public void add(int level, long curHp, long hp) {
        this.levelBoss.add(level);
        this.currHp.add(curHp);
        this.hp.add(hp);
        this.revived.add(0L);
    }

    public long getRevived(int index) {
        if (revived == null || index >= revived.size()) return 0;
        return revived.get(index);
    }

    public void setRevived(int index, long value) {
        if (revived == null) revived = new ArrayList<>();
        while (revived.size() <= index) revived.add(0L);
        revived.set(index, value);
    }

    public boolean bossDie() {
        if (System.currentTimeMillis() - bornTime > DateTime.DAY_MILLI_SECOND) return true;
        return currHp.stream().mapToLong(Long::longValue).sum() == 0;
    }
}
