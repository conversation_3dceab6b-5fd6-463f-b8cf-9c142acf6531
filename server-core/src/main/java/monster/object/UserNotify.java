package monster.object;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import monster.config.penum.NotifyType;

import java.util.Arrays;
import java.util.List;

public class UserNotify {
    public List<Long> values;
    int userId;

    public UserNotify(String dataNotify, int userId) {
        this.userId = userId;
        if (StringHelper.isEmpty(dataNotify)) dataNotify = "[]";
        values = GsonUtil.strToListLong(dataNotify);
        checkSize(NotifyType.values().length + 1);
    }

    void checkSize(int index) {
        while (values.size() < index) values.add(0L);
    }

    public void addValue(NotifyType notifyType, long value) {
        values.set(notifyType.value, values.get(notifyType.value) + value);
    }

    public long getValue(NotifyType notifyType) {
        return values.get(notifyType.value);
    }

    public UserNotify setValue(NotifyType notifyType, long value) {
        values.set(notifyType.value, value);
        return this;
    }

    public boolean isNotify(NotifyType notifyType) {
        return getValue(notifyType) == 1;
    }

    public String toString() {
        return StringHelper.toDBString(values);
    }

    public boolean update() {
        return DBJPA.update("user_data", Arrays.asList("data_notify", StringHelper.toDBString(values)), Arrays.asList("user_id", String.valueOf(userId)));
    }

}
