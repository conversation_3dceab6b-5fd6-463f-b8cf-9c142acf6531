package monster.cache.redis;

import grep.log.Logs;
import monster.server.AppConfig;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * <PERSON><PERSON>ng cho dữ liệu sự kiện
 * event_master
 */
public class JCacheEvent extends IRedis {
    static JCacheEvent instance;

    public static JCacheEvent getInstance() {
        if (instance == null) {
            instance = new JCacheEvent();
        }
        return instance;
    }

    public JCacheEvent() {
        this.host = AppConfig.cfg.redis.redisEvent;
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(10);
        config.setMaxIdle(5);
        pool = new JedisPool(config, this.host, 6379);
    }

}
