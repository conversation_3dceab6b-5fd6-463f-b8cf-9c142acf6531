package monster.cache.redis;

import com.google.gson.Gson;
import grep.helper.GUtil;
import grep.log.slib_Logger;
import monster.cache.JedisService;
import monster.dao.mapping.BattleTestEntity;
import monster.service.battle.dependence.BattleResultEntityNew;
import org.slf4j.Logger;
import protocol.Pbmethod;
import redis.clients.jedis.JedisPubSub;

public class BattleSubscriber extends JedisPubSub {

    public BattleSubscriber() {
    }

    @Override
    public void onMessage(String channel, String message) {
        int pos = message.indexOf("@");
        try {
            int service = Integer.parseInt(message.substring(0, pos));
            message = message.substring(pos + 1);

            getLogger().info(String.format("%s -> %s", JedisService.serviceName.get(service), message));
            switch (service) {
                case JedisService.BATTLE_SIMULATE:
                    simulate(message);
                    break;
                case JedisService.BATTLE_RESULT:
                    simulateResult(message);
                    break;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            getLogger().error(GUtil.exToString(ex));
        }
    }

    private void simulate(String message) {
        String gameChannel = message.substring(0, message.indexOf("@"));
        message = message.substring(message.indexOf("@") + 1);
        String channelId = message.substring(0, message.indexOf("@"));
        message = message.substring(message.indexOf("@") + 1);

        BattleTestEntity battleTestEntity = new Gson().fromJson(message, BattleTestEntity.class);
        BattleResultEntityNew battleResult = battleTestEntity.replayNew();
        String response = new Gson().toJson(battleResult);
        JCache.getInstance().publish(gameChannel, String.format("%s@%s@%s", JedisService.BATTLE_RESULT, channelId, response));
    }

    private void simulateResult(String message) {
        String channelId = message.substring(0, message.indexOf("@"));
        message = message.substring(message.indexOf("@") + 1);

        BattleResultEntityNew battleResult = new Gson().fromJson(message, BattleResultEntityNew.class);

        Pbmethod.ResponseData.Builder response = Pbmethod.ResponseData.newBuilder();

        Pbmethod.PbAction.Builder builder = Pbmethod.PbAction.newBuilder();
        builder.setActionId(98);
        builder.setData(battleResult.toReplayProto().build().toByteString());

        response.addAAction(builder.build());

//        writeResponse(BattleUtil.getChannel(channelId), response.build().toByteArray());
    }

    private void writeResponse(byte[] data) {
//        if (channel == null) {
//            Logs.warn("channel null");
//            return;
//        }
//
//        // Decide whether to close the connection or not.
//        boolean keepAlive = false;//isKeepAlive(request);
//
//        // Build the response object.
//        HttpResponse response = new DefaultHttpResponse(HTTP_1_1, OK);
////        response.setContent(ChannelBuffers.copiedBuffer(buf.toString(), CharsetUtil.UTF_8));
////        System.out.println("Length ===== " + getResponse().length);
//        response.setContent(ChannelBuffers.copiedBuffer(data));
//        response.setHeader(CONTENT_TYPE, "application/json");
//
//        if (keepAlive) {
//            // Add 'Content-Length' header only for a keep-alive connection.
//            response.setHeader(CONTENT_LENGTH, response.getContent().readableBytes());
//        }
//
//        // Encode the cookie.
////        String cookieString = request.getHeader(COOKIE);
////        if (cookieString != null) {
////            CookieDecoder cookieDecoder = new CookieDecoder();
////            Set<Cookie> cookies = cookieDecoder.decode(cookieString);
////            if (!cookies.isEmpty()) {
////                // Reset the cookies if necessary.
////                CookieEncoder cookieEncoder = new CookieEncoder(true);
////                for (Cookie cookie : cookies) {
////                    cookieEncoder.addCookie(cookie);
////                }
////                response.addHeader(SET_COOKIE, cookieEncoder.encode());
////            }
////        }
//
//        // Write the response.
//        if (channel.isWritable()) {
//            ChannelFuture future = channel.write(response);
//
//            // Close the non-keep-alive connection after the write operation is done.
//            if (!keepAlive) {
//                future.addListener(ChannelFutureListener.CLOSE);
//            }
//        } else channel.close();
    }

    @Override
    public void onPMessage(String pattern, String channel, String message) {

    }

    @Override
    public void onSubscribe(String channel, int subscribedChannels) {

    }

    @Override
    public void onUnsubscribe(String channel, int subscribedChannels) {

    }

    @Override
    public void onPUnsubscribe(String pattern, int subscribedChannels) {

    }

    @Override
    public void onPSubscribe(String pattern, int subscribedChannels) {

    }

    public Logger getLogger() {
        return slib_Logger.redis();
    }

}
