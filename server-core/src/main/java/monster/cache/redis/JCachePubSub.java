package monster.cache.redis;

import monster.config.CfgServer;
import monster.pubsub.Subscriber;
import monster.server.AppConfig;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * pub sub data sang GameTask
 */
public class JCachePubSub extends IRedis {
    public static String gameChannel = "server", chatChannel = "chat";
    public static String allGameChannel = "GameChannel";

    static JCachePubSub instance;

    public static JCachePubSub getInstance() {
        if (instance == null) {
            instance = new JCachePubSub();
            instance.PREFIX = "";
        }
        return instance;
    }

    public JCachePubSub() {
        this.host = AppConfig.cfg.redis.pubSubHost;
        init();
    }

    public void init() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(10);
        config.setMaxIdle(5);
        pool = new JedisPool(config, host, 6379);
        gameChannel = CfgServer.isRealServer() ? String.format("server%s", CfgServer.serverId) : String.format("test%s", CfgServer.serverId);
    }

    public void subscriberGameServer() {
        subscriberToChannel(allGameChannel, new Subscriber());
        if (!CfgServer.isSimulateServer()) subscriberToChannel(gameChannel, new Subscriber());
    }

    public void subscriberTelegram() {
        subscriberToChannel(AppConfig.cfg.telegram.redisChannel, new Subscriber());
        //        subscriberToChannel(AppConfig.cfg.telegram.redisChannel, new SubscriberTelegram());
    }

    public void subscriberTest() {
        subscriberToChannel(AppConfig.cfg.telegram.redisChannel + "_test", new Subscriber());
    }

    public void publishTest(String msg) {
        publish(AppConfig.cfg.telegram.redisChannel + "_test", msg);
    }

    public void publishAllGame(String msg) {
        publish(allGameChannel, msg);
    }

}
