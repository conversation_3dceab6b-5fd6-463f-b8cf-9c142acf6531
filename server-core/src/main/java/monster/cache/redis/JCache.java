package monster.cache.redis;

import grep.log.Config;
import monster.server.AppConfig;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Các task chính trong game server
 */
public class JCache extends IRedis {
    static JCache instance, prefixInstance, jobInstance;

    public static JCache getPrefixInstance() {
        if (prefixInstance == null) {
            prefixInstance = new JCache();
            prefixInstance.PREFIX = Config.getServerId() + ":";
        }
        return prefixInstance;
    }

    public static JCache getInstance() {
        if (instance == null) {
            instance = new JCache();
        }
        return instance;
    }

    public static JCache getJobInstance() {
        if (jobInstance == null) {
            jobInstance = new JCache();
        }
        return jobInstance;
    }

    public JCache() {
        this.host = AppConfig.cfg.redis.gameHost;
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(100);
        config.setMaxIdle(5);
        pool = new JedisPool(config, this.host, 6379);
    }

}
