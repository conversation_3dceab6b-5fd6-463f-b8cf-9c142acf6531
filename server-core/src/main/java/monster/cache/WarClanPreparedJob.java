package monster.cache;

import grep.database.DBJPA;
import grep.log.Config;
import monster.config.CfgLimitFunction;
import monster.config.CfgServer;
import monster.config.CfgWarClan;
import monster.dao.WarClanDAO;
import monster.dao.mapping.ClanEntity;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.WarClanHistoryEntity;
import monster.dao.mapping.WarClanLogPreparedEntity;
import monster.dao.mapping.main.ConfigEntity;
import monster.server.Main;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class WarClanPreparedJob {
    WarClanDAO dao = new WarClanDAO();
    boolean preparedData = true;

    public static void main(String args[]) throws Exception {
        new WarClanPreparedJob().process();
        System.exit(0);
    }

    private void process() throws Exception {
        // config for event
        Config.load("config.xml");
        CfgServer.serverId = Config.getInt("config.server.id");
        CfgServer.serverType = Config.getString("config.server.type");
        DBJPA.init("grepgame");
        initConfig();

        DBJPA.rawSQL("delete from war_clan_log_prepared", "delete from war_clan_user_prepared");
        int warId = CfgWarClan.getNextWarId();
        System.out.println("warId = " + warId);
        findOpponent(warId);
    }

    private void initConfig() throws Exception {
        Main.initConfig();
        CfgServer.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_server")).getV());
        CfgWarClan.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_warClan")).getV());
        CfgLimitFunction.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_limitFunction")).getV());
    }

    public void findOpponent(int warId) {
        int fixMember = CfgWarClan.config.memberRequired.get(0).intValue();
        int numberHistory = 5;
        List<ClanEntity> aClan = dao.getListValidClan(fixMember);
        List<Integer> solvedClanId = new ArrayList<>();

        String manualMatch = ((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "warClanMatch")).getV();
        System.out.println("manualMatch = " + manualMatch);
        if (manualMatch != null && manualMatch.length() > 0) {
            try {
                String[] tmp = manualMatch.split(",");
                for (int i = 0; i < tmp.length; i += 2) {
                    ClanEntity clan1 = findClan(aClan, Integer.parseInt(tmp[i]));
                    ClanEntity clan2 = findClan(aClan, Integer.parseInt(tmp[i + 1]));
                    if (clan1 != null && clan2 != null) {
                        matchClan(warId, solvedClanId, clan1, clan2, fixMember);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        for (int i = 0; i < aClan.size(); i++) {
            ClanEntity clan = aClan.get(i);
            if (!solvedClanId.contains(clan.getId())) {
                List<WarClanHistoryEntity> lastOppClan = dao.getWarHistoryLimit(clan.getId(), numberHistory); // numberHistory clan gần đây để không gặp lại
                List<Integer> lastOppId = lastOppClan.stream().map(WarClanHistoryEntity::getClanId).collect(Collectors.toList());
                for (int index = i + 1; index < aClan.size(); index++) {
                    ClanEntity oppClan = aClan.get(index);
                    if (!solvedClanId.contains(oppClan.getId())
                            && !lastOppId.contains(oppClan.getId())
                            && oppClan.getId() != clan.getId()) {
                        matchClan(warId, solvedClanId, clan, oppClan, fixMember);
                        break;
                    }
                }
            }
        }
    }

    public ClanEntity findClan(List<ClanEntity> aClan, int id) {
        return aClan.stream().filter(clanEntity -> clanEntity.getId() == id).findFirst().orElse(null);
    }

    public void matchClan(int warId, List<Integer> solvedClan, ClanEntity atkClan, ClanEntity defClan, int fixMember) {
        solvedClan.add(atkClan.getId());
        solvedClan.add(defClan.getId());

        System.out.println(String.format("Match clan (%s) %s - (%s) %s", atkClan.getId(), atkClan.getName(), defClan.getId(), defClan.getName()));
        List<UserEntity> atkMember = dao.getListSortedUser(atkClan.getId());
        List<UserEntity> defMember = dao.getListSortedUser(defClan.getId());

        fixMember = Math.min(atkMember.size(), defMember.size());

        DBJPA.save(new WarClanLogPreparedEntity(warId, atkClan, defClan, fixMember));
        DBJPA.save(new WarClanLogPreparedEntity(warId, defClan, atkClan, fixMember));
    }

}
