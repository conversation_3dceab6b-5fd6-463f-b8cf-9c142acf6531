package monster.cache;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import monster.config.CfgLimitFunction;
import monster.config.CfgServer;
import monster.config.CfgWarClan;
import monster.dao.mapping.main.ConfigEntity;
import monster.server.AppInit;
import monster.server.Main;
import monster.service.monitor.Telegram;

import java.math.BigInteger;
import java.util.Collections;
import java.util.List;

public class SettingServerList {

    public static void main(String args[]) throws Exception {
        new SettingServerList().process();
        System.exit(0);
    }

    private void process() throws Exception {
        // config for event
        AppInit.initAll(false);
        setting();
    }

    private void setting() {
        ConfigEntity cfg = DBJPA.getUnique("dson_main.config_api", ConfigEntity.class, "k", "server_list");
        int lastServerId = ((ConfigEntity) DBJPA.getUnique("dson_main.config_api", ConfigEntity.class, "k", "server_list_count")).getVInt();
        ConfigEntity disableServer = DBJPA.getUnique("dson_main.config", ConfigEntity.class, "k", "serverDisabledRegister");

        System.out.println("Server list = " + cfg.getV());
        System.out.println("Disable server = " + disableServer.getV());

        List<Integer> disabledId = GsonUtil.strToListInt(disableServer.getV());
        int chooseServerId = 0, minDAU = 1000000;
        int maxServerId = 0, maxServerDAU = 0;

        List values = DBJPA.getList("select server, count(*) number from dson.user where user.server>3 group by server");
        for (int i = 0; i < values.size(); i++) {
            Object[] data = (Object[]) values.get(i);
            int serverId = (Integer) data[0];
            int number = ((BigInteger) data[1]).intValue();

            System.out.println(serverId + " " + number);

            if (number < minDAU && !disabledId.contains(serverId) && serverId < lastServerId) {
                minDAU = number;
                chooseServerId = serverId;
            }
            if (maxServerId < serverId) {
                maxServerId = serverId;
                maxServerDAU = number;
            }
        }

        if (maxServerDAU >= 9000) {
            Telegram.sendNotify("\uD83D\uDE0E Server mới nhất DAU = " + maxServerDAU);
        }

        System.out.println("maxDAU = " + minDAU);
        System.out.println("chooseServerId = " + chooseServerId);

        JsonArray arr = GsonUtil.parseJsonArray(cfg.getV());
        List<JsonElement> a = arr.asList();
        while (true) {
            Collections.shuffle(a);
            JsonObject obj = a.get(0).getAsJsonObject();
            int id = obj.get("id").getAsInt();
            if (id == chooseServerId) break;
        }
        System.out.println(a);

        DBJPA.update("dson_main.config_api", List.of("v", GsonUtil.toJson(a)), List.of("k", "server_list"));

    }

    private void initConfig() throws Exception {
        Main.initConfig();
        CfgServer.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_server")).getV());
        CfgWarClan.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_warClan")).getV());
        CfgLimitFunction.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_limitFunction")).getV());
    }
}
