package monster.cache.job;

import grep.database.DBJPA;
import grep.helper.*;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.*;
import monster.dao.EventDAO;
import monster.dao.mapping.EventRewardEntity;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserMailEntity;
import monster.dao.mapping.main.ResMonsterTeam;
import monster.dao.mapping.main.ResRankingReward;
import monster.game.arena.config.ConfigArenaServer;
import monster.game.arena.dao.ArenaServerDAO;
import monster.game.arena.dao.ArenaServerJobDAO;
import monster.game.arena.entity.*;
import monster.game.arena.service.ArenaServerService;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.server.Main;
import monster.server.config.Guice;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.monitor.Telegram;
import monster.service.resource.ResMonster;
import monster.service.user.Bonus;
import monster.service.user.UserTeam;
import monster.task.dbcache.MailCreatorCache;

import java.util.*;
import java.util.stream.Collectors;

public class ArenaServerRanking extends AJob {

    ArenaServerJobDAO jobDAO;
    ArenaServerDAO arenaServerDAO;
    EventDAO eventDAO;
    ArenaServerState state;
    ArenaServerService service;
    List<Integer> eventIds;

    public static void main(String[] args) throws Exception {
        ArenaServerRanking ranking = new ArenaServerRanking();
        ranking.myInit();
//        ranking.whatToProcess(args.length > 0 ? args[0] : "");
        ranking.setupRound1();
        System.exit(0);
    }

    void myInit() throws Exception {
        init();
        Main.initConfig();
        functionName = "Tinh Anh";
        jobDAO = Guice.getInstance(ArenaServerJobDAO.class);
        arenaServerDAO = Guice.getInstance(ArenaServerDAO.class);
        eventDAO = Guice.getInstance(EventDAO.class);
        state = Guice.getInstance(ArenaServerState.class);
        service = Guice.getInstance(ArenaServerService.class);
        eventIds = new ArrayList<>();
        eventIds.add(state.getEventId(1));
        eventIds.add(state.getEventId(330));
    }

    void whatToProcess(String args) throws Exception {
        int roundId = state.getRoundId();
        Calendar calendar = Calendar.getInstance();
        int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
        System.out.println("whatToProcess roundId = " + roundId);
        int numberDayEvent = (int) DateTime.numberDayPassed(ConfigArenaServer.getDayStartCurrentEvent(), DateTime.getStartOfDay(new Date()));
        System.out.println("numberDay = " + numberDayEvent);
        System.out.println("hourOfDay = " + hourOfDay);
        if (args.equals("setupRound1")) {
            if (roundId == 1 && calendar.get(Calendar.HOUR_OF_DAY) < 1) {
                process("setupRound1");
            }
        } else if (args.equals("round") && hourOfDay == 22) {
            switch (roundId) {
                case 1 -> process("round1");
                case 2 -> process("round2");
                case 3 -> process("round3");
                case 4 -> process("round4");
            }
        } else if (args.equals("cacheRank") && hourOfDay == 23) {
            switch (roundId) {
                case 1 -> process("cacheRankingRound1");
                case 2 -> process("cacheRankingRound2");
                case 3 -> process("cacheRankingRound3");
                case 4 -> process("cacheRankingRound4");
            }
        } else if (roundId == 5) {
            int finalState = state.getFinalState();
            if (args.equals("finalRound")) {
                if (finalState == 1) process("finalRound1");
                else if (finalState == 2) process("finalRound2");
                else if (finalState == 3) process("finalRound3");
                else if (finalState == 4) process("finalRound4");
            } else if (args.equals("cacheFinalRound") && finalState == 4 && hourOfDay == 21) {
                process("cacheFinalRound");
            }
        } else if (roundId == -1 && args.equals("sendBonus")) { // kết thúc sk trao thưởng
            for (Integer eventId : eventIds) {
                EventRewardType eventType = EventRewardType.ARENA_SERVER_REWARD;
                int ret = eventDAO.needReward(0, eventType.value, String.valueOf(eventId));
                if (ret == 0) {
                    Telegram.sendNotify(EmojText.WARNING.get("Arena server reward error eventId=" + eventType));
                } else if (ret == 1) {
                    System.out.println("Ranking rồi");
                }
                int numberDay = (int) DateTime.numberDayPassed(ConfigArenaServer.getDayStartCurrentEvent(), DateTime.getStartOfDay(new Date()));
                if (numberDay == 10) {
                    if (eventDAO.save(new EventRewardEntity(0, eventType.value, String.valueOf(eventId)))) {
                        sendBonus(eventId);
                        sendBettingBonus(eventId);
                    }
                }
            }
        }
    }

    void process(String action) throws Exception {
        sendNotify("ArenaServer process " + action);
        switch (action) {
            case "setupRound1" -> setupRound1();
            case "round1" -> processRound(1, 4);
            case "round2" -> processRound(2, 4);// process round 2
            case "round3" -> processRound(3, 2); // process round 3
            case "round4" -> processRound4(4, 2); // process round 4
            case "finalRound1" -> processFinalRound(1); // vòng 16
            case "finalRound2" -> processFinalRound(2); // tứ kết
            case "finalRound3" -> processFinalRound(3); // bán kết
            case "finalRound4" -> processFinalRound(4); // chung kết
            case "cacheRankingRound1" -> cacheUserRankQualified(1);
            case "cacheRankingRound2" -> cacheUserRankQualified(2);
            case "cacheRankingRound3" -> cacheUserRankQualified(3);
            case "cacheRankingRound4" -> cacheUserRankQualified(4);
            case "cacheFinalRound" -> cacheFinalRound();
            //            case "sendBonus" -> sendBonus();
            //            case "sendBettingBonus" -> sendBettingBonus();
        }
        sendNotify("ArenaServer process finish");
    }

    void sendBettingBonus(int eventId) {
        System.out.println("betting -> eventId = " + eventId);
        List<List<Long>> rewardRights = ConfigArenaServer.getBetWinReward();
        List<List<Long>> rewardFails = ConfigArenaServer.getBetLooseReward();
        List<UserArenaServerBet> unReceived = arenaServerDAO.getRandomUnReceiveUserBet(eventId);
        while (!unReceived.isEmpty()) {
            for (UserArenaServerBet arenaBet : unReceived) {
                List<Long> bonus = new ArrayList<>();
                List<UserArenaServerBet> unReceivedByUserId = arenaServerDAO.getListUserBetUnreceived(arenaBet.getEventId(), arenaBet.getUserId());
                if (!unReceivedByUserId.isEmpty()) {
                    for (UserArenaServerBet userArenaServerBet : unReceivedByUserId) {
                        List<Long> addBonus;
                        if (userArenaServerBet.getMatchIndex() == 15) {
                            addBonus = userArenaServerBet.getResult() == 1 ? rewardRights.get(0) : rewardFails.get(0);
                        } else if (userArenaServerBet.getMatchIndex() >= 13) {
                            addBonus = userArenaServerBet.getResult() == 1 ? rewardRights.get(1) : rewardFails.get(1);
                        } else if (userArenaServerBet.getMatchIndex() >= 9) {
                            addBonus = userArenaServerBet.getResult() == 1 ? rewardRights.get(2) : rewardFails.get(2);
                        } else {
                            addBonus = userArenaServerBet.getResult() == 1 ? rewardRights.get(3) : rewardFails.get(3);
                        }
                        bonus = Bonus.merge(bonus, addBonus);
                    }
                }
                if (!bonus.isEmpty() && arenaServerDAO.updateBetUnreceived(arenaBet.getEventId(), arenaBet.getUserId())) {
                    String title = "Đại chiến tinh anh (%s)".formatted(arenaBet.getEventId() % 10000);
                    String message = "Phần quà đặt cược";
                    System.out.println("arenaBet = " + arenaBet.getUserId() + " " + bonus);

                    MailCreatorCache.sendMail(UserMailEntity.builder().userId(arenaBet.getUserId())
                            .title(title).message(message).bonus(StringHelper.toDBString(bonus)).build());
                }
            }
            unReceived = arenaServerDAO.getRandomUnReceiveUserBet(eventId);
        }
    }

    void sendBonus(int eventId) {
        Date dayReg1 = ConfigArenaServer.getDayStartCurrentEvent();
        Date dayReg2 = DateTime.getCalendar(dayReg1, Calendar.DATE, 1).getTime();
        List<UserArenaServer> arenaServers = arenaServerDAO.getListUserArenaServer(eventId, dayReg1, dayReg2);
        System.out.println(arenaServers.size());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, 30);
        String title = "Đại chiến tinh anh (%s)".formatted(eventId % 10000);
        for (UserArenaServer arenaServer : arenaServers) {
            MailCreatorCache.sendMail(UserMailEntity.builder().userId(arenaServer.getUserId())
                    .title(title).message("Phần quà báo danh").bonus(StringHelper.toDBString(ConfigArenaServer.getRegisterReward())).availableTime(calendar.getTime()).build());
            List<Integer> roundRankings = GsonUtil.strToListInt(arenaServer.getRoundRanking());
            if (!roundRankings.isEmpty()) { // ko qua vong loai
                for (int i = 0; i < roundRankings.size(); i++) {
                    String message = switch (i + 1) {
                        case 1 -> """
                                Vòng loại 1
                                Chúc mừng bạn đạt hạng %s
                                """;
                        case 2 -> """
                                Vòng loại 2
                                Chúc mừng bạn đạt hạng %s
                                """;
                        case 3 -> """
                                Vòng bảng 1
                                Chúc mừng bạn đạt hạng %s
                                """;
                        case 4 -> """
                                Vòng bảng 2
                                Chúc mừng bạn đạt hạng %s
                                """;
                        case 5 -> """
                                Vòng chung kết
                                Chúc mừng bạn đạt hạng %s
                                """;
                        default -> "";
                    };
                    List<Long> myBonus = new ArrayList<>();
                    ResRankingReward resArenaReward = ConfigArenaServer.mReward.get(i + 1);
                    var ranks = resArenaReward.getListRank();
                    var rewards = resArenaReward.getListReward();
                    for (int index = 0; index < ranks.size(); index++) {
                        var rank = ranks.get(index);
                        List<Long> reward = rewards.get(index);
                        if (rank.size() == 1 && rank.get(0) == roundRankings.get(i)) {
                            myBonus = reward;
                        } else if (rank.size() > 1 && rank.get(0) <= roundRankings.get(i) && roundRankings.get(i) <= rank.get(rank.size() - 1)) {
                            myBonus = reward;
                        }
                    }
                    if (!myBonus.isEmpty()) {
                        MailCreatorCache.sendMail(UserMailEntity.builder().userId(arenaServer.getUserId())
                                .title(title).message(message.formatted(roundRankings.get(i))).bonus(StringHelper.toDBString(myBonus)).availableTime(calendar.getTime()).build());
                    }
                }
            }
        }
    }

    void cacheFinalRound() {
        for (Integer eventId : eventIds) {
            List<UserArenaServerFinal> userArenaFinals = arenaServerDAO.getListUserArenaFinal(eventId);
            for (UserArenaServerFinal userArenaFinal : userArenaFinals) {
                if (userArenaFinal.getEventRound() == 4) {
                    UserArenaServer arenaServer = arenaServerDAO.getArenaServer(userArenaFinal.getWinUserId(), userArenaFinal.getEventId());
                    if (arenaServer != null) {
                        List<Integer> ranks = GsonUtil.strToListInt(arenaServer.getRoundRanking());
                        ranks.add(1);
                        arenaServer.update("round_ranking", StringHelper.toDBString(ranks));
                    }
                    arenaServer = arenaServerDAO.getArenaServer(userArenaFinal.getLooseUserId(), userArenaFinal.getEventId());
                    if (arenaServer != null) {
                        List<Integer> ranks = GsonUtil.strToListInt(arenaServer.getRoundRanking());
                        ranks.add(2);
                        arenaServer.update("round_ranking", StringHelper.toDBString(ranks));
                    }
                } else if (userArenaFinal.getEventRound() == 3) {
                    var arenaServer = arenaServerDAO.getArenaServer(userArenaFinal.getLooseUserId(), userArenaFinal.getEventId());
                    if (arenaServer != null) {
                        List<Integer> ranks = GsonUtil.strToListInt(arenaServer.getRoundRanking());
                        ranks.add(3);
                        arenaServer.update("round_ranking", StringHelper.toDBString(ranks));
                    }
                } else if (userArenaFinal.getEventRound() == 2) {
                    var arenaServer = arenaServerDAO.getArenaServer(userArenaFinal.getLooseUserId(), userArenaFinal.getEventId());
                    if (arenaServer != null) {
                        List<Integer> ranks = GsonUtil.strToListInt(arenaServer.getRoundRanking());
                        ranks.add(8);
                        arenaServer.update("round_ranking", StringHelper.toDBString(ranks));
                    }
                } else if (userArenaFinal.getEventRound() == 1) {
                    var arenaServer = arenaServerDAO.getArenaServer(userArenaFinal.getLooseUserId(), userArenaFinal.getEventId());
                    if (arenaServer != null) {
                        List<Integer> ranks = GsonUtil.strToListInt(arenaServer.getRoundRanking());
                        ranks.add(16);
                        arenaServer.update("round_ranking", StringHelper.toDBString(ranks));
                    }
                }
            }
        }
    }

    void cacheUserRankQualified(int round) {
        for (Integer eventId : eventIds) {
            List<UserArenaServerQualifyEntity> userQualifies = arenaServerDAO.getAllQualifyByRound(eventId, round);
            Map<Integer, List<UserArenaServerQualifyEntity>> mCluster = userQualifies.stream().collect(Collectors.groupingBy(UserArenaServerQualifyEntity::getCluster));
            for (List<UserArenaServerQualifyEntity> values : mCluster.values()) {
                values.sort(Comparator.comparing(UserArenaServerQualifyEntity::getPoint).reversed());
                for (int i = 0; i < values.size(); i++) {
                    var qualify = values.get(i);
                    int rank = i + 1;
                    UserArenaServer arenaServer = arenaServerDAO.getArenaServer(qualify.getUserId(), qualify.getEventId());
                    if (arenaServer != null) {
                        List<Integer> ranks = GsonUtil.strToListInt(arenaServer.getRoundRanking());
                        ranks.add(rank);
                        arenaServer.update("round_ranking", StringHelper.toDBString(ranks));
                    }
                }
            }
        }
    }

    /**
     *
     */
    void processFinalRound(int round) {
        for (Integer eventId : eventIds) {
            int numberMatch = Arrays.asList(8, 4, 2, 1).get(round - 1);
            int numberPreviousMatch = Arrays.asList(0, 8, 12, 14).get(round - 1);

            var arenaFinals = arenaServerDAO.getListUserArenaFinal(eventId).stream().filter(arenaFinal -> arenaFinal.getEventRound() == round).collect(Collectors.toList());
            for (int i = 0; i < arenaFinals.size(); i++) {
                int matchIndex = i + 1 + numberPreviousMatch;
                var arenaFinal = arenaFinals.get(i);

                UserEntity user1 = service.getUserEntity(arenaFinal.getUserId1());
                UserEntity user2 = service.getUserEntity(arenaFinal.getUserId2());

                System.out.println("process match = " + user1.getId() + " " + user2.getId());
                if (arenaFinal.getBattleId() > 0) {
                    System.out.println("match already done");
                    return;
                }
                BattleTeam team1 = user1.getId() > 0 ? UserTeam.getBattleTeam(user1.getId(), TeamType.ARENA_SERVER_DEF) : ResMonster.getMonsterTeam(Math.abs(user1.getId())).getBattleTeam();
                BattleTeam team2 = user2.getId() > 0 ? UserTeam.getBattleTeam(user2.getId(), TeamType.ARENA_SERVER_DEF) : ResMonster.getMonsterTeam(Math.abs(user2.getId())).getBattleTeam();
                long power1 = team1.getPower(), power2 = team2.getPower();
                BattleTeam atkTeam = power1 > power2 ? team1 : team2;
                BattleTeam defTeam = power1 > power2 ? team2 : team1;
                UserEntity attackerUser = power1 > power2 ? user1 : user2;
                UserEntity defUser = power1 > power2 ? user2 : user1;

                var battleResult = BattleBuilder.builder().setOffline(true).setTeam(atkTeam, defTeam).setMode(BattleType.ARENA_SERVER, BattleMode.NORMAL)
                        .setInfo(attackerUser.getId(), defUser.getId()).battle();
                protocol.Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(attackerUser, defUser, Lang.instance().get("title_arena_server") + " " + defUser.getName());

                int win1 = power1 > power2 ? battleResult.results[0] : battleResult.results[1];
                int win2 = power1 > power2 ? battleResult.results[1] : battleResult.results[0];

                BattleInputCache.builder().battleResult(battleResult)
                        .output(List.of(List.of(power1, power2)))
                        .outputStr(List.of(List.of(CfgServer.getServerName(attackerUser.getServer()), CfgServer.getServerName(defUser.getServer()))))
                        .build().saveOutputEarly();

                DBJPA.update("user_arena_server_final", Arrays.asList("win1", win1, "win2", win2, "battle_id", battleResult.battleId),
                        Arrays.asList("id", arenaFinal.getId()));
                arenaFinal.setWin1(win1);
                arenaFinal.setWin2(win2);
                arenaFinal.setBattleId(battleResult.battleId);

                DBJPA.rawSQL(String.format("update user_arena_server_bet set result=if(win_user_id=%s,1,2) where event_id=%s and match_index=%s",
                        arenaFinal.getWinUserId(), eventId, matchIndex));
            }
            if (round < 4) { // round=4 thì kết thúc rồi ko tạo cặp đấu mới
                for (int i = 0; i < arenaFinals.size(); i += 2) {
                    DBJPA.save(new UserArenaServerFinal(arenaFinals.get(i), arenaFinals.get(i + 1)));
                }
            } else { // save top user
                arenaFinals = arenaServerDAO.getListUserArenaFinal(eventId);
                var finalEntity = arenaFinals.stream().filter(arena -> arena.getEventRound() == 4).findFirst().orElse(null);
                saveTopUser(finalEntity.getWinUserId(), finalEntity.getWinPower(), 1, eventId);
                saveTopUser(finalEntity.getLooseUserId(), finalEntity.getLoosePower(), 2, eventId);
                var listRound3 = arenaFinals.stream().filter(arena -> arena.getEventRound() == 3).collect(Collectors.toList());
                for (UserArenaServerFinal userArenaServerFinal : listRound3) {
                    saveTopUser(userArenaServerFinal.getLooseUserId(), userArenaServerFinal.getLoosePower(), 3, eventId);
                }
            }
        }
    }

    void saveTopUser(int userId, long power, int rank, int eventId) {
        UserEntity user = service.getUserEntity(userId);
        DBJPA.save(new UserArenaServerTop(user, power, rank, eventId));
    }

    /**
     * - đánh nốt số trận
     * - xếp người chơi vào vòng trong
     */
    void processRound4(int round, int numberOk) {
        for (Integer eventId : eventIds) {
            logs("process round " + round);
            Map<Integer, List<UserArenaServerQualifyEntity>> mQualified = jobDAO.getListQualifiedRound(eventId, round)
                    .stream().collect(Collectors.groupingBy(UserArenaServerQualifyEntity::getCluster));
            logs("mQualified = " + mQualified.size());
            //
            logs("round %s attack".formatted(round));
            roundAttack(mQualified);
            //
            List<UserArenaServerQualifyEntity> finalQualifiedEntity = new ArrayList<>();
            for (Integer cluster : mQualified.keySet()) {
                List<UserArenaServerQualifyEntity> userQualifies = mQualified.get(cluster);
                userQualifies.sort(Comparator.comparing(UserArenaServerQualifyEntity::getPoint).reversed());
                userQualifies = userQualifies.subList(0, numberOk);
                finalQualifiedEntity.addAll(userQualifies);
            }
            logs("numberUserToNextRound = " + finalQualifiedEntity.size());
            finalQualifiedEntity.sort(Comparator.comparing(UserArenaServerQualifyEntity::getPower).reversed());
            // 2 nhánh lớn nhất
            List<List<List<Integer>>> groups = new ArrayList<>();
            groups.add(new ArrayList<>());
            groups.add(new ArrayList<>());
            // 4 nhánh con
            for (int i = 0; i < 2; i++) {
                groups.get(i).add(new ArrayList<>());
                groups.get(i).add(new ArrayList<>());
            }
            // 8 nhánh sâu nhất
            List<Integer> finalIndex = new ArrayList<>(List.of(1, 8, 4, 5, 2, 7, 3, 6));
            for (List<List<Integer>> group : groups) {
                for (int i = 0; i < group.size(); i++) {
                    group.get(i).add(finalIndex.remove(0));
                    group.get(i).add(finalIndex.remove(0));
                }
            }
            // random nào
            Collections.shuffle(groups);
            for (List<List<Integer>> group : groups) {
                Collections.shuffle(group);
                for (List<Integer> integers : group) {
                    Collections.shuffle(integers);
                }
            }
            // xong rồi save db thôi
            for (List<List<Integer>> group : groups) {
                for (List<Integer> integers : group) {
                    for (Integer index : integers) {
                        int realIndex = index - 1;
                        UserArenaServerQualifyEntity qualify1 = finalQualifiedEntity.get(realIndex);
                        UserArenaServerQualifyEntity qualify2 = finalQualifiedEntity.get(15 - realIndex);
                        DBJPA.save(new UserArenaServerFinal(qualify1, qualify2, 1));
                        logs("%s %s %s %s".formatted(qualify1.getUserId(), qualify1.getPower(), qualify2.getUserId(), qualify2.getPower()));
                    }
                }
            }
        }
    }

    /**
     * - đánh nốt số trận
     * - xếp người chơi vào vòng trong
     */
    void processRound(int round, int numberOk) {
        for (Integer eventId : eventIds) {
            logs("process round " + round);
            Map<Integer, List<UserArenaServerQualifyEntity>> mQualified = jobDAO.getListQualifiedRound(eventId, round)
                    .stream().collect(Collectors.groupingBy(UserArenaServerQualifyEntity::getCluster));
            logs("mQualified = " + mQualified.size());
            //
            logs("round %s attack".formatted(round));
            roundAttack(mQualified);
            //
            List<UserArenaServerQualifyEntity> finalQualifiedEntity = new ArrayList<>();
            for (Integer cluster : mQualified.keySet()) {
                List<UserArenaServerQualifyEntity> userQualifies = mQualified.get(cluster);
                userQualifies.sort(Comparator.comparing(UserArenaServerQualifyEntity::getPoint).reversed());
                userQualifies = userQualifies.subList(0, numberOk);
                finalQualifiedEntity.addAll(userQualifies);
            }
            logs("numberUserToNextRound = " + finalQualifiedEntity.size());
            finalQualifiedEntity.sort(Comparator.comparing(UserArenaServerQualifyEntity::getPower).reversed());
            List<List<UserArenaServerQualifyEntity>> groups = new ArrayList<>();
            // chia nhóm 8 theo lực chiến
            for (int i = 0; i < 8; i++) groups.add(new ArrayList<>());
            int number = finalQualifiedEntity.size() / 8;
            for (int i = 0; i < 8; i++) {
                for (int index = 0; index < number; index++) {
                    if (finalQualifiedEntity.size() > 0) groups.get(i).add(finalQualifiedEntity.remove(0));
                }
                logs("group %s size=%s".formatted(i + 1, groups.get(i).size()));
                for (UserArenaServerQualifyEntity qualify : groups.get(i)) {
                    logs("%s %s".formatted(qualify.getUserId(), qualify.getPower()));
                }
                Collections.shuffle(groups.get(i));
            }
            List<UserArenaServerQualifyEntity> finalData = new ArrayList<>();
            for (int i = 0; i < number; i++) {
                // 8 người mỗi nhóm
                for (int index = 0; index < 8; index++) {
                    if (!groups.get(index).isEmpty()) {
                        var entity = groups.get(index).remove(0);
                        entity.reset(round + 1, i + 1);
                        finalData.add(entity);
                    }
                }
            }
            DBJPA.save(finalData.toArray());
        }
    }

    void roundAttack(Map<Integer, List<UserArenaServerQualifyEntity>> mQualified) {
        for (Integer cluster : mQualified.keySet()) {
            List<UserArenaServerQualifyEntity> userQualifies = mQualified.get(cluster);
            var hasUser = userQualifies.stream().filter(userQualify -> userQualify.getUserId() > 0).findFirst().orElse(null) != null;
            if (hasUser) {
                for (UserArenaServerQualifyEntity attackerQualify : userQualifies) {
                    UserEntity attackerUser = service.getUserEntity(attackerQualify.getUserId());
                    var attackedIds = attackerQualify.getAttackedUserId();
                    for (UserArenaServerQualifyEntity defQualify : userQualifies) {
                        if (attackerQualify.getUserId() != defQualify.getUserId() && !attackedIds.contains(defQualify.getUserId())) { // không phải bản thân và chưa đánh
                            // Tự động đánh như trong vòng loại
                            List<List<Long>> summaryAttacks = GsonUtil.strTo2ListLong(attackerQualify.getSummaryAttack());
                            UserEntity defUser = service.getUserEntity(defQualify.getUserId());
                            BattleTeam atkTeam = attackerQualify.getUserId() > 0 ? UserTeam.getBattleTeam(attackerQualify.getUserId(), TeamType.ARENA_SERVER_DEF) : ResMonster.getMonsterTeam(Math.abs(attackerQualify.getUserId())).getBattleTeam();
                            BattleTeam defTeam = defQualify.getUserId() > 0 ? UserTeam.getBattleTeam(defQualify.getUserId(), TeamType.ARENA_SERVER_DEF) : ResMonster.getMonsterTeam(Math.abs(defQualify.getUserId())).getBattleTeam();
                            var battleResult = BattleBuilder.builder().setOffline(true).setTeam(atkTeam, defTeam).setMode(BattleType.ARENA_SERVER, BattleMode.NORMAL)
                                    .setInfo(attackerUser.getId(), defUser.getId()).battle();
                            protocol.Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(attackerUser, defUser, Lang.instance().get("title_arena_server") + " " + defUser.getName());

                            List<Long> summaryAttack = ListUtil.ofLong(defQualify.getUserId(), builder.getBattleId(), battleResult.isWin() ? 1 : 0, battleResult.isWin() ? 1 : 0, battleResult.isWin() ? 0 : 1);
                            summaryAttacks.add(summaryAttack);

                            int numberWin = attackerQualify.getNumberWin() + (battleResult.isWin() ? 1 : 0);
                            int numberLoose = attackerQualify.getNumberLoose() + (battleResult.isWin() ? 0 : 1);
                            long point = battleResult.isWin() ? service.calculateRankingPoint(numberWin) : Math.max(1, attackerQualify.getPoint());
                            long timeWin = battleResult.isWin() ? System.currentTimeMillis() : attackerQualify.getTimeWin();
                            var logAttack = new UserArenaServerAttackLog(attackerQualify, defQualify.getUserId(), battleResult.isWin() ? 1 : 0, builder.getBattleId());

                            arenaServerDAO.updateAttackQualified(attackerQualify.getId(), numberWin, numberLoose, point, StringHelper.toDBString(summaryAttacks), timeWin, logAttack); // coi như luôn thành công
                            attackerQualify.setSummaryAttack(StringHelper.toDBString(summaryAttacks));
                            attackerQualify.setNumberWin(numberWin);
                            attackerQualify.setNumberLoose(numberLoose);
                            attackerQualify.setPoint(point);
                            attackerQualify.setTimeWin(timeWin);

                            BattleInputCache.builder().battleResult(battleResult)
                                    .output(List.of(List.of(atkTeam.getPower(), defTeam == null ? 0 : defTeam.getPower())))
                                    .outputStr(List.of(List.of(CfgServer.getServerName(attackerUser.getServer()), CfgServer.getServerName(defUser.getServer()))))
                                    .build().saveOutputEarly();
                        }
                    }
                }
            }
        }
    }

    void setupRound1() throws Exception {
        for (Integer eventId : eventIds) {
            logs("setup round 1");
            var userArenaServers = jobDAO.getListRegisterWin(eventId);
            List<ResMonsterTeam> bots = new ArrayList<>(ResMonster.mMonsterTeamByFeature.get(MonsterTeamType.ARENA_SERVER.id));
            Collections.shuffle(bots);
            int numberBot = 1024 - userArenaServers.size();
            while (bots.size() > numberBot) bots.remove(0);

            logs("user %s bot %s".formatted(userArenaServers.size(), numberBot));

            List<UserArenaServerQualifyEntity> qualifiedUser = new ArrayList<>();
            for (UserArenaServer userArena : userArenaServers) {
                qualifiedUser.add(UserArenaServerQualifyEntity.builder().userId(userArena.getUserId()).eventId(userArena.getEventId()).round(1).serverId(userArena.getServerId())
                        .power(userArena.getPower()).summaryAttack("[]").build());
            }
            UserArenaServer tmp = userArenaServers.get(0);
            for (ResMonsterTeam bot : bots) {
                ResMonsterTeam monsterTeam = ResMonster.getMonsterTeam(bot.getId());
                qualifiedUser.add(UserArenaServerQualifyEntity.builder().userId(-bot.getId()).eventId(tmp.getEventId()).round(1).serverId(tmp.getServerId())
                        .power(monsterTeam.getPower()).summaryAttack("[]").build());
            }
            logs("numberUserToNextRound = " + qualifiedUser.size());
            qualifiedUser.sort(Comparator.comparing(UserArenaServerQualifyEntity::getPower).reversed());
            List<List<UserArenaServerQualifyEntity>> groups = new ArrayList<>();
            // chia nhóm 8 theo lực chiến
            for (int i = 0; i < 8; i++) groups.add(new ArrayList<>());
            int number = qualifiedUser.size() / 8;
            for (int i = 0; i < 8; i++) {
                for (int index = 0; index < number; index++) {
                    if (qualifiedUser.size() > 0) groups.get(i).add(qualifiedUser.remove(0));
                }
                logs("group %s size=%s".formatted(i + 1, groups.get(i).size()));
                for (UserArenaServerQualifyEntity qualify : groups.get(i)) {
                    logs("%s %s".formatted(qualify.getUserId(), qualify.getPower()));
                }
                Collections.shuffle(groups.get(i));
            }
            List<UserArenaServerQualifyEntity> finalData = new ArrayList<>();
            for (int i = 0; i < number; i++) {
                // 8 người mỗi nhóm
                for (int index = 0; index < 8; index++) {
                    if (!groups.get(index).isEmpty()) {
                        var entity = groups.get(index).remove(0);
                        entity.setCluster(i + 1);
                        finalData.add(entity);
                    }
                }
            }
            logs("insert data to db");
            DBJPA.save(finalData.toArray());
        }
    }

}
