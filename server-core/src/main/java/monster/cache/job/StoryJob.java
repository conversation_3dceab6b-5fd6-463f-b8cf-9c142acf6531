package monster.cache.job;

import grep.helper.DateTime;
import grep.helper.EmojText;
import grep.log.Logs;
import monster.game.story.dao.StoryBossDAO;
import monster.server.Main;
import monster.server.config.Guice;
import monster.service.monitor.Telegram;

import java.util.List;

public class Story<PERSON>ob extends AJob {

    int lastEventId = 0;
    StoryBossDAO storyBossDAO;

    public static void main(String[] args) throws Exception {
        StoryJob ranking = new StoryJob();
        ranking.myInit();
        ranking.whatToProcess(args.length > 0 ? args[0] : "");
        System.exit(0);
    }

    void myInit() throws Exception {
        init();
        Main.initConfig();
        lastEventId = DateTime.getTimeKey(DateTime.getYesterday());
        storyBossDAO = Guice.getInstance(StoryBossDAO.class);
    }

    void whatToProcess(String args) throws Exception {
        System.out.println("star ranking " + lastEventId);
        List<Integer> serverIds = storyBossDAO.getListServerId(lastEventId);
        for (Integer serverId : serverIds) {
            try {
                System.out.println("ranking server " + serverId);
                String key = "server" + serverId;
                boolean result = storyBossDAO.rawSQL(
                        String.format("SET @%s=0", key),
                        String.format("""
                                UPDATE user_story_boss SET daily_rank = @%s\\:=(@%s +1) where time_key=%s and server_id=%s ORDER BY max_damage DESC
                                """, key, key, lastEventId, serverId)
                );
                System.out.println("ranking server done " + serverId + " " + result);
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
        Telegram.sendNotify(EmojText.HEART.get("StoryBoss ranking event=%s done".formatted(lastEventId)));
    }

}
