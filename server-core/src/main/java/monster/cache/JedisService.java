package monster.cache;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JedisService {
    public static final int NAP_NOTIFY = 1;
    public static final int MAIL_NOTIFY = 2;
    public static final int SALE_NOTIFY = 3;
    public static final int TELEGRAM_NOTIFY = 4;

    // clear cache
    public static final int BATTLE_SIMULATE = 500;
    public static final int BATTLE_RESULT = 501;

    // Cfg reload
    public static final int CFG_ALL = 1000;
    public static final int CONFIG_RELOAD_KEY = 1001;
    public static final int CFG_EVENT_EXP = 1002;
    public static final int CFG_RELOAD_HERO = 1003;
    public static final int CFG_RELOAD_KING = 1004;

    private static List<String> simulateChannel = Arrays.asList("simulate6664", "simulate6665", "simulate6666");
    public static String gameChannel = "";

    public static String getSimulateChannel() {
        return simulateChannel.get((int) (System.currentTimeMillis() % simulateChannel.size()));
    }

    public static Map<Integer, String> serviceName = new HashMap<Integer, String>() {{
        put(NAP_NOTIFY, "nap");
        put(MAIL_NOTIFY, "mail");
        put(SALE_NOTIFY, "sale");
        put(TELEGRAM_NOTIFY, "tele");
    }};
}
