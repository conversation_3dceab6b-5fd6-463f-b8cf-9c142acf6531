package monster.cache;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.dao.mapping.UserTrialNewEntity;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.stream.Collectors;

public class DbToFile {

    static int count = 0;

    public static void main(String[] args) {
        DBJPA.init("greptest");
        if (args.length == 0) {
            System.out.println("Dont know what to do");
        } else if (args[0].equals("superTrial")) {
            new DbToFile().trialToFile();
        } else if (args[0].equals("trial")) {
            new DbToFile().cleanUserTrail();
        } else if (args[0].equals("mail")) {
            new DbToFile().cleanMail();
        }
        System.exit(0);
    }

    private void cleanMail() {
        long curTime = System.currentTimeMillis();
        int count = 0, add = 0;
        do {
            try {
                EntityManager session = null;
                try {
                    session = DBJPA.getEntityManager();
                    session.getTransaction().begin();
                    add = session.createNativeQuery("delete from dson.user_mail where remove=1 and mail_idx=0 limit 100").executeUpdate();
                    count += add;
                    System.out.println("count = " + count + " -> " + (System.currentTimeMillis() - curTime));
                    session.getTransaction().commit();
                } catch (Exception ex) {
                    Logs.error(GUtil.exToString(ex));
                } finally {
                    DBJPA.closeSession(session);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            curTime = System.currentTimeMillis();
        } while (add != 0);
    }

    private void cleanUserTrail() {
        long curTime = System.currentTimeMillis();
        boolean isOk = true;
        do {
            System.out.println("remove total = " + count + " -> " + (System.currentTimeMillis() - curTime));
            List<UserTrialNewEntity> aUserTrial = DBJPA.getSelectQuery("select t.* from dson.user u join dson.user_trial_new t on u.id=t.user_id where date(last_login) <= '2020-10-11' and length(t.stage)>10 limit 100", UserTrialNewEntity.class);
            if (!aUserTrial.isEmpty()) {
                count += aUserTrial.size();
                String ids = aUserTrial.stream().map(trial -> String.valueOf(trial.getUserId())).collect(Collectors.joining(","));
                System.out.println(ids.substring(0, 50));
                DBJPA.rawSQL("delete from user_trial_new where user_id in (" + ids + ")");
            } else {
                isOk = false;
                System.out.println("empty");
            }
            curTime = System.currentTimeMillis();
        } while (isOk);
    }

    private void trialToFile() {
        int count = 0, add = 0;
        do {
            List<UserTrialNewEntity> aUserTrial = DBJPA.getSelectQuery("select * from dson.user_trial_new where length(my_heroes)>10 limit 50", UserTrialNewEntity.class);
            add = aUserTrial.size();
            count += add;
            String userIds = aUserTrial.stream().map(value -> String.valueOf(value.getUserId())).collect(Collectors.joining(","));
            System.out.println(count + " -> " + userIds.substring(0, userIds.length() / 2));
            for (UserTrialNewEntity trial : aUserTrial) {
                if (!StringHelper.isEmpty(trial.getStage()) || !StringHelper.isEmpty(trial.getStageNight()) || !StringHelper.isEmpty(trial.getMyHeroes())) {
                    EntityManager session = null;
                    try {
                        session = DBJPA.getEntityManager();
                        trial.updateNewMyHeroes(trial.getMyHeroes());
                        trial.updateNewStage(trial.getStage());
                        trial.updateNewStageNight(trial.getStageNight());
                        session.getTransaction().begin();
                        session.createNativeQuery("update user_trial_new set stage='', stage_night='', my_heroes='' where user_id=" + trial.getUserId()).executeUpdate();
                        session.getTransaction().commit();
                    } catch (Exception ex) {
                        Logs.error(GUtil.exToString(ex));
                    } finally {
                        DBJPA.closeSession(session);
                    }
                }
            }
        } while (add > 0);
    }

}
