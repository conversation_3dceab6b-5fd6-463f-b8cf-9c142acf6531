package monster.cache.memcached;

import grep.helper.DateTime;
import grep.log.Config;
import grep.log.Logs;
import monster.server.AppConfig;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.ConnectionFactory;
import net.spy.memcached.ConnectionFactoryBuilder;
import net.spy.memcached.MemcachedClient;

import java.io.IOException;

public class MCache {

    public static final int DEFAULT_SESSION_EXPIRE = 60 * 60 * 24;// 1 day
    public static final int EXPIRE_1D = 60 * 60 * 24;
    public static final int EXPIRE_WEEK = 7 * EXPIRE_1D;
    public static final int EXPIRE_3D = 60 * 60 * 24 * 3;
    public static final int EXPIRE_1H = 60 * 60;
    public static final int EXPIRE_2H = 60 * 60 * 2;
    public static final int EXPIRE_12H = 60 * 60 * 12;
    public static final int EXPIRE_30M = 60 * 30;
    public static final int EXPIRE_15M = 60 * 15;
    public static final int EXPIRE_10M = 60 * 10;
    public static final int EXPIRE_5M = 60 * 5;
    public static final int EXPIRE_1M = 60;
    public static final int EXPIRE_1S = 1;
    public static String PREFIX = "";
    private static MCache instance;

    private MemcachedClient[] memCache = new MemcachedClient[2];
    private int index = 0;
    public long nextTime = 0;

    private MCache() throws IOException {
        PREFIX = Config.getServerId() + ":";
        switchClient();
    }

    private synchronized void switchClient() {
        if (nextTime < System.currentTimeMillis()) { // đến giờ đổi client
            nextTime = System.currentTimeMillis() + DateTime.HOUR_MILLI_SECOND / 2;
            try {
                int newIndex = 1 - index;
                Logs.warn("init memcached connection " + newIndex);
                ConnectionFactory cf = new ConnectionFactoryBuilder()
//                .setTimeoutExceptionThreshold(10)
//                .setMaxReconnectDelay(3)
                        .build();
                memCache[newIndex] = new MemcachedClient(cf, AddrUtil.getAddresses(AppConfig.cfg.memcached));
                Logs.warn(String.format("init memcached connection %s done", newIndex));
                // wait for new connection
                Thread.sleep(2000);
                index = newIndex;
                if (memCache[1 - newIndex] != null) memCache[1 - newIndex].shutdown(); // shutdown old connection
                Logs.warn(String.format("shutdown memcached connection %s done", 1 - newIndex));
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
    }

    public static MCache getInstance() {
        if (instance == null) {
            try {
                instance = new MCache();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (instance.nextTime < System.currentTimeMillis()) {
            new Thread(() -> instance.switchClient()).start();
        }
        return instance;
    }

    public void set(String key, Object value, int expire) {
        try {
            memCache[index].set(PREFIX + key, expire, value).get();
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public void set(String key, Object value) {
        try {
            memCache[index].set(PREFIX + key, DEFAULT_SESSION_EXPIRE, value).get();
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public Object get(String key) {
        return memCache[index].get(PREFIX + key);
    }

    public Object getNormal(String key) {
        return memCache[index].get(key);
    }

    public void setNormal(String key, Object value) {
        try {
            memCache[index].set(key, DEFAULT_SESSION_EXPIRE, value).get();
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public void setNormal(String key, Object value, int expire) {
        try {
            memCache[index].set(key, expire, value).get();
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }


    public Object getAndTouch(String key, int expire) {
        memCache[index].touch(PREFIX + key, expire);
        return get(key);
    }

    public Object getAndTouch(String key) {
        return getAndTouch(PREFIX + key, DEFAULT_SESSION_EXPIRE);
    }

    public Object touch(String key, int expire) {
        return memCache[index].touch(PREFIX + key, expire);
    }

    public void touch(String key) {
        memCache[index].touch(PREFIX + key, DEFAULT_SESSION_EXPIRE);
    }

    public Long getLong(String key) {
        try {
            return (Long) get(key);
        } catch (Exception ex) {
        }
        return null;
    }

    public Integer getInt(String key) {
        try {
            return (Integer) get(key);
        } catch (Exception ex) {
        }
        return null;
    }

    public void delete(String key) {
        memCache[index].delete(PREFIX + key);
    }

    public void deleteNormal(String key) {
        memCache[index].delete(key);
    }

    /**
     * Key cache
     */
    public static final String KEY_SHIELD = "SHIELD:";
    public static final String KEY_RANK_TROPHY = "RTROPHY:";
    public static final String KEY_MISSION = "MISSION1:";
    public static final String KEY_MISSION_NUMBER = "MISSION:";
}
