package monster.cache;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Config;
import grep.log.Logs;
import monster.config.CfgArenaSwap;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.dao.mapping.main.ConfigEntity;
import monster.server.AppConfig;
import monster.server.Main;
import monster.server.config.Guice;
import monster.service.monitor.Telegram;
import monster.service.user.Bonus;
import monster.util.DBHelper;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class ArenaSwapJob {

    public static void main(String args[]) throws Exception {
        String k = args.length == 0 ? "" : args[0];
        new ArenaSwapJob().process(k);
        System.exit(0);
    }

    private void process(String k) throws Exception {
        try {
            AppConfig.load("config.json");
            Config.load("config.xml");
            DBJPA.init("grepjob1");
            Guice.init();
            CfgServer.serverId = Config.getInt("config.server.id");
            CfgServer.serverType = Config.getString("config.server.type");
            initConfig();
            System.out.println("STATE_CLOSED = 0, STATE_SWAP = 1, STATE_FINAL = 2, STATE_DISABLE = 3");
            boolean isNotify = !(StringHelper.isEmpty(k) || k.equals("swapArena"));
            int swapState = CfgArenaSwap.getState();
            if (CfgServer.isRealServer()) {
                System.out.println("swapState = " + swapState);
                if (isNotify && swapState == CfgArenaSwap.STATE_FINAL) Telegram.sendNotify("Corrida Job Start = " + k);
                if (k.equals("setup") && CfgArenaSwap.getState() == CfgArenaSwap.STATE_FINAL) { // setup chung kết
                    CfgArenaSwap.setupFinalRound();
                } else if (k.equals("finish")) {
                    CfgArenaSwap.processFinalRound();
                } else if (k.equals("round1") && CfgArenaSwap.getState() == CfgArenaSwap.STATE_FINAL) {
                    CfgArenaSwap.processFinalRound(1);
                } else if (k.equals("round2") && CfgArenaSwap.getState() == CfgArenaSwap.STATE_FINAL) {
                    CfgArenaSwap.processFinalRound(2);
                } else if (k.equals("round3") && CfgArenaSwap.getState() == CfgArenaSwap.STATE_FINAL) {
                    CfgArenaSwap.processFinalRound(3);
                } else if (k.equals("round4")) { // Cần trừ 15p vì chạy job bị trễ vài giây sang state khác rồi
                    Calendar ca = Calendar.getInstance();
                    ca.add(Calendar.MINUTE, -15);
                    if (CfgArenaSwap.getState(ca) == CfgArenaSwap.STATE_FINAL) {
                        CfgArenaSwap.processFinalRound(4);
                        Telegram.sendNotify("Sending Bet Result");
                        sendBettingAward();
                    }
                } else {
                    cacheRank();
                }
                if (isNotify && swapState == CfgArenaSwap.STATE_FINAL) Telegram.sendNotify("Corrida Job finish");
            }
        } catch (Exception ex) {
            String exception = GUtil.exToString(ex);
            Logs.error(exception);
            Telegram.sendNotify("Corrida " + k + " -> err=" + exception);
        }
    }

    private void sendBettingAward() {
        int eventId = CfgArenaSwap.getEventId();
        System.out.println("betting -> eventId = " + eventId);
        String sql = String.format("SELECT user_id, COUNT(*) AS number FROM user_arena_swap_bet WHERE event_id=%s AND result=1 GROUP BY user_id;", eventId);
        List<Object[]> results = DBJPA.getList(sql);
        if (results != null) {
            System.out.println("number winning = " + results.size());
            List<String> sqls = new ArrayList<>();
            for (Object[] result : results) {
                int userId = ((Integer) result[0]).intValue();
                int number = ((Long) result[1]).intValue();
                String title = Lang.getTitle("title_corrida_bet");
                String content = String.format(Lang.getTitle("content_corrida_bet"), number);
                String bonus = Bonus.viewMaterial(MaterialType.GUILD_COIN, 2000 * number).toString();
                sqls.add(DBHelper.sqlMail(userId, title, content, bonus, "swap_bet:" + eventId));
            }
            DBJPA.rawSQL(sqls);
        }
    }

    private void initConfig() throws Exception {
        CfgServer.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_server")).getV());
        CfgArenaSwap.loadConfig(((ConfigEntity) DBJPA.getUnique(CfgServer.DB_MAIN + "config", ConfigEntity.class, "k", "config_arenaSwap")).getV());
        Main.initConfig();
    }

    private void cacheRank() {
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.MINUTE, -15);
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMddHH");
        int eventId = CfgArenaSwap.getEventId();
        int min = ca.get(Calendar.MINUTE) / 15;
        int timeKey = Integer.parseInt(dateFormat.format(ca.getTime())) * 10 + min;
        System.out.println(String.format("cacheRank eventId=%s, timeKey=%s", eventId, timeKey));
        if (CfgArenaSwap.getState(ca) == CfgArenaSwap.STATE_SWAP && !CfgArenaSwap.forceClosed(ca)) {
            String sql = String.format("insert into user_arena_swap_cache(event_id, user_id, hour, user_rank, cluster, physical_server) "
                    + "(select %s, user_id, %s, user_rank, cluster, physical_server from user_arena_swap where event_id=%s and user_rank>0)", eventId, timeKey, eventId);
            System.out.println("sql = " + sql);
            DBJPA.rawSQL(sql);
        }
    }

}
