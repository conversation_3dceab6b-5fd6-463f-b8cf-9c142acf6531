package monster.protocol.pbentity;

import lombok.Data;
import monster.dao.mapping.UserEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class PbJListBattleResult {
    private List<PbJBattleResult> aBattle = new ArrayList<>();
    private int teamWin;
    private List<Long> aBonus = new ArrayList<>();
    private PbJVector attacker = new PbJVector();
    private PbJVector defender = new PbJVector();
    private long battleId;
    private int battleType;
    private PbJVector info = new PbJVector();
    private List<Long> aFakeBonus = new ArrayList<>();
//    private Attacker attacker = new Attacker();
//    private Defender defender = new Defender();

    public void setAttacker(UserEntity user) {
        this.attacker.addALong(user.getId()).addALong(user.getAvatarType()).addALong( user.getAvatar()).addALong(user.getLevel()).addAString(user.getName());
    }

    public void setDefender(UserEntity user) {
        this.defender.addALong(user.getId()).addALong(user.getAvatarType()).addALong( user.getAvatar()).addALong(user.getLevel()).addAString(user.getName());
    }

    public void addAllABonus(List<Long> aBonus) {
        this.aBonus.addAll(aBonus);
    }

    public void addAllAFakeBonus(List<Long> aFakeBonus) {
        this.aFakeBonus.addAll(aFakeBonus);
    }

    public void addABattle(PbJBattleResult value){
        this.aBattle.add(value);
    }
}
