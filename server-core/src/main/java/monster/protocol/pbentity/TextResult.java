package monster.protocol.pbentity;

import com.google.gson.Gson;
import lombok.Data;

@Data
public class TextResult {

    int serviceId;
    int status = 1;
    String message = "", bonus = "";
    Object data;

    public TextResult success(Object data) {
        this.status = 1;
        this.message = "";
        this.data = data;
        return this;
    }

    public TextResult success(int actionId,Object data) {
        this.serviceId = actionId;
        this.status = 1;
        this.message = "";
        this.data = data;
        return this;
    }

    public TextResult success(String bonus) {
        this.status = 1;
        this.message = "";
        this.bonus = bonus;
        return this;
    }

    public TextResult error(String message) {
        this.status = 0;
        this.message = message;
        return this;
    }

    public TextResult noneData() {
        this.status = 2;
        this.data = null;
        return this;
    }

    public void setServiceId(int actionId) {
        this.serviceId = actionId;
    }

    @Override
    public String toString() {
        return new Gson().toJson(this);
    }
}
