package monster.client;

import lombok.Data;
import lombok.NonNull;
import protocol.Pbmethod.*;

@Data
public class ClientItem {

    private int GEM_UPGRADE = 307;

    @NonNull
    private GameClient client;

    public void gemUpgrade() {
        ResponseData response = client.sendRequest(client.initRequest(GEM_UPGRADE,
                CommonVector.newBuilder().addALong(3).addALong(2).addALong(1).build().toByteString()));
        System.out.println(response.toString());
    }

}
