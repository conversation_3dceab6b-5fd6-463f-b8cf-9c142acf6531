package monster.client;

import lombok.Data;
import lombok.NonNull;
import monster.server.IAction;
import protocol.Pbmethod.CommonVector;
import protocol.Pbmethod.PbAction;
import protocol.Pbmethod.ResponseData;

import java.util.Arrays;
import java.util.List;

@Data
public class ClientLabyrinth extends IAction {

    @NonNull
    private GameClient client;
    private String oppUsername = "";
    private List<Integer> cmmAction = Arrays.asList(HERO_LEVEL_UP);
    private int a = 0;

    public void start() {
        status();
    }

    private void status() {
        print(client.sendRequest(client.initRequest(LABYRINTH_STATUS, CommonVector.newBuilder().addALong(0).build().toByteString())));
    }

    private void heroExpertHeroDetail() {
        print(client.sendRequest(client.initRequest(EXPERT_HERO_DETAIL, CommonVector.newBuilder().addALong(85).build().toByteString())));
    }

    private void heroExpertPointDetail() {
        print(client.sendRequest(client.initRequest(EXPERT_POINT_DETAIL, CommonVector.newBuilder().addALong(85).build().toByteString())));
    }

    public void upLevel(int heroId) {
        ResponseData response = client.sendRequest(client.initRequest(HERO_LEVEL_UP, CommonVector.newBuilder()
                .addALong(heroId).build().toByteString()));
    }

    public void print(ResponseData response) {
        for (PbAction action : response.getAActionList()) {
            if (action.getActionId() == IAction.HERO_LEVEL_UP) {
                try {
                    System.out.println("level: " + (++a) + " " + CommonVector.parseFrom(action.getData().toByteArray()).getALong(3));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } else {
                System.out.println("<-- receive " + action.getActionId());
            }
        }
    }

}
