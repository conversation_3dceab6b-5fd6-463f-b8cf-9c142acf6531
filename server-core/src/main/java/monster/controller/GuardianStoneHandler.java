package monster.controller;

import grep.log.Logs;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * GuardianStoneHandler
 */
public class GuardianStoneHandler extends AHandler {
    private final String KEY_DATA = "relic";

    @Override
    public AHandler newInstance() {
        return new GuardianStoneHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList();
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    //endregion

    //region Logic
    //endregion

    //region Database
    //endregion

}