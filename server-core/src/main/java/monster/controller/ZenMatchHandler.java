package monster.controller;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.*;
import grep.log.Logs;
import monster.config.*;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.dao.mapping.*;
import monster.object.RankController;
import monster.protocol.CommonProto;
import monster.server.Constans;
import monster.service.monitor.UserOnline;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.text.SimpleDateFormat;
import java.util.*;

public class ZenMatchHandler extends AHandler {
    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(ZEN_MATCH_STATUS, ZEN_MATCH_FINISH, ZEN_MATCH_BUY, ZEN_MATCH_RECEIVE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public AHandler newInstance() {
        return new ZenMatchHandler();
    }

    static final String KEY_DATA = "user_event_sanji";
    public static final String KEY_RANK_CONTROLLER = CfgServer.isTestServer() ? "rank:testEventSanji" : "rank:eventSanji";
    RankController rankController;
    UserEventSanjiEntity uEventSanji;

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            if (!CfgZenMatch.isOpen()) {
                addErrResponse(getLang(Lang.user_function_closed));
                return;
            }

            switch (actionId) {
                case ZEN_MATCH_STATUS:
                    zenMatchStatus();
                    break;
                case ZEN_MATCH_BUY:
                    zenMatchBuy();
                    break;
                case ZEN_MATCH_FINISH:
                    zenMatchFinish();
                    break;
                case ZEN_MATCH_RECEIVE:
                    zenMatchReceive();
                    break;
            }

        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    void zenMatchStatus() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector(CfgZenMatch.getCountDownToCloseEvent()));
        CfgZenMatch.config.zenMatchItems.forEach(zenMatchItem -> builder.addAVector(getCommonVector(zenMatchItem.getFeeBuy())));
        UserZenMatchEntity userZenMatch = mUser.getCache().getUserZenMatch(this, mUser);
        if (userZenMatch == null) return;
        for (int i = 0; i < CfgZenMatch.config.zenMatchBonus.size(); i++) {
            int level = i + 1;
            int levelStatus = userZenMatch.getStatus(level);
            List<Long> bonus = CfgZenMatch.config.zenMatchBonus.get(i);
            Pbmethod.CommonVector.Builder cmmVectorBuilder = Pbmethod.CommonVector.newBuilder();
            cmmVectorBuilder.addALong(level).addALong(levelStatus).addAllALong(bonus);
            builder.addAVector(cmmVectorBuilder);
        }
        addResponse(builder.build());
    }

    void zenMatchBuy() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int level = aLong.get(0).intValue(), itemIndex = aLong.get(1).intValue(), numberBuy = aLong.get(2).intValue();
        UserZenMatchEntity userZenMatch = mUser.getCache().getUserZenMatch(this, mUser);
        if (userZenMatch == null) return;

        if (level > userZenMatch.getCurrentLevel()) {
            addErrResponse("Chưa hoàn thành ải trước");
            return;
        }

        List<Long> aBonus = CfgZenMatch.config.zenMatchItems.get(itemIndex).getFeeBuy();
        long numberMaterial = aBonus.get(aBonus.size() - 1) * numberBuy;
        aBonus.set(aBonus.size() - 1, numberMaterial);
        JsonArray arrConvertPrice = GsonUtil.parseFromListLong(aBonus);
        if (!mUser.checkPrice(this, arrConvertPrice)) {
            return;
        }

        aBonus = Bonus.receiveListItem(mUser, arrConvertPrice, "zen_match_buy");
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        Actions.save(user, "zen_match", "buy", "level", level, "item", itemIndex, "number", numberBuy);
        addResponse(getCommonVector(aBonus));
    }

    void zenMatchFinish() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int level = aLong.get(0).intValue();
        UserZenMatchEntity userZenMatch = mUser.getCache().getUserZenMatch(this, mUser);
        if (userZenMatch == null) return;

        if (level > userZenMatch.getCurrentLevel()) {
            addErrResponse("Chưa hoàn thành ải trước");
            return;
        }

        int currentStatus = userZenMatch.getStatus(level);
        boolean isLevelMax = level >= CfgZenMatch.config.zenMatchBonus.size();
        int newStatus = currentStatus == CfgZenMatch.ZEN_MATCH_NOT_COMPLETED ? CfgZenMatch.ZEN_MATCH_NOT_RECEIVED : currentStatus;
        if (isLevelMax) {
            Map<Integer, Integer> newStatusMapByLevel = new HashMap<>();
            newStatusMapByLevel.put(level, newStatus);
            String newUserZenMatchStatus = userZenMatch.getNewStatus(newStatusMapByLevel);
            if (!updateUserZenMatch(userZenMatch, newUserZenMatchStatus)) return;
            Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
            for (int i = 0; i < CfgZenMatch.config.zenMatchBonus.size(); i++) {
                int tmpLevel = i + 1;
                int levelStatus = userZenMatch.getStatus(tmpLevel);
                if (tmpLevel == level) levelStatus = newStatus;
                builder.addAVector(getCommonVector(tmpLevel, levelStatus));
            }
            Actions.save(user, "zen_match", "finish", "event_id", userZenMatch.getEventId(), "level", level, "status", newUserZenMatchStatus);
            addResponse(builder.build());
            return;
        }

        int currentStatusForNextLevel = userZenMatch.getStatus(level + 1);
        int newStatusForNextLevel = currentStatusForNextLevel == CfgZenMatch.ZEN_MATCH_NOT_ABLE_TO_PLAY ? CfgZenMatch.ZEN_MATCH_NOT_COMPLETED : currentStatus;

        Map<Integer, Integer> newStatusMapByLevel = new HashMap<>();
        newStatusMapByLevel.put(level, newStatus);
        newStatusMapByLevel.put(level + 1, newStatusForNextLevel);
        String newUserZenMatchStatus = userZenMatch.getNewStatus(newStatusMapByLevel);
        if (!updateUserZenMatch(userZenMatch, newUserZenMatchStatus)) {
            addErrResponse();
            return;
        }

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (int i = 0; i < CfgZenMatch.config.zenMatchBonus.size(); i++) {
            int tmpLevel = i + 1;
            int levelStatus = userZenMatch.getStatus(tmpLevel);
            if (tmpLevel == level) levelStatus = newStatus;
            else if (tmpLevel == level + 1) levelStatus = newStatusForNextLevel;
            builder.addAVector(getCommonVector(tmpLevel, levelStatus));
        }
        Actions.save(user, "zen_match", "finish", "event_id", userZenMatch.getEventId(), "level", level, "status", newUserZenMatchStatus);
        addResponse(builder.build());
    }

    void zenMatchReceive() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int level = aLong.get(0).intValue();
        UserZenMatchEntity userZenMatch = mUser.getCache().getUserZenMatch(this, mUser);
        if (userZenMatch == null) return;

        if (level > userZenMatch.getCurrentLevel()) {
            addErrResponse("Chưa hoàn thành ải trước");
            return;
        }

        int currentStatus = userZenMatch.getStatus(level);
        switch (currentStatus) {
            case CfgZenMatch.ZEN_MATCH_NOT_COMPLETED -> addErrResponse("Ải chưa hoàn thành");
            case CfgZenMatch.ZEN_MATCH_RECEIVED -> addErrResponse("Đã nhận quà");
            default -> addErrResponse("Chưa hoàn thành ải trước");
            case CfgZenMatch.ZEN_MATCH_NOT_RECEIVED -> {
                List<Long> aBonus = CfgZenMatch.config.zenMatchBonus.get(level - 1);
                aBonus = Bonus.receiveListItem(mUser, "zen_match_receive", aBonus);
                int newStatus = CfgZenMatch.ZEN_MATCH_RECEIVED;
                Map<Integer, Integer> newStatusMapByLevel = new HashMap<>();
                newStatusMapByLevel.put(level, newStatus);
                String newUserZenMatchStatus = userZenMatch.getNewStatus(newStatusMapByLevel);
                if (!updateUserZenMatch(userZenMatch, newUserZenMatchStatus)) return;
                Pbmethod.CommonVector.Builder cmmVectorBuilder = Pbmethod.CommonVector.newBuilder();
                cmmVectorBuilder.addALong(newStatus);
                cmmVectorBuilder.addAllALong(aBonus);
                addResponse(cmmVectorBuilder.build());
                Actions.save(user, "zen_match", "receive", "level", level, "status", newStatus, "bonus", aBonus);
            }
        }
    }

    //region Logic
    private boolean updateUserZenMatch(UserZenMatchEntity userZenMatch, String newStatus) {
        if (!dbUpdateUserZenMatch(newStatus)) {
            addErrResponse();
            return false;
        }

        userZenMatch.setStatus(newStatus);
        return true;
    }

    long calculateCountdown(Date timeFreeSend) {
        long timeLeft = (timeFreeSend.getTime() - System.currentTimeMillis()) / 1000;
        return timeLeft > 0 ? timeLeft : 0;
    }
    //endregion

    //region Database Access
    private boolean dbUpdateUserZenMatch(String newStatus) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update dson.user_zen_match set status=:newStatus where user_id=:userId and event_id=:eventId");
            query.setParameter("newStatus", newStatus);
            query.setParameter("userId", user.getId());
            query.setParameter("eventId", CfgZenMatch.getEventId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }
    //endregion
}
