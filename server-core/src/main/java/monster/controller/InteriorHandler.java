package monster.controller;

import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.LogicUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgClan;
import monster.config.CfgInterior;
import monster.config.CfgServer;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.dao.mapping.*;
import monster.dao.mapping.main.*;
import monster.protocol.CommonProto;
import monster.service.resource.ResHero;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class InteriorHandler extends AHandler {
    List<UserInteriorHeroEntity> listHero;
    List<UserInteriorItemEntity> inventory;
    List<UserInteriorCollectionEntity> allUserCollection;
    UserInteriorQuestEntity uIQuest;
    List<UserInteriorWallpaperEntity> aWallpaper;
    Map<Integer, UserInteriorHeroEntity> mUIHero = new HashMap<>();
    Map<Long, UserInteriorItemEntity> mUIItem = new HashMap<>();
    Map<Integer, UserInteriorWallpaperEntity> mWallpaper = new HashMap<>();
    Map<Integer, UserInteriorCollectionEntity> allUserCollectionMappedByHeroKey = new HashMap<>();
    UserInteriorEntity uInterior;
    UserInteriorWishlistEntity uIWishlist;
    public String[] args;


    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(INTERIOR_LIST_HERO, INTERIOR_CHOOSE_ITEM, INTERIOR_LIST_ITEM, INTERIOR_SAVE, INTERIOR_HERO_SET, INTERIOR_CREATE,
                INTERIOR_RECYCLE, INTERIOR_WISHLIST, INTERIOR_QUEST_STATUS, INTERIOR_QUEST_RECEIVE, INTERIOR_GALLERY, INTERIOR_AUTO_SET_ITEM, INTERIOR_UNLOCK_BOAT);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public AHandler newInstance() {
        return new InteriorHandler();
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");


        listHero = mUser.getCache().getUIHero(this, mUser);
        inventory = mUser.getCache().getUInteriorItem(this, mUser);
        uIQuest = mUser.getCache().getUInteriorQuest(this, mUser);
        aWallpaper = mUser.getCache().getUInteriorWallpaper(this, mUser);
        uInterior = mUser.getCache().getUInterior(this, mUser);
        allUserCollection = mUser.getCache().getAllUInteriorCollection(this, mUser);
        uIWishlist = mUser.getCache().getUInteriorWishlist(this, mUser);

        mUIHero.clear();
        for (UserInteriorHeroEntity uIHero : listHero) {
            mUIHero.put(uIHero.getHeroId(), uIHero);
        }

        mUIItem.clear();
        for (UserInteriorItemEntity uIItem : inventory) {
            mUIItem.put(uIItem.getItemId(), uIItem);
        }

        mWallpaper.clear();
        for (UserInteriorWallpaperEntity wallpaper : aWallpaper) {
            mWallpaper.put(wallpaper.getWallpaperId(), wallpaper);
        }

        allUserCollectionMappedByHeroKey.clear();
        for (UserInteriorCollectionEntity userInteriorCollection : allUserCollection) {
            allUserCollectionMappedByHeroKey.put(userInteriorCollection.getHeroKey(), userInteriorCollection);
        }

        try {
            switch (actionId) {
                case INTERIOR_LIST_HERO:
                    listHero();
                    break;
                case INTERIOR_SAVE:
                    save();
                    break;
                case INTERIOR_AUTO_SET_ITEM:
                    autoSetItem();
                    break;
                case INTERIOR_LIST_ITEM:
                    listItem();
                    break;
                case INTERIOR_HERO_SET:
                    heroSet();
                    break;
                case INTERIOR_CREATE:
                    create();
                    break;
                case INTERIOR_RECYCLE:
                    recycle();
                    break;
                case INTERIOR_WISHLIST:
                    wishList();
                    break;
                case INTERIOR_QUEST_STATUS:
                    questStatus();
                    break;
                case INTERIOR_QUEST_RECEIVE:
                    questReceive();
                    break;
                case INTERIOR_CHOOSE_ITEM:
                    chooseItem();
                    break;
                case INTERIOR_GALLERY:
                    gallery();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handler service
    void listItem() {
        //Declare
        List<List<Long>> aLLong = getListItemInfo();

        //Check null
        if (aLLong == null || uInterior == null) {
            addErrResponse();
            return;
        }

        if (uInterior.getIsWrong() == 1) {
            String newGalleryData = uInterior.getNewGalleryData(inventory);

            if (!dbUpdateWrongGallery(newGalleryData)) {
                addErrResponse();
                return;
            }

            uInterior.setGalleryData(newGalleryData);
            uInterior.setIsWrong(0);
            uIQuest.updateBetterQuest(CfgInterior.GALLERY, uInterior.getAllCompletedGalleryId());
        }


        //Client
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        aLLong.forEach(aLong -> builder.addAVector(getCommonVector(aLong)));
        builder.addAVector(getCommonVector(Bonus.viewMaterial(MaterialType.WOOD_HARMER, CfgInterior.config.getFee())));

        for (int i = 0; i < CfgInterior.config.getRecycle().size(); i++) {
            builder.addAVector(getCommonVector(Bonus.viewMaterial(MaterialType.WOOD_HARMER, CfgInterior.config.getRecycle().get(i))));
        }

        List<Integer> aWallpaperId = new ArrayList<>();
        for (Map.Entry<Integer, UserInteriorWallpaperEntity> entry : mWallpaper.entrySet()) {
            aWallpaperId.add(entry.getKey());
        }

        builder.addAVector(getCommonIntVector(aWallpaperId));
        builder.addAVector(getCommonIntVector(uInterior.getDataWallpaper(mUser.getResources().getMPet())));
        builder.addAVector(Pbmethod.CommonVector.newBuilder()
                .addALong(uInterior.getNumberCreated())
                .addALong(CfgInterior.config.getMaxNumberItem())
                .addALong(uIQuest.getQuestNotify()));
        builder.addAVector(getCommonIntVector(CfgInterior.config.getLevelCreateNumber()));

        addResponse(builder.build());
    }

    void listHero() {
        //Declare
        List<List<Long>> aLLong = getListHeroInfo();

        //Check null
        if (aLLong == null) {
            addErrResponse();
            return;
        }

        //Client
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        aLLong.forEach(aLong -> {
            if (aLong.get(1) != -1 && aLong.get(2) != -1) builder.addAVector(getCommonVector(aLong));
        });
        builder.addAVector(getCommonVector(GsonUtil.strToListLong(uIWishlist.getWishlist())));
        builder.addAVector(getCommonVector(uInterior.getIsNew()));
        addResponse(builder.build());

        try {
            List<UserInteriorTopEntity> newAllTimeTop6Interior = CfgInterior.getAllTimeTopInterior(inventory, listHero, mUser.getCache().getTopInterior(null, mUser), 6);
            if (!newAllTimeTop6Interior.isEmpty()) dbUpdateInteriorTop(this, newAllTimeTop6Interior);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        }
    }

    void heroSet() {
//        //Declare
//        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
//        int heroId = aLong.get(0).intValue();
//        int shipId = aLong.get(1).intValue();
//        int floorIndex = aLong.get(2).intValue();
//        int remove = aLong.get(3).intValue();
//        boolean isRemove = remove == 1;
//        int isNew = uInterior.getIsNew();
//
//        //Check null
//        if (listHero == null || mUIHero == null) {
//            addErrResponse();
//            return;
//        }
//
//        //Check input
//        if (!isOkParams(heroId, shipId, floorIndex)) {
//            addErrResponse(getLang(Lang.err_params));
//            return;
//        }
//
//        //Hero đã lắp trước đó
//        UserInteriorHeroEntity oldUIHero = getHeroSet(shipId, floorIndex);
//        UserHeroEntity oldUHero = null;
//
//        if (oldUIHero != null) oldUHero = mUser.getResources().getHero(oldUIHero.getHeroId());
//
//        //Tháo tướng
//        if (isRemove) {
//            //Update db
//            if (!dbDeleteUIHero(heroId)) {
//                addErrResponse();
//                return;
//            }
//
//            //Cache
//            mUser.getCache().removeUIHero(heroId);
//
//            //Client
//            addResponse(getCommonVector(uIQuest.getQuestNotify()));
//            if (oldUHero != null) {
//                oldUHero.calculatePointHero(mUser);
//            }
//            return;
//        }
//
//        //Nếu lắp tướng thì check hero đó đã được lắp chưa
//        if (mUIHero.containsKey(heroId)) {
//            addErrResponse(getLang(Lang.interior_hero_already_set));
//            return;
//        }
//
//        //Check sao tướng
//        UserHeroEntity newUHero = mUser.getResources().getHero(heroId);
//        if (newUHero.getStar() < 15) {
//            addErrResponse(getLang(Lang.interior_hero_not_enough_star));
//            return;
//        }
//
//        //Id hero đã lắp trước đó
//        int heroSetId = oldUIHero == null ? 0 : oldUIHero.getHeroId();
//
//        //Update db
//        UserInteriorHeroEntity newUIHero = new UserInteriorHeroEntity(heroId, newUHero.getHeroId(), user.getId(), shipId, floorIndex);
//        if (!dbUpdateUIHero(newUIHero, heroSetId, isNew)) {
//            addErrResponse();
//            return;
//        }
//
//        //Cache
//        mUser.getCache().addUInteriorHero(newUIHero);
//        mUser.getCache().removeUIHero(heroSetId);
//        uInterior.setIsNew(0);
//
//        //Client
//        addResponse(getCommonVector(uIQuest.getQuestNotify()));
//
//        //region Update quest
//        ResHeroEntity resHero = ResHero.getHero(newUHero.getHeroId());
////        uIQuest.updateBetterQuest(InteriorQuest.SET_HERO_FACTION, resHero.getHeroFaction().value);
//        //endregion
//
//        //Tính lại điểm cho hero
//        if (oldUHero != null) {
//            oldUHero.calculatePointHero(mUser);
//        }
//        newUHero.calculatePointHero(mUser);
//
//        try {
//            List<UserInteriorTopEntity> newAllTimeTop6Interior = CfgInterior.getAllTimeTopInterior(inventory, listHero, mUser.getCache().getTopInterior(null, mUser), 6);
//            if (!newAllTimeTop6Interior.isEmpty()) dbUpdateInteriorTop(this, newAllTimeTop6Interior);
//        } catch (Exception ex) {
//            Logs.error(GUtil.exToString(ex));
//        }
    }

    void save() {
//        //Declare;
//        Pbmethod.ListCommonVector aCmm = CommonProto.parseListCommonVector(requestData);
//        int shipId = aCmm.getAVector(0).getALongList().get(0).intValue();
//        int floorIndex = aCmm.getAVector(0).getALongList().get(1).intValue();
//
//        debug("-------1------");
//
//        if (aCmm.getAVectorCount() < 3) {
//            addErrResponse(getLang(Lang.err_params));
//            return;
//        }
//
//        debug("-------2------");
//
//
//        //Check null
//        if (uInterior == null) {
//            debug("-------1------");
//            addErrResponse();
//            return;
//        }
//
//        debug("-------3------");
//
//        //Wallpaper
//        int wallpaperId = aCmm.getAVector(2).getALongList().get(0).intValue();
//        if (!mWallpaper.containsKey(wallpaperId)) {
//            addErrResponse(getLang(Lang.interior_wallpaper_not_existed));
//            return;
//        }
//
//        debug("-------4------");
//
//        String newDataWallpaper = uInterior.getNewWallpaperData(mUser.getResources().getMPet(), shipId, floorIndex, wallpaperId);
//        Map<Integer, UserInteriorItemEntity> mapItemWithSpaceIndex = getMItemWithSpaceIndex(aCmm.getAVector(1).getALongList(), shipId, floorIndex);
//
//        debug("-------5------");
//
//        if (mapItemWithSpaceIndex == null) return;
//
//        debug("-------6------");
//
//        int numberItemRemove = getCountItemInShipOnFloor(shipId, floorIndex);
//        int numberItemSet = mapItemWithSpaceIndex.size();
//        int numberInventoryIncrease = Math.max(0, numberItemRemove - numberItemSet);
//
//        if (!isNumberItemInInventoryOK(numberInventoryIncrease)) {
//            addErrResponse(String.format(getLang(Lang.interior_max_number_furniture), CfgInterior.config.getMaxNumberItem()));
//            return;
//        }
//
//        debug("-------7------");
//
//        if (numberItemSet == 0) {
//            if (!dbUpdateToRemoveItemOnly(shipId, floorIndex)) {
//                debug("-------2------");
//                addErrResponse();
//                return;
//            }
//        } else {
//            List<String> aStrData = getAStringDataWhenSetUpItem(mapItemWithSpaceIndex, shipId, floorIndex);
//            String sql = getSqlToSaveItem(aStrData);
//            if (!dbUpdateToSaveItem(sql, shipId, floorIndex, newDataWallpaper)) {
//                debug("-------3------");
//                return;
//            }
//        }
//
//        debug("-------8------");
//
//
//        //region Nếu update thành công thì cache
//        cacheSaveItem(aCmm, shipId, floorIndex, newDataWallpaper);
//        //endregion
//
//        debug("-------9------");
//
//        //region Update quest
//        int countItemByQuality = CfgInterior.getCountUsedItemByQuality(inventory, getAQualityToQuest());
//        uIQuest.updateBetterQuest(CfgInterior.ITEM_QUALITY2, Collections.singletonList(countItemByQuality));
//        //endregion
//
//        debug("-------10------");
//
//        //region Client
//        addResponse(getCommonVector(uIQuest.getQuestNotify()));
//        //endregion
//
//        //region Tính lại điểm cho hero
//        UserHeroEntity uHero = getUIHeroInShipOnFloor(shipId, floorIndex);
//        if (uHero != null) uHero.calculatePointHero(mUser);
//        //endregion
//
//        try {
//            List<UserInteriorTopEntity> newAllTimeTop6Interior = CfgInterior.getAllTimeTopInterior(inventory, listHero, mUser.getCache().getTopInterior(null, mUser), 6);
//            if (!newAllTimeTop6Interior.isEmpty()) dbUpdateInteriorTop(this, newAllTimeTop6Interior);
//        } catch (Exception ex) {
//            Logs.error(GUtil.exToString(ex));
//        }
    }

    void create() {
        //Declare
        int number = CommonProto.parseCommonVector(requestData).getALong(0) == 0 ? 1 : 10;
        int numberItemFree = 0;
        boolean isFirstTime = false;

        if (uInterior == null || uIWishlist == null) {
            addErrResponse();
            return;
        }

        if (uInterior.isCreateTooFast()) {
            return;
        }

        Map<Integer, ResInteriorHeroEntity> allResInteriorHeroMappedByHeroKey = dbGetResInteriorHero();
        if (allResInteriorHeroMappedByHeroKey == null) {
            addErrResponse();
            return;
        }

        //Đếm số đồ chưa lắp
        for (UserInteriorItemEntity uIItem : inventory) {
            if (uIItem.isFree()) numberItemFree += 1;
        }

        //Check max số lượng đồ chưa lắp trong hòm đồ
        if (numberItemFree >= CfgInterior.config.getMaxNumberItem()) {
            addErrResponse(String.format(getLang(Lang.interior_max_number_furniture), CfgInterior.config.getMaxNumberItem()));
            return;
        }

        number = Math.min(number, CfgInterior.config.getMaxNumberItem() - numberItemFree);

        if (uInterior.getTotalNumberCreated() == 0) {
            //Lần đầu chế đồ
            if (listHero.isEmpty()) {
                addErrResponse();
                return;
            }

            if (number != 1) {
                addErrResponse();
                return;
            }

            isFirstTime = true;
        }

        Set<Integer> wishList = new HashSet<>(GsonUtil.strToListInt(uIWishlist.getWishlist()));
        List<UserInteriorItemEntity> aNewUIItem = new ArrayList<>();
        List<Long> aNewId = new ArrayList<>();

        if (isFirstTime) {
            //Lần đầu chế đồ
            UserInteriorItemEntity newUIItem = getRandomUIItemSingleTime(user.getId(), wishList, true, listHero.get(0), false, allResInteriorHeroMappedByHeroKey);
            aNewUIItem.add(newUIItem);
        } else {
            List<ResInteriorItemEntity> allResItemBefore = new ArrayList<>();
            for (int i = 0; i < number; i++) {
                UserInteriorItemEntity newUIItem = getRandomUIItem(user.getId(), wishList, false, null, false, allResItemBefore, allResInteriorHeroMappedByHeroKey);
                if (newUIItem != null) aNewUIItem.add(newUIItem);
            }
        }
        number = aNewUIItem.size();

        //Check số búa gỗ
        JsonArray arrConvertPrice = GsonUtil.parseFromListLong(Bonus.viewMaterial(MaterialType.WOOD_HARMER, isFirstTime ? 0 : CfgInterior.config.getFee() * number));
        if (!mUser.checkPrice(this, arrConvertPrice)) {
            return;
        }

        //Trừ búa gỗ
        List<Long> aBonus = Bonus.receiveListItem(mUser, arrConvertPrice, "interior_create");
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        //Bonus thẻ
        int newNumberCreated = uInterior.getNumberCreated() + number;
        List<Long> aBonusCard = CfgInterior.getBonusCard(newNumberCreated, uInterior.getLastReceive());
        if (aBonusCard == null) {
            addErrResponse();
            return;
        }

        int newLastReceive = CfgInterior.getNewLastReceive(newNumberCreated, uInterior.getLastReceive());
        if (newNumberCreated >= CfgInterior.getMaxLevelCreateNumber())
            newNumberCreated -= CfgInterior.getMaxLevelCreateNumber();
        String newGalleryData = uInterior.getNewGalleryData(aNewUIItem);
        String sqlToUpdateCollection = getSqlToUpdateCollection(aNewUIItem);
        Map<Integer, String> allNewDataCreatedAsStringMappedByHeroKey = getAllNewDataCreatedAsStringMappedByHeroKey(aNewUIItem);
        Map<Integer, Integer> allNewNumberCreatedAsStringMappedByHeroKey = getAllNewNumberCompleteMappedByHeroKey(aNewUIItem);
        int newTotalNumberCreated = uInterior.getTotalNumberCreated() + number;

//        Update db, trả lại búa gỗ nếu update fail
        if (!dbUpdateCreate(aNewUIItem, newNumberCreated, newTotalNumberCreated, uInterior.getNumberUsedYellowCard(), uInterior.getNumberUsedRedCard(), newLastReceive, newGalleryData, sqlToUpdateCollection)) {
            arrConvertPrice = GsonUtil.parseFromListLong(Bonus.viewMaterial(MaterialType.WOOD_HARMER, CfgInterior.config.getFee() * number));
            Bonus.receiveListItem(mUser, arrConvertPrice, "interior_create_fail");

            addErrResponse();
            return;
        }

        //Cache
        uInterior.setNumberCreated(newNumberCreated);
        uInterior.setLastReceive(newLastReceive);
        uInterior.setGalleryData(newGalleryData);
        uInterior.setTotalNumberCreated(newTotalNumberCreated);
        uInterior.setLastTimeCreate(new Date());
        allNewDataCreatedAsStringMappedByHeroKey.forEach((heroKey, newDataCreatedAsString) -> {
            UserInteriorCollectionEntity userCollection = allUserCollectionMappedByHeroKey.get(heroKey);
            userCollection.setDataCreated(newDataCreatedAsString);
        });
        allNewNumberCreatedAsStringMappedByHeroKey.forEach((heroKey, newNumberComplete) -> {
            UserInteriorCollectionEntity userCollection = allUserCollectionMappedByHeroKey.get(heroKey);
            userCollection.setNumberComplete(newNumberComplete);
        });

        List<Integer> allNewItemKey = new ArrayList<>();
        for (UserInteriorItemEntity uIItem : aNewUIItem) {
            aNewId.add(uIItem.getItemId());
            allNewItemKey.add(uIItem.getItemKey());
        }

        //Nhận thẻ
        aBonusCard = Bonus.receiveListItem(mUser, "bonus_card", aBonusCard);
        aBonus.addAll(aBonusCard);

        Actions.save(user, "interior", "create",
                "ids", StringHelper.toDBString(aNewId),
                "keys", StringHelper.toDBString(allNewItemKey),
                "bonus", aBonus,
                "collection");

        //Cache
        mUser.getCache().addUIItem(aNewUIItem);
        uInterior.setNumberCreated(newNumberCreated);

        uIQuest.updateBetterQuest(CfgInterior.GALLERY, uInterior.getAllCompletedGalleryId());
        uIQuest.updateBetterQuest(CfgInterior.ITEM_CREATE, Collections.singletonList(uInterior.getTotalNumberCreated()));

        //Client
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector((long) uInterior.getNumberCreated(), (long) uIQuest.getQuestNotify()));
        builder.addAVector(getCommonVector(aBonus));
        for (UserInteriorItemEntity uIItem : aNewUIItem) {
            builder.addAVector(getCommonVector(uIItem.getInfo()));
        }

        addResponse(builder.build());
    }

    void chooseItem() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int clazz = aLong.get(0).intValue();
        int faction = aLong.get(1).intValue();
        int heroKey = aLong.get(2).intValue();
        int numberItem = 0;
        boolean isYellowCard = heroKey < 1;

        //Đếm số đồ chưa lắp
        for (UserInteriorItemEntity uIItem : inventory) {
            if (uIItem.isFree()) numberItem += 1;
        }

        //Check max số lượng đồ chưa lắp trong hòm đồ
        if (numberItem >= CfgInterior.config.getMaxNumberItem()) {
            addErrResponse(String.format(getLang(Lang.interior_max_number_furniture), CfgInterior.config.getMaxNumberItem()));
            return;
        }

        //Check số thẻ
        MaterialType materialType = isYellowCard ? MaterialType.INTERIOR_CARD_GOLD : MaterialType.INTERIOR_CARD_RED;

        JsonArray arrConvertPrice = GsonUtil.parseFromListLong(Bonus.viewMaterial(materialType, 1));
        if (!mUser.checkPrice(this, arrConvertPrice)) {
            addErrResponse(getLang(Lang.err_not_enough_material));
            return;
        }

        //Trừ thẻ
        List<Long> aBonus = Bonus.receiveListItem(mUser, arrConvertPrice, "interior_choose");
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        Set<Integer> wishList = new HashSet<>(GsonUtil.strToListInt(uIWishlist.getWishlist()));
        Map<Integer, ResInteriorHeroEntity> allResInteriorHeroMappedByHeroKey = dbGetResInteriorHero();
        if (allResInteriorHeroMappedByHeroKey == null) {
            addErrResponse();
            return;
        }

        UserInteriorItemEntity newUIItem = getRandomUIItemByCard(clazz, faction, heroKey, wishList, allResInteriorHeroMappedByHeroKey);
        if (newUIItem == null) {
            addErrResponse();
            return;
        }

        String newGalleryData = uInterior.getNewGalleryData(Collections.singletonList(newUIItem));
        List<UserInteriorItemEntity> allNewItem = Collections.singletonList(newUIItem);
        String sqlToUpdateCollection = getSqlToUpdateCollection(allNewItem);
        int newNumberUsedYellowCard = isYellowCard ? uInterior.getNumberUsedYellowCard() + 1 : uInterior.getNumberUsedYellowCard();
        int newNumberUsedRedCard = isYellowCard ? uInterior.getNumberUsedRedCard() : uInterior.getNumberUsedRedCard() + 1;
        Map<Integer, String> allNewDataCreatedAsStringMappedByHeroKey = getAllNewDataCreatedAsStringMappedByHeroKey(allNewItem);

        if (!dbUpdateCreate(allNewItem, uInterior.getNumberCreated(), uInterior.getTotalNumberCreated(), newNumberUsedYellowCard, newNumberUsedRedCard, uInterior.getLastReceive(), newGalleryData, sqlToUpdateCollection)) {
            arrConvertPrice = GsonUtil.parseFromListLong(Bonus.viewMaterial(materialType, 1));
            Bonus.receiveListItem(mUser, arrConvertPrice, "interior_choose_fail");

            addErrResponse();
            return;
        }

        //Log
        Actions.save(user, "interior", "choose_item", "id", newUIItem.getItemId());

        //Cache
        mUser.getCache().addUIItem(newUIItem);
        uInterior.setNumberUsedRedCard(newNumberUsedRedCard);
        uInterior.setNumberUsedYellowCard(newNumberUsedYellowCard);
        uInterior.setGalleryData(newGalleryData);

        allNewDataCreatedAsStringMappedByHeroKey.forEach((heroId, newDataCreatedAsString) -> {
            UserInteriorCollectionEntity userCollection = allUserCollectionMappedByHeroKey.get(heroId);
            userCollection.setDataCreated(newDataCreatedAsString);
        });

        //Client
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector(newUIItem.getInfo()));
        builder.addAVector(getCommonVector(aBonus));
        addResponse(builder.build());

        uIQuest.updateBetterQuest(CfgInterior.GALLERY, uInterior.getAllCompletedGalleryId());
        if (isYellowCard) {
            uIQuest.updateBetterQuest(CfgInterior.YELLOW_CARD, Collections.singletonList(uInterior.getNumberUsedYellowCard()));
        } else {
            uIQuest.updateBetterQuest(CfgInterior.RED_CARD, Collections.singletonList(uInterior.getNumberUsedRedCard()));
        }
    }

    void recycle() {
        List<Long> aItemId = CommonProto.parseCommonVector((requestData)).getALongList();

        if (aItemId == null || aItemId.isEmpty()) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }

        int number = 0;

        for (Long aLong : aItemId) {
            long itemId = aLong;
            UserInteriorItemEntity uIItem = mUIItem.get(itemId);

            if (uIItem == null) {
                addErrResponse(getLang(Lang.interior_item_not_existed));
                return;
            }

            if (uIItem.getShipId() > 0 && uIItem.getFloorIndex() >= 0 && uIItem.getSpaceIndex() >= 0) {
                addErrResponse(getLang(Lang.interior_recycle_require_remove));
                return;
            }

            ResInteriorItemEntity resItem = CfgInterior.mResItem.get(uIItem.getItemKey());
            if (resItem == null) {
                addErrResponse();
                return;
            }

            number += CfgInterior.config.getRecycle().get(resItem.getQuality() - 1);
        }

        String itemIds = aItemId.toString();
        itemIds = "(" + itemIds.substring(1, itemIds.length() - 1) + ")";
        String sql = "delete from dson.user_interior_item where item_id in " + itemIds;

        List<Long> aBonus = Bonus.receiveListItem(mUser, "interior_recycle", Bonus.viewMaterial(MaterialType.WOOD_HARMER, number));
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        if (!DBJPA.rawSQL(sql)) {
            addErrResponse();
            return;
        }

        //Log
        Actions.save(user, "interior", "recycle", "ids", StringHelper.toDBString(aItemId), "bonus", aBonus);

        addResponse(getCommonVector(aBonus));
        mUser.getCache().removeUIItem(aItemId);
    }

    void wishList() {
        List<Long> aHeroKey = CommonProto.parseCommonVector(requestData).getALongList();
        Set<Integer> setWishlist = new HashSet<>();
        for (Long heroKey : aHeroKey) {
            setWishlist.add(heroKey.intValue());
        }

        if (!dbUpdateWishlist(setWishlist.toString())) {
            addErrResponse();
            return;
        }

        uIWishlist.setWishlist(setWishlist.toString());

        addResponse(null);
    }

    void questStatus() {
        if (uIQuest == null) {
            addErrResponse();
            return;
        }

        addResponse(uIQuest.getAllQuestInfo().build());
    }

    void questReceive() {
        //Declare
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int questId = aLong.get(0).intValue();
        Long value = aLong.get(1);
        List<Long> questData = uIQuest.getQuestData(questId);
        List<Long> questReceive = uIQuest.getQuestReceive(questId);
        ResInteriorQuestEntity resQuest = CfgInterior.mResQuest.get(questId);

        //Check nhận quà?
        if (isReceived(resQuest.isPlusType(), questReceive, value)) {
            addErrResponse(getLang(Lang.bonus_already_received));
            return;
        }

        //Check hoàn thành?
        if (!isCompleted(resQuest.isPlusType(), questData, value)) {
            addErrResponse(getLang(Lang.quest_incomplete));
            return;
        }
        int newWallPaperId = resQuest.getWallpaper(value);

        List<Long> aBonus = resQuest.getBonus(value);

        String newReceive = uIQuest.getNewReceive(questId, resQuest.isPlusType(), value);

        //Update db
        if (!DBJPA.update("user_interior_quest", Arrays.asList("receive", newReceive), Arrays.asList("user_id", user.getId()))) {
            addErrResponse();
            return;
        }

        //Add bonus
        if (!aBonus.isEmpty()) {
            aBonus = Bonus.receiveListItem(mUser, "interior_quest", aBonus);
            if (aBonus.isEmpty()) {
                addErrResponse();
                return;
            }
        }

        //Add wallpaper
        if (newWallPaperId > 0 && !mWallpaper.containsKey(newWallPaperId)) {
            UserInteriorWallpaperEntity newWallPaper = new UserInteriorWallpaperEntity(user.getId(), newWallPaperId);
            //Update db
            if (!DBJPA.save(newWallPaper)) {
                addErrResponse();
                return;
            }

            //Cache
            mUser.getCache().addUIWallpaper(Collections.singletonList(newWallPaper));
        }

        uIQuest.setReceive(newReceive);

        //Logs
        Actions.save(user, "interior", "quest", "quest_id", questId, "bonus", aBonus, "wallpaper", newWallPaperId, "data", questData, "receive", questReceive);

        //Client
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector(aBonus));
        builder.addAVector(getCommonVector(newWallPaperId, uIQuest.getQuestNotify()));
        addResponse(builder.build());
    }

    void gallery() {
        if (uInterior == null) {
            addErrResponse();
            return;
        }
        List<List<Integer>> aGalleryData = uInterior.getDataGallery();

        if (aGalleryData == null) {
            addErrResponse();
            return;
        }

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (List<Integer> galleryData : aGalleryData) {
            List<Integer> galleryInfo = new ArrayList<>(galleryData);
            galleryInfo.remove(0);
            builder.addAVector(getCommonIntVector(galleryInfo));
        }

        addResponse(builder.build());
    }

    void autoSetItem() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int shipId = aLong.get(0).intValue();
        int floorIndex = aLong.get(1).intValue();

        UserInteriorHeroEntity uIHero = null;
        for (UserInteriorHeroEntity hero : listHero) {
            if (hero.getShipId() == shipId && hero.getFloorIndex() == floorIndex) uIHero = hero;
        }

        if (uIHero == null) {
            addErrResponse("Chưa lắp tướng");
            return;
        }

        ResInteriorCollectionEntity resCollection = CfgInterior.collectionMapByHeroKey.get(uIHero.getHeroKey());

        if (resCollection == null) {
            addErrResponse("Tướng này chưa có data, heroKey: " + uIHero.getHeroKey());
            return;
        }

        List<Integer> setItem = resCollection.getACollection();
        List<Long> aAutoItemId = new ArrayList<>();
        Set<Long> setAutoItemId = new HashSet<>();

        for (int i = 0; i < CfgInterior.config.getNumberSlot(); i++) {
            ResInteriorItemEntity bestItem = CfgInterior.mResItem.get(setItem.get(i));
            UserInteriorItemEntity autoItem = getAutoItem(uIHero, bestItem, setAutoItemId);
            if (autoItem != null) {
                setAutoItemId.add(autoItem.getItemId());
                aAutoItemId.add(autoItem.getItemId());
                continue;
            }

            aAutoItemId.add(-1L);
        }

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        builder.addAVector(getCommonVector(aAutoItemId));

        int suitableWallpaperId = -1;
        for (int wallpaperId : mWallpaper.keySet()) {
            if (wallpaperId == resCollection.getWallpaperId()) {
                suitableWallpaperId = wallpaperId;
                break;
            }
        }

        builder.addAVector(getCommonVector(suitableWallpaperId));
        addResponse(builder.build());
    }
    //endregion

    //region Logic
    UserInteriorItemEntity getAutoItem(UserInteriorHeroEntity uIHero, ResInteriorItemEntity bestItem, Set<Long> setAutoItemId) {
        UserInteriorItemEntity itemForHero = null;
        UserInteriorItemEntity itemForClassAndFaction = null;
        UserInteriorItemEntity itemForClassOrFaction = null;

        for (UserInteriorItemEntity uIItem : inventory) {
            ResInteriorItemEntity resItem = CfgInterior.mResItem.get(uIItem.getItemKey());
            ResHeroEntity resHero = ResHero.getHero(uIHero.getHeroKey());

            if (setAutoItemId.contains(uIItem.getItemId()) ||
                    (!uIItem.isFree() && !uIItem.isSetInShipAndOnFloor(uIHero.getShipId(), uIHero.getFloorIndex())) ||
                    resItem.isOnGround() != bestItem.isOnGround()) {
                continue;
            }


            if (uIItem.getItemKey() == bestItem.getId() && itemForHero == null) {
                itemForHero = uIItem;
            }

            if (resItem.getFaction() == resHero.getHeroFaction().value &&
                    resItem.getClazz() == resHero.getHeroClass().value &&
                    itemForClassAndFaction == null) {
                itemForClassAndFaction = uIItem;
            }

            if ((resItem.getFaction() == resHero.getHeroFaction().value || resItem.getClazz() == resHero.getHeroClass().value) &&
                    itemForClassOrFaction == null) {
                itemForClassOrFaction = uIItem;
            }

            if (itemForHero != null && itemForClassAndFaction != null && itemForClassOrFaction != null) {
                break;
            }
        }

        if (itemForHero != null) return itemForHero;
        if (itemForClassAndFaction != null) return itemForClassAndFaction;
        return itemForClassOrFaction;
    }

    /**
     * @param heroId     heroId
     * @param shipId     id thuyền
     * @param floorIndex index tầng
     * @return true nếu input hợp lệ
     */
    boolean isOkParams(int heroId, int shipId, int floorIndex) {
        return mUser.getResources().getMHero().containsKey(heroId) &&
                mUser.getResources().getMPet().containsKey(shipId) &&
                floorIndex >= -1 && floorIndex < CfgInterior.config.getNumberFloor();
    }

    /**
     * @param itemId     itemId
     * @param shipId     id thuyền
     * @param floorIndex index tầng
     * @param spaceIndex index ô
     * @return true nếu input hợp lệ
     */
    boolean isOkParams(long itemId, int shipId, int floorIndex, int spaceIndex) {
        return itemId >= 1 &&
                spaceIndex >= -1 && spaceIndex < CfgInterior.config.getNumberSlot() &&
                mUser.getResources().getMPet().containsKey(shipId) &&
                floorIndex >= -1 && floorIndex < CfgInterior.config.getNumberFloor();
    }

    /**
     * @return list info của các item
     */
    List<List<Long>> getListItemInfo() {
        List<List<Long>> aLLong = new ArrayList<>();
        if (inventory == null) return null;

        inventory.forEach(item -> aLLong.add(item.getInfo()));

        return aLLong;
    }

    /**
     * @return list info của các tướng
     */
    List<List<Long>> getListHeroInfo() {
        List<List<Long>> aLLong = new ArrayList<>();
        if (listHero == null) return null;

        listHero.forEach(hero -> aLLong.add(hero.getInfo()));

        return aLLong;
    }

    /**
     * @param shipId     id thuyền
     * @param floorIndex index tầng
     * @return
     */
    UserInteriorHeroEntity getHeroSet(int shipId, int floorIndex) {
        for (UserInteriorHeroEntity uIHero : listHero) {
            if (uIHero.getShipId() == shipId && uIHero.getFloorIndex() == floorIndex) return uIHero;
        }

        return null;
    }

    boolean isReceived(boolean isPlus, List<Long> questReceive, long value) {
        if (isPlus) {
            return value <= questReceive.get(1);
        }

        for (int i = 1; i < questReceive.size() - 1; i += 2) {
            if (value == questReceive.get(i)) {
                if (questReceive.get(i + 1) == 1) {
                    return true;
                }
            }
        }

        return false;
    }

    boolean isCompleted(boolean isPlus, List<Long> questData, long value) {
        if (isPlus) {
            return questData.get(1) >= value;
        }

        for (int i = 1; i < questData.size() - 1; i += 2) {
            if (value != questData.get(i)) continue;
            return questData.get(i + 1) == 1;
        }

        return false;
    }

    public void cacheSaveItem(Pbmethod.ListCommonVector aCmm, int shipId, int floorIndex, String newDataWallpaper) {
        for (UserInteriorItemEntity uIItem : inventory) {
            if (uIItem.isSetInShipAndOnFloor(shipId, floorIndex)) {
                uIItem.setShipId(-1);
                uIItem.setFloorIndex(-1);
                uIItem.setSpaceIndex(-1);
            }

        }

        //Cache lắp đồ
        List<Long> aLong = aCmm.getAVector(1).getALongList();

        for (int i = 0; i < aLong.size() - 1; i += 2) {
            long itemId = aLong.get(i);
            int spaceIndex = aLong.get(i + 1).intValue();

            UserInteriorItemEntity uIItem = mUIItem.get(itemId);
            uIItem.setShipId(shipId);
            uIItem.setFloorIndex(floorIndex);
            uIItem.setSpaceIndex(spaceIndex);
        }

        //Cache wallpaper
        uInterior.setWallpaperData(newDataWallpaper);
    }

    private UserHeroEntity getUIHeroInShipOnFloor(int shipId, int floorIndex) {
        for (UserInteriorHeroEntity uIHero : listHero) {
            if (uIHero.getShipId() == shipId && uIHero.getFloorIndex() == floorIndex)
                return mUser.getResources().getHero(uIHero.getHeroId());
        }

        return null;
    }

    private Map<Integer, UserInteriorItemEntity> getMItemWithSpaceIndex(List<Long> aLong, int shipId, int floorIndex) {
        Map<Integer, UserInteriorItemEntity> mapItemWithSpaceIndex = new HashMap<>();
        int countGroundItem = 0, countWallItem = 0;
//
        for (int i = 0; i < aLong.size() - 1; i = i + 2) {
            long itemId = aLong.get(i);
            int spaceIndex = aLong.get(i + 1).intValue();

            if (!isOkParams(itemId, shipId, floorIndex, spaceIndex)) {
                addErrResponse(getLang(Lang.err_params));
                return null;
            }

            UserInteriorItemEntity uIItem = mUIItem.get(itemId);
            if (uIItem == null) {
                addErrResponse();
                return null;
            }

            ResInteriorItemEntity resItem = CfgInterior.mResItem.get(uIItem.getItemKey());
            if (resItem.isOnGround()) {
                countGroundItem += 1;
            } else {
                countWallItem += 1;
            }

            mapItemWithSpaceIndex.put(spaceIndex, uIItem);
        }

        //Check số lượng đồ đặt đất và treo tường
        if (Math.max(countGroundItem, countWallItem) > CfgInterior.config.getNumberSlot() / 2) {
            addErrResponse(getLang(Lang.interior_wrong_place));
            return null;
        }

        return mapItemWithSpaceIndex;
    }


    private List<String> getAStringDataWhenSetUpItem(Map<Integer, UserInteriorItemEntity> mapItemWithSpaceIndex, int shipId, int floorIndex) {
        List<String> aStrData = new ArrayList<>();
        for (Map.Entry<Integer, UserInteriorItemEntity> entry : mapItemWithSpaceIndex.entrySet()) {
            UserInteriorItemEntity uIItem = entry.getValue();
            int spaceIndex = entry.getKey();
            aStrData.add(String.format("(%s,%s,%s,%s)", uIItem.getItemId(), shipId, floorIndex, spaceIndex));
        }

        return aStrData;
    }

    private String getSqlToSaveItem(List<String> aStrData) {
        String sql = "INSERT INTO dson.user_interior_item (item_id, ship_id,floor_index,space_index) VALUES %s ON DUPLICATE KEY UPDATE " +
                "ship_id = VALUES(ship_id),floor_index = VALUES(floor_index),space_index = VALUES(space_index)";
        sql = String.format(sql, aStrData.stream().collect(Collectors.joining(",")));

        return sql;
    }

    public boolean isNumberItemInInventoryOK(int numberItemIncrease) {
        int numberItem = 0;

        //Đếm số đồ chưa lắp có trong hòm đồ
        for (UserInteriorItemEntity uIItem : inventory) {
            if (uIItem.isFree()) numberItem += 1;
        }

        //Check max (số lượng đồ chưa lắp trong hòm đồ + số lượng đồ tăng thêm vào hòm đồ khi tháo)
        if (numberItem + numberItemIncrease > CfgInterior.config.getMaxNumberItem()) {
            return false;
        }

        return true;
    }

    public List<Integer> getAQualityToQuest() {
        List<Integer> aQuality = new ArrayList<>();
        for (int i = 2; i <= CfgInterior.maxQuality; i++) {
            aQuality.add(i);
        }

        return aQuality;
    }

    public int getCountItemInShipOnFloor(int shipId, int floorIndex) {
        int count = 0;
        for (UserInteriorItemEntity uIItem : inventory) {
            if (shipId == uIItem.getShipId() && floorIndex == uIItem.getFloorIndex()) count++;
        }

        return count;
    }

    private Map<Integer, List<Integer>> getAllNewDataCreatedMappedByHeroKey(List<UserInteriorItemEntity> allNewItem) {
        Map<Integer, List<Integer>> allNewDataCreatedMappedByHeroKey = new HashMap<>();
        Map<Integer, List<Integer>> allNewItemKeyCreatedMappedByHeroKey = new HashMap<>();
        allUserCollectionMappedByHeroKey.forEach((heroKey, userCollection) -> {
            List<Integer> allNewItemKeyCreatedForHero = new ArrayList<>();

            for (UserInteriorItemEntity newItem : allNewItem) {
                ResInteriorItemEntity resItem = CfgInterior.mResItem.get(newItem.getItemKey());
                if (resItem.getQuality() != CfgInterior.maxQuality) continue;
                if (resItem.getHero() != heroKey) continue;

                allNewItemKeyCreatedForHero.add(newItem.getItemKey());
            }

            allNewItemKeyCreatedMappedByHeroKey.put(heroKey, allNewItemKeyCreatedForHero);
        });

        allNewItemKeyCreatedMappedByHeroKey.forEach((heroKey, allNewItemKeyForHero) -> {
            UserInteriorCollectionEntity userCollection = allUserCollectionMappedByHeroKey.get(heroKey);
            allNewDataCreatedMappedByHeroKey.put(heroKey, userCollection.getNewDataCreated(allNewItemKeyForHero));
        });

        return allNewDataCreatedMappedByHeroKey;
    }

    private Map<Integer, Integer> getAllNewNumberCompleteMappedByHeroKey(List<UserInteriorItemEntity> allNewItem) {
        Map<Integer, Integer> allNewNumberCompleteMappedByHeroKey = new HashMap<>();
        Map<Integer, List<Integer>> allNewItemKeyCreatedMappedByHeroKey = new HashMap<>();
        allUserCollectionMappedByHeroKey.forEach((heroKey, userCollection) -> {
            List<Integer> allNewItemKeyCreatedForHero = new ArrayList<>();

            for (UserInteriorItemEntity newItem : allNewItem) {
                ResInteriorItemEntity resItem = CfgInterior.mResItem.get(newItem.getItemKey());
                if (resItem.getQuality() != CfgInterior.maxQuality) continue;
                if (resItem.getHero() != heroKey) continue;

                allNewItemKeyCreatedForHero.add(newItem.getItemKey());
            }

            allNewItemKeyCreatedMappedByHeroKey.put(heroKey, allNewItemKeyCreatedForHero);
        });

        allNewItemKeyCreatedMappedByHeroKey.forEach((heroKey, allNewItemKeyForHero) -> {
            UserInteriorCollectionEntity userCollection = allUserCollectionMappedByHeroKey.get(heroKey);
            allNewNumberCompleteMappedByHeroKey.put(heroKey, userCollection.getNewNumberComplete(allNewItemKeyForHero));
//            System.out.println("allNewNumberCompleteMappedByHeroKey: " + allNewNumberCompleteMappedByHeroKey.get(heroKey));
        });

        return allNewNumberCompleteMappedByHeroKey;
    }

    private Map<Integer, String> getAllNewDataCreatedAsStringMappedByHeroKey(List<UserInteriorItemEntity> allNewItem) {
        Map<Integer, List<Integer>> allNewDataCreatedMappedByHeroKey = getAllNewDataCreatedMappedByHeroKey(allNewItem);
        Map<Integer, String> allNewDataCreatedAsStringMappedByHeroKey = new HashMap<>();
        allNewDataCreatedMappedByHeroKey.forEach((heroKey, allNewItemKey) -> {
            allNewDataCreatedAsStringMappedByHeroKey.put(heroKey, StringHelper.toDBString(allNewItemKey));
        });


        return allNewDataCreatedAsStringMappedByHeroKey;
    }

    private List<String> getAStringDataToUpdateCollection(List<UserInteriorItemEntity> allNewItem) {
        Map<Integer, String> allNewDataCreatedMappedByHeroKey = getAllNewDataCreatedAsStringMappedByHeroKey(allNewItem);
        Map<Integer, Integer> allNewNumberCompleteMappedByHeroKey = getAllNewNumberCompleteMappedByHeroKey(allNewItem);
        List<String> aStrData = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : allNewDataCreatedMappedByHeroKey.entrySet()) {
            String newDataCreated = entry.getValue();
            int newNumberComplete = allNewNumberCompleteMappedByHeroKey.get(entry.getKey());
            aStrData.add(String.format("(%s,%s,'%s',%s)", user.getId(), entry.getKey(), newDataCreated, newNumberComplete));
        }

        return aStrData;
    }

    private String getSqlToUpdateCollection(List<UserInteriorItemEntity> allNewItem) {
        List<String> aStrDataToUpdateCollection = getAStringDataToUpdateCollection(allNewItem);
        if (aStrDataToUpdateCollection.isEmpty()) return null;

        String sql = "INSERT INTO dson.user_interior_collection (user_id, hero_key,data_created,number_complete) VALUES %s ON DUPLICATE KEY UPDATE " +
                "data_created = VALUES(data_created), number_complete = VALUES(number_complete)";
        sql = String.format(sql, aStrDataToUpdateCollection.stream().collect(Collectors.joining(",")));

        return sql;
    }

    /**
     * @param userId userId
     * @return đồ được random
     */
    public UserInteriorItemEntity getRandomUIItem(int userId, Set<Integer> wishList, boolean isFistTime, UserInteriorHeroEntity firstHero, boolean isMaxQuality, List<ResInteriorItemEntity> allResItemBefore, Map<Integer, ResInteriorHeroEntity> allResInteriorHero) {
        if (allResInteriorHero == null) return null;

        //List ResInteriorItem theo quailty, wishlist
        List<ResInteriorItemEntity> aResItem = getAResIItem(wishList, isFistTime, firstHero, isMaxQuality, allResInteriorHero);

        //Random trong list ResInteriorItem vừa lấy
        ResInteriorItemEntity resultResItem = LogicUtil.getRandom(aResItem);

        if (allResItemBefore == null) return new UserInteriorItemEntity(userId, resultResItem.getId());

        int countLoop = 0;
        while (allResItemBefore.contains(resultResItem)) {
            if (countLoop >= 10000) {
                return null;
            }

            resultResItem = LogicUtil.getRandom(aResItem);
            countLoop++;
        }
        allResItemBefore.add(resultResItem);

        return new UserInteriorItemEntity(userId, resultResItem.getId());
    }

    public UserInteriorItemEntity getRandomUIItemSingleTime(int userId, Set<Integer> wishList, boolean isFistTime, UserInteriorHeroEntity firstHero, boolean isMaxQuality, Map<Integer, ResInteriorHeroEntity> allResInteriorHeroMappedByHeroKey) {
        return getRandomUIItem(userId, wishList, isFistTime, firstHero, isMaxQuality, null, allResInteriorHeroMappedByHeroKey);
    }

    public List<ResInteriorItemEntity> getAResIItem(Set<Integer> sWishlist, boolean isFirstTime, UserInteriorHeroEntity firstHero, boolean isMaxQuality, Map<Integer, ResInteriorHeroEntity> allResInteriorHeroMappedByHeroKey) {
        //Random quality của đồ
        int quality;
        if (isFirstTime) {
            ResHeroEntity resHero = ResHero.getHero(firstHero.getHeroKey());
            return CfgInterior.getAllResItemNotMaxQualityByClazzFaction(resHero.getHeroClass().value, resHero.getHeroFaction().value);
        } else if (isMaxQuality) {
            quality = CfgInterior.maxQuality;
        } else {
            quality = LogicUtil.getRandomByRate(CfgInterior.config.getQuality(), CfgInterior.config.getRate());
        }

//        if (CfgServer.isTestServer()) quality = 3;

        List<ResInteriorItemEntity> aResIItem = new ArrayList<>();
        List<Integer> aHeroKey = CfgInterior.getAllHeroKey();

        if (sWishlist == null) {
            sWishlist = new HashSet<>();
        }

        int numberLoop = CfgInterior.config.getNumberWishList() - sWishlist.size();

        for (int i = 0; i < numberLoop; i++) {
            int heroKey = LogicUtil.getRandom(aHeroKey);
            int count = 0;

            while (sWishlist.contains(heroKey) && sWishlist.size() < CfgInterior.config.getNumberWishList()) {
                heroKey = LogicUtil.getRandom(aHeroKey);

                count++;
                if (count > 10000) {
                    break;
                }
            }

            sWishlist.add(heroKey);
        }


        for (Map.Entry<Integer, ResInteriorItemEntity> entry : CfgInterior.mResItem.entrySet()) {
            ResInteriorItemEntity resIItem = entry.getValue();

            if (resIItem.getQuality() != quality) {
                continue;
            }

            if (quality != CfgInterior.maxQuality) {
                aResIItem.add(resIItem);
                continue;
            }

            ResInteriorHeroEntity resInteriorHero = allResInteriorHeroMappedByHeroKey.get(resIItem.getHero());
            if (resInteriorHero == null || (resInteriorHero.getRelease() == 0 && CfgServer.isRealServer())) continue;

            if (isOkToCreate(resIItem, sWishlist)) {
                aResIItem.add(resIItem);
            }
        }

        return aResIItem;
    }

    private List<Integer> getListItemLeft(ResInteriorItemEntity resIItem, UserInteriorCollectionEntity userCollection) {
        ResInteriorCollectionEntity resCollection = CfgInterior.collectionMapByHeroKey.get(resIItem.getHero());
        List<Integer> dataCreated = new ArrayList<>(userCollection.getDataCreated());
        List<Integer> listItemLeft = new ArrayList<>(resCollection.getACollection());
        for (int itemCreated : dataCreated) {
            for (Iterator<Integer> iterator = listItemLeft.iterator(); iterator.hasNext(); ) {
                int itemId = iterator.next();
                if (itemId == itemCreated) {
                    iterator.remove();
                    break;
                }
            }
        }

        return listItemLeft;
    }

    private boolean isOkToCreate(ResInteriorItemEntity resIItem, Set<Integer> sWishlist) {
        if (!sWishlist.contains(resIItem.getHero())) return false;
        UserInteriorCollectionEntity userCollection = allUserCollectionMappedByHeroKey.get(resIItem.getHero());
        if (userCollection.getDataCreated().isEmpty()) return true;

        List<Integer> listItemLeft = getListItemLeft(resIItem, userCollection);
        return listItemLeft.contains(resIItem.getId());
    }

    public UserInteriorItemEntity getRandomUIItemByCard(int clazz, int faction, int heroKey, Set<Integer> wishList, Map<Integer, ResInteriorHeroEntity> allResInteriorHeroMappedByHeroKey) {
        //Quay thẻ đỏ
        if (heroKey > 0)
            return getRandomUIItemSingleTime(user.getId(), wishList, false, null, true, allResInteriorHeroMappedByHeroKey);

        //Quay thẻ vàng
        List<ResInteriorItemEntity> aResItem = CfgInterior.getAllResItemNotMaxQualityByClazzFaction(clazz, faction);

        if (aResItem.isEmpty()) {
            addErrResponse("Chưa có trang bị phù hợp cho nghề và hệ này");
            return null;
        }

        //Random trong list ResInteriorItem vừa lấy
        ResInteriorItemEntity resultResItem = LogicUtil.getRandom(aResItem);

        return new UserInteriorItemEntity(user.getId(), resultResItem.getId());
    }

    public List<Integer> getListIItemOfHero(UserInteriorHeroEntity uIHero) {
        List<Integer> listIItemKey = new ArrayList<>();
        for (UserInteriorItemEntity item : inventory) {
            if (uIHero.getShipId() == item.getShipId() && uIHero.getFloorIndex() == item.getFloorIndex())
                listIItemKey.add(item.getItemKey());
        }

        return listIItemKey;
    }


    //endregion

    //region Database access

    /**
     * @param newUIHero UserInteriorHeroEntity mới
     * @param heroSetId id hero đã lắp trước đó
     * @return true nếu update thành công
     */
    boolean dbUpdateUIHero(UserInteriorHeroEntity newUIHero, int heroSetId, int isNew) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Nếu đã có tướng ở tầng đó
            if (heroSetId > 0) {
                Query query = session.createNativeQuery("delete from user_interior_hero where hero_id=:heroSetId");
                query.setParameter("heroSetId", heroSetId);
                query.executeUpdate();
            }

            if (isNew == 1) {
                Query query = session.createNativeQuery("update user_interior set is_new = 0 where user_id=:userId");
                query.setParameter("userId", user.getId());
                query.executeUpdate();
            }

            session.persist(newUIHero);
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    boolean dbUpdateWishlist(String wishlist) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update user_interior_wishlist set wishlist ='" + wishlist + "' where user_id=:userId");
            query.setParameter("userId", user.getId());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    boolean dbDeleteUIHero(int heroId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("delete from user_interior_hero where hero_id=:heroId");
            query.setParameter("heroId", heroId);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    boolean dbUpdateCreate(List<UserInteriorItemEntity> aNewUIItem, int newNumberCreated, int newTotalNumberCreated, int newNumberUsedYellowCard, int newNumberUsedRedCard, int newLastReceive, String newGalleryData, String sqlToUpdateCollection) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            //
            String sqlToUpdateItem = "insert into user_interior_item (user_id,item_key,ship_id,floor_index,space_index) values ";
            for (int i = 0; i < aNewUIItem.size(); i++) {
                if (i == 0) {
                    sqlToUpdateItem += "(" + aNewUIItem.get(i).getUserId() + "," + aNewUIItem.get(i).getItemKey() + "," + aNewUIItem.get(i).getShipId() + "," + aNewUIItem.get(i).getFloorIndex() + "," + aNewUIItem.get(i).getSpaceIndex() + ") ";
                    continue;
                }
                sqlToUpdateItem += ", (" + aNewUIItem.get(i).getUserId() + "," + aNewUIItem.get(i).getItemKey() + "," + aNewUIItem.get(i).getShipId() + "," + aNewUIItem.get(i).getFloorIndex() + "," + aNewUIItem.get(i).getSpaceIndex() + ") ";
            }

            session.createNativeQuery(sqlToUpdateItem).executeUpdate();

            //
            List<Long> aNewItemId = session.createNativeQuery("select LAST_INSERT_ID()").getResultList();
            if (aNewItemId == null || aNewItemId.isEmpty()) return false;
            long newItemId = aNewItemId.getFirst();
            for (int i = 0; i < aNewUIItem.size(); i++) {
                aNewUIItem.get(i).setItemId(newItemId + i);
            }

            //
            String sqlToUpdateUserInterior = "update user_interior set number_created = " + newNumberCreated +
                    ",total_number_created = " + newTotalNumberCreated +
                    ",number_used_yellow_card=" + newNumberUsedYellowCard +
                    ",number_used_red_card=" + newNumberUsedRedCard +
                    ", last_receive = " + newLastReceive +
                    ", gallery_data ='" + newGalleryData +
                    "', last_time_create = '" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) +
                    "' where user_id = " + aNewUIItem.get(0).getUserId();
            session.createNativeQuery(sqlToUpdateUserInterior).executeUpdate();

            //
            if (!StringHelper.isEmpty(sqlToUpdateCollection))
                session.createNativeQuery(sqlToUpdateCollection).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    public int dbRemoveAllItemAtFloor(EntityManager session, int shipId, int floorIndex) {
        Query query = session.createNativeQuery("update user_interior_item set ship_id=-1,floor_index=-1,space_index=-1 where user_id =:userId and ship_id=:shipId and floor_index=:floorIndex");
        query.setParameter("userId", user.getId());
        query.setParameter("shipId", shipId);
        query.setParameter("floorIndex", floorIndex);

        return query.executeUpdate();
    }

    public boolean dbUpdateToRemoveItemOnly(int shipId, int floorIndex) {
        EntityManager session = null;
        try {
            //region Update db
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update user_interior_item set ship_id=-1,floor_index=-1,space_index=-1 where user_id =:userId and ship_id=:shipId and floor_index=:floorIndex");
            query.setParameter("userId", user.getId());
            query.setParameter("shipId", shipId);
            query.setParameter("floorIndex", floorIndex);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
            //endregion
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    public boolean dbUpdateToSaveItem(String sqlSetItem, int shipId, int floorIndex, String newDataWallpaper) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Tháo toàn bộ đồ khỏi tầng
            Query query = session.createNativeQuery("update user_interior_item set ship_id=-1,floor_index=-1,space_index=-1 where user_id =:userId and ship_id=:shipId and floor_index=:floorIndex");
            query.setParameter("userId", user.getId());
            query.setParameter("shipId", shipId);
            query.setParameter("floorIndex", floorIndex);
            query.executeUpdate();

            //ắp dồ
            session.createNativeQuery(sqlSetItem).executeUpdate();

            //Wallpaper
            session.createNativeQuery("update dson.user_interior set wallpaper_data='" + newDataWallpaper +
                    "' where user_id = " + user.getId()).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private Map<Integer, ResInteriorHeroEntity> dbGetResInteriorHero() {
        List<ResInteriorHeroEntity> allResInteriorHero = DBJPA.getList(CfgServer.DB_MAIN + "res_interior_hero", ResInteriorHeroEntity.class);
        if (allResInteriorHero == null || allResInteriorHero.isEmpty()) return null;

        Map<Integer, ResInteriorHeroEntity> allResInteriorHeroMappedByHeroKey = new HashMap<>();
        for (ResInteriorHeroEntity resInteriorHero : allResInteriorHero) {
            allResInteriorHeroMappedByHeroKey.put(resInteriorHero.getHeroKey(), resInteriorHero);
        }

        return allResInteriorHeroMappedByHeroKey;
    }

    private boolean dbUpdateWrongGallery(String newGalleryData) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Tháo toàn bộ đồ khỏi tầng
            session.createNativeQuery("update user_interior set is_wrong = 0, gallery_data='" + newGalleryData + "' where user_id =:userId").setParameter("userId", user.getId()).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    public static boolean dbUpdateInteriorTop(AHandler handler, List<UserInteriorTopEntity> newAllTimeTop6Interior) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("delete from dson.user_interior_top where user_id=:userId").setParameter("userId", handler.user.getId()).executeUpdate();
            String sqlInsert = "insert into user_interior_top " +
                    "(user_id,top,hero_key,number_red_item_effect3,number_red_item_effect2,number_red_item_effect1," +
                    "number_yellow_item_effect2,number_yellow_item_effect1,number_purple_item) values ";
            for (int i = 0; i < newAllTimeTop6Interior.size(); i++) {
                UserInteriorTopEntity interiorTop = newAllTimeTop6Interior.get(i);
                if (i == 0) {
                    sqlInsert += "(" + interiorTop.getUserId() + "," + interiorTop.getTop() + "," + interiorTop.getHeroKey() + "," +
                            interiorTop.getNumberRedItemEffect3() + "," +
                            interiorTop.getNumberRedItemEffect2() + "," +
                            interiorTop.getNumberRedItemEffect1() + "," +
                            interiorTop.getNumberYellowItemEffect2() + "," +
                            interiorTop.getNumberYellowItemEffect1() + "," +
                            interiorTop.getNumberPurpleItem() + ") ";
                    continue;
                }
                sqlInsert += ", (" + interiorTop.getUserId() + "," + interiorTop.getTop() + "," + interiorTop.getHeroKey() + "," +
                        interiorTop.getNumberRedItemEffect3() + "," +
                        interiorTop.getNumberRedItemEffect2() + "," +
                        interiorTop.getNumberRedItemEffect1() + "," +
                        interiorTop.getNumberYellowItemEffect2() + "," +
                        interiorTop.getNumberYellowItemEffect1() + "," +
                        interiorTop.getNumberPurpleItem() + ") ";
            }

            session.createNativeQuery(sqlInsert).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            handler.closeSession(session);
        }
        return false;
    }
//endregion
}
