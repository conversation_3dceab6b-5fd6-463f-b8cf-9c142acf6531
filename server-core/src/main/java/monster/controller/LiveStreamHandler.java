package monster.controller;

import grep.log.Logs;
import monster.config.CfgArenaCrystal;
import monster.config.CfgArenaTrial;
import monster.config.lang.Lang;
import monster.game.arena.entity.UserArenaCrystalEntity;
import monster.game.arena.entity.UserArenaTrialEntity;
import monster.dao.mapping.UserEntity;
import monster.object.BattleTeam;
import monster.protocol.CommonProto;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.monitor.UserOnline;
import protocol.Pbmethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class LiveStreamHandler extends AHandler {

    @Override
    public AHandler newInstance() {
        return new LiveStreamHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(LIVESTREAM_PLAYER_BATTLE, LIVESTREAM_PLAYER_INO);
        actions.forEach(action -> mHandler.put(action, this));
    }

    //
    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        try {
            List<String> allowIps = Arrays.asList("**************", "*************", "************");
            if (true || allowIps.contains(getIp())) {
                switch (actionId) {
                    case LIVESTREAM_PLAYER_BATTLE:
                        battle();
                        break;
                    case LIVESTREAM_PLAYER_INO:
                        info();
                        break;
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //<editor-fold desc="Handle service">
    void info() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int typeId = aLong.get(0).intValue();
        int userId = aLong.get(1).intValue();
        switch (typeId) {
            case 1: // đơn đấu
                infoCrystal(userId);
                break;
            case 2: // liên đấu
                infoTrial(userId);
                break;
        }
    }

    private void infoCrystal(int userId) {
        UserEntity user = UserOnline.getDbUser(userId);
        CfgArenaCrystal.clearCache();
        UserArenaCrystalEntity userArena = CfgArenaCrystal.getArenaCrystal(user.getId());

        Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());

        addResponse(pbUser.build());
    }

    private void infoTrial(int userId) {
        UserEntity user = UserOnline.getDbUser(userId);
        CfgArenaCrystal.clearCache();
        UserArenaTrialEntity userArena = CfgArenaTrial.getArenaTrial(user.getId());

        Pbmethod.PbUser.Builder pbUser = user.protoTinyUser();
        pbUser.setInfo(Pbmethod.CommonVector.newBuilder().addALong(userArena.getPoint()));
        BattleTeam.toPbUserProto(pbUser, userArena.getDefTeamEntity());

        addResponse(pbUser.build());
    }

    void battle() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int typeId = aLong.get(0).intValue();
        int user1 = aLong.get(1).intValue();
        int user2 = aLong.get(2).intValue();
        switch (typeId) {
            case 1: // đơn đấu
                battleCrystal(user1, user2);
                break;
            case 2: // liên đấu
                battleTrial(user1, user2);
                break;
        }
    }

    private void battleCrystal(int userId1, int userId2) {
        UserEntity user1 = UserOnline.getDbUser(userId1);
        UserArenaCrystalEntity userArena1 = CfgArenaCrystal.getArenaCrystal(user1.getId());
        BattleTeam team1 = userArena1.getDefTeamEntity();

        UserEntity user2 = UserOnline.getDbUser(userId2);
        UserArenaCrystalEntity userArena2 = CfgArenaCrystal.getArenaCrystal(user2.getId());
        BattleTeam team2 = userArena2.getDefTeamEntity();

        BattleResultEntityNew battleResult = null;//BattleUtil.battleNew(BattleMode.NORMAL, team1.getAHero(), team1.getAPet(), team2.getAHero(), team2.getAPet(), BattleType.NONE, false);

        String title = Lang.getTitle("title_crystal_fight");
        //        String title = Lang.instance().isVi() ? "Đơn đấu với " : "Fight in Arena with ";
        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user1, user2, title + user2.getName());
        addResponse(builder.build());
    }

    private void battleTrial(int userId1, int userId2) {
        UserEntity user1 = UserOnline.getDbUser(userId1);
        UserArenaTrialEntity userArena1 = CfgArenaTrial.getArenaTrial(user1.getId());
        BattleTeam team1 = userArena1.getDefTeamEntity();

        UserEntity user2 = UserOnline.getDbUser(userId2);
        UserArenaTrialEntity userArena2 = CfgArenaTrial.getArenaTrial(user2.getId());
        BattleTeam team2 = userArena2.getDefTeamEntity();

        BattleResultEntityNew battleResult = null;//BattleUtil.battleNew(BattleMode.NORMAL,team1.getAHero(), team1.getAPet(), team2.getAHero(), team2.getAPet(), BattleType.NONE, false);

        String title = Lang.getTitle("title_trial_fight");
        //        String title = Lang.instance().isVi() ? "Liên đấu với " : "Fight in Grand Arena with ";
        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user1, user2, title + user2.getName());
        addResponse(builder.build());
    }
    //</editor-fold>

    //<editor-fold desc="Logic">
    //</editor-fold>

    //<editor-fold desc="Database">
    //</editor-fold>

}
