//package monster.controller;
//
//import grep.log.Logs;
//import monster.config.CfgHeroExpert;
//import monster.config.lang.Lang;
//import monster.config.penum.FunctionType;
//import monster.config.penum.NotifyType;
//import monster.dao.mapping.UserHeroExpertEntity;
//import monster.protocol.CommonProto;
//import monster.server.config.Guice;
//import monster.service.common.NotifyService;
//import monster.service.user.Bonus;
//import protocol.Pbmethod;
//import protocol.Pbmethod.ResponseData;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
///**
// * Thông thạo tướng
// */
//public class ExpertHandler extends AHandler {
//    final String KEY_DATA = "heroExpert";
//
//    @Override
//    public AHandler newInstance() {
//        return new ExpertHandler();
//    }
//
//    @Override
//    public void initAction(Map<Integer, AHandler> mHandler) {
//        List<Integer> actions = Arrays.asList(EXPERT_STATUS, EXPERT_HERO_DETAIL, EXPERT_POINT_DETAIL, EXPERT_RECEIVE);
//        actions.forEach(action -> mHandler.put(action, this));
//    }
//
//    @Override
//    public void handle(ResponseData.Builder response, String session, int actionId, byte[] requestData) {
//        super.handle(response, session, actionId, requestData);
//
//        if (!validateSession(session)) {
//            addResponse(LOGIN_REQUIRE, null);
//            return;
//        }
//        checkTimeMonitor("s");
//        if (!FunctionType.HERO_EXPERT.isEnable()) {
//            addErrResponse(getLang(Lang.user_function_closed));
//            return;
//        }
//        try {
//            switch (actionId) {
//                case EXPERT_STATUS -> status();
//                case EXPERT_HERO_DETAIL -> heroDetail();
//                case EXPERT_POINT_DETAIL -> pointDetail();
//                case EXPERT_RECEIVE -> receive();
//            }
//        } catch (Exception ex) {
//            Logs.error(ex);
//        }
//    }
//
//    //region Handle service
//    void status() {
//        List<UserHeroExpertEntity> aExpert = CfgHeroExpert.getListUserExpert(mUser);
//        if (aExpert != null) {
//            Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
//            builder.addALong(0).addALong(CfgHeroExpert.getPointStatus(mUser));
//            for (UserHeroExpertEntity expert : aExpert) {
//                if (!expert.isFinish()) builder.addALong(expert.getHeroId()).addALong(expert.isNotify() ? 1 : 0);
//            }
//            for (UserHeroExpertEntity expert : aExpert) {
//                if (expert.isFinish()) builder.addALong(expert.getHeroId()).addALong(expert.isNotify() ? 1 : 0);
//            }
//            addResponse(builder.build());
//        } else addErrResponse();
//    }
//
//    private void pointDetail() {
//        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
//        {
//            List<Long> pointStatus = CfgHeroExpert.getPoint(mUser);
//            builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(pointStatus).addALong(0)); //.addALong(CfgHeroExpert.getPointStatus(mUser)));
//        }
//        {
//            for (int i = 0; i < CfgHeroExpert.config.finalPoint.size(); i++) {
//                builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(CfgHeroExpert.config.finalPoint.get(i)).addAllALong(CfgHeroExpert.config.finalBonus.get(i)));
//            }
//        }
//        addResponse(builder.build());
//    }
//
//    void heroDetail() {
//        int heroId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
//        List<UserHeroExpertEntity> aExpert = CfgHeroExpert.getListUserExpert(mUser);
//        if (aExpert != null) {
//            UserHeroExpertEntity heroExpert = aExpert.stream().filter(expert -> expert.getHeroId() == heroId).findFirst().orElse(null);
//            if (heroExpert != null) {
//                addResponse(heroExpert.toProto(mUser));
//            } else addErrResponse(getLang(Lang.hero_not_found));
//        } else addErrResponse();
//    }
//
//    private void receive() {
//        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
//        int heroId = aLong.get(0).intValue();
//        int index = aLong.get(1).intValue();
//        if (heroId == 0) { // nhận thưởng điểm
//            receivePoint();
//        } else {
//            List<UserHeroExpertEntity> aExpert = CfgHeroExpert.getListUserExpert(mUser);
//            if (aExpert != null) {
//                UserHeroExpertEntity heroExpert = aExpert.stream().filter(expert -> expert.getHeroId() == heroId).findFirst().orElse(null);
//                if (heroExpert != null) {
//                    heroExpert.receive(this, index);
//                } else addErrResponse(getLang(Lang.hero_not_found));
//            } else addErrResponse();
//        }
//        Guice.getInstance(NotifyService.class).setNotify(mUser, NotifyType.HERO_EXPERT, CfgHeroExpert.hasCacheNotify(mUser) ? 1 : 0);
//    }
//
//    private void receivePoint() {
//        List<Long> pointStatus = CfgHeroExpert.getPoint(mUser);
//        int index = pointStatus.get(1).intValue();
//        if (CfgHeroExpert.config.finalPoint.get(index) < pointStatus.get(0)) {
//            List<Long> aBonus = CfgHeroExpert.config.finalBonus.get(index);
//            if (CfgHeroExpert.addPointIndex(mUser, 1)) {
//                addResponse(getCommonVector(Bonus.receiveListItem(mUser, "hero_expert", aBonus)));
//            }
//        } else addErrResponse(Lang.hero_expert_not_enough_point);
//    }
//    //end region casino
//
//    //region Logic
//    private UserHeroExpertEntity getHeroExert(List<UserHeroExpertEntity> aExpert, int heroId) {
//
//        return null;
//    }
//
//    //endregion
//
//    //region Database
//    //endregion
//
//}
