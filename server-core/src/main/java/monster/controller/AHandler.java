package monster.controller;

import com.google.protobuf.AbstractMessage;
import com.google.protobuf.ByteString;
import grep.database.DBJPA;
import grep.log.Logs;
import lombok.Data;
import lombok.SneakyThrows;
import monster.config.lang.Lang;
import monster.config.penum.BattleType;
import monster.dao.mapping.UserEntity;
import monster.object.BattleInputCache;
import monster.object.MyUser;
import monster.protocol.CommonProto;
import monster.protocol.pbentity.PbJListVector;
import monster.protocol.pbentity.PbJVector;
import monster.protocol.pbentity.TextResult;
import monster.server.AppConfig;
import monster.server.Constans;
import monster.server.IAction;
import monster.service.monitor.CCUCounter;
import monster.service.monitor.UserOnline;
import monster.util.GameDebug;
import protocol.Pbmethod;
import protocol.Pbmethod.PbAction;
import protocol.Pbmethod.ResponseData;

import jakarta.persistence.EntityManager;

import java.net.SocketAddress;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Handler xử lý cho các chức năng riêng biệt của người chơi.
 * Gần như không liên quan tới người khác, phân biệt với các sự kiện chung
 * Mỗi request sẽ tạo 1 instance riêng.
 * Cố gắng chia càng nhỏ handler càng tốt cho dễ quản lý
 */
@Data
public abstract class AHandler extends IAction {

    protected ResponseData.Builder response;
    protected String session, ip;
    protected int actionId;
    protected byte[] requestData;
    protected MyUser mUser;
    protected UserEntity user;
    protected boolean hasResponse = true;
    public TextResult result = new TextResult();
    protected String debug = "";
    protected long time = System.currentTimeMillis();

    public void handle(ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        this.response = response;
        this.session = session;
        this.actionId = actionId;
        this.requestData = requestData;
        debug(actionId + " --> " + requestData.length);
    }

    public String handleText(SocketAddress address, String input, byte[] requestData) {
        return null;
    }

    protected boolean validateSession(String session) {
        checkTimeMonitor();
        if (session == null || session.length() == 0) {
            return false;
        }
        //
        if (mUser == null) {
            try {
                mUser = UserOnline.getMyUser(session);
                user = mUser.getUser();
                checkTimeMonitor("1s");
                if (user != null) CCUCounter.getInstance(user.getServer()).count(mUser);
                checkTimeMonitor("2s");
            } catch (Exception ex) {
                return false;
            }
        }

        if (mUser != null && user.getId() != 14237) {
            checkTimeMonitor("3s");
            if (!UserOnline.validateSession(session, mUser.getUser().getUsername())) {
                return false;
            }
            checkTimeMonitor("4s");
        } else if (Constans.blockId.contains(user.getId())) {
            UserOnline.invalidateSession(session);
            Constans.blockId.remove(user.getId());
            return false;
        }

        return true;
    }

    public abstract void initAction(Map<Integer, AHandler> mHandler);

    @SneakyThrows
    public AHandler newInstance() {
        return (AHandler) getClass().getConstructors()[0].newInstance();
    }

    public void addErrResponse(boolean isJson) {
        result.error(getLang(Lang.err_system_down));
    }

    public void addErrResponse(boolean isJson, String msg) {
        if (isJson) {
            result.error(msg);
            return;
        }
        addResponse(MSG_TOAST, CommonProto.getCommonVectorProto(null, Arrays.asList(msg)));
    }

    public void addErrResponse() {
        addResponse(MSG_TOAST, CommonProto.getCommonVectorProto(null, Arrays.asList(getLang(Lang.err_system_down))));
    }

    public void addErrResponse(String msg) {
        addResponse(MSG_TOAST, CommonProto.getCommonVectorProto(null, Arrays.asList(getLang(msg))));
    }

    public void addErrParams() {
        addErrResponse(getLang(Lang.err_params));
    }

    public void addPopupResponse(String msg) {
        addResponse(MSG_POPUP, CommonProto.getCommonVectorProto(null, Arrays.asList(getLang(msg))));
    }

    public void checkNotify() {
        if (mUser != null) {
            //            UserNotify uNotify = mUser.getUData().getUNotify();
            checkTimeMonitor();

            // notification
            List<Long> aNotify = new ArrayList<>();
            if (!mUser.getSetNotify().isEmpty()) {
                aNotify.addAll(Arrays.stream(mUser.getSetNotify().toArray(new Integer[]{})).map(value -> value.longValue()).collect(Collectors.toList()));
                mUser.getSetNotify().clear();
            }
            //            if (uNotify.getValue(NotifyType.TAVERN) > 0 && uNotify.getValue(NotifyType.TAVERN) < System.currentTimeMillis()) {
            //                aNotify.add((long) NotifyType.TAVERN.value);
            //                uNotify.setValue(NotifyType.TAVERN, 0);
            //            }
            if (!aNotify.isEmpty()) {
                //                Logs.warn("check notify -> " + user.getId() + " " + aNotify);
                addResponse(IAction.USER_UPDATE_NOTIFY, Pbmethod.ListCommonVector.newBuilder().addAVector(getCommonVector(aNotify)).build());
            }

            // Popup msg
            if (!mUser.getMsgNotify().isEmpty()) {
                int size = mUser.getMsgNotify().size();
                for (int i = 0; i < size; i++) {
                    response.addAAction(mUser.getMsgNotify().get(i));
                    debug(mUser.getMsgNotify().get(i).toString());
                }
                mUser.getMsgNotify().clear();
            }

            // Bonus notify
            if (!mUser.getABonus().isEmpty()) {
                addResponse(IAction.UPDATE_BONUS, getCommonVector(mUser.getABonus()));
                mUser.getABonus().clear();
            }
            checkTimeMonitor("n");
        }
    }

    public void addResponse(AbstractMessage msg) {
        addResponse(actionId, msg);
    }

    public void addResponse(int action, AbstractMessage msg) {
        String username = user == null ? "none" : user.getUsername();
        debug(username + " <-- " + action + " <-- " + (msg == null ? "none" : msg.toString()));
        PbAction.Builder builder = PbAction.newBuilder();
        builder.setActionId(action);
        if (msg != null) {
            builder.setData(msg.toByteString());
        }
        response.addAAction(builder.build());
    }

    public void addRawResponse(int action, byte[] data) {
        PbAction.Builder builder = PbAction.newBuilder();
        builder.setActionId(action);
        builder.setData(ByteString.copyFrom(data));
        response.addAAction(builder.build());
    }

    public String getLang(Object... keys) {
        Lang lang = mUser != null ? mUser.getLang() : null;
        if (lang == null) lang = Lang.instance();
        if (keys.length == 1) return lang.get((String) keys[0]);
        else if (keys.length == 2) return String.format(lang.get((String) keys[0]), keys[1]);
        return String.format(lang.get((String) keys[0]), keys[1], keys[2]);
    }

    public Pbmethod.CommonVector getCommonVector(Object... values) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        for (Object value : values) {
            builder.addALong(Long.parseLong(value.toString()));
        }
        return builder.build();
    }


    public Pbmethod.CommonVector getCommonVector(String... values) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        for (String value : values) {
            builder.addAString(value);
        }
        return builder.build();
    }

    public Pbmethod.CommonVector getCommonVector(Long... values) {
        return CommonProto.getCommonVectorProto(values);
    }

    public Pbmethod.CommonVector getCommonVector(Integer... values) {
        return CommonProto.getCommonIntVectorProto(values);
    }

    public Pbmethod.CommonVector getCommonVector(List<Long> values) {
        return CommonProto.getCommonVectorProto(values);
    }

    public Pbmethod.CommonVector getCommonIntVector(List<Integer> values) {
        return CommonProto.getCommonIntVectorProto(values);
    }

    public PbJVector getPbVector() {
        return new PbJVector();
    }

    public PbJListVector getPbListVector() {
        return new PbJListVector();
    }

    protected void closeSession(EntityManager session) {
        DBJPA.closeSession(session);
    }

    public void debug(String msg) {
        if (AppConfig.cfg.isDebug()) {
            System.out.println(msg.length() < 500 ? msg : msg.substring(0, 500) + " ...");
            int userId = user == null ? 0 : user.getId();
            GameDebug.addResponseLog(userId, msg);
        }
    }

    public void debugFile(String msg) {
        Logs.debug(msg);
    }

    public void checkTimeMonitor(String... k) {
        if (k.length == 0) this.time = System.currentTimeMillis();
        else {
            this.debug += "|" + k[0] + (System.currentTimeMillis() - this.time);
            this.time = System.currentTimeMillis();
        }
    }

    protected Pbmethod.CommonVector parseCommonInput() {
        return CommonProto.parseCommonVector(requestData);
    }

    public int getInputInt() {
        return CommonProto.parseCommonVector(requestData).getALongList().get(0).intValue();
    }

    public long getInputLong() {
        return CommonProto.parseCommonVector(requestData).getALongList().get(0);
    }

    public List<Long> getInputALong() {
        return CommonProto.parseCommonVector(requestData).getALongList();
    }

    public void addBattleInput(BattleInputCache battleInputCache) {
        mUser.getMBattleInput().put(battleInputCache.battleType, battleInputCache);
    }

    public BattleInputCache getBattleInputCache(BattleType battleType, boolean... noVerify) {
        BattleInputCache input = mUser.getMBattleInput().get(battleType);
        if (input == null) {
            addErrResponse(getLang(Lang.battle_input_error));
        } else if (noVerify.length > 0 && noVerify[0]) {
            addResponse(input.toProto());
        }
        mUser.getMBattleInput().remove(battleType);
        return input;
    }

    protected void commonVerify(BattleInputCache battleInput) {
        if (battleInput != null) processVerify(battleInput);
    }

    protected void processVerify(BattleInputCache battleInput) {

    }

    public boolean quickAttackButtonClick(BattleType battleType) {
        return mUser.getUserSync().quickAttackButtonClick(this, BattleType.MODE_PRACTICE);
    }
}
