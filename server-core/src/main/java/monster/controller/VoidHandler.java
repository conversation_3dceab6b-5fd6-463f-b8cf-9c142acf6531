package monster.controller;

import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgHero;
import monster.config.CfgServer;
import monster.config.CfgSpecialItem;
import monster.config.CfgVoid;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.FunctionType;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.UserInteriorHeroEntity;
import monster.dao.mapping.UserMarketEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.protocol.CommonProto;
import monster.service.Services;
import monster.service.monitor.EventMonitor;
import monster.service.resource.ResHero;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.util.TextGameFormat;
import protocol.Pbmethod;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaUpdate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Hero treasure
 */
public class VoidHandler extends AHandler {

    @Override
    public AHandler newInstance() {
        return new VoidHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(VOID_STATUS, VOID_UPGRADE, VOID_UPGRADE_SKILL, VOID_RESET);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        if (!FunctionType.VOID.isEnable(mUser, this)) return;
        try {
            switch (actionId) {
                case VOID_STATUS:
                    status();
                    break;
                case VOID_UPGRADE:
                    upgrade();
                    break;
                case VOID_UPGRADE_SKILL:
                    upgradeSkill();
                    break;
                case VOID_RESET:
                    reset();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    void reset() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int idHero = aLong.get(0).intValue();

        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (uHero == null || uHero.getLevel() == 0) {
            addErrResponse(getLang(Lang.hero_not_own));
            return;
        }
        if (uHero.getTalentLevel() > 0) {
            addErrResponse(Lang.talent_reset_required);
            return;
        }
        //        if (uHero.getStar() < 15) {
        //            addErrResponse(getLang(Lang.void_required_e5));
        //            return;
        //        }
        if (!uHero.isVoidUpgraded()) {
            addErrResponse(getLang(Lang.void_reset_err1));
            return;
        }
        if (user.getGem() < CfgVoid.feeReset) {
            addErrResponse(getLang(Lang.err_not_enough_gem));
            return;
        }
        List<Long> bonus = Bonus.receiveListItem(mUser, "void_reset", Bonus.viewGem(-CfgVoid.feeReset));
        if (bonus.isEmpty()) {
            addErrResponse();
            return;
        }

        JsonArray voidLevels = GsonUtil.parseJsonArray(uHero.getHeroVoid());

        int levelVoid = uHero.getIntVoidLevel();
        if (levelVoid > 0) {// void 1
            mUser.getUData().addCountVoid1(-1);
        }
        if (levelVoid > 1) {// void 2
            mUser.getUData().addCountVoid2(-1);
        }
        if (levelVoid > 2) {// void 3
            mUser.getUData().addCountVoid3(-1);
        }
        if (levelVoid > 3) {// void 4
            mUser.getUData().addCountVoid4(-1);
        }

        if (uHero.update("hero_void", "")) {
            uHero.setHeroVoid("");
            uHero.calculatePointHero(mUser);

            Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
            builder.addAllALong(bonus);
            builder.addAllALong(Bonus.receiveListItem(mUser, "void_reset", CfgVoid.getReturnResource(voidLevels, uHero.getHeroId())));
            addResponse(builder.build());
            Actions.save(user, "void", "reset", "id", uHero.getId(), "key", uHero.getHeroId(), "session", mUser.getSession());
        } else {
            Bonus.receiveListItem(mUser, "void_reset", Bonus.viewGem(CfgVoid.feeReset));
            addErrResponse();
        }
    }


    void status() {

        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int idHero = aLong.get(0).intValue();
        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (uHero == null || uHero.getLevel() == 0) {
            addErrResponse(getLang(Lang.hero_not_own));
            return;
        }
        //        if (uHero.getStar() < 15) {
        //            addErrResponse(getLang(Lang.void_required_e5));
        //            return;
        //        }
        if (!isEnable(uHero.getHeroId())) {
            addErrResponse(getLang(Lang.void_hero_disable));
            return;
        }
        JsonArray voidLevels = GsonUtil.parseJsonArray(uHero.getHeroVoid());
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        for (int i = 0; i < voidLevels.size(); i++) {
            int level = voidLevels.get(i).getAsInt();
            if (level > 0) builder.addALong(level);
            else if (i % 4 == 3) { // buff skill
                builder.addALong(Services.srvVoid.couldUpgradeVoidSkill(uHero, i) ? level : -1);
            } else { // buff point
                builder.addALong(Services.srvVoid.couldUpgrade(uHero, i) ? level : -1);
            }
        }
        addResponse(builder.build());
    }

    void upgrade() {
        //        if (!Services.srvVoid.hasAbove6E5Hero(mUser)) {
        //            addErrResponse(getLang(Lang.void_required_number_e5));
        //            return;
        //        }
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int idHero = aLong.get(0).intValue();
        int index = aLong.get(1).intValue() + aLong.get(1).intValue() / 3;
        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (uHero == null || uHero.getLevel() == 0) {
            addErrResponse(getLang(Lang.hero_not_own));
            return;
        }
        //        if (uHero.getStar() < 15) {
        //            addErrResponse(getLang(Lang.void_required_e5));
        //            return;
        //        }
        if (!isEnable(uHero.getHeroId())) {
            addErrResponse(getLang(Lang.void_hero_disable));
            return;
        }
        if (!Services.srvVoid.couldUpgrade(uHero, index)) {
            addErrResponse(getLang(Lang.void_required_void_level));
            return;
        }

        JsonArray voidLevels = GsonUtil.parseJsonArray(uHero.getHeroVoid());
        int voidLevel = voidLevels.get(index).getAsInt();
        if (voidLevel >= Services.srvVoid.getMaxVoidLevel(index)) {
            addErrResponse(getLang(Lang.max_level));
            return;
        }
        List<Long> upgradeCost = Services.srvVoid.getUpgradeCost(index, voidLevel);
        long requiredNumber = Math.abs(upgradeCost.get(upgradeCost.size() - 1));

        String checkData = Bonus.checkMoney(mUser, upgradeCost);
        if (StringHelper.isEmpty(checkData)) {
            List<Long> bonus = Bonus.receiveListItem(mUser, "void_upgrade", upgradeCost);
            if (bonus.isEmpty()) {
                addErrResponse();
                return;
            }
            voidLevels.set(index, new JsonPrimitive(voidLevel + 1));
            uHero.update("hero_void", voidLevels.toString());
            uHero.setHeroVoid(voidLevels.toString());
            uHero.calculatePointHero(mUser);
            addResponse(Pbmethod.CommonVector.newBuilder().addALong(voidLevel + 1).addAllALong(bonus).build());
            UserMarketEntity.updateUserShopCry(mUser);
            Actions.save(user, "void", "upgrade", "id", uHero.getId(), "key", uHero.getHeroId(), "void", uHero.getHeroVoid(), "session", session);
        } else {
            addErrResponse(checkData);
        }
    }

    void upgradeSkill() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int idHero = aLong.get(0).intValue();
        int index = aLong.get(1).intValue() * 4 + 3; // 3 7 11 15
        UserHeroEntity uHero = mUser.getResources().getHero(idHero);
        if (uHero == null || uHero.getLevel() == 0) {
            addErrResponse(getLang(Lang.hero_not_own));
            return;
        }
        //        if (uHero.getStar() < 15) {
        //            addErrResponse(getLang(Lang.void_required_e5));
        //            return;
        //        }
        if (!isEnable(uHero.getHeroId())) {
            addErrResponse(getLang(Lang.void_hero_disable));
            return;
        }
        if (!Services.srvVoid.couldUpgradeVoidSkill(uHero, index)) {
            addErrResponse(getLang(Lang.void_required_void_level));
            return;
        }
        JsonArray voidLevels = GsonUtil.parseJsonArray(uHero.getHeroVoid());
        int voidLevel = voidLevels.get(index).getAsInt();
        if (voidLevel >= Services.srvVoid.getMaxVoidLevel(index)) {
            addErrResponse(getLang(Lang.max_level));
            return;
        }
        // làm thêm cái điều kiện nâng cấp void: void8 =6 void4
        // void 12 = 6 void8, void 16- 6 void 12
        int nextVoid = index + 1;
        //        if (nextVoid == 8 && mUser.getUData().getCountHerovoid4() < CfgVoid.config.conditionVoid8.get(1)) {
        //            addErrResponse(String.format(Lang.getTitle(ConditionType.NUMBER_HERO_VOID_4.title1), CfgVoid.config.conditionVoid8.get(1)));
        //            return;
        //        }
        //        if (nextVoid == 12 && mUser.getUData().getCountHerovoid8() < CfgVoid.config.conditionVoid12.get(1)) {
        //            addErrResponse(String.format(Lang.getTitle(ConditionType.NUMBER_HERO_VOID_8.title1), CfgVoid.config.conditionVoid12.get(1)));
        //            return;
        //        }
        //        if (nextVoid == 16 && mUser.getUData().getCountHerovoid12() < CfgVoid.config.conditionVoid16.get(1)) {
        //            addErrResponse(String.format(Lang.getTitle(ConditionType.NUMBER_HERO_VOID_12.title1), CfgVoid.config.conditionVoid16.get(1)));
        //            return;
        //        }
        List<UserHeroEntity> usedHeroes = new ArrayList<>();
        int numFood = 0, numClone = 0;
        for (int i = 2; i < aLong.size(); i++) {
            if (aLong.get(i).intValue() != idHero) {
                UserHeroEntity tmpHero = mUser.getResources().getHero(aLong.get(i).intValue());
                if (tmpHero == null) {
                    addErrResponse(getLang(Lang.err_params));
                    return;
                }
                if (tmpHero.getStar() == 10 && !usedHeroes.contains(tmpHero)) {
                    usedHeroes.add(tmpHero);
                    numFood++;
                }

                if (tmpHero.getHeroId() == ResHero.getHero(uHero.getHeroId()).getHero5Star() && !usedHeroes.contains(tmpHero)) {
                    usedHeroes.add(tmpHero);
                    numClone++;
                }

            }
        }

        for (UserHeroEntity usedUserHero : usedHeroes) {
            //Tướng lắp ở nột thất thuyền ko được đổi
            List<UserInteriorHeroEntity> aUIHero = mUser.getCache().getUIHero(this, mUser);
            for (UserInteriorHeroEntity uIHero : aUIHero) {
                if (uIHero.getHeroId() == usedUserHero.getId()) {
                    addErrResponse(getLang(Lang.hero_altar_err5));
                    return;
                }
            }
        }

        int costHero = Services.srvVoid.getUpgradeSkillCostHero(index / 4);
        int costHeroClone = Services.srvVoid.getUpgradeSkillCostHeroClone(index / 4);
        if (costHero != numFood) {
            addErrResponse(String.format(getLang(Lang.void_required_hero10star), costHero, TextGameFormat.getHeroStar(mUser, 10)));
            return;
        }
        if (costHeroClone != numClone) {
            ResHeroEntity rHero = ResHero.getHero(uHero.getHeroId());
            addErrResponse(String.format(getLang(Lang.void_required_hero_clone), costHeroClone, rHero.getName()));
            return;
        }
        List<Long> costItem = Services.srvVoid.getUpgradeSkillCostItem(index / 4);
        //        long requiredNumber = Math.abs(costItem.get(costItem.size() - 1));

        //        List<Long> bonusItem = Services.srvVoid.getUpgradeSkillCostItemBonus(index / 4);
        String checkCostItem = Bonus.checkMoney(mUser, costItem);
        if (StringHelper.isEmpty(checkCostItem)) { // cost item
            List<Long> aBonus = Bonus.receiveListItem(mUser, "void_skill", costItem);
            if (!costItem.isEmpty() && aBonus.isEmpty()) {
                addErrResponse();
                return;
            }
            //            aBonus.addAll(Bonus.receiveListItem(mUser, "void_skill", bonusItem));
            voidLevels.set(index, new JsonPrimitive(voidLevel + 1));
            if (dbUpgradeSkill(uHero, voidLevels.toString(), usedHeroes.stream().map(hero -> String.valueOf(hero.getId())).collect(Collectors.joining(",")))) {
                aBonus.addAll(Bonus.receiveListItem(mUser, "void_skill", CfgHero.getSacrificeGermaBonus(usedHeroes)));
                aBonus.addAll(Bonus.receiveListItem(mUser, "void_skill", CfgSpecialItem.getBonusUsedMaterial(usedHeroes))); // trả lại nguyên liệu trang bị đặc thù
                usedHeroes.forEach(hero -> mUser.getResources().removeHero(hero.getId())); // Xóa tướng và trả lại item
                uHero.setHeroVoid(voidLevels.toString());
                uHero.calculatePointHero(mUser);
                addResponse(Pbmethod.CommonVector.newBuilder().addALong(voidLevel + 1).addAllALong(aBonus).build());
                Actions.save(user, "void", "skill", "id", uHero.getId(), "key", uHero.getHeroId(), "void", uHero.getHeroVoid());
                //                EventMonitor.getInstance().addDropItem(user.getId(), EventType.EVENT_VOID_STONE, requiredNumber);
                if (nextVoid == 4) mUser.getUData().addCountVoid1(1);
                else if (nextVoid == 8) mUser.getUData().addCountVoid2(1);
                else if (nextVoid == 12) mUser.getUData().addCountVoid3(1);
                else if (nextVoid == 16) mUser.getUData().addCountVoid4(1);
                EventMonitor.getInstance().addDropItem(user.getId(), Arrays.asList((long) EventType.EVENT_VOID_SKILL.value + uHero.getHeroId(), (long) nextVoid / 4));
            } else addErrResponse();
        } else addErrResponse(checkCostItem);
    }
    //endregion

    //region Logic
    boolean isEnable(int heroId) {
        ResHeroEntity resHero = ResHero.getHero(heroId);
        if (resHero != null) return CfgServer.isTestServer() || resHero.getVoidEnable() == 1;
        return false;
    }

    //endregion

    //region Database
    private boolean dbUpgradeSkill(UserHeroEntity hero, String voidLevel, String strIdHeroes) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            CriteriaBuilder cb = session.getCriteriaBuilder();
            CriteriaUpdate<UserHeroEntity> criteriaUpdate = cb.createCriteriaUpdate(UserHeroEntity.class);
            Root<UserHeroEntity> root = criteriaUpdate.from(UserHeroEntity.class);
            criteriaUpdate.set(root.get("heroVoid"), voidLevel).where(cb.equal(root.get("id"), hero.getId()));

            session.getTransaction().begin();
            session.createQuery(criteriaUpdate).executeUpdate();
            session.createNativeQuery("delete from user_hero where user_id=" + user.getId() + " and id in (" + strIdHeroes + ")").executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //endregion

}
