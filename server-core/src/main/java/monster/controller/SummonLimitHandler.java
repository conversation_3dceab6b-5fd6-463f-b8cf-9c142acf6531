package monster.controller;

import grep.helper.GsonUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgHeroRanking;
import monster.config.CfgSummonLimit;
import monster.config.CfgSummonLimit2;
import monster.config.lang.Lang;
import monster.config.penum.EventLimitType;
import monster.config.penum.EventType;
import monster.object.ServiceResult;
import monster.server.config.Guice;
import monster.service.common.MaterialService;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.util.ValidateParam;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class SummonLimitHandler extends AHandler {

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(EVENT_LIST, SUMMON_LIMIT_INFO, SUMMON_LIMIT_CHOOSE, SUMMON_LIMIT_SUMMON);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case EVENT_LIST -> eventStatus();
                default -> {
                    if (!CfgSummonLimit.inEvent(user.getServer())) {
                        addErrResponse(getLang(Lang.event_ended));
                        return;
                    }
                    switch (actionId) {
                        case SUMMON_LIMIT_INFO -> info();
                        case SUMMON_LIMIT_CHOOSE -> choose();
                        case SUMMON_LIMIT_SUMMON -> summon(getInputInt());
                    }
                }

            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void eventStatus() {
        Pbmethod.ListCommonVector.Builder pb = Pbmethod.ListCommonVector.newBuilder();
        if (CfgSummonLimit.inEvent(user.getServer())) {
            int time = (int) ((CfgSummonLimit.getConfigEvent(user.getServer()).getToTime().getTime() - System.currentTimeMillis()) / 1000);
            Pbmethod.CommonVector.Builder cm1 = getCommonVector(EventLimitType.SUMMON_LIMIT.value, Math.max(time, 0), CfgSummonLimit.config.showInHome).toBuilder();
            cm1.addAString(CfgSummonLimit.config.eventName).addAString(CfgSummonLimit.config.eventIcon);
            pb.addAVector(cm1);
        }

        if (CfgHeroRanking.inEvent(user.getServer())) {
            int time = (int) ((CfgHeroRanking.timeEnd.getTime() - System.currentTimeMillis()) / 1000);
            Pbmethod.CommonVector.Builder cm1 = getCommonVector(EventLimitType.RANKING_HERO.value, Math.max(time, 0), CfgHeroRanking.config.showInHome, CfgHeroRanking.config.heroId).toBuilder();
            cm1.addAString(CfgHeroRanking.config.eventName).addAString(CfgHeroRanking.config.eventIcon);
            pb.addAVector(cm1);
        }
        if (CfgSummonLimit2.inEvent(user.getServer())) {
            int time = (int) ((CfgSummonLimit2.getConfigEvent(user.getServer()).getToTime().getTime() - System.currentTimeMillis()) / 1000);
            Pbmethod.CommonVector.Builder cm1 = getCommonVector(EventLimitType.SUMMON_LIMIT_2.value, Math.max(time, 0), CfgSummonLimit2.config.showInHome).toBuilder();
            cm1.addAString(CfgSummonLimit2.config.eventName).addAString(CfgSummonLimit2.config.eventIcon);
            pb.addAVector(cm1);
        }
        addResponse(pb.build());
    }


    private void info() {
        Pbmethod.ListCommonVector.Builder lsc = Pbmethod.ListCommonVector.newBuilder();
        // hero Select + danh sách các tướng được cho phép chọn
        Pbmethod.CommonVector.Builder cmm0 = Pbmethod.CommonVector.newBuilder();
        cmm0.addAllALong(GsonUtil.toListLong(CfgSummonLimit.getHeroId(user.getServer())));
        cmm0.addAString(getLang(Lang.insurance_summon));
        lsc.addAVector(cmm0);
        // heroSelect - ensure - maxEnsure
        List<Integer> infos = mUser.getUData().getInfoSummonLimit(user.getServer());
        lsc.addAVector(getCommonVector(infos.get(1), infos.get(2), CfgSummonLimit.config.ensure, CfgSummonLimit.config.feeId));
        // rate reward
        Pbmethod.CommonVector.Builder cmm2 = Pbmethod.CommonVector.newBuilder();
        cmm2.addAllALong(GsonUtil.toListLong(CfgSummonLimit.rateSummon));
        cmm2.addAString(StringHelper.toDBString(CfgSummonLimit.config.bonus));
        lsc.addAVector(cmm2);
        // list pack
        lsc.addAVector(getCommonIntVector(new ArrayList<>()));
        addResponse(lsc.build());
    }

    private void choose() {
        int heroInput = getInputInt();
        if (!CfgSummonLimit.getHeroId(user.getServer()).contains(heroInput)) {
            addErrParams();
            return;
        }
        List<Integer> info = mUser.getUData().getInfoSummonLimit(user.getServer());
        if (info.get(1) != heroInput) {
            info.set(1, heroInput);
            if (!mUser.getUData().updateInfoSummon(info)) {
                addErrResponse(getLang(Lang.err_system_down));
                return;
            }
        }
        addResponse(getCommonVector(heroInput));
    }

    private void summon(int number) {
        if (!ValidateParam.number(this, number)) return;

        List<Integer> info = mUser.getUData().getInfoSummonLimit(user.getServer());
        int heroSelect = info.get(1);
        if (heroSelect == 0) {
            addErrResponse(getLang(Lang.has_select_hero_first));
            return;
        }

        List<Long> aBonus = CfgSummonLimit.getFeeSummon(number);
        String err = Bonus.checkMoney(mUser, aBonus);
        if (err != null) {
            addErrResponse(err);
            return;
        }
        int ensure = info.get(2);
        int logIsHero = ensure;
        int curHero = info.get(3);
        int minEnsure = CfgSummonLimit.getMinInsure(curHero);
        boolean isHero = false;
        for (int i = 0; i < number; i++) {
            if (ensure >= CfgSummonLimit.config.ensure - 1) {
                aBonus.addAll(Bonus.viewHero(heroSelect));
                ensure = 0;
                curHero++;
                minEnsure = CfgSummonLimit.getMinInsure(curHero);
                isHero = true;
            } else {
                ensure++;
                logIsHero = ensure;
                int rand = NumberUtil.getRandom(10000);
                for (int j = 0; j < CfgSummonLimit.rateAdd.size(); j++) {
                    if (rand < CfgSummonLimit.rateAdd.get(j)) {
                        // rơi ra tướng thì reset đảm bảo
                        if (j == 0) {
                            if (ensure > minEnsure) {
                                isHero = true;
                                aBonus.addAll(Bonus.viewHero(heroSelect));
                                ensure = 0;
                                curHero++;
                                minEnsure = CfgSummonLimit.getMinInsure(curHero);
                                break;
                            } else {
                                // random lại
                                rand = NumberUtil.getRandom(10000);
                                for (int k = 1; k < CfgSummonLimit.rateAdd.size(); k++) {
                                    if (rand < CfgSummonLimit.rateAdd.get(k)) {
                                        aBonus.addAll(CfgSummonLimit.config.bonus.get(k));
                                        break;
                                    }
                                }
                                break;
                            }
                        } else {
                            aBonus.addAll(CfgSummonLimit.config.bonus.get(j));
                            break;
                        }
                    }
                }
            }
        }
        MaterialService materialService = Guice.getInstance(MaterialService.class);
        info.set(2, ensure);
        info.set(3, curHero);

        if (mUser.getUData().updateInfoSummon(info)) {
            ServiceResult<List<Long>> resultAddHero = materialService.addBonus(mUser, aBonus, "summon_limit");
            if (resultAddHero.success) {
                Pbmethod.ListCommonVector.Builder pb = Pbmethod.ListCommonVector.newBuilder();
                pb.addAVector(getCommonVector(resultAddHero.data));
                pb.addAVector(getCommonVector(ensure));
                addResponse(pb.build());
                Actions.save(user, "hero_summon_limit", "save", "number", number, "isHero", isHero ? 1 : 0, "ensure", logIsHero);
                EventType.SUMMON_LIMIT.addEvent(mUser, number);
            } else {
                resultAddHero.writeResponse(this);
            }
        } else {
            addErrResponse();
        }
    }
}
