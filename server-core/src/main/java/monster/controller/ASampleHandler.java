package monster.controller;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * User pet
 */
public class ASampleHandler extends AHandler {

    @Override
    public AHandler newInstance() {
        return new ASampleHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList();
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }

        switch (actionId) {
        }
    }

    //region Handle service
    //endregion

    //region Logic
    //endregion

    //region Database
    private boolean dbTest() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }
    //endregion

}
