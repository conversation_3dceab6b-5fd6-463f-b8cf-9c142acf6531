package monster.controller.logic;

import grep.database.DBJPA;
import grep.helper.NumberUtil;
import monster.config.CfgAlbum;
import monster.dao.mapping.UserAlbumEntity;
import monster.dao.mapping.main.ResAlbumImageEntity;
import monster.object.MyUser;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class AlbumService {

    static String sampleData = "0".repeat(64);

    public static String getDefaultImageData(int albumId, int imgId) {
        return sampleData.substring(0, CfgAlbum.getImage(albumId, imgId).numberFragment);
    }

    public static int getRandomFragment(int albumId, int imgId) {
        return NumberUtil.getRandom(CfgAlbum.getImage(albumId, imgId).numberFragment);
    }

    public static boolean isFinish(String imgData) {
        return "1".repeat(imgData.length()).equals(imgData);
    }

    public static int numberOpenFragment(String imgData) {
        return imgData.length() - imgData.replace("1", "").length();
    }

    public static List<ResAlbumImageEntity> getUnlockImage(MyUser mUser) {
        Map<String, UserAlbumEntity> mImage = mUser.getResources().mImage;
        List<ResAlbumImageEntity> unlockImages = new ArrayList<>();
        mImage.forEach((key, image) -> {
            if (image.finish) {
                String albumId = key.split("_")[0];
                String imageId = key.split("_")[1];
                ResAlbumImageEntity resImage = CfgAlbum.getImage(Integer.parseInt(albumId), Integer.parseInt(imageId));

                if (resImage.imageId == 1) { // cover
                    ResAlbumImageEntity nextCoverImage = CfgAlbum.mKeyAlbum.get(String.format("%s_1", resImage.albumId + 1));
                    if (nextCoverImage != null && !mImage.containsKey(nextCoverImage.key()) && nextCoverImage.requiredLevel <= mUser.getUser().getLevel()) {
                        unlockImages.add(nextCoverImage);
                    }
                }

                ResAlbumImageEntity nextImage = CfgAlbum.mKeyAlbum.get(String.format("%s_%s", resImage.albumId, resImage.imageId + 1));
                if (nextImage != null && !mImage.containsKey(nextImage.key()) && nextImage.requiredLevel <= mUser.getUser().getLevel()) {
                    unlockImages.add(nextImage);
                }
            }
        });
        for (ResAlbumImageEntity unlockImage : unlockImages) {
            UserAlbumEntity uImage = new UserAlbumEntity(mUser.getUser().getId(), unlockImage.albumId, unlockImage.imageId);
            if (DBJPA.save(uImage)) mUser.getResources().addImage(uImage);
        }
        return unlockImages;
    }

}
