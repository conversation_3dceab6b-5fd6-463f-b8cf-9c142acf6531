package monster.controller.logic;

import grep.database.DBJPA;
import monster.config.CfgArenaCrystal;
import monster.config.CfgArenaSwap;
import monster.config.CfgArenaTrial;
import monster.config.penum.TeamType;
import monster.dao.mapping.UserHeroEntity;
import monster.object.MyUser;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class UserHeroService {

    public void checkLockedHero(int userId, List<UserHeroEntity> aHero) {
        List<String> sqls = new ArrayList<>();
        if (!CfgArenaTrial.isOpen()) {
            List<Long> heroIds = new ArrayList<>();
            aHero.stream().filter(hero -> hero.getLockedTrial() != 0).forEach(hero -> {
                hero.setLockedTrial(0);
                heroIds.add(hero.getId());
            });
            if (!heroIds.isEmpty()) {
                sqls.add(String.format("update user_hero set locked_trial=0 where user_id=%s and id in (%s)", userId,
                        heroIds.stream().map(value -> String.valueOf(value)).collect(Collectors.joining(","))));
            }
        }
        if (CfgArenaSwap.getState() == CfgArenaSwap.STATE_CLOSED) {
            List<Long> heroIds = new ArrayList<>();
            aHero.stream().filter(hero -> hero.getLockedVodau() != 0).forEach(hero -> {
                hero.setLockedVodau(0);
                heroIds.add(hero.getId());
            });
            if (!heroIds.isEmpty()) {
                sqls.add(String.format("update user_hero set locked_vodau=0 where user_id=%s and id in (%s)", userId,
                        heroIds.stream().map(value -> String.valueOf(value)).collect(Collectors.joining(","))));
            }
        }
        if (!sqls.isEmpty()) DBJPA.rawSQL(sqls);
    }

    public void calculateHeroPoint(MyUser myUser, int valuesIndex, UserHeroEntity... heroEntity) {
        for (UserHeroEntity userHeroEntity : heroEntity) {
            if (userHeroEntity != null)
                userHeroEntity.calculatePointHero(myUser, valuesIndex);
        }
        checkUpdateDefendTeam(myUser);
    }

    public void checkUpdateDefendTeam(MyUser mUser) {

    }
}
