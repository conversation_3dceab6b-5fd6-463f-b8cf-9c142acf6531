package monster.controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.*;
import grep.log.Logs;
import monster.config.CfgDungeon;
import monster.config.CfgLimitFunction;
import monster.config.lang.Lang;
import monster.config.penum.*;
import monster.dao.mapping.UserDungeonEntity;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.main.MarketDungeon;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.game.user.service.UserService;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.battle.dependence.Point;
import monster.service.battle.dependence.entity.SimulateHero;
import monster.service.common.MaterialService;
import monster.service.common.NotifyService;
import monster.service.common.TeamService;
import monster.service.monitor.TopMonitor;
import monster.service.resource.ResRanking;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import net.sf.json.JSONArray;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class AspenDungeonHandler extends AHandler {

    final String KEY_DATA = "dungeon";
    final int RESULT_MERCHANT = 1, RESULT_PORTION = 2, RESULT_BONUS = 3;
    int eventId;
    UserDungeonEntity userDungeon;

    @Override
    public AHandler newInstance() {
        return new AspenDungeonHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(ASPEN_STATUS, ASPEN_REQUIRED_SELECT_HERO, ASPEN_SELECT_HERO, ASPEN_FIRST_BONUS, ASPEN_BATTLE, ASPEN_BUY_IN_BATTLE, ASPEN_NOTIFY, ASPEN_MERCHANT_LIST, ASPEN_MERCHANT_BUY, ASPEN_RANK, ASPEN_USED_PORTION, ATK_VERIFY_MODE_ASPEN_DUNGEON);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        if (actionId != ASPEN_NOTIFY) {
            if (!FunctionType.ASPEND_DUNGEON.isEnable(mUser, this)) return;
        }
        eventId = CfgDungeon.getEventId(mUser);
        if (actionId == ASPEN_NOTIFY) {
            if (FunctionType.ASPEND_DUNGEON.isEnable(mUser)) {
                addResponse(getCommonVector(eventId == 0 ? 0 : 1));
            } else {
                addResponse(getCommonVector(0L));
            }
        }
        if (eventId == 0) {
            addResponse(ASPEN_CLOSED, getCommonVector(CfgDungeon.countdown(mUser)));
            return;
        }
        userDungeon = Services.userService.getUserDungeon(mUser, this);
        if (userDungeon.getDataHero() == null && actionId != ASPEN_SELECT_HERO) {
            addResponse(ASPEN_REQUIRED_SELECT_HERO, null);
            return;
        }
        try {
            switch (actionId) {
                case ASPEN_STATUS -> status();
                case ASPEN_SELECT_HERO -> selectHero();
                //                case ASPEN_FIRST_BONUS ->
                case ASPEN_BUY_IN_BATTLE -> addResponse(null);
                case ASPEN_MERCHANT_LIST -> merchantList();
                case ASPEN_MERCHANT_BUY -> merchantBuy(getInputInt());
                case ASPEN_RANK -> rank();
                case ASPEN_USED_PORTION -> usePortion();
                case ASPEN_BATTLE -> battle(getInputInt());
                case ATK_VERIFY_MODE_ASPEN_DUNGEON -> commonVerify(getBattleInputCache(BattleType.MODE_ASPEN_DUNGEON));
            }
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        }
    }

    //region Handle service
    protected void processVerify(BattleInputCache input) {
        int heroIndex = input.values.get(0).intValue();
        List<HeroInfoEntity> aHero = userDungeon.getMyHero();
        ResMonsterEntity[] oppMonster = userDungeon.getOppHero();
        boolean isWin = input.battleResult.isAllOpponentDie();
        HeroInfoEntity hero = userDungeon.getMyHero().get(heroIndex);
        updateHeroInBattle(hero, oppMonster, input.battleResult.aResult.get(0).team1, input.battleResult.aResult.get(0).team2);

        int addLevel = 0;
        int addGold = 0, addGem = 0, addMaterial = 0, materialId = 0;
        int portionIndex = -1, portionAdd = 0;
        if (isWin) {
            addLevel = 1;
            int bonusIndex = CfgDungeon.config.drops.getBonusIndex();
            if (bonusIndex == 0 || bonusIndex == 11)
                addGold = CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
            else if (bonusIndex == 2)
                addGem = CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
            else if (bonusIndex < 5) {
                addMaterial = CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
                if (bonusIndex == 1) materialId = MaterialType.MAGIC_DUST.id;
                else if (bonusIndex == 3) materialId = MaterialType.CHIP.id;
                else if (bonusIndex == 4) materialId = MaterialType.HEROIC_SUMMON.id;
            } else if (bonusIndex == 11) { // shop cũ chuyển thành gold
                //                dataShop = new Gson().toJson(CfgDungeon.config.getShopItem(userDungeon));
            } else { // portion
                portionAdd = CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
                portionIndex = bonusIndex - 5;
            }
        }
        if (addLevel == 1) {
            oppMonster = CfgDungeon.getMonster(userDungeon.getLevel() + 1);
        }

        JSONArray arrPortion = JSONArray.fromObject(userDungeon.getDataItem());
        if (portionIndex >= 0) {
            arrPortion.set(portionIndex, arrPortion.getInt(portionIndex) + portionAdd);
            if (portionIndex >= 3 && arrPortion.getInt(portionIndex) > 20) {
                arrPortion.set(portionIndex, 20);
            }
        }
        Gson gson = new Gson();
        String dataHero = gson.toJson(aHero), dataOpponent = gson.toJson(oppMonster);
        List<Long> aBonus = new ArrayList<>();
        {
            List<Long> bonus = new ArrayList<>();
            if (addGold > 0) bonus.addAll(Bonus.viewGold(addGold));
            if (addGem > 0) bonus.addAll(Bonus.viewGem(addGem));
            if (materialId > 0)
                bonus.addAll(Bonus.view(Bonus.BONUS_MATERIAL, MaterialType.TYPE_USED_ITEM, materialId, addMaterial));
            if (!bonus.isEmpty()) {
                aBonus = Bonus.receiveListItem(mUser, KEY_DATA + "_battle", bonus);
                if (aBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }
            }
        }
        if (dbUpdateBattleResult(dataHero, dataOpponent, heroIndex, addLevel, arrPortion.toString())) {
            userDungeon.setSelectedHero(heroIndex);
            userDungeon.setLevel(userDungeon.getLevel() + addLevel);
            userDungeon.setDataHero(dataHero);
            userDungeon.setDataOpponent(dataOpponent);
            userDungeon.setDataItem(arrPortion.toString());
            // add Info
            if (portionAdd > 0) {
                input.addOutput(ListUtil.ofLong(RESULT_PORTION, portionIndex));
                if (portionIndex >= 3) {
                    addPortion(portionIndex, portionAdd);
                    dbUpdateMyHero();
                }
            } else if (!aBonus.isEmpty()) {
                input.addOutput(ListUtil.ofLong(RESULT_BONUS, aBonus));
            }
            addResponse(input.toProto());
            Actions.save(user, KEY_DATA, "verify", "level", userDungeon.getLevel(), "addLevel", addLevel);
            if (addLevel > 0) { // win
                EventType.ASPEN_DUNGEON_LEVEL.addEvent(mUser, userDungeon.getLevel());
            }
            if (mUser.getUData().getDungeonLevel() < userDungeon.getLevel()) {
                mUser.getUData().updateDungeonLevel(userDungeon.getLevel());
                ResRanking.addRank(RankType.ASPEN_DUNGEON, user, userDungeon.getLevel() - 1);
            }
        } else {
            userDungeon.setAMyHero(null);
            userDungeon.setAOppHero(null);
            addErrResponse();
        }
    }

    void battle(int heroIndex) {
        if (CfgDungeon.isMaxLevel(userDungeon.getLevel()) || userDungeon.getLevel() >= CfgLimitFunction.config.dungeon) {
            addErrResponse(getLang(Lang.max_level));
            return;
        }
        List<HeroInfoEntity> aHero = userDungeon.getMyHero();
        if (heroIndex < 0 || heroIndex >= aHero.size()) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        HeroInfoEntity hero = aHero.get(heroIndex);
        if (hero.point.getStartHpPercent() <= 0) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        HeroInfoEntity heroBreath = Guice.getInstance(UserService.class).getHeroBreath(user.getId());
        BattleTeam myTeam = BattleTeam.builder().aHero(new HeroInfoEntity[]{null, null, hero, null, null, null}).heroBreath(heroBreath).build();
        ResMonsterEntity[] oppMonster = userDungeon.getOppHero();

        BattleResultEntityNew battleResult = BattleBuilder.builder().setInfo(user.getId()).setMode(BattleType.MODE_ASPEN_DUNGEON).setTeam(myTeam, Guice.getInstance(TeamService.class).getMonsterTeam(oppMonster)).battle(mUser); // BattleUtil.battle(new HeroInfoEntity[]{null, hero, null, null, null, null}, oppMonster, true);

        String title = Lang.getTitle("title_aspen_dungeon");
        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user, title + userDungeon.getLevel());
        builder.setClientToGo(1);
        addResponse(builder.build());
        Actions.save(user, KEY_DATA, "attack", "level", userDungeon.getLevel(), "bid", builder.getBattleId());
        addBattleInput(BattleInputCache.builder().values(ListUtil.ofLong(heroIndex)).team(myTeam).battleResult(battleResult).battleType(BattleType.MODE_ASPEN_DUNGEON).build());
    }

    void merchantList() {
        int shopLevel = Math.min(6, CfgDungeon.getMaxShopLevel(userDungeon.getLevel()));
        List<Integer> buyStatus = GsonUtil.strToListInt(userDungeon.getDataShop());
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        { // shop info
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            List<String> names = List.of("Dễ", "Thường", "Khó", "Siêu Khó", "Địa Ngục", "Ác Mộng");
            for (int i = 0; i < names.size(); i++) {
                cmm.addAString(names.get(i));
                cmm.addALong(shopLevel >= i + 1 ? 1 : 0);
            }
            builder.addAVector(cmm);
        }
        for (int i = 1; i <= shopLevel; i++) {
            List<MarketDungeon> items = CfgDungeon.mShopLevel.get(i);
            for (MarketDungeon item : items) {
                boolean isBuy = (item.getId() >= buyStatus.size() ? 0 : buyStatus.get(item.getId())) == 1;
                builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(item.getId()).addALong(isBuy ? 0 : 1).addALong(item.getShopLevel()).addALong(item.getDungeonLevelRequired()).addAllALong(GsonUtil.strToListLong(item.getPrice())).addALong(-1).addAllALong(GsonUtil.strToListLong(item.getItem())));
            }
        }
        addResponse(builder.build());
    }

    void merchantBuy(int itemId) {
        List<Integer> buyStatus = GsonUtil.strToListInt(userDungeon.getDataShop());
        boolean isBuy = (itemId >= buyStatus.size() ? 0 : buyStatus.get(itemId)) == 1;
        if (isBuy) {
            addErrResponse(Lang.err_max_buy);
        } else {
            MarketDungeon item = CfgDungeon.mShopItem.get(itemId);
            if (item == null) {
                addErrResponse(Lang.err_params);
            } else {
                JsonArray aBonus = GsonUtil.parseJsonArray(item.getPrice());
                if (!mUser.checkPrice(this, aBonus)) return;
                aBonus.addAll(GsonUtil.parseJsonArray(item.getItem()));
                while (buyStatus.size() <= itemId) buyStatus.add(0);
                buyStatus.set(itemId, 1);
                if (userDungeon.update("data_shop", StringHelper.toDBString(buyStatus))) {
                    userDungeon.setDataShop(StringHelper.toDBString(buyStatus));
                    Guice.getInstance(MaterialService.class).addBonus(mUser, GsonUtil.strToListLong(aBonus.toString()), "dungeon_shop").writeResponse(this);
                    Actions.save(user, "dungeon", "shop", "id", itemId);
                }
            }
        }
    }

    void rank() {
        addResponse(TopMonitor.getInstance().get(TopType.ASPEN_DUNGEON));
    }

    void usePortion() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int heroIndex = aLong.get(0).intValue(), portionIndex = aLong.get(1).intValue();
        if (portionIndex < 0 || portionIndex > 2) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        List<HeroInfoEntity> aHero = userDungeon.getMyHero();
        if (heroIndex < 0 || heroIndex >= aHero.size()) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        HeroInfoEntity hero = aHero.get(heroIndex);
        if (hero.point.getCurrentHP() <= 0) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        JSONArray arrPortion = JSONArray.fromObject(userDungeon.getDataItem());
        if (arrPortion.getInt(portionIndex) <= 0) {
            addErrResponse(getLang(Lang.err_not_enough_material));
            return;
        }
        int healPercent = 100;
        if (portionIndex == 1) healPercent = 50;
        else if (portionIndex == 2) healPercent = 25;

        float percent = hero.point.getStartHpPercent() + healPercent;
        if (percent > 100) percent = 100;
        hero.point.setStartHpPercent(percent);
        hero.point.setPercentHp((long) percent);
        //        hero.point.addHp(hero.point.get(Point.BASE_HP) * healPercent / 100);
        //        hero.point.setStartHp(hero.point.getHP());

        String strHero = new Gson().toJson(aHero);
        arrPortion.set(portionIndex, arrPortion.getInt(portionIndex) - 1);
        if (dbUsedPortion(strHero, arrPortion.toString())) {
            userDungeon.setStrDataHero(strHero);
            userDungeon.setDataItem(arrPortion.toString());
            addResponse(getCommonVector((int) percent, arrPortion.getInt(portionIndex)));
        } else addErrResponse();
    }

    void status() {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            cmm.addALong(CfgDungeon.countdown(mUser)).addALong(userDungeon.getLevel());
            cmm.addALong(0); //cmm.addALong(userDungeon.getDataHiddenShop() != null ? 1 : 0);
            if (!userDungeon.hasShop()) cmm.addALong(0);
            else {
                //                UserDungeonEntity.ItemInfo info = userDungeon.getItemInfo();
                //                cmm.addALong(info.merchantId).addAllALong(info.price);
                //                cmm.addALong(-1).addAllALong(info.item);
            }
            builder.addAVector(cmm);
        }

        {
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            cmm.addALong(userDungeon.getSelectedHero());
            ResMonsterEntity[] aHeroInfo = userDungeon.getOppHero();
            if (aHeroInfo != null) {
                for (ResMonsterEntity resMonster : aHeroInfo) {
                    if (resMonster != null) {
                        int percent = (resMonster.getPoint().getStartHpPercent() > 0 && resMonster.getPoint().getStartHpPercent() < 1) ? 1 : (int) resMonster.getPoint().getStartHpPercent();
                        cmm.addALong(resMonster.getId()).addALong(resMonster.getStar()).addALong(resMonster.getLevelShow()).addALong(percent);
                    }
                }
            }
            builder.addAVector(cmm);
        }

        {
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
            List<HeroInfoEntity> aHeroInfo = userDungeon.getMyHero();
            aHeroInfo.forEach(hero -> {
                int percent = (hero.point.getStartHpPercent() > 0 && hero.point.getStartHpPercent() < 1) ? 1 : (int) hero.point.getStartHpPercent();
                if (hero.point.getRealPercentHp() == 0) percent = 0;
                cmm.addALong(hero.heroId).addALong(hero.skin).addALong(hero.star).addALong(hero.level).addALong(percent);
            });
            builder.addAVector(cmm);
        }

        {
            Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();

            JSONArray arr = JSONArray.fromObject(userDungeon.getDataItem());
            for (int i = 0; i < 3; i++) cmm.addALong(arr.getInt(i));
            for (int i = 3; i < 6; i++) cmm.addALong(arr.getInt(i)).addALong(20);
            Arrays.asList(String.format(getLang(Lang.heal_percent_hp), "100%"), String.format(getLang(Lang.heal_percent_hp), "50%"), String.format(getLang(Lang.heal_percent_hp), "20%"),
                    String.format(getLang(Lang.increase_speed), arr.getInt(3) * 10), String.format(getLang(Lang.increase_percent_attack), arr.getInt(4) * 1.5f),
                    String.format(getLang(Lang.increase_percent_crit), arr.getInt(5) * 2)).forEach(value -> cmm.addAString(value));
            builder.addAVector(cmm);
        }

        addResponse(ASPEN_STATUS, builder.build());
        if (CfgDungeon.isMaxLevel(userDungeon.getLevel())) addErrResponse(getLang(Lang.max_level));
    }

    void selectHero() {
        if (userDungeon.getDataHero() != null) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        List<Long> aHeroId = CommonProto.parseCommonVector(requestData).getALongList();
        UserHeroEntity[] aUserHero = mUser.getResources().getTeam(aHeroId);
        List<HeroInfoEntity> aHeroInfo = new ArrayList<>();
        for (int i = 0; i < aUserHero.length; i++) {
            if (aUserHero[i] != null) aHeroInfo.add(aUserHero[i].toHeroInfo(1, i % BattleConfig.TEAM_HERO_SIZE));
        }
        while (aHeroInfo.size() > 5) aHeroInfo.remove(0);
        if (aHeroInfo.isEmpty()) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }
        JsonArray arrBase = new JsonArray();
        aHeroInfo.forEach(hero -> arrBase.add(hero.point.getCurrentAttack()));

        int startLevel = 1;
        // smash tại level 51 101 151 201 ...
        if (mUser.getUData().getDungeonLevel() > 50 && !CfgDungeon.noSmashId(user.getId())) {
            startLevel = 50 * ((mUser.getUData().getDungeonLevel() - 1) / 50) + 1;
        }
        String strData = new Gson().toJson(aHeroInfo);
        if (!dbInitGame(strData, startLevel, arrBase.toString())) addErrResponse();
        userDungeon.setDataHeroBase(arrBase.toString());
        userDungeon.setLevel(startLevel);
        userDungeon.setDataHero(strData);
        userDungeon.setAMyHero(aHeroInfo);
        addResponse(null);
        if (startLevel > 50 && !CfgDungeon.noSmashId(user.getId())) {
            smashToLevel(startLevel);
        }
        status();
        Guice.getInstance(NotifyService.class).remove(mUser, NotifyType.ASPEN_DUNGEON);
    }
    //endregion

    //region Logic
    void smashToLevel(int level) {
        int numberShopItem = 0;
        int addGold = 0, addGem = 0;
        int magicDust = 0, casinoChip = 0, heroicSummon = 0;
        int[] portion = new int[6];
        for (int i = 2; i < level; i++) {
            int bonusIndex = CfgDungeon.config.drops.getBonusIndex();
            if (bonusIndex == 0 || bonusIndex == 11)
                addGold += CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
            else if (bonusIndex == 2)
                addGem += CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
            else if (bonusIndex < 5) {
                if (bonusIndex == 1)
                    magicDust += CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
                else if (bonusIndex == 3)
                    casinoChip += CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
                else if (bonusIndex == 4)
                    heroicSummon += CfgDungeon.config.drops.getBonusValue(userDungeon.getLevel(), bonusIndex);
            } else if (bonusIndex == 11) { // shop cũ chuyển thành gold
                //                if (numberShopItem++ < 50) shop.add(CfgDungeon.config.getShopItem(userDungeon));
            } else { // portion
                portion[bonusIndex - 5]++;
                if (bonusIndex - 5 >= 3 && portion[bonusIndex - 5] > 20) portion[bonusIndex - 5] = 20;
            }
        }
        List<Long> aBonus = new ArrayList<>();
        if (addGold > 0) aBonus.addAll(Bonus.viewGold(addGold));
        if (addGem > 0) aBonus.addAll(Bonus.viewGem(addGem));
        if (magicDust > 0) aBonus.addAll(Bonus.viewMaterial(MaterialType.MAGIC_DUST, magicDust));
        if (casinoChip > 0) aBonus.addAll(Bonus.viewMaterial(MaterialType.CHIP, casinoChip));
        if (heroicSummon > 0) aBonus.addAll(Bonus.viewMaterial(MaterialType.HEROIC_SUMMON, heroicSummon));

        //        String hiddenShop = new Gson().toJson(shop);
        String strPortion = new Gson().toJson(portion);
        String dataOpponent = new Gson().toJson(CfgDungeon.getMonster(level));
        if (dbSmashBonus(strPortion, dataOpponent)) {
            //            userDungeon.setDataHiddenShop(hiddenShop);
            userDungeon.setDataItem(strPortion);
            userDungeon.setDataOpponent(dataOpponent);

            List<Long> result = Bonus.receiveListItem(mUser, KEY_DATA + "_smash", aBonus);
            Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
            builder.addAVector(getCommonVector(result));
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(portion[0]).addALong(portion[1]).addALong(portion[2]).build());
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(portion[3]).addALong(portion[4]).addALong(portion[5]).build());
            addResponse(ASPEN_FIRST_BONUS, builder.build());

            // buff portion
            addPortion(3, portion[3]);
            addPortion(4, portion[4]);
            addPortion(5, portion[5]);
            dbUpdateMyHero();
        }
    }

    private void addPortion(int portionIndex, int number) {
        JsonArray arrBase = GsonUtil.parseJsonArray(userDungeon.getDataHeroBase());
        for (int i = 0; i < userDungeon.getMyHero().size(); i++) {
            HeroInfoEntity hero = userDungeon.getAMyHero().get(i);
            switch (portionIndex) {
                case 3: // speed
                    hero.point.addNotNegative(Point.SPEED, number * 10);
                    break;
                case 4:  // atk
                    hero.point.addNotNegative(Point.ATTACK, (int) (number * arrBase.get(i).getAsInt() * 1.5f / 100));
                    break;
                case 5: // crit
                    hero.point.addNotNegative(Point.CRIT, number * 20);
                    break;
            }
        }
    }

    void updateHeroInBattle(HeroInfoEntity hero, ResMonsterEntity[] oppMonster, List<SimulateHero> atkTeam, List<SimulateHero> defTeam) {
        for (SimulateHero heroBattle : atkTeam) {
            if (heroBattle.id == hero.id) {
                float percentFloat = heroBattle.outputPoint.percentHpFloat();
                if (percentFloat == 0 && heroBattle.outputPoint.getCurrentHP() > 0) percentFloat = 0.01f;

                //                hero.point.set(Point.HP, heroBattle.point.getHP());
                //                hero.point.setStartHp(heroBattle.point.getHP());
                hero.point.setStartHpPercent(percentFloat);
                hero.point.setPercentHp(heroBattle.outputPoint.percentHpLong());
                hero.point.setCurrentValue(Point.ANGER, heroBattle.outputPoint.getCurrentAnger());
                hero.point.setCurrentValue(Point.REVIVED, heroBattle.outputPoint.getRevived());
            }
        }
        for (int i = 0; i < oppMonster.length; i++) {
            if (oppMonster[i] != null) {
                for (SimulateHero heroBattle : defTeam) {
                    if (heroBattle.id == oppMonster[i].getId() && heroBattle.pos == i) {
                        float percentFloat = heroBattle.outputPoint.percentHpFloat();
                        if (percentFloat == 0 && heroBattle.outputPoint.getCurrentHP() > 0) percentFloat = 0.01f;

                        //                        oppMonster[i].getPoint().set(Point.HP, heroBattle.point.getHP());
                        //                        oppMonster[i].getPoint().setStartHp(heroBattle.point.getHP());

                        oppMonster[i].getPoint().setStartHpPercent(percentFloat);
                        oppMonster[i].getPoint().setPercentHp(heroBattle.outputPoint.percentHpLong());
                        oppMonster[i].getPoint().setCurrentValue(Point.ANGER, heroBattle.outputPoint.getCurrentAnger());
                        oppMonster[i].getPoint().setCurrentValue(Point.REVIVED, heroBattle.outputPoint.getRevived());
                    }
                }
            }
        }
    }

    public List<Long> getListPortion(int portionIndex, int portionAdd) {
        List<Long> aLong = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            aLong.add(i == portionIndex ? (long) portionAdd : 0);
        }
        return aLong;
    }
    //endregion

    //region Database Access
    boolean dbUsedPortion(String myHeroes, String dataPortion) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update user_dungeon set data_hero=:dataHero,data_item=:dataItem where user_id=" + user.getId() + " and event_id=" + eventId);
            query.setParameter("dataHero", GZip.compress(myHeroes));
            query.setParameter("dataItem", dataPortion);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    boolean dbUpdateBattleResult(String myHeroes, String oppHeroes, int heroIndex, int addLevel, String portion) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update user_dungeon set selected_hero=" + heroIndex + ",data_hero=:dataHero,data_opponent=:dataOpponent,data_item=:dataItem, level=level+" + addLevel + " where user_id=" + user.getId() + " and event_id=" + eventId);
            query.setParameter("dataHero", GZip.compress(myHeroes));
            query.setParameter("dataOpponent", GZip.compress(oppHeroes));
            query.setParameter("dataItem", portion);
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return false;
    }

    boolean dbUpdateHiddenShop(String data) {
        return DBJPA.update("user_dungeon", Arrays.asList("data_hidden_shop", data), Arrays.asList("user_id", user.getId(), "event_id", eventId));
    }

    boolean dbBuyBattleShop() {
        return DBJPA.update("user_dungeon", Arrays.asList("data_shop", ""), Arrays.asList("user_id", user.getId(), "event_id", eventId));
    }

    boolean dbUpdateMyHero() {
        String strDataHero = new Gson().toJson(userDungeon.getMyHero());
        try {
            if (DBJPA.update("user_dungeon", Arrays.asList("data_hero", GZip.compress(strDataHero)), Arrays.asList("user_id", user.getId(), "event_id", eventId))) {
                userDungeon.setDataHero(strDataHero);
                return true;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return false;
    }

    boolean dbInitGame(String strHeroData, int level, String heroBase) {
        try {
            return DBJPA.update("user_dungeon", Arrays.asList("data_hero", GZip.compress(strHeroData), "level", level, "data_hero_base", heroBase), Arrays.asList("user_id", user.getId(), "event_id", eventId));
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return false;
    }

    boolean dbSmashBonus(String item, String dataOpponent) {
        try {
            return DBJPA.update("user_dungeon", Arrays.asList("data_item", item, "data_opponent", GZip.compress(dataOpponent)), Arrays.asList("user_id", user.getId(), "event_id", eventId));
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return false;
    }

    //endregion

}