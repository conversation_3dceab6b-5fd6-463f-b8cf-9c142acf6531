package monster.controller;

import com.google.common.collect.ImmutableList;
import com.google.gson.JsonArray;
import grep.database.DBJPA;
import grep.helper.*;
import grep.log.Logs;
import monster.config.CfgAbyss;
import monster.config.CfgInterior;
import monster.config.lang.Lang;
import monster.config.penum.BattleType;
import monster.config.penum.FunctionType;
import monster.dao.mapping.*;
import monster.dao.mapping.main.*;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.protocol.CommonProto;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.battle.dependence.Point;
import monster.service.battle.dependence.entity.SimulateHero;
import monster.service.battle.dependence.entity.SimulateResult;
import monster.service.resource.ResArtifact;
import monster.service.resource.ResHero;
import monster.service.resource.ResItem;
import monster.service.resource.ResTreasure;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class AbyssHandler extends AHandler {
    UserAbyssEntity uAbyss;

    private static final int TYPE_ATTACK = 0;
    private static final int TYPE_HP = 1;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(ABYSS_STATUS, ABYSS_BUY_TURN, ABYSS_STATUS_DIFFICULT, ABYSS_ATTACK, ABYSS_CHOOSE_REWARD, ABYSS_STATUS_DETAIL, ABYSS_CLAIM_QUEST, ABYSS_CLAIM_REWARD, ATK_VERIFY_ABYSS);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public AHandler newInstance() {
        return new AbyssHandler();
    }

    @Override
    public void handle(protocol.Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");

        if (CfgAbyss.getEventIndex() < 0) {
            addErrResponse(getLang(Lang.user_function_not_open));
            return;
        }
        if (!FunctionType.ABYSS.isEnable(mUser, this)) return;
        uAbyss = mUser.getCache().getUAbyss(this, mUser);
        if (uAbyss == null) {
            addErrResponse();
            return;
        }

        if (uAbyss.isClose()) {
            addErrResponse(getLang(Lang.user_function_not_open));
            return;
        }

        if (uAbyss.isNewEvent()) {
            if (!updateUserAbyssWhenNewEvent()) {
                addErrResponse();
                return;
            }
            Actions.save(user, "abyss", "new", "difficult", uAbyss.getDifficult(),
                    "status", StringHelper.toDBString(uAbyss.getAbyssStatus()),
                    "receive", uAbyss.isReceived(),
                    "last_receive", uAbyss.isLastSeasonReceived());
        } else {
            if (!uAbyss.updateNewDay(user)) {
                addErrResponse();
                return;
            }
        }

        try {
            switch (actionId) {
                case ABYSS_STATUS:
                    status();
                    break;
                case ABYSS_BUY_TURN:
                    buyTurn();
                    break;
                case ABYSS_STATUS_DIFFICULT:
                    statusDifficult();
                    break;
                case ABYSS_ATTACK:
                    attack();
                    break;
                case ATK_VERIFY_ABYSS:
                    commonVerify(getBattleInputCache(BattleType.MODE_ABYSS));
                    break;
                case ABYSS_CHOOSE_REWARD:
                    chooseReward();
                    break;
                case ABYSS_STATUS_DETAIL:
                    statusDetail();
                    break;
                case ABYSS_CLAIM_QUEST:
                    claimQuest();
                    break;
                case ABYSS_CLAIM_REWARD:
                    claimReward();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handler service
    protected void processVerify(BattleInputCache input) {
        BattleResultEntityNew battleResult = input.battleResult;
        int positionIndex = input.values.get(0).intValue(), battleId = input.values.get(1).intValue();
        String newStatusForUserAbyss = getNewStatusForUserAbyss(battleResult, positionIndex);
        int newCurrentPosition = battleResult.isWin() ?
                Math.min(CfgAbyss.config.getNumberPosition() - 1, Math.max(positionIndex + 1, uAbyss.getCurrentPosition()))
                :
                uAbyss.getCurrentPosition();
        int newTotalStar = getNewTotalStarForUserAbyss(newStatusForUserAbyss);
        int dailyTurnOfPosition = uAbyss.getDailyTurn().get(positionIndex) - 1;
        String newDailyTurn = uAbyss.getNewDailyTurn(positionIndex, dailyTurnOfPosition);
        if (!dbUpdateStatusForUserAbyss(newStatusForUserAbyss, newCurrentPosition, newTotalStar, newDailyTurn)) {
            addErrResponse();
            return;
        }

        uAbyss.setStatus(newStatusForUserAbyss);
        uAbyss.setCurrentPosition(newCurrentPosition);
        uAbyss.setTotalStar(newTotalStar);
        uAbyss.setDailyTurn(newDailyTurn);
        uAbyss.setLastTime(new Date());

        Actions.save(user, "abyss", "verify", "battle_id", battleId,
                "status", newStatusForUserAbyss,
                "total_star", newTotalStar,
                "position", uAbyss.getCurrentPosition());
        addResponse(input.toProto());
    }

    void status() {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(uAbyss.getDifficult());
        builder.addALong(uAbyss.isLastSeasonReceived() ? 1 : 0);
        builder.addALong(uAbyss.isBreakTime() ? 1 : 0);
        builder.addALong(getCountdownSecond());
        builder.addALong(CfgAbyss.config.getNumberGemBuyTurn());
        addResponse(builder.build());
    }

    void statusDifficult() {
        boolean isChoosingDifficult = uAbyss.getDifficult() == -1;
        if (isChoosingDifficult) {
            List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
            int difficult = aLong.get(0).intValue();
            if (difficult < 0) {
                addErrResponse(getLang(Lang.err_params));
                return;
            }

            if (!updateUserAbyssWhenChooseDifficult(difficult, user.getServer())) {
                debug("---------------1------------");
                addErrResponse();
                return;
            }
            Actions.save(user, "abyss", "choose_difficult", "difficult", uAbyss.getDifficult());
        }

        List<UserHeroCloneEntity> allHeroClone = mUser.getCache().getAllHeroClone(this, mUser);
        if (isChoosingDifficult || allHeroClone == null || allHeroClone.isEmpty()) {
            List<UserHeroCloneEntity> listNewHeroClone = new ArrayList<>();
            List<UserHeroTopEntity> listHeroTop = mUser.getCache().getUserHeroTop(this, mUser);

            List<UserPetCloneEntity> listNewPetClone = new ArrayList<>();
            UserPetTopEntity strongestPet = PetHandler.getPetTop(this, 1);

            List<UserClanCloneEntity> listNewClanClone = new ArrayList<>();
            UserClanTopEntity strongestClan = ClanSkillHandler.getClanTop(this, 1);

            List<UserInteriorCloneEntity> listNewInteriorClone = new ArrayList<>();
            List<UserInteriorTopEntity> listInteriorTop = mUser.getCache().getTopInterior(this, mUser);
            if (listInteriorTop.isEmpty()) {
                List<UserInteriorItemEntity> inventory = mUser.getCache().getUInteriorItem(this, mUser);
                List<UserInteriorHeroEntity> listHero = mUser.getCache().getUIHero(this, mUser);
                List<UserInteriorTopEntity> newAllTimeTop6Interior = CfgInterior.getAllTimeTopInterior(inventory, listHero, mUser.getCache().getTopInterior(null, mUser), 6);
                if (!newAllTimeTop6Interior.isEmpty())
                    InteriorHandler.dbUpdateInteriorTop(this, newAllTimeTop6Interior);
                listInteriorTop = mUser.getCache().getTopInterior(this, mUser);
            }

            for (int positionIndex = 0; positionIndex < CfgAbyss.config.getNumberPosition(); positionIndex++) {
                List<UserHeroCloneEntity> listHeroCloneForPosition = getAllHeroCloneForPosition(positionIndex, listHeroTop);
                if (listHeroCloneForPosition == null || listHeroCloneForPosition.isEmpty()) {
                    debug("---------------2------------");
                    addErrResponse();
                    return;
                }

                listNewHeroClone.addAll(listHeroCloneForPosition);
                listNewPetClone.addAll(getListPetCloneForPosition(positionIndex, strongestPet));

                UserClanCloneEntity clanClone = getClanCloneForPosition(strongestClan, positionIndex);
                if (clanClone != null) {
                    listNewClanClone.add(clanClone);
                }
                List<UserInteriorCloneEntity> listInteriorCloneForPosition = getInteriorCloneForPosition(positionIndex, listInteriorTop);
                if (listInteriorCloneForPosition == null) {
                    addErrResponse();
                    return;
                }
                listNewInteriorClone.addAll(listInteriorCloneForPosition);
            }

            List<UserAlbumCloneEntity> listNewAlbumClone = new ArrayList<>(getListUserAlbumClone());

            if (!dbUpdateHeroClone(listNewHeroClone)
                    || !dbUpdatePetClone(listNewPetClone)
                    || !dbUpdateClanClone(listNewClanClone)
                    || !dbUpdateInteriorClone(listNewInteriorClone)
                    || !dbUpdateAlbumClone(listNewAlbumClone)) {
                debug("---------------4------------");
                addErrResponse();
                return;
            }

            mUser.getCache().clearDataOfAbyss();
        }

        Pbmethod.ListCommonVector.Builder listCmmBuilder = Pbmethod.ListCommonVector.newBuilder();
        listCmmBuilder.addAVector(getCommonVector((long) uAbyss.getCurrentPosition(),
                (long) uAbyss.getTotalRewardStatus(uAbyss.getDifficult()),
                (long) uAbyss.getNumberStar(),
                (long) CfgAbyss.getMaxStar()));
        for (int i = 0; i < CfgAbyss.config.getNumberPosition(); i++) {
            listCmmBuilder.addAVector(getCommonVectorForPosition(i));
        }
        for (int rewardIndex = 0; rewardIndex < CfgAbyss.config.getDifficultData(uAbyss.getDifficult()).getNumberReward(); rewardIndex++) {
            listCmmBuilder.addAVector(getCommonVectorForReward(rewardIndex));
        }

        addResponse(listCmmBuilder.build());
    }

    void statusDetail() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int positionIndex = aLong.get(0).intValue() - 1;

        BattleTeam teamClone = getTeamClone(positionIndex);
        if (teamClone == null) {
            addErrResponse();
            return;
        }

        Pbmethod.PbUser.Builder builder = getPbUser(teamClone);
        addResponse(builder.build());
    }

    void attack() {
        if (!uAbyss.isLastSeasonReceived()) {
            addErrResponse("Hãy nhận quà mùa trước");
            return;
        }

        if (uAbyss.isBreakTime()) {
            addErrResponse("Tính năng đang trong thời gian nghỉ");
            return;
        }

        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        if (aLong.size() != BattleConfig.TEAM_INPUT + 1) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }

        int positionIndex = aLong.get(0).intValue();
        if (positionIndex > uAbyss.getCurrentPosition()) {
            addErrResponse(getLang(Lang.event_abyss_require_win_previous_stage));
            return;
        }

        if (uAbyss.getDailyTurn().get(positionIndex) <= 0) {
            addErrResponse("Đã hết lượt đánh trong hôm nay");
            return;
        }

        List<Long> allHeroIdForBattle = aLong.subList(1, aLong.size());
        BattleTeam userTeam = BattleTeam.getInstance(mUser, allHeroIdForBattle);
        BattleTeam teamClone = getTeamClone(positionIndex);
        if (teamClone == null) {
            addErrResponse();
            return;
        }

        BattleResultEntityNew battleResult = BattleBuilder.builder().setTeam(userTeam, teamClone)
                .setMode(BattleType.MODE_ABYSS).setInfo(user.getId()).battle();
        //Log

        //Client
        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(mUser.getUser(), getLang("title_abyss"));
        addResponse(builder.build());
        Actions.save(user, "abyss", "attack", "battle_id", builder.getBattleId(),
                "index", positionIndex,
                "position", uAbyss.getCurrentPosition());
        addBattleInput(BattleInputCache.builder()
                .values(new ArrayList<>(List.of((long) positionIndex, builder.getBattleId())))
                .output(new ArrayList<>(List.of(getAllInfoQuestCompleteThisTime(battleResult, positionIndex))))
                .battleResult(battleResult).battleType(BattleType.MODE_ABYSS).build());
    }

    void claimQuest() {
        if (!uAbyss.isLastSeasonReceived()) {
            addErrResponse("Hãy nhận quà mùa trước");
            return;
        }

        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int positionIndex = aLong.get(0).intValue(), questId = aLong.get(1).intValue();
        if (positionIndex > uAbyss.getCurrentPosition()) {
            addErrResponse(getLang(Lang.event_abyss_require_win_previous_stage));
            return;
        }
        List<Integer> abyssStatusOfPosition = uAbyss.getAbyssStatus().get(positionIndex);

        for (int i = 0; i < abyssStatusOfPosition.size() - 1; i += 2) {
            if (abyssStatusOfPosition.get(i) != questId) continue;

            if (abyssStatusOfPosition.get(i + 1) == 0) {
                addErrResponse(getLang(Lang.quest_incomplete));
                return;
            }

            if (abyssStatusOfPosition.get(i + 1) == 2) {
                addErrResponse("Đã nhận phần thưởng");
                return;
            }
        }

        ResAbyssFormationEntity resAbyssFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        List<Integer> listQuestId = resAbyssFormation.getListQuestId();
        ImmutableList<ImmutableList<Long>> listQuestBonus = CfgAbyss.config.getDifficultData(uAbyss.getDifficult()).getAllQuestBonusForPosition(positionIndex);
        List<Long> questBonus = new ArrayList<>();
        for (int i = 0; i < listQuestId.size(); i++) {
            if (listQuestId.get(i) != questId) continue;

            questBonus = new ArrayList<>(listQuestBonus.get(i));
            break;
        }
        if (questId <= 0) {
            addErrResponse(getLang(Lang.err_params));
            return;
        }

        String newStatusForUserAbyss = uAbyss.getNewStatusWhenClaimQuest(positionIndex, questId);
        if (!dbUpdateStatusForUserAbyss(newStatusForUserAbyss)) {
            addErrResponse();
            return;
        }

        uAbyss.setStatus(newStatusForUserAbyss);
        uAbyss.setLastTime(new Date());

        List<Long> aBonus = Bonus.receiveListItem(mUser, "abyss_claim_quest", questBonus);
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        Actions.save(user, "abyss", "claim_quest", "bonus", "index", positionIndex, "quest_id", questId, "bonus", aBonus, "status", newStatusForUserAbyss);

        //Client
        addResponse(getCommonVector(aBonus));
    }

    void chooseReward() {
        if (!uAbyss.isLastSeasonReceived()) {
            addErrResponse("Hãy nhận quà mùa trước");
            return;
        }

        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        String newChosenReward = uAbyss.getNewChosenReward(this, aLong);

        if (newChosenReward == null) {
            addErrResponse();
            return;
        }

        if (!dbUpdateChosenRewardForUserAbyss(newChosenReward)) {
            addErrResponse();
            return;
        }

        uAbyss.setChosenReward(newChosenReward);
        uAbyss.setLastTime(new Date());
        Actions.save(user, "abyss", "choose_reward", "chosen_reward", newChosenReward);
        addResponse(getCommonVector(GsonUtil.strToListLong(newChosenReward)));
    }

    void buyTurn() {
        if (!uAbyss.isLastSeasonReceived()) {
            addErrResponse("Hãy nhận quà mùa trước");
            return;
        }

        if (uAbyss.isBreakTime()) {
            addErrResponse("Tính năng đang trong thời gian nghỉ");
            return;
        }

        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int positionIndex = aLong.get(0).intValue(), numberBuy = aLong.get(1).intValue();
        int dailyTurnOfPosition = uAbyss.getDailyTurn().get(positionIndex);
        int dailyBuyOfPosition = uAbyss.getDailyBuy().get(positionIndex);
        numberBuy = Math.min(numberBuy, CfgAbyss.config.getNumberBuyDaily() - dailyBuyOfPosition);

        if (numberBuy <= 0) {
            addErrResponse("Đã hết lượt mua trong ngày");
            return;
        }

        //Phí mua
        int totalFee = CfgAbyss.config.getNumberGemBuyTurn() * numberBuy;
        List<Long> aFeeBonusView = Bonus.viewGem(totalFee);

        //Check phí
        JsonArray arrConvertPrice = GsonUtil.parseFromListLong(aFeeBonusView);
        if (!mUser.checkPrice(this, arrConvertPrice)) {
            addErrResponse(getLang(Lang.err_not_enough_gem));
            return;
        }
        List<Long> aFeeBonus = Bonus.receiveListItem(mUser, arrConvertPrice, "abyss_buy_turn");
        //Check trừ nl
        if (aFeeBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        dailyTurnOfPosition += numberBuy;
        dailyBuyOfPosition += numberBuy;
        String newDailyTurn = uAbyss.getNewDailyTurn(positionIndex, dailyTurnOfPosition);
        String newDailyBuy = uAbyss.getNewDailyBuy(positionIndex, dailyBuyOfPosition);

        if (!dbUpdateTurn(newDailyTurn, newDailyBuy)) {
            arrConvertPrice = GsonUtil.parseFromListLong(aFeeBonusView);
            Bonus.receiveListItem(mUser, arrConvertPrice, "abyss_buy_turn_fail");
            addErrResponse();
            return;
        }

        uAbyss.setDailyTurn(newDailyTurn);
        uAbyss.setDailyBuy(newDailyBuy);
        uAbyss.setLastTime(new Date());
        Actions.save(user, "abyss", "buy_turn", "position", positionIndex, "number", numberBuy, "turn", dailyTurnOfPosition, "buy", dailyBuyOfPosition);
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(numberBuy).addALong(dailyTurnOfPosition).addALong(dailyBuyOfPosition).addAllALong(aFeeBonus);
        addResponse(builder.build());
    }

    void claimReward() {
        if (!uAbyss.isBreakTime()) {
            addErrResponse("Chỉ nhận được quà trong giai đoạn nghỉ ngơi");
            return;
        }

        if (uAbyss.isReceived()) {
            addErrResponse("Đã nhận phần thưởng");
            return;
        }

        List<Integer> chosenReward = uAbyss.getChosenReward();
        if (chosenReward == null || chosenReward.isEmpty()) {
            addErrResponse();
            return;
        }
        List<Long> questBonus = new ArrayList<>();
        List<AbyssStatisticEntity> listAbyssStat = dbGetListAbyssStat(uAbyss);
        for (int i = 0; i < chosenReward.size() - 1; i += 2) {
            int rewardIndex = chosenReward.get(i);
            int chosenIndex = chosenReward.get(i + 1);
            if (uAbyss.canReceiveReward(rewardIndex)) {
                questBonus.addAll(new ArrayList<>(CfgAbyss.config.getDifficultData(uAbyss.getDifficult()).getAllRewardForIndex(rewardIndex).get(chosenIndex)));
                AbyssStatisticEntity abyssStatistic = listAbyssStat.stream().filter(abyssStat -> abyssStat.getRewardIndex() == rewardIndex).findFirst().orElse(null);
                if (abyssStatistic != null) {
                    switch (chosenIndex) {
                        case 0:
                            abyssStatistic.setSlot1(abyssStatistic.getSlot1() + 1);
                            abyssStatistic.setPercentSlot1(abyssStatistic.getSlot1() * 100 / abyssStatistic.getTotalSlot123());
                            break;
                        case 1:
                            abyssStatistic.setSlot2(abyssStatistic.getSlot2() + 1);
                            abyssStatistic.setPercentSlot2(abyssStatistic.getSlot2() * 100 / abyssStatistic.getTotalSlot123());
                            break;
                        case 2:
                            abyssStatistic.setSlot3(abyssStatistic.getSlot3() + 1);
                            abyssStatistic.setPercentSlot3(abyssStatistic.getSlot3() * 100 / abyssStatistic.getTotalSlot123());
                            break;
                    }
                }
            }
        }

        if (!updateUserAbyssWhenClaimReward(uAbyss) || !dbUpdateListAbyssStat(getSqlToUpdateStat(listAbyssStat))) {
            addErrResponse();
            return;
        }

        List<Long> aBonus = Bonus.receiveListItem(mUser, "abyss_claim_reward", questBonus);
        if (aBonus.isEmpty()) {
            addErrResponse();
            return;
        }

        Actions.save(user, "abyss", "claim_reward", "bonus", aBonus, "received", uAbyss.isReceived(), "last_received", uAbyss.isLastSeasonReceived());

        //Client
        addResponse(getCommonVector(aBonus));
    }
    //endregion

    //region Logic
    private protocol.Pbmethod.PbUser.Builder getPbUser(BattleTeam teamClone) {
        protocol.Pbmethod.PbUser.Builder pbUser = Pbmethod.PbUser.newBuilder();
        BattleTeam.toPbUserProto(pbUser, teamClone);
        return pbUser;
    }

    private Pbmethod.CommonVector.Builder getCommonVectorForPosition(int positionIndex) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        CfgAbyss.DifficultData difficultData = CfgAbyss.config.getDifficultData(uAbyss.getDifficult());
        List<Integer> positionStatus = uAbyss.getAbyssStatus().get(positionIndex);
        int dailyTurnOfPosition = uAbyss.getDailyTurn().get(positionIndex);
        int dailyBuyOfPosition = uAbyss.getDailyBuy().get(positionIndex);
        ResAbyssFormationEntity resAbyssFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        List<Integer> listQuestIdForPosition = resAbyssFormation.getListQuestId();
        int numberQuest = listQuestIdForPosition.size();
        List<Integer> listMainId = new ArrayList<>();
        List<Integer> listQuestData = new ArrayList<>();
        for (int i = 0; i < listQuestIdForPosition.size(); i++) {
            int questId = listQuestIdForPosition.get(i);
            ResAbyssQuestEntity resAbyssQuest = CfgAbyss.resQuestMapByQuestId.get(questId);
            listMainId.add(resAbyssQuest.getMainId());
            if (resAbyssQuest.getDataQuest().isEmpty()) listQuestData.add(0);
            else listQuestData.addAll(resAbyssQuest.getDataQuest());
            listQuestData.add(-1);
        }

        if (positionStatus.size() / 2 != numberQuest) {
            addErrResponse(getLang(Lang.err_params));
            return null;
        }

        builder.addALong(positionIndex);
        builder.addALong(dailyTurnOfPosition);
        builder.addALong(CfgAbyss.config.getNumberTurnDaily());
        builder.addALong(dailyBuyOfPosition);
        builder.addALong(CfgAbyss.config.getNumberBuyDaily());
        builder.addALong(numberQuest);
        builder.addAllALong(GsonUtil.toListLong(positionStatus));
        builder.addAllALong(ListUtil.listIntegerToListLong(listMainId));
        builder.addAllALong(ListUtil.listIntegerToListLong(listQuestData));
        builder.addAllALong(difficultData.getBonusInfoForClient(positionIndex));

        for (int questId : listQuestIdForPosition) {
            ResAbyssQuestEntity resQuest = CfgAbyss.resQuestMapByQuestId.get(questId);
            ResAbyssQuestTypeEntity resAbyssQuestType = CfgAbyss.resAbyssQuestTypeMapByTypeId.get(resQuest.getTypeId());
            if (!resAbyssQuestType.isSpecial() && resAbyssQuestType.getStyle().contains(CfgAbyss.STYLE_HERO_KEY)) {
                String title = resAbyssQuestType.getTitle();
                int numberHero = resQuest.getDataQuest().size();
                String[] listHeroName = new String[numberHero];
                for (int i = 0; i < numberHero; i++) {
                    ResHeroEntity resHero = ResHero.getHero(resQuest.getDataQuest().get(i));
                    listHeroName[i] = resHero.getName();
                }
                builder.addAString(String.format(title, listHeroName));
            } else {
                String nameKey = resQuest.getNameKey();
                builder.addAString(getLang(nameKey));
            }
        }

        return builder;
    }

    private String getItemAsStringForHeroClone(String strongest2ndHeroItem, String clazz, List<Integer> artifact) {
        List<Integer> allHeroItem = GsonUtil.strToListInt(strongest2ndHeroItem).subList(0, 4);

        List<Integer> allSuitItem = new ArrayList<>();
        for (int itemId : allHeroItem) {
            if (itemId == 0) {
                allSuitItem.add(0);
                continue;
            }
            ResItemEntity oldResItem = ResItem.getItem(itemId);
            if (oldResItem == null) return null;
            ResItemEntity suitableResItem = ResItem.getItem(oldResItem, clazz);
            if (suitableResItem == null) return null;

            allSuitItem.add(suitableResItem.getId());
        }


        allSuitItem.addAll(artifact);

        return StringHelper.toDBString(allSuitItem);
    }

    private List<UserHeroCloneEntity> getAllHeroCloneForPosition(int positionIndex, List<UserHeroTopEntity> listHeroTop) {
        debug("positionIndex: " + positionIndex);
        List<UserHeroCloneEntity> allHeroCloneForPosition = new ArrayList<>();
        ResAbyssFormationEntity resFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        debug("resFormation: " + resFormation);
        ResAbyssMechanicEntity resAbyssMechanic = CfgAbyss.resAbyssMechanicMapById.get(resFormation.getMechanicId());
        debug("resAbyssMechanic: " + resAbyssMechanic);
        List<Integer> listHeroTypeId = resFormation.getListHeroTypeId();
        debug("listHeroTypeId: " + listHeroTypeId);
        List<ResAbyssHeroTypeEntity> listResAbyssHeroType = CfgAbyss.getListResAbyssHeroType(listHeroTypeId);
        if (listResAbyssHeroType == null) {
            return null;
        }

        int powerScale = CfgAbyss.config.getDifficultData(uAbyss.getDifficult()).getPowerScale().get(positionIndex);

        for (int i = 0; i < listResAbyssHeroType.size(); i++) {
            int fromHeroTop = resAbyssMechanic.getSlotOrder().get(i);
            ResAbyssHeroTypeEntity resAbyssHeroType = listResAbyssHeroType.get(i);

            //            resFormation: difficult = 3 - positionIndex = 4
            //            resAbyssMechanic: id = 2
            //            listHeroTypeId: [49, 4, 11, 23, 33, 22]

            UserHeroTopEntity heroTop = getHeroTop(listHeroTop, fromHeroTop);
            if (heroTop == null) {
                System.out.println("----------------333----------");
                continue;
            }
            //            debug("heroTop: " + heroTop);

            int heroCloneKey = resAbyssHeroType.getHeroKey();
            //            debug("heroCloneKey: " + heroCloneKey);
            if (heroCloneKey == -1) {
                System.out.println("----------------444----------");
                continue;
            }
            int heroCloneLevel = heroTop.getLevel();
            int heroCloneStar = heroTop.getStar();
            List<Integer> heroCloneArtifact = getHeroCloneArtifact(heroTop.getItem(), resAbyssHeroType.getArtifact());
            String heroCloneSkills = getHeroCloneSkills(resAbyssHeroType.getSkills(), heroCloneStar);
            int heroCloneTreasure = heroTop.getTreasure();
            int heroCloneTreasureEffect = getTreasureEffectForHeroClone(heroCloneTreasure, resAbyssHeroType);
            int heroCloneTreasureIndex = getTreasureIndexForHeroClone(heroCloneTreasure, heroCloneTreasureEffect, resAbyssHeroType);
            int heroCloneSpecialItemType = resAbyssHeroType.getSpecialItemType();

            ResHeroEntity resHero = ResHero.getHero(heroCloneKey);
            debug("heroCloneKey: " + heroCloneKey);
            if (resHero == null || resHero.getClazz() == null) {
                debug("null reshero key: " + heroCloneKey);
                return null;
            }

            String clazz = resHero.getClazz().toLowerCase();
            String heroCloneItem = getItemAsStringForHeroClone(heroTop.getItem(), clazz, heroCloneArtifact);
            String heroCloneSpecialItem = getHeroCloneSpecialItems(heroTop.getSpecialItem(), heroCloneSpecialItemType);
            String heroCloneVoid = heroTop.getHeroVoid();
            UserHeroCloneEntity heroClone = new UserHeroCloneEntity(
                    user.getId(),
                    heroCloneKey,
                    positionIndex,
                    heroCloneLevel,
                    heroCloneStar,
                    heroCloneItem,
                    heroCloneSkills,
                    heroCloneSpecialItem,
                    heroCloneVoid,
                    heroCloneTreasure,
                    heroCloneTreasureEffect,
                    heroCloneTreasureIndex,
                    powerScale
            );
            allHeroCloneForPosition.add(heroClone);
        }

        debug("allHeroCloneForPosition.size: " + allHeroCloneForPosition.size());

        return allHeroCloneForPosition;
    }

    private int getTreasureEffectForHeroClone(int treasure, ResAbyssHeroTypeEntity resAbyssHeroType) {
        ResTreasureEntity resTreasure = ResTreasure.getTreasure(treasure);
        if (resTreasure == null) return 0;

        return Math.min(
                (int) resTreasure.getAPointDesc().stream().filter(pointDesc -> pointDesc != null && pointDesc.length > 0).count() - 1
                ,
                resAbyssHeroType.getTreasureEffect()
        );
    }

    private int getTreasureIndexForHeroClone(int treasure, int treasureEffect, ResAbyssHeroTypeEntity resAbyssHeroType) {
        ResTreasureEntity resTreasure = ResTreasure.getTreasure(treasure);
        if (resTreasure == null) return 0;

        return Math.min(
                (int) resTreasure.getListPointDescOfEffect(treasureEffect).stream().filter(Objects::nonNull).count() - 1
                ,
                resAbyssHeroType.getTreasureIndex(treasure)
        );
    }

    private List<UserPetCloneEntity> getListPetCloneForPosition(int positionIndex, UserPetTopEntity strongestPet) {
        List<UserPetCloneEntity> listPetCloneForPosition = new ArrayList<>();
        ResAbyssFormationEntity resFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        ResAbyssMechanicEntity resAbyssMechanic = CfgAbyss.resAbyssMechanicMapById.get(resFormation.getMechanicId());
        if (strongestPet == null) return new ArrayList<>();

        int petCloneUserId = strongestPet.getUserId();
        int petClonePetId = resAbyssMechanic.getPet();
        int petCloneLevel = strongestPet.getLevel();
        int petCloneTier = strongestPet.getTier();
        int petClonePassive1 = strongestPet.getPassive1();
        int petClonePassive2 = strongestPet.getPassive2();
        int petClonePassive3 = strongestPet.getPassive3();
        int petClonePassive4 = strongestPet.getPassive4();
        int petCloneRune1 = strongestPet.getRune1();
        int petCloneRune2 = strongestPet.getRune2();
        int petCloneRune3 = strongestPet.getRune3();
        UserPetCloneEntity petClone = new UserPetCloneEntity(
                petCloneUserId,
                petClonePetId,
                positionIndex,
                petCloneLevel,
                petCloneTier,
                petClonePassive1,
                petClonePassive2,
                petClonePassive3,
                petClonePassive4,
                petCloneRune1,
                petCloneRune2,
                petCloneRune3
        );
        listPetCloneForPosition.add(petClone);

        return listPetCloneForPosition;
    }

    private List<UserAlbumCloneEntity> getListUserAlbumClone() {
        List<UserAlbumCloneEntity> listUserAlbumClone = new ArrayList<>();
        List<UserAlbumEntity> listUserAlbum = mUser.getResources().images.stream().filter(UserAlbumEntity::isFinish).collect(Collectors.toList());
        for (UserAlbumEntity userAlbum : listUserAlbum) {
            listUserAlbumClone.add(userAlbum.toClone());
        }

        return listUserAlbumClone;
    }

    private UserClanCloneEntity getClanCloneForPosition(UserClanTopEntity strongestClan, int positionIndex) {
        if (strongestClan == null) return null;
        ResAbyssFormationEntity resFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        ResAbyssMechanicEntity resAbyssMechanic = CfgAbyss.resAbyssMechanicMapById.get(resFormation.getMechanicId());
        List<Integer> clanOrderSkill = resAbyssMechanic.getClanOrderSkill();
        List<List<Integer>> oldClanSkills = GsonUtil.strTo2ListInt(strongestClan.getSkills());
        List<List<Integer>> oldClanSkills2 = GsonUtil.strTo2ListInt(strongestClan.getSkills2());
        List<List<Integer>> clanCloneSkills = getClanCloneSkills(oldClanSkills, clanOrderSkill);
        List<List<Integer>> ClanCloneSkills2 = getClanCloneSkills(oldClanSkills2, clanOrderSkill);

        return new UserClanCloneEntity(
                mUser.getUser().getId(),
                positionIndex,
                StringHelper.toDBString(clanCloneSkills),
                StringHelper.toDBString(ClanCloneSkills2)
        );
    }

    private List<List<Integer>> getClanCloneSkills(List<List<Integer>> clanSkills, List<Integer> clanOrderSkill) {
        List<List<Integer>> clanCloneSkills = GsonUtil.strTo2ListInt("[[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0]]");
        List<List<Integer>> clanSkillsOrderDesc = getClanSkillsOrderDesc(clanSkills);

        for (int i = 0; i < clanOrderSkill.size(); i++) {
            int order = clanOrderSkill.get(i);
            clanCloneSkills.set(order, clanSkillsOrderDesc.get(i));
        }

        return clanCloneSkills;
    }

    private List<List<Integer>> getClanSkillsOrderDesc(List<List<Integer>> clanSkills) {
        List<Integer> strongestSkillsForClass1st = new ArrayList<>(Arrays.asList(0, 0, 0, 0, 0, 0, 0, 0));
        List<Integer> strongestSkillsForClass2nd = new ArrayList<>(Arrays.asList(0, 0, 0, 0, 0, 0, 0, 0));
        List<Integer> strongestSkillsForClass3rd = new ArrayList<>(Arrays.asList(0, 0, 0, 0, 0, 0, 0, 0));
        List<Integer> strongestSkillsForClass4th = new ArrayList<>(Arrays.asList(0, 0, 0, 0, 0, 0, 0, 0));
        List<Integer> strongestSkillsForClass5th = new ArrayList<>(Arrays.asList(0, 0, 0, 0, 0, 0, 0, 0));

        for (List<Integer> listSkillForClass : clanSkills) {
            int totalSkillLevelOfClass = LogicUtil.getSumInt(listSkillForClass);

            if (totalSkillLevelOfClass > LogicUtil.getSumInt(strongestSkillsForClass1st)) {
                strongestSkillsForClass1st = listSkillForClass;
            } else if (totalSkillLevelOfClass > LogicUtil.getSumInt(strongestSkillsForClass2nd)) {
                strongestSkillsForClass2nd = listSkillForClass;
            } else if (totalSkillLevelOfClass > LogicUtil.getSumInt(strongestSkillsForClass3rd)) {
                strongestSkillsForClass3rd = listSkillForClass;
            } else if (totalSkillLevelOfClass > LogicUtil.getSumInt(strongestSkillsForClass4th)) {
                strongestSkillsForClass4th = listSkillForClass;
            } else if (totalSkillLevelOfClass > LogicUtil.getSumInt(strongestSkillsForClass5th)) {
                strongestSkillsForClass5th = listSkillForClass;
            }
        }

        return new ArrayList<>(Arrays.asList(strongestSkillsForClass1st,
                strongestSkillsForClass2nd,
                strongestSkillsForClass3rd,
                strongestSkillsForClass4th,
                strongestSkillsForClass5th));
    }

    private List<UserInteriorCloneEntity> getInteriorCloneForPosition(int positionIndex, List<UserInteriorTopEntity> listInteriorTop) {
        if (listInteriorTop.isEmpty()) return new ArrayList<>();
        List<UserInteriorCloneEntity> listInteriorClone = new ArrayList<>();
        ResAbyssFormationEntity resFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        List<Integer> listHeroTypeId = resFormation.getListHeroTypeId();
        List<ResAbyssHeroTypeEntity> listResAbyssHeroType = CfgAbyss.getListResAbyssHeroType(listHeroTypeId);
        if (listResAbyssHeroType == null) {
            return null;
        }

        for (int i = 0; i < listResAbyssHeroType.size(); i++) {
            ResAbyssHeroTypeEntity resAbyssHeroType = listResAbyssHeroType.get(i);
            UserInteriorTopEntity interiorTop = listInteriorTop.get(i);
            int heroKey = resAbyssHeroType.getHeroKey();
            String itemKeyForInteriorClone = getItemKeyForInteriorClone(heroKey, interiorTop);

            UserInteriorCloneEntity interiorClone = UserInteriorCloneEntity.builder()
                    .userId(user.getId())
                    .positionIndex(positionIndex)
                    .heroKey(heroKey)
                    .itemKey(itemKeyForInteriorClone)
                    .build();


            listInteriorClone.add(interiorClone);
        }

        return listInteriorClone;
    }

    private String getItemKeyForInteriorClone(int heroKey, UserInteriorTopEntity interiorTop) {
        List<Integer> listItemKey = new ArrayList<>();
        ResHeroEntity resHero = ResHero.getHero(heroKey);
        int heroClass = resHero.getHeroClass().value, heroFaction = resHero.getHeroFaction().value;
        List<Integer> listResRedItemKeyOfHero = CfgInterior.getListResItemKeyOfHero(heroKey);
        List<ResInteriorItemEntity> listResRedItemByClassAndFaction = CfgInterior.getListResRedItemByClassAndFaction(heroClass, heroFaction);
        List<ResInteriorItemEntity> listResRedItemByClassOrFactionOnly = CfgInterior.getListResRedItemByClassOrFactionOnly(heroClass, heroFaction);
        List<ResInteriorItemEntity> listResYellowItemByClazzAndFaction = CfgInterior.getListResYellowItemByClassAndFaction(heroClass, heroFaction);
        List<ResInteriorItemEntity> listResYellowItemByClazzOrFactionOnly = CfgInterior.getListResYellowItemByClassOrFactionOnly(heroClass, heroFaction);
        List<ResInteriorItemEntity> listResPurpleItemByFaction = CfgInterior.getListResPurpleItemByFaction(heroFaction);

        for (int i = 0; i < interiorTop.getNumberRedItemEffect3(); i++) {
            listItemKey.add(listResRedItemKeyOfHero.get(i));
        }

        for (int i = 0; i < interiorTop.getNumberRedItemEffect2(); i++) {
            listItemKey.add(listResRedItemByClassAndFaction.get(i).getId());
        }

        for (int i = 0; i < interiorTop.getNumberRedItemEffect1(); i++) {
            listItemKey.add(listResRedItemByClassOrFactionOnly.get(i).getId());
        }

        for (int i = 0; i < interiorTop.getNumberYellowItemEffect2(); i++) {
            listItemKey.add(listResYellowItemByClazzAndFaction.get(i).getId());
        }

        for (int i = 0; i < interiorTop.getNumberYellowItemEffect1(); i++) {
            listItemKey.add(listResYellowItemByClazzOrFactionOnly.get(i).getId());
        }

        for (int i = 0; i < interiorTop.getNumberPurpleItem(); i++) {
            listItemKey.add(listResPurpleItemByFaction.get(i).getId());
        }

        return StringHelper.toDBString(listItemKey);
    }

    private BattleTeam getTeamClone(int positionIndex) {
        List<UserHeroCloneEntity> allHeroClone = mUser.getCache().getAllHeroClone(this, mUser);
        List<Integer> allHeroIdForBattle = new ArrayList<>();

        for (UserHeroCloneEntity heroClone : allHeroClone) {
            if (heroClone.getPositionIndex() != positionIndex) continue;
            allHeroIdForBattle.add(heroClone.getId());
        }

        for (int i = allHeroIdForBattle.size(); i < BattleConfig.TEAM_HERO_SIZE; i++) {
            allHeroIdForBattle.add(-1);
        }

        ResAbyssFormationEntity resFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        ResAbyssMechanicEntity resAbyssMechanic = CfgAbyss.resAbyssMechanicMapById.get(resFormation.getMechanicId());
        allHeroIdForBattle.add(resAbyssMechanic.getPet());

        return BattleTeam.getInstanceClone(mUser, allHeroIdForBattle, positionIndex);
    }

    private Pbmethod.CommonVector.Builder getCommonVectorForReward(int rewardIndex) {
        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(rewardIndex);
        builder.addALong(uAbyss.canReceiveReward(rewardIndex) ? 1 : 0);
        builder.addALong(uAbyss.getChosenReward().get(rewardIndex * 2 + 1));
        builder.addAllALong(CfgAbyss.getDataOfReward(uAbyss.getDifficult(), rewardIndex));
        builder.addAString(String.format(getLang(Lang.event_abyss_require_number_star), CfgAbyss.config.getDifficultData(uAbyss.getDifficult()).getRequireStarToGetReward().get(rewardIndex)));
        return builder;
    }

    private List<Long> getAllInfoQuestCompleteThisTime(BattleResultEntityNew battleResult, int positionIndex) {
        List<Integer> allQuestIdCompleteThisTime = getAllQuestIdCompleteThisTime(battleResult, positionIndex);
        CfgAbyss.DifficultData difficultData = CfgAbyss.config.getDifficultData(uAbyss.getDifficult());
        ResAbyssFormationEntity resAbyssFormation = CfgAbyss.getAbyssFormationForPosition(uAbyss.getDifficult(), positionIndex, user.getServer());
        List<Long> allInfoQuestCompleteThisTime = new ArrayList<>();
        for (int questIdCompleteThisTime : allQuestIdCompleteThisTime) {
            if (!allInfoQuestCompleteThisTime.isEmpty()) allInfoQuestCompleteThisTime.add(-1L);
            allInfoQuestCompleteThisTime.add((long) questIdCompleteThisTime);
            List<Integer> listQuestId = resAbyssFormation.getListQuestId();
            for (int i = 0; i < listQuestId.size(); i++) {
                if (listQuestId.get(i) != questIdCompleteThisTime) continue;

                allInfoQuestCompleteThisTime.addAll(difficultData.getAllQuestBonusForPosition(positionIndex).get(i));
            }
        }

        return allInfoQuestCompleteThisTime;
    }

    private String getNewStatusForUserAbyss(BattleResultEntityNew battleResult, int positionIndex) {
        List<Integer> allQuestIdCompleteThisTime = getAllQuestIdCompleteThisTime(battleResult, positionIndex);
        List<List<Integer>> newStatusForUserAbyss = new ArrayList<>(uAbyss.getAbyssStatus());
        List<Integer> newQuestStatusForPosition = new ArrayList<>(uAbyss.getAbyssStatus().get(positionIndex));
        for (int i = 0; i < newQuestStatusForPosition.size() - 1; i += 2) {
            int questId = newQuestStatusForPosition.get(i);
            if (!allQuestIdCompleteThisTime.contains(questId)) continue;

            newQuestStatusForPosition.set(i + 1, 1);
        }

        newStatusForUserAbyss.set(positionIndex, newQuestStatusForPosition);

        return StringHelper.toDBString(newStatusForUserAbyss);
    }


    private int getNewTotalStarForUserAbyss(String newAbyssStatus) {
        int newTotalStar = 0;
        List<List<Integer>> newStatusForUserAbyss = GsonUtil.strTo2ListInt(newAbyssStatus);
        for (List<Integer> stageStatus : newStatusForUserAbyss)
            for (int i = 1; i < stageStatus.size(); i += 2) if (stageStatus.get(i) > 0) newTotalStar++;

        return newTotalStar;
    }

    private int getNewStageStarForUserAbyss(String newAbyssStatus, int positionIndex) {
        int newStageStar = 0;
        List<Integer> stageStatus = GsonUtil.strTo2ListInt(newAbyssStatus).get(positionIndex);
        for (int i = 1; i < stageStatus.size(); i += 2) if (stageStatus.get(i) > 0) newStageStar++;

        return newStageStar;
    }

    private List<Integer> getAllQuestIdCompleteThisTime(BattleResultEntityNew battleResult, int positionIndex) {
        List<Integer> allQuestStatusOfPosition = uAbyss.getAbyssStatus().get(positionIndex);
        List<Integer> allQuestIdCompleteThisTime = new ArrayList<>();
        for (int i = 0; i < allQuestStatusOfPosition.size() - 1; i += 2) {
            int questId = allQuestStatusOfPosition.get(i);
            int questStatus = allQuestStatusOfPosition.get(i + 1);
            if (questStatus == 1 || questStatus == 2) continue;

            if (!isQuestComplete(battleResult, questId)) continue;

            allQuestIdCompleteThisTime.add(questId);
        }

        return allQuestIdCompleteThisTime;
    }

    private boolean isQuestComplete(BattleResultEntityNew battleResult, int questId) {
        ResAbyssQuestEntity resAbyssQuest = CfgAbyss.resQuestMapByQuestId.get(questId);
        int mainId = resAbyssQuest.getMainId();
        List<Integer> typeData = resAbyssQuest.getDataQuest();
        ResHeroEntity resHero = null;
        SimulateResult simulateResult = battleResult.aResult.get(0);
        List<SimulateHero> atkTeam = simulateResult.team1;
        switch (mainId) {
            case CfgAbyss.WIN -> {
                return battleResult.isWin();
            }
            case CfgAbyss.WIN_WITH_NUM_FACTION -> {
                int requireNumberHeroOfFaction = typeData.get(0);
                int questFaction = typeData.get(1);
                int countHeroOfFaction = 0;
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    if (resHero == null || resHero.getHeroFaction().value != questFaction) continue;

                    countHeroOfFaction++;
                }

                return battleResult.isWin() && countHeroOfFaction >= requireNumberHeroOfFaction;
            }
            case CfgAbyss.WIN_WITH_NUM_CLASS -> {
                int requireNumberHeroOfClass = typeData.get(0);
                int questClass = typeData.get(1);
                int countHeroOfFaction = 0;
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    if (resHero == null || resHero.getHeroClass().value != questClass) continue;

                    countHeroOfFaction++;
                }

                return battleResult.isWin() && countHeroOfFaction >= requireNumberHeroOfClass;
            }
            case CfgAbyss.WIN_ALL_IN_1_FACTION -> {
                resHero = ResHero.getHero(atkTeam.get(0).heroId);
                int faction = resHero.getHeroFaction().value;
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    if (resHero.getHeroFaction().value != faction) return false;
                }

                return battleResult.isWin();
            }
            case CfgAbyss.WIN_WITHOUT_FACTION, CfgAbyss.WIN_WITHOUT_FACTION_X2 -> {
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    if (typeData.contains(resHero.getHeroFaction().value)) return false;
                }

                return battleResult.isWin();
            }
            case CfgAbyss.WIN_WITH_HERO_KEY_X2 -> {
                int heroKey1 = typeData.get(0);
                ResHeroEntity resHero1 = ResHero.getHero(heroKey1);
                List<Integer> listHeroKey1 = ResHero.aHero.stream().filter(tmpResHero -> tmpResHero.getAvatar() == resHero1.getAvatar()).map(ResHeroEntity::getId).collect(Collectors.toList());
                int heroKey2 = typeData.get(1);
                ResHeroEntity resHero2 = ResHero.getHero(heroKey2);
                List<Integer> listHeroKey2 = ResHero.aHero.stream().filter(tmpResHero -> tmpResHero.getAvatar() == resHero2.getAvatar()).map(ResHeroEntity::getId).collect(Collectors.toList());
                boolean isContainHeroKey1 = false, isContainHeroKey2 = false;
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    if (listHeroKey1.contains(simulateHero.heroId)) isContainHeroKey1 = true;
                    if (listHeroKey2.contains(simulateHero.heroId)) isContainHeroKey2 = true;
                }

                return battleResult.isWin() && isContainHeroKey1 && isContainHeroKey2;
            }
            case CfgAbyss.WIN_WITH_NUM_FACTION_X2 -> {
                int requireNumberHeroOfFaction1 = typeData.get(0);
                int requireFaction1 = typeData.get(1);
                int requireNumberHeroOfFaction2 = typeData.get(2);
                int requireFaction2 = typeData.get(3);
                int countHeroOfFaction1 = 0, countHeroOfFaction2 = 0;
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    if (resHero.getHeroFaction().value == requireFaction1) {
                        countHeroOfFaction1++;
                    } else if (resHero.getHeroFaction().value == requireFaction2) {
                        countHeroOfFaction2++;
                    }
                }

                return battleResult.isWin() && countHeroOfFaction1 >= requireNumberHeroOfFaction1 && countHeroOfFaction2 >= requireNumberHeroOfFaction2;
            }
            case CfgAbyss.WIN_WITH_NUM_DIFF_FACTION -> {
                int requireNumberDiffFaction = typeData.get(0);
                Set<Integer> zetFaction = new HashSet<>();
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    zetFaction.add(resHero.getHeroFaction().value);
                }

                return battleResult.isWin() && zetFaction.size() >= requireNumberDiffFaction;
            }
            case CfgAbyss.WIN_WITH_NUM_ROUND -> {
                int requireNumberRound = typeData.get(0);
                int roundEnd = simulateResult.numberRound;

                return battleResult.isWin() && roundEnd <= requireNumberRound;
            }
            case CfgAbyss.WIN_WITHOUT_CLASS_X2, CfgAbyss.WIN_WITHOUT_CLASS -> {
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    resHero = ResHero.getHero(simulateHero.heroId);
                    if (typeData.contains(resHero.getHeroClass().value)) return false;
                }

                return battleResult.isWin();
            }
            case CfgAbyss.WIN_WITHOUT_NUM_DEAD_HERO -> {
                int requireNumberDeadHero = typeData.get(0);
                int numberAliveHero = simulateResult.atkHeroLive.size() / 3;
                int numberHeroInput = (int) simulateResult.team1.stream().filter(simulateHero -> simulateHero.pos < BattleConfig.TEAM_HERO_SIZE).count();
                int numberDeadHero = numberHeroInput - numberAliveHero;

                return battleResult.isWin() && numberDeadHero <= requireNumberDeadHero;
            }
            //            case CfgAbyss.WIN_WITHOUT_NUM_SAME_HERO ->{
            //                typeDataIndex = resAbyssQuest.getTypeDataIndex();
            //                typeData = resAbyssQuestType.getTypeData().get(typeDataIndex);
            //                int requireNumberSameHero = typeData.get(0);
            //                long maxNumberSameHero = 0;
            //                Set<Integer> zetHeroKey = new HashSet<>();
            //                for (SimulateHero simulateHero : atkTeam) {
            //                    if (simulateHero == null || simulateHero.heroAnalysis == null) continue;
            //                    zetHeroKey.add(simulateHero.heroId);
            //                }
            //
            //                List<Integer> listHeroKey = new ArrayList<>(zetHeroKey);
            //                for (int heroKey : listHeroKey) {
            //                    long numberSameHero = listHeroBattleAttack.stream().filter(heroBattleAttack -> heroBattleAttack.isHero()
            //                            && heroBattleAttack.heroId == heroKey).count();
            //                    maxNumberSameHero = Math.max(numberSameHero, maxNumberSameHero);
            //                }
            //
            //
            //                return battleResult.isWin() && maxNumberSameHero < requireNumberSameHero;
            //            }
            case CfgAbyss.WIN_WITH_NUM_HERO_LEFT -> {
                int requireNumberHeroLeft = typeData.get(0);
                int numberAliveHero = simulateResult.atkHeroLive.size() / 3;

                return battleResult.isWin() && numberAliveHero <= requireNumberHeroLeft;
            }
            case CfgAbyss.WIN_WITH_NUM_HERO -> {
                int requireNumberHero = typeData.get(0);
                long numberHeroQualified = atkTeam.stream().filter(simulateHero -> simulateHero.pos < BattleConfig.TEAM_HERO_SIZE).count();

                return battleResult.isWin() && numberHeroQualified == requireNumberHero;
            }

            case CfgAbyss.WIN_WITH_NUM_HERO_TRIGGER_NUM_ACTIVE_SKILL -> {
                int requireNumberHero = typeData.get(0), requireNumberActiveSkill = typeData.get(1);
                int numberHeroQualified = 0;
                for (SimulateHero simulateHero : atkTeam) {
                    if (simulateHero == null || simulateHero.heroAnalysis == null || simulateHero.pos >= BattleConfig.TEAM_HERO_SIZE)
                        continue;
                    if (simulateHero.getHeroAnalysis().cntActiveSkill >= requireNumberActiveSkill)
                        numberHeroQualified++;
                }

                return battleResult.isWin() && numberHeroQualified >= requireNumberHero;
            }
            case CfgAbyss.WIN_WITHOUT_NUM_HERO_UNDER_NUM_PERCENT_HP -> {
                int requireNumberHero = typeData.get(0), requirePercentHP = typeData.get(1);
                int numberHeroQualified = 0;
                for (int i = 0; i < simulateResult.atkHeroLive.size(); i += 3) {
                    int percentHp = Math.toIntExact(simulateResult.atkHeroLive.get(i + 1));
                    if (percentHp < requirePercentHP) numberHeroQualified++;
                }

                return battleResult.isWin() && numberHeroQualified < requireNumberHero;
            }
            case CfgAbyss.WIN_WITH_NUM_HERO_NUM_STAR -> {
                int requireNumberHero = typeData.get(0), requireStar = typeData.get(1);
                int numberHeroQualified = (int) atkTeam.stream().filter(simulateHero -> simulateHero.pos < BattleConfig.TEAM_HERO_SIZE && simulateHero.star >= requireStar).count();
                return battleResult.isWin() && numberHeroQualified >= requireNumberHero;
            }
            case CfgAbyss.WIN_WITH_HEROES_IN_NUM_LINE_KILL_NUM_ENEMY -> {
                int requireLine = typeData.get(0), requireNumberKill = typeData.get(1);
                int numberHeroQualified = (int) atkTeam.stream()
                        .filter(simulateHero -> HeroBattleEntity.getLine(simulateHero.pos) + 1 == requireLine && simulateHero.numberKill >= requireNumberKill)
                        .count();
                return battleResult.isWin() && numberHeroQualified >= 1;
            }
            case CfgAbyss.WIN_WITH_NUM_HERO_START_BATTLE_FULL_ENERGY -> {
                int requireNumberHero = typeData.get(0);
                int numberHeroQualified = (int) atkTeam.stream().filter(simulateHero -> simulateHero.pos < BattleConfig.TEAM_HERO_SIZE && simulateHero.point.get(Point.ANGER) + 50 >= 100).count();
                return battleResult.isWin() && numberHeroQualified >= requireNumberHero;
            }

            case CfgAbyss.WIN_WITH_ENEMY_ATTACK_FIRST -> {
                return battleResult.isWin() && !simulateResult.isTeam1AttackFirst;
            }
            //            case CfgAbyss.WIN_WITHOUT_ANY_HERO_TAKE_EFFECT_NUM->{
            //                int requireNumberHero = 0;
            //                BattleEffectType battleEffectType = BattleEffectType.get(typeData.get(0));
            //                if (battleEffectType == null) return false;
            //                numberHeroQualified = listHeroBattleAttack.stream().filter(heroBattle ->
            //                                heroBattle.isHero() && heroBattle.isAlreadyGotStunEffect)
            //                        .count();
            //                return battleResult.isWin() && numberHeroQualified <= requireNumberHero;
            //            }
        }

        return false;
    }

    private String getHeroCloneSkills(String strSkills, int star) {
        if (star < 10) return "[0,0,0,0,0,0,0,0,0,0]";

        return StringHelper.toDBString(GsonUtil.strToListInt(strSkills).subList(0, star - 10 + 1));
    }

    private List<Integer> getHeroCloneArtifact(String strOldItems, String strHeroTypeArtifact) {
        List<Integer> oldArtifact = GsonUtil.strToListInt(strOldItems).subList(4, 6);
        List<Integer> heroTypeArtifact = GsonUtil.strToListInt(strHeroTypeArtifact);
        int artifactQuality = oldArtifact.get(1);

        if (artifactQuality == 6) {
            return new ArrayList<>(Arrays.asList(heroTypeArtifact.get(0), artifactQuality));
        } else if (artifactQuality == 5) {
            return new ArrayList<>(Arrays.asList(heroTypeArtifact.get(1), artifactQuality));
        } else {
            return getRandomArtifactByQuality(artifactQuality);
        }
    }

    private List<Integer> getRandomArtifactByQuality(int quality) {
        ResArtifactEntity randomArtifact = LogicUtil.getRandom(ResArtifact.getListArtifactByQuality(quality));
        return randomArtifact == null ? new ArrayList<>(Arrays.asList(0, 0)) : new ArrayList<>(Arrays.asList(randomArtifact.getId(), quality));
    }

    private String getHeroCloneSpecialItems(String strSpecialItems, int specialItemType) {
        List<Integer> arrSpecialItem = GsonUtil.strToListInt(strSpecialItems);
        int attackLevel = arrSpecialItem.get(0);
        int attackTier = arrSpecialItem.get(1);
        int hpLevel = arrSpecialItem.get(2);
        int hpTier = arrSpecialItem.get(3);

        if (attackLevel == hpLevel && attackTier == hpTier) {
            //            if (attackLevel == CfgSpecialItem.getMaxEnhance()) return "[200,15,200,15]";

            return strSpecialItems;
        }

        int maxLevel = Math.min(Math.max(attackLevel, hpLevel), 200);
        int minLevel = Math.min(Math.min(attackLevel, hpLevel), 200);
        int maxTier = Math.min(Math.max(attackTier, hpTier), 15);
        int minTier = Math.min(Math.min(attackTier, hpTier), 15);

        return specialItemType == TYPE_ATTACK ?
                StringHelper.toDBString(new ArrayList<>(Arrays.asList(maxLevel, maxTier, minLevel, minTier)))
                :
                StringHelper.toDBString(new ArrayList<>(Arrays.asList(minLevel, minTier, maxLevel, maxTier)));
    }

    private UserHeroTopEntity getHeroTop(List<UserHeroTopEntity> listHeroTop, int top) {
        for (UserHeroTopEntity heroTop : listHeroTop) {
            if (heroTop.getTop() != top) continue;

            return heroTop;
        }

        return null;
    }

    private long getCountdownSecond() {
        Date targetTime = uAbyss.isBreakTime() ? CfgAbyss.getNextStartDate() : CfgAbyss.getEndAttackDate();
        return (targetTime.getTime() - new Date().getTime()) / 1000;
    }

    private boolean updateUserAbyssWhenNewEvent() {
        if (!dbUpdateUserAbyssWhenNewEvent()) {
            return false;
        }

        uAbyss.setDifficult(-1);
        uAbyss.setCurrentPosition(0);
        uAbyss.setReceived(false);
        uAbyss.setLastSeasonReceived(true);
        uAbyss.setTotalStar(0);
        uAbyss.setLastTime(new Date());
        return true;
    }

    private boolean updateUserAbyssWhenChooseDifficult(int difficult, int server) {
        if (!dbUpdateUserAbyssWhenChooseDifficult(difficult)) {
            return false;
        }

        uAbyss.setStatus(CfgAbyss.getDefaultUserAbyssStatus(difficult, server));
        uAbyss.setChosenReward(CfgAbyss.getDefaultChosenReward(difficult));
        uAbyss.setDifficult(difficult);
        uAbyss.setLastTime(new Date());
        return true;
    }

    private boolean updateUserAbyssWhenClaimReward(UserAbyssEntity uAbyss) {
        if (!dbUpdateUserAbyssWhenClaimReward()) {
            return false;
        }

        uAbyss.setReceived(true);
        uAbyss.setLastSeasonReceived(true);
        uAbyss.setLastTime(new Date());
        return true;
    }

    private static boolean updateUserAbyssBackupWhenClaimReward(UserAbyssEntity uAbyss) {
        if (!dbUpdateUserAbyssBackupWhenClaimReward(uAbyss.getUserId())) {
            return false;
        }

        uAbyss.setReceived(true);
        uAbyss.setLastSeasonReceived(true);
        uAbyss.setLastTime(new Date());
        return true;
    }

    private static String getSqlToUpdateStat(List<AbyssStatisticEntity> listAbyssStat) {
        List<String> aStrData = getAStringDataToUpdateStat(listAbyssStat);
        if (aStrData.isEmpty()) return null;

        String sql = "INSERT INTO dson.abyss_statistic (difficult, reward_index,star,slot1, slot2, slot3, percent_slot1, percent_slot2, percent_slot3) " +
                "VALUES %s ON DUPLICATE KEY UPDATE " +
                "slot1 = VALUES(slot1), " +
                "slot2 = VALUES(slot2), " +
                "slot3 = VALUES(slot3), " +
                "percent_slot1 = VALUES(percent_slot1), " +
                "percent_slot2 = VALUES(percent_slot2), " +
                "percent_slot3 = VALUES(percent_slot3)";
        sql = String.format(sql, aStrData.stream().collect(Collectors.joining(",")));
        return sql;
    }

    private static List<String> getAStringDataToUpdateStat(List<AbyssStatisticEntity> listAbyssStat) {
        List<String> aStrData = new ArrayList<>();
        for (AbyssStatisticEntity abyssStatistic : listAbyssStat) {
            aStrData.add(String.format("(%s,%s,%s,%s,%s,%s,%s,%s,%s)", abyssStatistic.getDifficult(), abyssStatistic.getRewardIndex(), abyssStatistic.getStar(),
                    abyssStatistic.getSlot1(), abyssStatistic.getSlot2(), abyssStatistic.getSlot3(),
                    abyssStatistic.getPercentSlot1(), abyssStatistic.getPercentSlot2(), abyssStatistic.getPercentSlot3()));
        }

        return aStrData;
    }
    //endregion

    //region Database access
    private boolean dbUpdateStatusForUserAbyss(String newStatusForUserAbyss, int newPositionIndexForUser, int newTotalStar, String newDailyTurn) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update user_abyss set " +
                            "status =:newStatusForUserAbyss,daily_turn=:newDailyTurn, current_position =:newPositionIndexForUser, total_star=:newTotalStar, last_time=:lastTime where user_id=:userId")
                    .setParameter("userId", user.getId())
                    .setParameter("newStatusForUserAbyss", newStatusForUserAbyss)
                    .setParameter("newDailyTurn", newDailyTurn)
                    .setParameter("newPositionIndexForUser", newPositionIndexForUser)
                    .setParameter("newTotalStar", newTotalStar)
                    .setParameter("lastTime", DateTime.getFullDate());
            query.executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateStatusForUserAbyss(String newStatusForUserAbyss) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss set status =:newStatusForUserAbyss, last_time=:lastTime where user_id=:userId")
                    .setParameter("newStatusForUserAbyss", newStatusForUserAbyss)
                    .setParameter("lastTime", DateTime.getFullDate())
                    .setParameter("userId", user.getId()).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateUserAbyssWhenClaimReward() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss set received = true, last_season_received=true, last_time=:lastTime where user_id=:userId")
                    .setParameter("userId", user.getId())
                    .setParameter("lastTime", DateTime.getFullDate())
                    .executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private static boolean dbUpdateUserAbyssBackupWhenClaimReward(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss_backup set received = true, last_season_received=true, last_time=:lastTime where user_id=:userId")
                    .setParameter("userId", userId)
                    .setParameter("lastTime", DateTime.getFullDate())
                    .executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateUserAbyssWhenNewEvent() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss set difficult=-1, " +
                            "current_position=0," +
                            "received=false," +
                            "last_season_received=true," +
                            "total_star=0, " +
                            "last_time=:lastTime " +
                            "where user_id=:userId")
                    .setParameter("lastTime", DateTime.getFullDate())
                    .setParameter("userId", user.getId())
                    .executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateUserAbyssWhenChooseDifficult(int difficult) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss set difficult=:difficult, status=:newStatus,chosen_reward=:newChosenReward, last_time=:lastTime where user_id=:userId")
                    .setParameter("newStatus", CfgAbyss.getDefaultUserAbyssStatus(difficult, user.getServer()))
                    .setParameter("newChosenReward", CfgAbyss.getDefaultChosenReward(difficult))
                    .setParameter("lastTime", DateTime.getFullDate())
                    .setParameter("difficult", difficult)
                    .setParameter("userId", user.getId())
                    .executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateChosenRewardForUserAbyss(String newChosenReward) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss set chosen_reward =:newChosenReward, last_time=:lastTime where user_id=:userId")
                    .setParameter("newChosenReward", newChosenReward)
                    .setParameter("lastTime", DateTime.getFullDate())
                    .setParameter("userId", user.getId())
                    .executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateTurn(String newDailyTurn, String newDailyBuy) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery("update user_abyss set daily_turn =:newDailyTurn, daily_buy =:newDailyBuy, last_time=:lastTime where user_id=:userId")
                    .setParameter("userId", user.getId())
                    .setParameter("newDailyTurn", newDailyTurn)
                    .setParameter("newDailyBuy", newDailyBuy)
                    .setParameter("lastTime", DateTime.getFullDate())
                    .executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateHeroClone(List<UserHeroCloneEntity> allNewHeroClone) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Tháo toàn bộ đồ khỏi tầng
            session.createNativeQuery("delete from dson.user_hero_clone where user_id = " + mUser.getUser().getId()).executeUpdate();

            String sqlToInsertClone = "insert into user_hero_clone (user_id,hero_id,position_index,level,star,item,skills," +
                    "treasure,treasure_effect,treasure_index,special_item,locked,locked_crystal,locked_trial,locked_vodau,hero_void,power_scale) values ";
            for (int i = 0; i < allNewHeroClone.size(); i++) {
                UserHeroCloneEntity heroClone = allNewHeroClone.get(i);
                if (i == 0) {
                    sqlToInsertClone += "(" + heroClone.getUserId() + "," + heroClone.getHeroId() + "," + heroClone.getPositionIndex() + "," +
                            heroClone.getLevel() + "," + heroClone.getStar() + ",'" + heroClone.getItem() + "','" + heroClone.getSkills()
                            + "'," + heroClone.getTreasure() + "," + heroClone.getTreasureEffect() + "," + heroClone.getTreasureIndex() + ",'"
                            + heroClone.getSpecialItem() + "'," + heroClone.getLocked() + "," + heroClone.getLockedCrystal() + "," + heroClone.getLockedTrial()
                            + "," + heroClone.getLockedVodau() + ",'" + heroClone.getHeroVoid() + "', " + heroClone.getPowerScale() + ") ";
                    continue;
                }
                sqlToInsertClone += ", (" + heroClone.getUserId() + "," + heroClone.getHeroId() + "," + heroClone.getPositionIndex() + "," +
                        heroClone.getLevel() + "," + heroClone.getStar() + ",'" + heroClone.getItem() + "','" + heroClone.getSkills()
                        + "'," + heroClone.getTreasure() + "," + heroClone.getTreasureEffect() + "," + heroClone.getTreasureIndex() + ",'"
                        + heroClone.getSpecialItem() + "'," + heroClone.getLocked() + "," + heroClone.getLockedCrystal() + "," + heroClone.getLockedTrial()
                        + "," + heroClone.getLockedVodau() + ",'" + heroClone.getHeroVoid() + "', " + heroClone.getPowerScale() + ") ";
            }
            session.createNativeQuery(sqlToInsertClone).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdatePetClone(List<UserPetCloneEntity> allNewPetClone) {
        if (allNewPetClone == null || allNewPetClone.isEmpty()) return true;

        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Tháo toàn bộ đồ khỏi tầng
            session.createNativeQuery("delete from dson.user_pet_clone where user_id = " + mUser.getUser().getId()).executeUpdate();

            String sqlToInsertClone = "insert into user_pet_clone (user_id,pet_id,position_index,level,tier," +
                    "passive1,passive2,passive3,passive4,rune1,rune2,rune3) values ";
            for (int i = 0; i < allNewPetClone.size(); i++) {
                UserPetCloneEntity petClone = allNewPetClone.get(i);
                if (i == 0) {
                    sqlToInsertClone += "(" + petClone.getUserId() + "," + petClone.getPetId() + "," + petClone.getPositionIndex() + "," +
                            petClone.getLevel() + "," + petClone.getTier() + "," + petClone.getPassive1() + "," + petClone.getPassive2() + "," + petClone.getPassive3()
                            + "," + petClone.getPassive4() + "," + petClone.getRune1() + "," + petClone.getRune2() + ","
                            + petClone.getRune3() + ") ";
                    continue;
                }
                sqlToInsertClone += ", (" + petClone.getUserId() + "," + petClone.getPetId() + "," + petClone.getPositionIndex() + "," +
                        petClone.getLevel() + "," + petClone.getTier() + "," + petClone.getPassive1() + "," + petClone.getPassive2() + "," + petClone.getPassive3()
                        + "," + petClone.getPassive4() + "," + petClone.getRune1() + "," + petClone.getRune2() + ","
                        + petClone.getRune3() + ") ";
            }
            session.createNativeQuery(sqlToInsertClone).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateClanClone(List<UserClanCloneEntity> listClanClone) {
        if (listClanClone == null || listClanClone.isEmpty()) return true;

        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Tháo toàn bộ đồ khỏi tầng
            session.createNativeQuery("delete from dson.user_clan_clone where user_id = " + mUser.getUser().getId()).executeUpdate();
            String sqlToInsertClone = "insert into user_clan_clone (user_id,position_index,skills,skills2) values ";
            for (int i = 0; i < listClanClone.size(); i++) {
                UserClanCloneEntity clanClone = listClanClone.get(i);
                if (i == 0) {
                    sqlToInsertClone += "(" + clanClone.getUserId() + "," + clanClone.getPositionIndex() + ",'" + clanClone.getSkills() + "','" +
                            clanClone.getSkills2() + "') ";
                    continue;
                }
                sqlToInsertClone += ", (" + clanClone.getUserId() + "," + clanClone.getPositionIndex() + ",'" + clanClone.getSkills() + "','" +
                        clanClone.getSkills2() + "') ";
            }
            session.createNativeQuery(sqlToInsertClone).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateInteriorClone(List<UserInteriorCloneEntity> listInteriorClone) {
        if (listInteriorClone == null || listInteriorClone.isEmpty()) return true;
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            //Tháo toàn bộ đồ khỏi tầng
            session.createNativeQuery("delete from dson.user_interior_clone where user_id = " + mUser.getUser().getId()).executeUpdate();

            String sqlToInsertClone = "insert into user_interior_clone (user_id,position_index,hero_key,item_key) values ";
            for (int i = 0; i < listInteriorClone.size(); i++) {
                UserInteriorCloneEntity interiorClone = listInteriorClone.get(i);
                if (i == 0) {
                    sqlToInsertClone += "(" + interiorClone.getUserId() + "," + interiorClone.getPositionIndex() + "," + interiorClone.getHeroKey() + ",'" +
                            interiorClone.getItemKey() + "') ";
                    continue;
                }
                sqlToInsertClone += ", (" + interiorClone.getUserId() + "," + interiorClone.getPositionIndex() + "," + interiorClone.getHeroKey() + ",'" +
                        interiorClone.getItemKey() + "') ";
            }
            session.createNativeQuery(sqlToInsertClone).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateAlbumClone(List<UserAlbumCloneEntity> listAlbumClone) {
        if (listAlbumClone.isEmpty()) return true;
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();

            session.createNativeQuery("delete from dson.user_album_clone where user_id = " + user.getId()).executeUpdate();

            String sqlToInsertClone = "insert into user_album_clone (user_id,album_id,image_id) values ";
            for (int i = 0; i < listAlbumClone.size(); i++) {
                UserAlbumCloneEntity albumClone = listAlbumClone.get(i);
                if (i == 0) {
                    sqlToInsertClone += "(" + albumClone.getUserId() + "," + albumClone.albumId + "," + albumClone.imageId + ") ";
                    continue;
                }
                sqlToInsertClone += ", (" + albumClone.getUserId() + "," + albumClone.albumId + "," + albumClone.imageId + ") ";
            }
            session.createNativeQuery(sqlToInsertClone).executeUpdate();

            session.createNativeQuery("update user_abyss set last_time = '" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where user_id=" + user.getId()).executeUpdate();

            session.getTransaction().commit();
            return true;
        } catch (
                Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    public static List<AbyssStatisticEntity> dbGetListAbyssStat(UserAbyssEntity uAbyss) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<AbyssStatisticEntity> listAbyssStat = session.createNativeQuery("select * from dson.abyss_statistic where difficult=" + uAbyss.getDifficult(), AbyssStatisticEntity.class).getResultList();
            if (listAbyssStat == null || listAbyssStat.isEmpty()) {
                session.getTransaction().begin();
                listAbyssStat = new ArrayList<>();
                String sqlToInsertClone = "insert into abyss_statistic  values ";
                for (int i = 0; i < uAbyss.getChosenReward().size() / 2; i++) {
                    AbyssStatisticEntity abyssStatistic = AbyssStatisticEntity.builder().difficult(uAbyss.getDifficult()).rewardIndex(i).star((i + 1) * 3).build();
                    listAbyssStat.add(abyssStatistic);
                    if (i == 0) {
                        sqlToInsertClone += "(" + abyssStatistic.getDifficult() + ","
                                + abyssStatistic.getRewardIndex() + ","
                                + abyssStatistic.getStar() + ","
                                + abyssStatistic.getSlot1() + ","
                                + abyssStatistic.getSlot2() + ","
                                + abyssStatistic.getSlot3() + ","
                                + abyssStatistic.getPercentSlot1() + ","
                                + abyssStatistic.getPercentSlot2() + ","
                                + abyssStatistic.getPercentSlot3() + ") ";
                        continue;
                    }
                    sqlToInsertClone += ", (" + abyssStatistic.getDifficult() + ","
                            + abyssStatistic.getRewardIndex() + ","
                            + abyssStatistic.getStar() + ","
                            + abyssStatistic.getSlot1() + ","
                            + abyssStatistic.getSlot2() + ","
                            + abyssStatistic.getSlot3() + ","
                            + abyssStatistic.getPercentSlot1() + ","
                            + abyssStatistic.getPercentSlot2() + ","
                            + abyssStatistic.getPercentSlot3() + ") ";

                }
                session.createNativeQuery(sqlToInsertClone).executeUpdate();
                session.getTransaction().commit();
            }

            return listAbyssStat;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    public static boolean dbUpdateListAbyssStat(String sqlToUpdateStat) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            session.createNativeQuery(sqlToUpdateStat).executeUpdate();
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }
    //endregion
}
