package monster.controller;

import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import monster.config.CfgBrokenSpace;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserEventDetails2Entity;
import monster.dao.mapping.UserEventDetailsEntity;
import monster.dao.mapping.UserMaterialEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.object.BattleTeam;
import monster.object.UserInt;
import monster.protocol.CommonProto;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.dependence.BattleResultEntity;
import monster.service.battle.dependence.Point;
import monster.service.monitor.EventMonitor;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.util.ValidateParam;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * Only campaign here
 */
public class BrokenSpaceHandler extends AHandler {

    private final String KEY_DATA = "broken_space";

    @Override
    public AHandler newInstance() {
        return new BrokenSpaceHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(BROKEN_SPACE, BROKEN_BUY, BROKEN_SPACE_NEW);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");

//        if (true && mUser.getUser().getId() != 6802) {
//            addErrResponse("Chức năng đang bảo trì");
//            return;
//        }

        try {
            switch (actionId) {
                case BROKEN_SPACE:
                    attack();
                    break;
                case BROKEN_BUY:
                    buy();
                    break;
                case BROKEN_SPACE_NEW:
                    attackNew();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handle service
    private void buy() {
        Integer eventId = dbGetCurrentEventId();
        if (eventId == null) addErrResponse();
        else {
            UserInt uInt = mUser.getUData().getUInt();
            if (eventId != uInt.getValue(UserInt.BROKEN_SPACE_ID)) {
                uInt.setValue(UserInt.BROKEN_SPACE_ID, eventId);
                uInt.setValue(UserInt.BROKEN_SPACE_BUY_NUMBER, 0);
            }
            int numberBought = uInt.getValue(UserInt.BROKEN_SPACE_BUY_NUMBER);
            int number = (int) CommonProto.parseCommonVector(requestData).getALong(0);
            if (number < 0) number = 1;
            if (numberBought >= 300) {
                addErrResponse(getLang(Lang.err_event_max_buy, "300", "tù và"));
                return;
            }
            if (numberBought + number >= 300) {
                number = 300 - numberBought;
            }
            if (user.getGem() < CfgBrokenSpace.feeMaterial * number) {
                addErrResponse(getLang(Lang.err_not_enough_gem));
                return;
            }
            List<Long> aLong = Bonus.viewGem(-CfgBrokenSpace.feeMaterial * number);
            aLong.addAll(Bonus.viewMaterial(MaterialType.CHALLENGE_BADGE, number));
            addResponse(getCommonVector(Bonus.receiveListItem(mUser, KEY_DATA + "_buy", aLong)));
            EventMonitor.getInstance().addDropItem(user.getId(), EventType.BUY_ATTACK_BROKEN_SPACE, number);
            uInt.setValue(UserInt.BROKEN_SPACE_BUY_NUMBER, numberBought + number);
            uInt.update(user.getId());
        }
    }

    // 4822
    private void attackNew() {
        Pbmethod.ListCommonVector aCmm = CommonProto.parseListCommonVector(requestData);
        int eventId = (int) aCmm.getAVector(0).getALong(0);
        int type = (int) aCmm.getAVector(0).getALong(1);
        int number = (int) aCmm.getAVector(0).getALong(2);
        if (!ValidateParam.number(this, number)) return;

        List<Long> heroIds = aCmm.getAVector(1).getALongList();
        UserMaterialEntity uMaterial = mUser.getResources().getMaterial(MaterialType.CHALLENGE_BADGE);
        if (uMaterial.getNumber() < number) {
            addErrResponse(getLang(Lang.err_not_enough_material));
            return;
        }

        UserEventDetails2Entity event = UserEventDetails2Entity.get(user.getId(), eventId);
        if (event == null) {
            addErrResponse();
            return;
        }

        if (user.getLevel() < 50) {
            addErrResponse(getLang(Lang.user_function_level_required, user.getLevel()));
            return;
        }

        BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
        if (myTeam == null) {
            addErrResponse(getLang(Lang.err_hero_duplicate));
            return;
        }
        if (!myTeam.isOk()) {
            addErrResponse(getLang(Lang.err_team_invalid));
            return;
        }

        int level = event.getLevel();
        if (level == -1) {
            addErrResponse(getLang(Lang.level_passed));
            return;
        }
        ResMonsterEntity[] aMonster = event.getMonster(level);
        if (aMonster == null) {
            addErrResponse();
            return;
        }

        UserEventDetails2Entity.BrokenSpaceData data = event.getData(level);

        int numberMaterial = 0;
        BattleResultEntity battleResult = null;
        while (numberMaterial < number) {
            numberMaterial++;

            // atk and update hp
            battleResult = null;//BattleUtil.battle(myTeam, aMonster, true);
            List<HeroBattleEntity> defHero = battleResult.getMode().getDefTeam();

            long totalMaxHp = defHero.stream().filter(HeroBattleEntity::isHero).mapToLong(hero -> hero.point.getCalculatedValue(Point.HP)).sum();
            if (totalMaxHp > data.maxHp) data.maxHp = totalMaxHp;
            data.hp = defHero.stream().filter(HeroBattleEntity::isHero).mapToLong(hero -> hero.point.getCurrentHP()).sum();

            data.curHp = new long[aMonster.length];
            for (int index = 0; index < aMonster.length; index++) {
                if (aMonster[index] != null) {
                    aMonster[index].getPoint().setStartHp(defHero.get(index).point.getCurrentHP());
                    data.curHp[index] = defHero.get(index).point.getCurrentHP();
                }
            }

            if (battleResult.isWin()) break;
        }
        String title = Lang.getTitle("title_broken_space");
        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user, title);
        List<Long> aBonus = CfgBrokenSpace.getBonusNew(level, numberMaterial);
        aBonus.addAll(Bonus.viewMaterial(MaterialType.CHALLENGE_BADGE, -numberMaterial));
        if (event.updateEndBattle(level, data)) {
            builder.addAllABonus(Bonus.receiveListItem(mUser, KEY_DATA, aBonus));
            builder.addAllAFakeBonus(CfgBrokenSpace.getBonusNew(level, numberMaterial));
            builder.addAllAFakeBonus(CfgBrokenSpace.getBonusNew(level, numberMaterial));
            builder.setInfo(getCommonVector(1));
            addResponse(builder.build());
//            Actions.save(user, KEY_DATA, "attack", "hp", newHp, "damage", oldHp - newHp);
            if (battleResult.isWin()) EventMonitor.getInstance().addDropItem(user.getId(), EventType.BOSS_BROKEN_SPACE);
            EventMonitor.getInstance().addDropItem(user.getId(), EventType.BROKEN_SPACE_ATTACK, numberMaterial);
        } else addErrResponse();
    }

    // 4737
    private void attack() {
        Pbmethod.ListCommonVector aCmm = CommonProto.parseListCommonVector(requestData);
        int eventId = (int) aCmm.getAVector(0).getALong(0);
        int type = (int) aCmm.getAVector(0).getALong(1);
        int number = (int) aCmm.getAVector(0).getALong(2);
        if (!ValidateParam.number(this, number)) return;

        List<Long> heroIds = aCmm.getAVector(1).getALongList();

        UserMaterialEntity uMaterial = mUser.getResources().getMaterial(MaterialType.CHALLENGE_BADGE);
        if (uMaterial.getNumber() < number) {
            addErrResponse(getLang(Lang.err_not_enough_material));
            return;
        }

        UserEventDetailsEntity event = UserEventDetailsEntity.get(user.getId(), eventId);
        if (event == null) {
            addErrResponse();
            return;
        }

        if (user.getLevel() < 50) {
            addErrResponse(getLang(Lang.user_function_level_required, user.getLevel()));
            return;
        }

        BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
        if (myTeam == null) {
            addErrResponse(getLang(Lang.err_hero_duplicate));
            return;
        }
        if (!myTeam.isOk()) {
            addErrResponse(getLang(Lang.err_team_invalid));
            return;
        }

        int level = event.getLevel(type);
        if (level == -1) {
            addErrResponse(getLang(Lang.level_passed));
            return;
        }
        ResMonsterEntity[] aMonster = event.getMonster(type, level);
        if (aMonster == null) {
            addErrResponse();
            return;
        }
        long oldHp = Stream.of(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.getPoint().getStartHp()).sum();
        BattleResultEntity battleResult = null;//BattleUtil.battle(myTeam, aMonster, true);
        String title = Lang.getTitle("title_broken_space");

        Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user, title);
        List<HeroBattleEntity> defHero = battleResult.getMode().getDefTeam();
        long newHp = defHero.stream().filter(Objects::nonNull).mapToLong(hero -> hero.point.getCurrentHP()).sum();
        long minusHp = oldHp - newHp;
        int numberMaterial = 1;
        while (newHp > 0 && numberMaterial < number) {
            numberMaterial++;
            newHp -= minusHp;
            if (newHp < 0) newHp = 0;
        }
        List<Long> aBonus = CfgBrokenSpace.getBonus(type, level, numberMaterial);
        aBonus.addAll(Bonus.viewMaterial(MaterialType.CHALLENGE_BADGE, -numberMaterial));

        if (event.updateEndBattle(type, level, newHp)) {
            builder.addAllABonus(Bonus.receiveListItem(mUser, KEY_DATA, aBonus));
            builder.addAllAFakeBonus(CfgBrokenSpace.getBonus(type, level, numberMaterial));
            builder.addAllAFakeBonus(CfgBrokenSpace.getBonus(type, level, numberMaterial));
            builder.setInfo(getCommonVector(oldHp - newHp));
            addResponse(builder.build());
            Actions.save(user, KEY_DATA, "attack", "hp", newHp, "damage", oldHp - newHp);
            if (newHp == 0)
                EventMonitor.getInstance().addDropItem(user.getId(), EventType.BOSS_BROKEN_SPACE);

            EventMonitor.getInstance().addDropItem(user.getId(), EventType.BROKEN_SPACE_ATTACK, numberMaterial);
        } else addErrResponse();
    }
    //endregion

    //region Logic
    //endregion

    //region Database
    private Integer dbGetCurrentEventId() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return Integer.parseInt(session.createNativeQuery("select id from dson.config_event where template=13 and now() between from_time and to_time limit 1;").getSingleResult().toString());
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion
}
