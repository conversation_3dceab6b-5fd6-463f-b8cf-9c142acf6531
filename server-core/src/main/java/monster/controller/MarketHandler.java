package monster.controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgShopCry;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.MarketType;
import monster.config.penum.TopType;
import monster.dao.mapping.TopUserEntity;
import monster.dao.mapping.UserMarketEntity;
import monster.dao.mapping.main.MarketDetailEntity;
import monster.dao.mapping.main.MarketEntity;
import monster.dao.mapping.main.ShopNewDetailEntity;
import monster.object.ServiceResult;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.common.MarketService;
import monster.service.common.MaterialService;
import monster.service.monitor.EventMonitor;
import monster.service.monitor.TopMonitor;
import monster.service.resource.SuperMarket;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import javax.persistence.EntityManager;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mail and Friend
 */
public class MarketHandler extends AHandler {

    static final String KEY_DATA = "super_market";
    MaterialService materialService = Guice.getInstance(MaterialService.class);
    MarketService marketService = Guice.getInstance(MarketService.class);
    UserMarketEntity userMarket;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(MARKET_STATUS, SHOP_CRY_BUY, MARKET_STATUS_NEW, MARKET_BUY, MARKET_REFRESH, TOP_PLAYER, MARKET_IAP_VERIFY,
                SHOP_CRY_STATUS, UPDATE_VIP_POINT);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");
        userMarket = marketService.getUserMarket(mUser);
        if (userMarket != null) {
            try {
                switch (actionId) {
                    case MARKET_STATUS_NEW -> marketStatus();
                    case MARKET_BUY -> marketBuy();
                    case MARKET_REFRESH -> refreshMarket();
                    case MARKET_IAP_VERIFY -> verifyIAP();
                    case TOP_PLAYER -> topPlayer();
                    case SHOP_CRY_STATUS -> shopCryStatus();
                    case SHOP_CRY_BUY -> shopCryBuy();
                    case UPDATE_VIP_POINT -> updateVipPoint();
                }
            } catch (Exception ex) {
                Logs.error(ex);
            }
        } else addErrResponse("market error");
    }

    //region Handle service
    private void updateVipPoint() {
        addResponse(UPDATE_VIP_POINT, getCommonVector(user.getVip(), user.getVipExp()));
    }

    private void topPlayer() {
        int type = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        TopType top = TopType.get(type);
        if (top == null) {
            addErrResponse(getLang(Lang.err_ranking_board));
            return;
        }
        Pbmethod.PbListUser pbListUser = TopMonitor.getInstance().get(top, String.valueOf(user.getServer()));
        List<Pbmethod.PbUser> pbUsers = pbListUser.getAUserList();
        Pbmethod.PbUser myProto = pbUsers.stream().filter(pbUser -> pbUser.getId() == user.getId()).findFirst().orElse(null);
        if (myProto != null) {
            int index = pbUsers.indexOf(myProto);
            addResponse(TOP_PLAYER_ME, Pbmethod.PbListUser.newBuilder().setMyInfo(myProto).setMyRank(index + 1).build());
        } else if (!StringHelper.isEmpty(top.sqlMyRank) && !StringHelper.isEmpty(top.sqlMyInfo)) {
            Integer myRank = dbGetRank(String.format(top.sqlMyRank, String.valueOf(user.getServer()), user.getId()));
            TopUserEntity topUser = dbGetInfo(String.format(top.sqlMyInfo, user.getId(), String.valueOf(user.getServer())));
            if (myRank != null && topUser != null) {
                if (myRank == 0) myRank = 9999;
                addResponse(TOP_PLAYER_ME, Pbmethod.PbListUser.newBuilder().setMyInfo(topUser.toProto()).setMyRank(myRank).build());
            } else addDefault();
        } else addDefault();
        addResponse(pbListUser);
    }

    private void addDefault() {
        addResponse(TOP_PLAYER_ME, Pbmethod.PbListUser.newBuilder().setMyInfo(user.protoTinyUser()).setMyRank(9999).build());
    }

    private void verifyIAP() {
        //        List<String> aStr = CommonProto.parseCommonVector(requestData).getAStringList();
        //        String os = aStr.get(0).toLowerCase().equals("ios") ? "0" : "1", token = aStr.get(1), data = aStr.get(2);
        //
        //        String msg = ApiServer.verifyIap(user, token, os, data);
        //        addPopupResponse(msg);
        //        UserEntity user = new UserDAO().getUser(mUser.getUser().getId());
        //        if (user != null && mUser.getUser().getVipExp() != user.getVipExp()) {
        //            mUser.getUser().setGem(user.getGem());
        //            mUser.getUser().setVipExp(user.getVipExp());
        //            addResponse(IAction.UPDATE_BONUS, Pbmethod.CommonVector.newBuilder()
        //                    .addAllALong(Arrays.asList((long) Bonus.BONUS_VIP_EXP, (long) mUser.getUser().getVip(), (long) mUser.getUser().getVipExp(), 0l))
        //                    .addAllALong(Arrays.asList((long) Bonus.BONUS_GEM, user.getGem(), 0l)).build());
        //        }
    }

    private void refreshMarket() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int marketId = aLong.get(0).intValue();
        //        if (marketId == MarketType.HUYET_THACH_TUONG.id) {
        //            marketId = MarketType.SHOP_HT.id;
        //            userMarket.checkShopHTError();
        //        } else if (marketId == MarketType.GUILD.id) {
        //            marketId = MarketType.SHOP_GUILD_NEW.id;
        //        }
        boolean isFree = aLong.get(1) == 1L;
        MarketType market = MarketType.get(marketId);
        if (market.type == MarketType.SHOP_TYPE_STOCK || market.type == MarketType.SHOP_TYPE_REFRESH) {
            MarketEntity marketEntity = SuperMarket.getMarket(marketId);

            long countdown = userMarket.getCountdown(market);
            if (countdown == 0 && isFree) { // free fresh
                if (userMarket.refreshShop(mUser, market, true)) {
                    addResponse(Pbmethod.CommonVector.newBuilder().build());
                } else addErrResponse();
            } else if (countdown != 0 && !isFree) {
                JsonArray price = GsonUtil.parseJsonArray(marketEntity.getPriceReset());
                if (mUser.checkPrice(this, price)) {
                    List<Long> aBonus = Bonus.receiveListItem(mUser, price, "refresh_" + market.key);
                    if (aBonus.isEmpty()) addErrResponse();
                    else if (userMarket.refreshShop(mUser, market, false)) {
                        addResponse(getCommonVector(aBonus));
                    } else {
                        price.set(price.size() - 1, new JsonPrimitive(-price.get(price.size() - 1).getAsInt()));
                        Bonus.receiveListItem(mUser, price, "refresh_" + market.key + "_fail");
                        addErrResponse();
                    }
                }
            } else marketStatus();
        } else addErrResponse();
    }

    private void marketBuy() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int marketId = aLong.get(0).intValue();
        int shopId = aLong.get(1).intValue();
        int number = 1;
        if (aLong.size() > 2) {
            number = aLong.get(2).intValue();
            if (number <= 0) number = 1;
            if (number >= 100) number = 100;
        }
        MarketType market = MarketType.get(marketId);
        if (market.type == MarketType.SHOP_TYPE_UNLIMITED) {
            buyShopUnlimited(market, SuperMarket.getItem(shopId), number);
        } else if (market.type == MarketType.SHOP_TYPE_STOCK || market.type == MarketType.SHOP_TYPE_REFRESH
                || market.type == MarketType.SHOP_TYPE_REFRESH_DAILY || market.type == MarketType.SHOP_TYPE_REFRESH_MONTH || market.type == MarketType.SHOP_TYPE_LABYRINTH_THANBI) {
            buyShopStock(market, shopId, number);
        } else if (market.type == MarketType.SHOP_SEQUENCE) {
            buyShopSequence(market, shopId);
        }

        boolean checkSuccess = false;
        for (int i = 0; i < response.getAActionList().size(); i++) {
            if (response.getAActionList().get(i).getActionId() == MARKET_BUY) checkSuccess = true;
        }
        if (!checkSuccess) return;

        switch (market) {
            case CASINO:
                EventMonitor.getInstance().addDropItem(user.getId(), EventType.BUY_SHOP_CASINO);
                break;
            case ALTAR:
                EventMonitor.getInstance().addDropItem(user.getId(), EventType.BUY_SHOP_ALTAR);
                break;
            case GUILD:
                EventMonitor.getInstance().addDropItem(user.getId(), EventType.BUY_SHOP_CLAN);
                break;
            case ARENA_TRIAL:
                EventMonitor.getInstance().addDropItem(user.getId(), EventType.BUY_SHOP_ARENA);
                break;
            case MARKETPLACE:
                EventMonitor.getInstance().addDropItem(user.getId(), EventType.BUY_MARKETPLACE);
                break;
            case SHOP_GUILD_NEW:
                break;
        }
    }

    private void buyShopUnlimited(MarketType market, MarketDetailEntity detailEntity, int number) {
        if (market.id != detailEntity.getMarketId()) addErrResponse(getLang(Lang.err_params));
        else {
            List<Long> aPrice = new ArrayList<>();
            List<Long> aItem = new ArrayList<>();
            List<Long> price = detailEntity.getShowListPriceBonus();
            List<Long> item = GsonUtil.strToListLong(detailEntity.getItem());
            if (item.get(0).intValue() == Bonus.BONUS_AVATAR || item.get(0).intValue() == Bonus.BONUS_HERO_SKIN || item.get(0).intValue() == Bonus.BONUS_AVATAR_FRAME)
                number = 1;
            for (int i = 0; i < number; i++) {
                aPrice = Bonus.merge(aPrice, price);
                aItem = Bonus.merge(aItem, item);
            }
            ServiceResult<String> serviceResult = materialService.checkAvatarOwner(mUser, aItem);
            if (serviceResult.success) {
                JsonArray arrPrice = GsonUtil.parseJsonArray(aPrice.toString());
                if (mUser.checkPrice(this, arrPrice)) {
                    arrPrice.addAll(GsonUtil.parseJsonArray(aItem.toString()));
                    List<Long> aBonus = Bonus.receiveListItem(mUser, arrPrice, "buy_" + market.key, StringHelper.toDBString(aItem));
                    if (aBonus.isEmpty()) addErrResponse();
                    else {
                        addResponse(getCommonVector(aBonus));
                    }
                }
            } else serviceResult.writeResponse(this);
        }
    }

    private void buyShopSequence(MarketType market, int shopIndex) {
        List<MarketDetailEntity> aItem = userMarket.getShopItem(market);
        MarketDetailEntity detailEntity = aItem.get(shopIndex);
        if (detailEntity == null || detailEntity.getStock() <= 0)
            addErrResponse(getLang(Lang.err_params));
        else {
            List<Long> price = detailEntity.getListPriceBonus();
            String checkMoney = Bonus.checkMoney(mUser, price);
            if (StringHelper.isEmpty(checkMoney)) {
                detailEntity.setStock(detailEntity.getStock() - 1);
                String strValue = new Gson().toJson(aItem);
                if (userMarket.updateShop(market, strValue)) {
                    price.addAll(GsonUtil.strToListLong(detailEntity.getItem()));
                    List<Long> aBonus = Bonus.receiveListItem(mUser, "buy_" + market.key, price);
                    if (aBonus.isEmpty()) {
                        detailEntity.setStock(detailEntity.getStock() + 1);
                        userMarket.updateShop(market, new Gson().toJson(aItem));
                        addErrResponse();
                    } else {
                        addResponse(getCommonVector(aBonus));
                    }
                } else addErrResponse();
            } else addErrResponse(checkMoney);
        }
    }

    private void buyShopStock(MarketType market, int shopIndex, int number) {
        List<MarketDetailEntity> aItem = userMarket.getShopItem(market);
        MarketDetailEntity detailEntity = aItem.get(shopIndex);
        if (detailEntity == null || detailEntity.getStock() <= 0)
            addErrResponse(getLang(Lang.err_params));
        else {
            number = Math.min(detailEntity.getStock(), number);
            ServiceResult<String> serviceResult = materialService.checkAvatarOwner(mUser, GsonUtil.strToListLong(detailEntity.getItem()));
            if (serviceResult.success) {
                List<Long> price = new ArrayList<>();
                for (int i = 0; i < number; i++) {
                    price = Bonus.merge(price, detailEntity.getListPriceBonus());
                }
                String checkMoney = Bonus.checkMoney(mUser, price);
                if (StringHelper.isEmpty(checkMoney)) {
                    detailEntity.setStock(detailEntity.getStock() - number);
                    String strValue = new Gson().toJson(aItem);
                    if (userMarket.updateShop(market, strValue)) {
                        List<Long> aAddItem = new ArrayList<>();
                        for (int i = 0; i < number; i++) {
                            //                            price = Bonus.merge(price, GsonUtil.strToListLong(detailEntity.getItem()));
                            aAddItem = Bonus.merge(aAddItem, GsonUtil.strToListLong(detailEntity.getItem()));
                        }
                        price.addAll(aAddItem);
                        List<Long> aBonus = Bonus.receiveListItem(mUser, "buy_" + market.key, price, StringHelper.toDBString(aAddItem));
                        if (aBonus.isEmpty()) {
                            detailEntity.setStock(detailEntity.getStock() + number);
                            userMarket.updateShop(market, new Gson().toJson(aItem));
                            addErrResponse();
                        } else {
                            addResponse(getCommonVector(aBonus));
                        }
                    } else addErrResponse();
                } else addErrResponse(checkMoney);
            } else serviceResult.writeResponse(this);
        }
    }

    private void marketStatus() {
        int marketId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        //        if (CfgServer.isRealServer() && marketId == MarketType.SHOP_ARENA_TEAM.id) {
        //            addErrResponse(getLang(Lang.user_function_closed));
        //            return;
        //        }
        //        if (marketId == MarketType.HUYET_THACH_TUONG.id) {
        //            marketId = MarketType.SHOP_HT.id;
        //            userMarket.checkShopHTError();
        //        } else if (marketId == MarketType.GUILD.id) {
        //            marketId = MarketType.SHOP_GUILD_NEW.id;
        //        }
        MarketType market = MarketType.get(marketId);
        switch (market.type) {
            case MarketType.SHOP_TYPE_UNLIMITED -> {
                List<MarketDetailEntity> aItem = SuperMarket.getListShopItem(marketId);
                Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
                {
                    builder.addAVector(getCommonVector(marketId, -1, 0));
                }
                aItem.forEach(item -> builder.addAVector(item.toProtoUnlimitedNew()));
                addResponse(MARKET_STATUS_NEW, builder.build());
            }
            case MarketType.SHOP_TYPE_STOCK, MarketType.SHOP_TYPE_REFRESH, MarketType.SHOP_TYPE_REFRESH_DAILY,
                 MarketType.SHOP_TYPE_REFRESH_MONTH,
                 MarketType.SHOP_TYPE_LABYRINTH_THANBI -> {
                MarketEntity marketEntity = SuperMarket.getMarket(marketId);
                if (userMarket.needRefresh(mUser, market)) {
                    userMarket.refreshShop(mUser, market, true);
                }
                List<MarketDetailEntity> aItem = userMarket.getShopItem(market);
                Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
                {
                    builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(marketId).addALong(userMarket.getCountdown(market))
                            .addAllALong(marketEntity.getListPriceReset()).build());
                }
                for (int i = 0; i < aItem.size(); i++) {
                    builder.addAVector(aItem.get(i).toProtoStock(i));
                }
                addResponse(MARKET_STATUS_NEW, builder.build());
            }
            case MarketType.SHOP_SEQUENCE -> {
                MarketEntity marketEntity = SuperMarket.getMarket(marketId);

                List<MarketDetailEntity> aItem = userMarket.getShopItem(market);
                if (aItem.isEmpty()) {
                    aItem = SuperMarket.getListShopItem(marketId);
                    aItem = aItem.stream().filter(item -> item.getTypeHard() == 1).collect(Collectors.toList());
                    userMarket.updateShop(market, new Gson().toJson(aItem));
                } else {
                    MarketDetailEntity tmp = aItem.stream().filter(item -> item.getStock() > 0).findFirst().orElse(null);
                    if (tmp == null) { // outofstock
                        final int typeHard = aItem.get(0).getTypeHard() + 1;
                        List<MarketDetailEntity> allItem = SuperMarket.getListShopItem(marketId);
                        List<MarketDetailEntity> tmpItem = allItem.stream().filter(item -> item.getTypeHard() == typeHard).collect(Collectors.toList());
                        if (!tmpItem.isEmpty()) {
                            aItem = tmpItem;
                            userMarket.updateShop(market, new Gson().toJson(aItem));
                        }
                    }
                }
                Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
                {
                    builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(marketId).addALong(userMarket.getCountdown(market)).addAllALong(marketEntity.getListPriceReset()).build());
                }
                for (int i = 0; i < aItem.size(); i++) {
                    builder.addAVector(aItem.get(i).toProtoStock(i));
                }
                addResponse(MARKET_STATUS_NEW, builder.build());
            }
        }
    }

    private void shopCryStatus() {
        UserMarketEntity.updateUserShopCry(mUser);
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        long timeCountDown = getTimeCountDownShopCry();
        Pbmethod.CommonVector.Builder cmm = Pbmethod.CommonVector.newBuilder();
        int levelShop = mUser.getUData().getLevelShopCry() == 0 ? 1 : mUser.getUData().getLevelShopCry();
        cmm.addALong(levelShop).addALong(timeCountDown);
        cmm.addAString(CfgShopCry.mShopCry.get(levelShop).desc);
        builder.addAVector(cmm);
        List<ShopNewDetailEntity> aItem = userMarket.checkInstanceShopCry(user, (int) timeCountDown, mUser.getUData().getLevelShopCry());
        List<Long> items = new ArrayList<>();
        List<Integer> limit = new ArrayList<>();
        List<Integer> lstId = new ArrayList<>();
        aItem.forEach(item -> {
            items.addAll(GsonUtil.strToListLong(item.getItem()));
            items.addAll(GsonUtil.strToListLong(item.getPrice()));
            limit.add(item.getLimit());
            lstId.add(item.getId());
        });
        builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(items));
        builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(GsonUtil.toListLong(limit)));
        builder.addAVector(Pbmethod.CommonVector.newBuilder().addAllALong(GsonUtil.toListLong(lstId)));
        addResponse(builder.build());
    }

    private void shopCryBuy() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int idItem = Math.toIntExact(aLong.get(0));
        int levelShop = Math.toIntExact(aLong.get(1));
        int number = Math.toIntExact(aLong.get(2));

        // check khi len level shop user mua sai vat pham
        levelShop = levelShop > mUser.getUData().getLevelShopCry() ? mUser.getUData().getLevelShopCry() : levelShop;
        List<ShopNewDetailEntity> aItems = userMarket.checkInstanceShopCry(user, (int) getTimeCountDownShopCry(), levelShop);
        ShopNewDetailEntity detailEntity = userMarket.getShopCryById(user, idItem, (int) getTimeCountDownShopCry(), levelShop);

        if (detailEntity == null || detailEntity.getLimit() <= 0) {
            addErrResponse(getLang(Lang.event_sanji_out_of_stock));
        } else if (number > detailEntity.getLimit()) {
            addErrResponse(getLang(Lang.err_params));
        } else if (detailEntity.getSpecial() != 0) { // check vat pham dac biet
            if (UserMarketEntity.hasBuyItemSpecial(mUser, detailEntity.getSpecial())) {
                List<Long> pricePerNum = GsonUtil.strToListLong(detailEntity.getPrice());
                pricePerNum.set(pricePerNum.size() - 1, pricePerNum.get(pricePerNum.size() - 1) * number);
                JsonArray price = GsonUtil.parseFromListLong(pricePerNum);
                if (mUser.checkPrice(this, price)) {
                    detailEntity.setLimit(detailEntity.getLimit() - number);
                    List<ShopNewDetailEntity> aItemUpdate = userMarket.updateShopNewDetail(aItems, detailEntity);
                    String strValue = new Gson().toJson(aItemUpdate);
                    if (userMarket.updateShopCry(strValue)) {
                        List<Long> itemPerNum = GsonUtil.strToListLong(detailEntity.getItem());
                        itemPerNum.set(itemPerNum.size() - 1, itemPerNum.get(itemPerNum.size() - 1) * number);
                        JsonArray items = GsonUtil.parseFromListLong(itemPerNum);
                        price.addAll(items);
                        List<Long> aBonus = Bonus.receiveListItem(mUser, price, "buy_shop_cry");
                        if (aBonus.isEmpty()) {
                            detailEntity.setLimit(detailEntity.getLimit() + number);
                            userMarket.updateShopCry(new Gson().toJson(aItems));
                        } else {
                            addResponse(getCommonVector(aBonus));
                        }
                    } else addErrResponse();
                }
            } else {
                CfgShopCry.LevelShopCry shop = CfgShopCry.mShopSpecial.get(detailEntity.getSpecial());
                if (shop != null) {
                    addErrResponse(shop.desc);
                } else
                    addErrResponse();
            }
        } else {
            List<Long> pricePerNum = GsonUtil.strToListLong(detailEntity.getPrice());
            pricePerNum.set(pricePerNum.size() - 1, pricePerNum.get(pricePerNum.size() - 1) * number);
            JsonArray price = GsonUtil.parseFromListLong(pricePerNum);
            if (mUser.checkPrice(this, price)) {
                detailEntity.setLimit(detailEntity.getLimit() - number);
                List<ShopNewDetailEntity> aItemUpdate = userMarket.updateShopNewDetail(aItems, detailEntity);
                String strValue = new Gson().toJson(aItemUpdate);
                if (userMarket.updateShopCry(strValue)) {
                    List<Long> itemPerNum = GsonUtil.strToListLong(detailEntity.getItem());
                    itemPerNum.set(itemPerNum.size() - 1, itemPerNum.get(itemPerNum.size() - 1) * number);
                    JsonArray items = GsonUtil.parseFromListLong(itemPerNum);
                    price.addAll(items);
                    List<Long> aBonus = Bonus.receiveListItem(mUser, price, "buy_shop_cry");
                    if (aBonus.isEmpty()) {
                        detailEntity.setLimit(detailEntity.getLimit() + number);
                        userMarket.updateShopCry(new Gson().toJson(aItems));
                    } else {
                        addResponse(getCommonVector(aBonus));
                    }
                } else addErrResponse();
            }

        }
    }


    private long getTimeCountDownShopCry() {
        Calendar ca = Calendar.getInstance();
        int dayOfWeekNow = ca.get(Calendar.DAY_OF_WEEK);
        int hour = ca.get(Calendar.HOUR_OF_DAY);
        int minute = ca.get(Calendar.MINUTE);
        int second = ca.get(Calendar.SECOND);
        long timeRefresh = ((CfgShopCry.config.dayOfWeek * 24 + CfgShopCry.config.hour) * 60 + CfgShopCry.config.minute) * 60;
        long timeNow = ((dayOfWeekNow * 24 + hour) * 60 + minute) * 60 + second;
        if (timeNow <= timeRefresh) {
            return timeRefresh - timeNow;
        } else {
            return timeRefresh + 86400 * 7 - timeNow;
        }

    }
    //endregion

    //    private UserMarketEntity dbGetUserMarket() {
    //        EntityManager session = null;
    //        try {
    //            session = DBJPA.getEntityManager();
    //            List<UserMarketEntity> aMarket = session.createNativeQuery("select * from user_market where user_id=" + user.getId(), UserMarketEntity.class).getResultList();
    //            if (aMarket.isEmpty()) {
    //                UserMarketEntity market = new UserMarketEntity(user.getId());
    //                session.getTransaction().begin();
    //                session.persist(market);
    //                session.getTransaction().commit();
    //                return market;
    //            }
    //            UserMarketEntity market = aMarket.get(0);
    //            if (!StringHelper.isEmpty(market.getShopAltar()) || !StringHelper.isEmpty(market.getShopGuild())
    //                    || !StringHelper.isEmpty(market.getMarketplace()) || !StringHelper.isEmpty(market.getShopCasino())
    //                    || !StringHelper.isEmpty(market.getShopHt())) {
    //                market.updateShop(MarketType.MARKETPLACE, market.getMarketplace());
    //                market.updateShop(MarketType.SHOP_GUILD_NEW, market.getShopGuild());
    //                market.updateShop(MarketType.SHOP_HT, market.getShopHt());
    //                market.updateShop(MarketType.CASINO, market.getShopCasino());
    //                market.updateShop(MarketType.ALTAR, market.getShopAltar());
    //                session.getTransaction().begin();
    //                session.createNativeQuery("update user_market set shop_altar='', shop_guild='', marketplace='', shop_casino='', shop_ht='' where user_id=" + user.getId()).executeUpdate();
    //                session.getTransaction().commit();
    //            }
    //            return aMarket.get(0);
    //        } catch (Exception ex) {
    //            Logs.error(GUtil.exToString(ex));
    //        } finally {
    //            closeSession(session);
    //        }
    //        return null;
    //    }

    private Integer dbGetRank(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List listResult = session.createNativeQuery(sql).getResultList();
            return listResult.isEmpty() ? null : ((BigInteger) listResult.get(0)).intValue();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    private TopUserEntity dbGetInfo(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<TopUserEntity> aUser = session.createNativeQuery(sql, TopUserEntity.class).getResultList();
            return aUser.isEmpty() ? null : aUser.get(0);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }
    //endregion

}
