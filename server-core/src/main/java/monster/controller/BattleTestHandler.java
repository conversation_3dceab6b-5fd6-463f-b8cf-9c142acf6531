package monster.controller;

import grep.helper.Filer;
import grep.log.Logs;
import monster.config.penum.BattleMode;
import monster.config.penum.BattleType;
import monster.dao.mapping.UserHeroEntity;
import monster.dao.mapping.UserPetEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResHeroExclusiveSkill;
import monster.dao.mapping.main.ResHeroExclusiveSkillUpgrade;
import monster.object.BattleTeam;
import monster.protocol.CommonProto;
import monster.service.battle.common.config.TriggerType;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResHero;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

/**
 * Battle Test or replay ???
 */
public class BattleTestHandler extends AHandler {

    @Override
    public AHandler newInstance() {
        return new BattleTestHandler();
    }

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(TEST_BATTLE, TEST_BATTLE_WITH_RELIC);
        actions.forEach(action -> mHandler.put(action, this));
    }

    //
    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        try {
            switch (actionId) {
                case TEST_BATTLE:
                    testBattle(true, requestData);
                    break;
                case TEST_BATTLE_WITH_RELIC:
                    testBattleWithRelic(true, requestData);
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //<editor-fold desc="Handle service">

    /**
     * 91 --> 247
     * [3, 1, 1, 1000, 50, 3000, 100, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1000, 50, 3001, -1, -1, -1, -1, 0]
     * [10, 0, 1, 1000, 50, 3000, 100, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1000, 50, 3000, -1, -1, -1, -1, -1]
     * level: 0
     */
    void testBattleWithRelic(boolean saveBattle, byte[] inputData) {
        Pbmethod.ListCommonVector aVector = CommonProto.parseListCommonVector(inputData);
        HeroInfoEntity[] aHeroInfoTeam1 = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE];
        HeroInfoEntity[] aHeroInfoTeam2 = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE];
        UserPetEntity[] atkPets = new UserPetEntity[1];
        UserPetEntity[] defPets = new UserPetEntity[1];

        IntStream.of(1, 3).forEach(index -> {
            Pbmethod.CommonVector vector = aVector.getAVector(index);
            List<Long> aLong = vector.getALongList();
            for (int i = 0; i < aLong.size(); i += 27) {
                int heroId = aLong.get(i).intValue();
                int pos = aLong.get(i + 1).intValue();
                int star = aLong.get(i + 2).intValue();
                int level = aLong.get(i + 3).intValue();
                int exclusiveLevel = aLong.get(i + 4).intValue();
                UserHeroEntity hero = new UserHeroEntity();
                hero.setLevel(level);
                hero.setExclusiveSkillLevel(exclusiveLevel);
                hero.setStar(star);
                hero.setHeroId(heroId);
                hero.getHeroPoint().clear();

                for (int j = 0; j < 14; j++) {
                    hero.getHeroPoint().setCurrentValue(j, aLong.get(j + i + 5).intValue());
                    hero.getHeroPoint().setBaseValue(j, aLong.get(j + i + 5).intValue());
                }

                List<Integer> skillIndex = new ArrayList<>();
                for (int j = 17; j < 22; j++) {
                    skillIndex.add(aLong.get(j + i + 5).intValue());
                }
                hero.setSkills(skillIndex.toString());
                List<Integer> skillIds = hero.getListSkillIds();
                ResHeroEntity resHero = ResHero.getHero(hero.getHeroId());
                List<SkillEntity> aSkill = resHero.getSkills(hero.getStar(), hero.getLevel(), hero.getIntVoidLevel(), skillIds, hero.getSkinUse());
                if (aSkill.isEmpty()) return;
                aSkill.remove(0);
//                System.out.println("-------aSkill: " + aSkill);
                aSkill.stream().filter(skillEntity -> skillEntity.trigger == TriggerType.IMMEDIATELY.value).forEach(id -> IMath.buffPointSkill(hero.getHeroPoint(), id));
                IMath.calculateExclusiveSkill(hero.getId(), hero.getHeroId(),  hero.getHeroPoint(), hero.getExclusiveSkillLevel());
//                if (hero.getHeroId() == 40)
//                    System.out.println("totalHeroPoint1: " + hero.getPoint().listValues.get(Point.CURRENT_VALUES_INDEX));
//                if (hero.getHeroId() == 40)
//                    System.out.println("calculatedHeroPoint1: " + hero.getPoint().listValues.get(Point.CALCULATED_VALUES_INDEX));
                hero.getPoint().setCurrentValue();
                hero.getPoint().setCalculatedValue();
//                if (hero.getHeroId() == 40)
//                    System.out.println("totalHeroPoint2: " + hero.getPoint().listValues.get(Point.CURRENT_VALUES_INDEX));
//                if (hero.getHeroId() == 40)
//                    System.out.println("calculatedHeroPoint2: " + hero.getPoint().listValues.get(Point.CALCULATED_VALUES_INDEX));

                if (index == 1) {
                    aHeroInfoTeam1[pos] = hero.toHeroInfo(1, pos);
                } else {
                    aHeroInfoTeam2[pos] = hero.toHeroInfo(2, pos);
                }
            }
        });
        IntStream.of(4, 5).forEach(index -> {
            if (aVector.getAVectorList().size() > index) {
                List<Long> aLong = aVector.getAVector(index).getALongList();
                if (aLong.get(0) > 0) {
                    int pet_id = aLong.get(0).intValue();
                    int level_pet = aLong.get(1).intValue();
                    int level_passive1 = aLong.get(2).intValue();
                    int level_passive2 = aLong.get(3).intValue();
                    int level_passive3 = aLong.get(4).intValue();
                    int level_passive4 = aLong.get(5).intValue();
                    UserPetEntity uPet = new UserPetEntity(0, pet_id);
                    uPet.setLevel(level_pet);
                    uPet.setPassive1(level_passive1);
                    uPet.setPassive2(level_passive2);
                    uPet.setPassive3(level_passive3);
                    uPet.setPassive4(level_passive4);
                    if (index == 4) atkPets[0] = uPet;
                    else defPets[0] = uPet;
                }
            }
        });

        BattleTeam atkTeam = BattleTeam.builder().aHero(aHeroInfoTeam1).aPet(atkPets).build();
        BattleTeam defTeam = BattleTeam.builder().aHero(aHeroInfoTeam2).aPet(defPets).build();
        { // atk relics
            List<Long> relicData = aVector.getAVector(aVector.getAVectorList().size() - 2).getALongList();
            for (int index = 0; index < relicData.size(); index += 2) {
                int relicId = relicData.get(index).intValue(), number = relicData.get(index + 1).intValue();
                for (int i = 0; i < number; i++) {
                    atkTeam.getARelics().add(relicId);
                }
            }
        }
        { // def relics
            List<Long> relicData = aVector.getAVector(aVector.getAVectorList().size() - 1).getALongList();
            for (int index = 0; index < relicData.size(); index += 2) {
                int relicId = relicData.get(index).intValue(), number = relicData.get(index + 1).intValue();
                for (int i = 0; i < number; i++) {
                    defTeam.getARelics().add(relicId);
                }
            }
        }

        BattleBuilder battleBuilder = BattleBuilder.builder().setMode(BattleType.MODE_BATTLE_TEST_RELIC, BattleMode.NORMAL)
                .setInfo(0).setTeam(atkTeam, defTeam);
        var battleResult = battleBuilder.battle();
        var proto = battleResult.toProto(null, null, "battle_test");
        Filer.saveFile("logs/testRelic", proto.toString());
        addResponse(TEST_BATTLE_WITH_RELIC, proto.build());
    }

    void testBattle(boolean saveBattle, byte[] inputData) {
        Pbmethod.ListCommonVector aVector = CommonProto.parseListCommonVector(inputData);
        HeroInfoEntity[] heroTeam1 = new HeroInfoEntity[6];
        HeroInfoEntity[] heroTeam2 = new HeroInfoEntity[6];
        UserPetEntity[] pet1 = new UserPetEntity[1];
        UserPetEntity[] pet2 = new UserPetEntity[1];

        IntStream.of(1, 3).forEach(index -> {
            Pbmethod.CommonVector vector = aVector.getAVector(index);
            List<Long> aLong = vector.getALongList();
            for (int i = 0; i < aLong.size(); i += 25) {
                int heroId = aLong.get(i).intValue();
                int pos = aLong.get(i + 1).intValue();
                int star = aLong.get(i + 2).intValue();
                UserHeroEntity hero = new UserHeroEntity();
                hero.setLevel(250);
                hero.setStar(15);
                hero.setHeroId(heroId);
                hero.getHeroPoint().clear();

                for (int j = 0; j < 14; j++) {
                    hero.getHeroPoint().setCurrentValue(j, aLong.get(j + i + 3).intValue());
                    hero.getHeroPoint().setBaseValue(j, aLong.get(j + i + 3).intValue());
                }

                List<Integer> skillIndex = new ArrayList<>();
                for (int j = 17; j < 21; j++) {
                    skillIndex.add(aLong.get(j + i + 3).intValue());
                }
                hero.setSkills(skillIndex.toString());
                List<Integer> skillIds = hero.getListSkillIds();
                ResHeroEntity resHero = ResHero.getHero(hero.getHeroId());
                List<SkillEntity> aSkill = resHero.getSkills(hero.getStar(), hero.getLevel(), hero.getIntVoidLevel(), skillIds, hero.getSkinUse());
                if (aSkill.isEmpty()) return;
                aSkill.remove(0);
//                System.out.println("-------aSkill: " + aSkill);
                aSkill.stream().filter(skillEntity -> skillEntity.trigger != TriggerType.IMMEDIATELY.value).forEach(id -> IMath.buffPointSkill(hero.getHeroPoint(), id));
//                if (hero.getHeroId() == 40)
//                    System.out.println("totalHeroPoint1: " + hero.getPoint().listValues.get(Point.CURRENT_VALUES_INDEX));
//                if (hero.getHeroId() == 40)
//                    System.out.println("calculatedHeroPoint1: " + hero.getPoint().listValues.get(Point.CALCULATED_VALUES_INDEX));
                hero.getPoint().setCurrentValue();
                hero.getPoint().setCalculatedValue();
//                if (hero.getHeroId() == 40)
//                    System.out.println("totalHeroPoint2: " + hero.getPoint().listValues.get(Point.CURRENT_VALUES_INDEX));
//                if (hero.getHeroId() == 40)
//                    System.out.println("calculatedHeroPoint2: " + hero.getPoint().listValues.get(Point.CALCULATED_VALUES_INDEX));

                if (index == 1) {
                    heroTeam1[pos] = hero.toHeroInfo(1, pos);
                    heroTeam1[pos].setHeroInfo(skillIds);
                } else {
                    heroTeam2[pos] = hero.toHeroInfo(2, pos);
                    heroTeam2[pos].setHeroInfo(skillIds);
                }
            }
        });
        IntStream.of(4, 5).forEach(index -> {
            if (aVector.getAVectorList().size() > index) {
                List<Long> aLong = aVector.getAVector(index).getALongList();
                if (aLong.get(0) > 0) {
                    int pet_id = aLong.get(0).intValue();
                    int level_pet = aLong.get(1).intValue();
                    int level_passive1 = aLong.get(2).intValue();
                    int level_passive2 = aLong.get(3).intValue();
                    int level_passive3 = aLong.get(4).intValue();
                    int level_passive4 = aLong.get(5).intValue();
                    UserPetEntity uPet = new UserPetEntity(0, pet_id);
                    uPet.setLevel(level_pet);
                    uPet.setPassive1(level_passive1);
                    uPet.setPassive2(level_passive2);
                    uPet.setPassive3(level_passive3);
                    uPet.setPassive4(level_passive4);
                    if (index == 4) pet1[0] = uPet;
                    else pet2[0] = uPet;
                }
            }
        });
        { // atk relics
            List<Long> relicIds = aVector.getAVector(6).getALongList();
            for (int i = 0; i < relicIds.size(); i += 2) {
                int relicId = relicIds.get(i).intValue(), number = relicIds.get(i + 1).intValue();
            }
        }

        BattleTeam atkTeam = BattleTeam.builder().aHero(heroTeam1).aPet(pet1).build();
        BattleTeam defTeam = BattleTeam.builder().aHero(heroTeam2).aPet(pet2).build();

        BattleBuilder battleBuilder = BattleBuilder.builder().setTeam(atkTeam, defTeam)
                .setMode(BattleType.NONE, BattleMode.NORMAL);
        var battleResult = battleBuilder.battle();
        Pbmethod.PbListBattleResult.Builder proto = battleResult.toProto(null, "test");
        Filer.saveFile("logs/test", proto.toString());
        addResponse(TEST_BATTLE, proto.build());
    }
    //</editor-fold>

    //<editor-fold desc="Logic">
    //</editor-fold>

    //<editor-fold desc="Database">
    //</editor-fold>

}
