package monster.controller;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.ListUtil;
import grep.helper.NumberUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgCampaign;
import monster.config.CfgIdle;
import monster.config.CfgLimitFunction;
import monster.config.CfgUser;
import monster.config.lang.Lang;
import monster.config.penum.*;
import monster.dao.EventDAO;
import monster.dao.mapping.*;
import monster.dao.mapping.logs.UserPassFunction;
import monster.dao.mapping.main.ResCampaignEntity;
import monster.game.adventure.config.ConfigAdventure;
import monster.game.battlehistory.BattleHistory;
import monster.game.battlehistory.entity.BattleHistoryEntity;
import monster.game.herotest.entity.UserHeroTestEntity;
import monster.game.truongevent.service.TruongProEventService;
import monster.game.user.service.UserService;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.object.ModePlay;
import monster.protocol.CommonProto;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.common.CampaignService;
import monster.service.common.IdleService;
import monster.game.system.service.LogService;
import monster.service.monitor.TopMonitor;
import monster.service.resource.ResCampaign;
import monster.service.resource.ResRanking;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import java.util.*;

/**
 * Only campaign here
 */
public class CampaignHandler extends AHandler {

    final String KEY_DATA = "campaign";

    IdleService idleService = Guice.getInstance(IdleService.class);
    CampaignService campaignService = Guice.getInstance(CampaignService.class);
    LogService logService = Guice.getInstance(LogService.class);

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(CAMPAIGN_STATUS, CAMPAIGN_EQUIP_TEAM, CAMPAIGN_GET_AWARDS, CAMPAIGN_GET_LOOTS, CAMPAIGN_TOP, CAMPAIGN_LIST_HISTORY,
                CAMPAIGN_BATTLE, CAMPAIGN_SELECT_MAP_AUTO, CAMPAIGN_QUICK_BONUS_BUY_PACK, CAMPAIGN_QUICK_BONUS_STATUS, CAMPAIGN_GET_QUICK_BONUS, CAMPAIGN_BATTLE_TEST, ATK_VERIFY_MODE_CAMPAIGN,
                CAMPAIGN_SWEEP);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        if (!FunctionType.CAMPAIGN_AFK.isEnable(mUser, this)) return;
        checkTimeMonitor("s");
        try {
            switch (actionId) {
                case CAMPAIGN_STATUS -> status();
                //                case CAMPAIGN_EQUIP_TEAM ->
                case CAMPAIGN_GET_AWARDS -> getAwards();
                case CAMPAIGN_GET_LOOTS -> getLootsOld();
                case CAMPAIGN_BATTLE_TEST -> attackTest();
                case CAMPAIGN_TOP -> top();
                case CAMPAIGN_SELECT_MAP_AUTO -> selectMap();
                case CAMPAIGN_BATTLE -> attack(new ArrayList<>(getInputALong()));
                case CAMPAIGN_LIST_HISTORY -> listHistory();
                case ATK_VERIFY_MODE_CAMPAIGN -> commonVerify(getBattleInputCache(BattleType.MODE_CAMPAIGN));
                case CAMPAIGN_SWEEP -> sweep();
                default -> {
                    if (!FunctionType.CAMPAIGN_QUICK_BONUS.isEnable(mUser, this)) return;
                    switch (actionId) {
                        case CAMPAIGN_QUICK_BONUS_STATUS -> getQuickBonusStatus();
                        case CAMPAIGN_GET_QUICK_BONUS -> getQuickBonus();
                        case CAMPAIGN_QUICK_BONUS_BUY_PACK -> quickBonusBuyPack();
                    }
                }
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void sweep() {
        UserTeamEntity userTeam = UserTeam.getTeam(user.getId(), TeamType.CAMPAIGN);
        if (userTeam == null) {
            addErrResponse("Chưa chọn đội hình AFK");
            return;
        }
        BattleTeam myTeam = userTeam.getBattleTeam();
        if (myTeam == null || !myTeam.isOk()) {
            addErrResponse(Lang.err_team_invalid);
            return;
        }
        List<Long> heroIds = new ArrayList<>();
        List<Integer> heroKeys = new ArrayList<>();
        for (HeroInfoEntity aHero : myTeam.getAHero()) {
            if (aHero != null && aHero.id != 0) {
                heroIds.add(aHero.id);
                if (!(aHero.id < 0 || mUser.getResources().getHero(aHero.id) == null)) {
                    heroKeys.add(aHero.heroId);
                }
            } else heroIds.add(0L);
        }

        // nếu có tướng dùng thử thì thay bằng con khác có lực chiến lớn nhất
        for (int i = 0; i < heroIds.size(); i++) {
            if (heroIds.get(i) != 0) {
                if (heroIds.get(i) < 0 || mUser.getResources().getHero(heroIds.get(i)) == null) {
                    mUser.getResources().heroes.sort(Comparator.comparing(UserHeroEntity::getPower).reversed());
                    for (int index = 0; index < mUser.getResources().heroes.size(); index++) {
                        UserHeroEntity uHero = mUser.getResources().heroes.get(index);
                        if (!heroIds.contains(uHero.getId()) && !heroKeys.contains(uHero.getHeroId())) {
                            heroIds.set(i, uHero.getId());
                            heroKeys.add(uHero.getHeroId());
                            break;
                        }
                    }
                }
            }
        }
        int numberTeam = myTeam.getAPet().length;
        int sizeOfHero = heroIds.size() / numberTeam;
        List<Long> inputHeroIds = new ArrayList<>();
        for (int i = 0; i < numberTeam; i++) {
            List<Long> heroId = heroIds.subList(i * sizeOfHero, (i + 1) * sizeOfHero);
            inputHeroIds.addAll(heroId);
            if (myTeam.getAPet() != null && myTeam.getAPet().length > i && myTeam.getAPet()[i] != null) {
                inputHeroIds.add((long) myTeam.getAPet()[i].getPetId());
            } else inputHeroIds.add(0L);
        }
        attack(inputHeroIds);
    }

    //region Handle service
    protected void processVerify(BattleInputCache input) {
        if (idleService.isIdling(mUser, CfgIdle.GAME_TYPE_CAMPAIGN)) {
            addErrResponse(Lang.idle_processing);
            return;
        }
        int oldLevel = user.getLevel();
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            if (input != null) {
                ResCampaignEntity resCampaign = ResCampaign.getCampaign(campaign.getLevel(), user.getServer());
                if (input.battleResult.isWinAbsolute()) {
                    List<Long> retBonus = new ArrayList<>();
                    if (campaign.getLevel() >= campaign.getLevelBug()) {
                        retBonus = Bonus.receiveListItem(mUser, "campaign_map", resCampaign.getWinBonus());
                        if (retBonus.isEmpty()) {
                            addErrResponse();
                            return;
                        }
                    } else {
                        //                        addErrResponse("Bạn đã nhận phần thưởng này rồi");
                    }
                    switch (campaign.getLevel()) {
                        case 1 -> NewUserActionType.CAMPAIGN_LEVEL_1.log(mUser);
                        case 2 -> NewUserActionType.CAMPAIGN_LEVEL_2.log(mUser);
                    }
                    if (campaign.getLevel() > campaign.getLevelAuto()) {
                        campaign.setLevelAuto(campaign.getLevel());
                    }
                    if (campaign.getLevel() == campaign.getLevelAuto()) {
                        EventType.CAMPAIGN_STAGE.addEvent(mUser, resCampaign.getChapter());
                        campaign.update(Arrays.asList("level", campaign.getLevel() + 1, "level_auto", campaign.getLevel() + 1, "last_time_attack_win", DateTime.getFullDate()));
                        campaign.setLevel(campaign.getLevel() + 1);
                        campaign.setLevelAuto(campaign.getLevelAuto() + 1);
                    }
                    input.addOutput(retBonus);
                    EventType.CAMPAIGN_LEVEL.addEvent(mUser, resCampaign.getChapterId() + 1);
                    Guice.getInstance(UserService.class).updateMaxPower(mUser, BattleType.MODE_CAMPAIGN, input.team.getPower());
                } else if (campaign.getLevel() != campaign.getFirstLooseLevel()) { // cập nhật thông tin lần đầu thua ải
                    UserPowerEntity userPowerEntity = mUser == null ? new UserPowerEntity() : Guice.getInstance(UserService.class).getUserPower(mUser);
                    if (userPowerEntity.getPowerMaxCampaign() <= input.team.getPower()) {
                        campaign.setFirstLoosePower(input.team.getPower());
                        campaign.setFirstLooseLevel(campaign.getLevel());
                        campaign.update("first_loose_power", campaign.getFirstLoosePower(), "first_loose_level", campaign.getLevel());
                    }
                }

                input.addOutput(ListUtil.ofLong(campaign.getLevel()));
                addResponse(input.toProto());

                UserTeam.saveOrUpdateUserTeam(user, TeamType.CAMPAIGN, input.team, input.battleResult.atkPower, input.battleResult.isWin());
                if (user.getLevel() > oldLevel) {
                    Services.userService.afterUserLevelUp(mUser, user.getLevel() - oldLevel, this);
                }

                EventType.CAMPAIGN_ATTACK.addEvent(mUser);
                Actions.save(mUser.getUser(), "campaign", "verify", "win", input.battleResult.isWinAbsolute(), "level", campaign.getLevel(), "bid", input.battleId, "power", input.team.getPower());
                if (input.battleResult.isWinAbsolute()) {
                    ResRanking.addRank(RankType.CAMPAIGN, mUser.getUser(), campaign.getLevel());

                    //                Services.daoCampaign.addTower(user, campaign.getLevel() - 1, myTeam.getHeroInfo(), myTeam.getPetInfo(),
                    //                        battleResult.logInput, battleResult.logBin, myTeam.getListPower(), battleResult.getListTotalRound());
                    if (input.battleResult.isWin())
                        logService.logPassFunction(user.getServer(), BattleType.MODE_CAMPAIGN.value, resCampaign.getId());
                    BattleHistory.checkHistory(user, BattleHistory.getCampaignKey(user.getServer(), campaign.getLevel() - 1), input.battleId, input.data, input.outputBytes);
                }
                input.checkVerifyTime(mUser, BattleType.MODE_CAMPAIGN, campaign.getLevel() - 1);
            }
        }
    }

    void listHistory() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign == null) {
            addErrResponse();
            return;
        }
        List<BattleHistoryEntity> listHistory = BattleHistory.getListHistory(user.getServer(), BattleHistory.getCampaignKey(user.getServer(), campaign.getLevel()));
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        for (BattleHistoryEntity history : listHistory)
            builder.addAVector(history.toProto());
        addResponse(builder.build());
    }

    void attack(List<Long> heroIds) {
        if (idleService.isIdling(mUser, CfgIdle.GAME_TYPE_CAMPAIGN)) {
            addErrResponse(Lang.idle_processing);
            return;
        }
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            if (campaign.getLevel() > CfgLimitFunction.config.limitCampaignLevel) {
                addErrResponse(getLang(Lang.err_max_level));
                return;
            }
            ResCampaignEntity resCampaign = ResCampaign.getCampaign(campaign.getLevel(), user.getServer());
            ModePlay modePlay = resCampaign.getModePlay();
            int numberTeam = modePlay == null ? ModePlayType.CAMPAIGN.numberTeam : modePlay.numberTeam;
            BattleMode battleMode;
            if (numberTeam == 1) battleMode = BattleMode.ENDING;
            else battleMode = BattleMode.NORMAL_ONE;
            campaignService.autoPeakTeam(mUser, heroIds, numberTeam);
            if (heroIds.size() != BattleConfig.TEAM_INPUT * numberTeam) {
                addErrResponse(getLang(Lang.err_params));
                return;
            }
            if (modePlay != null) {
                if (!modePlay.checkHeroOk(heroIds, mUser, this)) {
                    return;
                }
            }
            if (user.getLevel() < resCampaign.getRequireLevel()) {
                addErrResponse(getLang(Lang.user_function_level_required, resCampaign.getRequireLevel()));
                return;
            }
            for (Long heroId : heroIds) {
                if (heroId < 0) {
                    UserHeroTestEntity userHeroTest = mUser.getResources().getHeroTest(heroId);
                    int index = BattleType.listCheckHeroTest.indexOf(BattleType.MODE_CAMPAIGN);
                    List<Long> listUsedNumber = userHeroTest.getUsagePerDay();
                    if (listUsedNumber.get(index) <= 0) {
                        addErrResponse("Bạn đã hết lượt đánh cho tướng dùng thử!");
                        return;
                    }
                }
            }
            BattleTeam myTeam = BattleTeam.getInstanceNew(mUser, heroIds);
            if (myTeam == null) {
                addErrResponse(getLang(Lang.err_hero_duplicate));
                return;
            }
            if (!myTeam.isOk()) {
                addErrResponse(getLang(Lang.err_team_invalid));
                return;
            }
            if (myTeam.getPower() < resCampaign.getMinPower()) {
                addErrResponse(String.format(getLang(Lang.err_power_attack), resCampaign.getMinPower()));
                return;
            }
            UserPassFunction passFunction = logService.getUserPassFunction(user.getServer(), BattleType.MODE_CAMPAIGN.value, resCampaign.getId());
            BattleTeam monsterTeam = resCampaign.getBattleTeam(mUser, campaign, myTeam.getPower(), passFunction.number);
            BattleResultEntityNew battleResult = BattleBuilder.builder().setTeam(myTeam, monsterTeam)
                    .setMode(BattleType.MODE_CAMPAIGN, battleMode).setInfo(user.getId()).battle(mUser);
            String title = Lang.getTitle("title_campaign");
            Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user, title + " " + campaign.getLevel());
            builder.setBackgroundId(resCampaign.getBackground());
            if (modePlay != null && modePlay.modePlay == BattleMode.SUPPER_HERO_SUPPORT.value && !battleResult.isWin()) {
                builder.setBattleMode(modePlay.modePlay);
                List<Long> superId = new ArrayList<>();
                for (int i = 0; i < modePlay.res_monster_support.length; i++) {
                    superId.add(modePlay.res_monster_support[i] * -1L);
                }
                superId.add(0L);// thêm pet
                if (!superId.isEmpty()) {
                    myTeam = BattleTeam.getInstanceNew(mUser, superId);
                    battleResult = BattleBuilder.builder().setTeam(myTeam, resCampaign.getBattleTeam(mUser, campaign, myTeam.getPower(), passFunction.number)).setMode(BattleType.MODE_CAMPAIGN, battleMode).setInfo(user.getId()).battle(mUser);
                    builder.addAllABattle(battleResult.toProto(user, title + campaign.getLevel()).getABattleList());
                    builder.setTeamWin(battleResult.getTeamWin());
                }
            }
            builder.setNextBattle(battleResult.isWin());
            addResponse(CAMPAIGN_BATTLE, builder.build());
            Actions.save(mUser.getUser(), "campaign", "attack", "win", battleResult.isWinAbsolute(), "level", campaign.getLevel(), "bid", builder.getBattleId());
            addBattleInput(BattleInputCache.builder().battleId(builder.getBattleId()).values(heroIds).team(myTeam).battleResult(battleResult).battleType(BattleType.MODE_CAMPAIGN).data(battleResult.isWin() ? builder.build().toByteArray() : null).build());
        }
    }

    void attackTest() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            List<Long> heroIds = new ArrayList<>();
            heroIds.addAll(CommonProto.parseCommonVector(requestData).getALongList());
            long type = heroIds.remove(heroIds.size() - 1);
            ResCampaignEntity resCampaign = (ResCampaignEntity) (DBJPA.getList("select * from dson_main.res_campaign where  id=:level", ResCampaignEntity.class, "level", campaign.getLevel()).get(0));
            ModePlay mode = new Gson().fromJson(resCampaign.getCondition(), ModePlay.class);
            int numberTeam = mode == null ? ModePlayType.CAMPAIGN.numberTeam : mode.numberTeam;
            BattleMode modePlay;
            //modePlay = BattleMode.get(mode.modePlay);
            if (numberTeam == 1) modePlay = BattleMode.ENDING;
            else modePlay = BattleMode.NORMAL_ONE;
            if (campaign.getLevel() != campaign.getLevelAuto() || heroIds.size() != BattleConfig.TEAM_INPUT * numberTeam) {
                addErrResponse(getLang(Lang.err_params));
                return;
            }
            if (mode != null && !mode.checkHeroOk(heroIds, mUser, this)) {
                return;
            }

            if (user.getLevel() < resCampaign.getRequireLevel()) {
                addErrResponse(getLang(Lang.user_function_level_required, resCampaign.getRequireLevel()));
                return;
            }
            BattleTeam myTeam = BattleTeam.getInstanceNew(mUser, heroIds);
            if (myTeam == null) {
                addErrResponse(getLang(Lang.err_hero_duplicate));
                return;
            }
            if (!myTeam.isOk()) {
                addErrResponse(getLang(Lang.err_team_invalid));
                return;
            }
            if (myTeam.getPower() < resCampaign.getMinPower()) {
                addErrResponse(getLang(Lang.err_power_attack));
                return;
            }
            BattleResultEntityNew battleResult = null;//BattleUtil.battleNew(modePlay, myTeam.getAHero(), myTeam.getAPet(), resCampaign.getAMonsterTest(), null, BattleType.MODE_CAMPAIGN, true);
            String title = Lang.getTitle("title_campaign_test");
            Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(user, title + campaign.getLevel());
            if (battleResult.isWinAbsolute() && type == 1) {
                List<Long> retBonus = Bonus.receiveListItem(mUser, "campaign_map", resCampaign.getWinBonus());
                if (retBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }
                if (campaign.getLevel() == campaign.getLevelAuto()) {
                    DBJPA.updateNumber("user_campaign", Arrays.asList("level", 1, "level_auto", 1), Arrays.asList("user_id", user.getId()));
                    campaign.setLevel(campaign.getLevel() + 1);
                    campaign.setLevelAuto(campaign.getLevelAuto() + 1);
                }
                builder.addAllABonus(retBonus);
            }

            builder.setInfo(getCommonVector(campaign.getLevel()));
            addResponse(builder.build());
        }
    }

    void selectMap() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            int mapLevel = campaign.getLevel();
            if (mapLevel < 1 || mapLevel > campaign.getLevel()) {
                addErrResponse(getLang(Lang.err_params));
                return;
            } else if (mapLevel == campaign.getLevelAuto()) {
                addResponse(null);
                return;
            }
            if (DBJPA.update("user_campaign", Arrays.asList("level_auto", mapLevel), Arrays.asList("user_id", user.getId()))) {
                campaign.setLevelAuto(mapLevel);
                addResponse(null);
            } else addErrResponse();
        }
    }

    void getQuickBonus() {
        TruongProEventService truongProEventService = Guice.getInstance(TruongProEventService.class);
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            long timeRemain = CfgCampaign.getTimeRemainPack(campaign);
            if ((campaign.getQuickBonus() == null || DateTime.getDayDiff(campaign.getQuickBonus(), new Date()) > 0)
                    && !campaign.resetQuickBonus(timeRemain > 0)) {
                addErrResponse();
                return;
            }

            int maxBuy = CfgCampaign.getMaxNumberBuy(timeRemain);
            int numberBuy = campaign.getNumberBuyBonus();
            if (numberBuy >= maxBuy) {
                addErrResponse(getLang(Lang.err_max_buy));
                return;
            }
            int oldLevel = user.getLevel();
            boolean isFree = campaign.getQuickBonusFree() > 0;
            if (isFree) {
                FeatureNameType feature = FeatureNameType.CAMPAIGN_FREE;
                if (!feature.hasBuy(user)) {
                    addErrResponse(getLang(Lang.block_buy));
                    return;
                }
                if (campaign.updateQuickBonus()) {
                    List<Long> aBonus = campaign.calculateRewardByTime(mUser, CfgCampaign.config.timeBonus);
                    aBonus.addAll(truongProEventService.redRibbonBonus(mUser, BattleType.MODE_QUICK_BONUS));
                    aBonus.addAll(campaignService.eventTetDropItem(mUser, (int) (CfgCampaign.config.timeBonus * DateTime.HOUR_MILLI_SECOND / 5000)));
                    if (truongProEventService.inEvent304(user.getServer())) {
                        aBonus.addAll(Bonus.viewMaterial(MaterialType.RUONG_30_4, NumberUtil.getRandom(10,15)));
                    }
                    List<Long> retBonus = Bonus.receiveListItem(mUser, feature.value, aBonus);
                    if (retBonus.isEmpty()) {
                        addErrResponse();
                        return;
                    }
                    if (FunctionType.ADVENTURE.isEnable(mUser)) {
                        float hourIdle = CfgCampaign.config.timeBonus;
                        int addAdventurePoint = (int) (hourIdle * ConfigAdventure.cfgAdventure.getPointPerHourIdle());
                        retBonus.addAll(Bonus.receiveListItem(mUser, "adventure_point", Bonus.viewMaterial(MaterialType.ADVENTURE_POINT, addAdventurePoint)));
                        Actions.save(user, "adventure", "receive_idle", "hour_idle", hourIdle, "add_value", addAdventurePoint);
                    }
                    addResponse(CommonProto.getCommonVectorProto(retBonus, null));
                    EventType.QUICK_BONUS_CAMPAIGN.addEvent(mUser);
                } else addErrResponse();
            } else {
                int feeGem = Math.toIntExact(CfgCampaign.getNumberBuyQuickBonus(numberBuy));
                if (user.getGem() < feeGem) {
                    addErrResponse(getLang(Lang.err_not_enough_gem));
                    return;
                }
                if (campaign.updateNumberBuyBonus()) {
                    Actions.save(user, KEY_DATA, "quick_bonus_buy", "number", campaign.getNumberBuyBonus());
                    List<Long> aBonus = new ArrayList<>();
                    aBonus.addAll(Bonus.view(Bonus.BONUS_GEM, -feeGem));
                    aBonus.addAll(campaign.calculateRewardByTime(mUser, CfgCampaign.config.timeBonus));
                    aBonus.addAll(truongProEventService.redRibbonBonus(mUser, BattleType.MODE_QUICK_BONUS));
                    aBonus.addAll(campaignService.eventTetDropItem(mUser, (int) (CfgCampaign.config.timeBonus * DateTime.HOUR_MILLI_SECOND / 5000)));
                    if (truongProEventService.inEvent304(user.getServer())) {
                        aBonus.addAll(Bonus.viewMaterial(MaterialType.RUONG_30_4, NumberUtil.getRandom(10,15)));
                    }
                    List<Long> retBonus = Bonus.receiveListItem(mUser, "campaign_quick_bonus_buy", aBonus);
                    if (retBonus.isEmpty()) {
                        addErrResponse();
                        return;
                    }
                    if (FunctionType.ADVENTURE.isEnable(mUser)) {
                        float hourIdle = CfgCampaign.config.timeBonus;
                        int addAdventurePoint = (int) (hourIdle * ConfigAdventure.cfgAdventure.getPointPerHourIdle());
                        retBonus.addAll(Bonus.receiveListItem(mUser, "adventure_point", Bonus.viewMaterial(MaterialType.ADVENTURE_POINT, addAdventurePoint)));
                        Actions.save(user, "adventure", "receive_idle", "hour_idle", hourIdle, "add_value", addAdventurePoint);
                    }
                    addResponse(CommonProto.getCommonVectorProto(retBonus, null));
                    EventType.QUICK_BONUS_CAMPAIGN.addEvent(mUser);
                } else addErrResponse();
            }
            if (user.getLevel() != oldLevel) {
                Services.userService.afterUserLevelUp(mUser, user.getLevel() - oldLevel, this);
            }
        } else addErrResponse();
    }

    void quickBonusBuyPack() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign == null) {
            addErrResponse(getLang(Lang.err_system_down));
            return;
        }
        long timeRemain = CfgCampaign.getTimeRemainPack(campaign);
        if (timeRemain > 0) {
            addErrParams();
            return;
        }
        List<Long> fee = CfgCampaign.feeBuyPack();
        String err = Bonus.checkMoney(mUser, fee);
        if (err != null) {
            addErrResponse(err);
            return;
        }
        fee.addAll(CfgCampaign.config.toItems);
        if (campaign.buyPackQuickBonus()) {
            addResponse(getCommonVector(Bonus.receiveListItem(mUser, "buy_pack_campaign", fee)));
            getQuickBonusStatus();
            // log thu thập nhanh
            Guice.getInstance(EventDAO.class).save(LogEventBuyPack.builder().createdOn(new Date()).eventId(1).userId(mUser.getUser().getId()).number(1)
                    .serverId(mUser.getUser().getServer()).item(StringHelper.toDBString(CfgCampaign.config.toItems)).vipExp(CfgCampaign.config.fromItems.get(1).intValue()).message("")
                    .build());
        } else {
            addErrResponse(getLang(Lang.err_system_down));
            Bonus.receiveListItem(mUser, "buy_pack_campaign_err", new ArrayList<>(CfgCampaign.config.fromItems));
        }

    }

    void getQuickBonusStatus() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            long timeRemain = CfgCampaign.getTimeRemainPack(campaign);
            if (DateTime.getDayDiff(campaign.getQuickBonus(), new Date()) > 0 && !campaign.resetQuickBonus(timeRemain > 0)) {
                addErrResponse();
                return;
            }

            int numberBuy = campaign.getNumberBuyBonus();
            int maxBuy = CfgCampaign.getMaxNumberBuy(timeRemain);
            Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
            builder.addAVector(getCommonVector(numberBuy, maxBuy, timeRemain, campaign.getQuickBonusFree()));
            builder.addAVector(getCommonVector(CfgCampaign.config.costBuy));
            builder.addAVector(getCommonVector(CfgCampaign.config.fromItems.get(1))); // client đang ko lấy theo bonus nên phỉa sửa như này
            builder.addAVector(getCommonVector(CfgCampaign.config.toItems));
            addResponse(CAMPAIGN_QUICK_BONUS_STATUS, builder.build());
        } else addErrResponse();
    }

    List<Long> getLoots() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            List<Long> aLoot = campaign.getLoots(user.getVip());
            if (campaign.resetLoots()) {
                return Bonus.receiveListItem(mUser, "campaign_loots", aLoot);
            }
        }
        return new ArrayList<>();
    }

    void getLootsOld() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            List<Long> aLoot = campaign.getLoots(user.getVip());
            if (aLoot.isEmpty()) addErrResponse(getLang(Lang.no_reward_yet));
            else if (campaign.resetLoots()) {
                List<Long> retBonus = Bonus.receiveListItem(mUser, "campaign_loots", aLoot);
                addResponse(CAMPAIGN_GET_LOOTS, CommonProto.getCommonVectorProto(retBonus, null));
            } else addErrResponse();
        }
    }

    void getAwards() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            if (campaign.getGold() == 0) return;
            ResCampaignEntity resCampaign = ResCampaign.getCampaign(campaign.getLevel(), user.getServer());
            int oldLevel = user.getLevel();
            int vipPercent = CfgUser.getVipAutoCampaign(user.getVip());
            List<Long> aBonus = new ArrayList<>();
            aBonus.addAll(Bonus.view(Bonus.BONUS_GOLD, (int) (campaign.getGold() + campaign.getGold() * vipPercent / 100)));
            aBonus.addAll(Bonus.viewMaterial(MaterialType.SPIRIT, (int) (campaign.getSpirit() + campaign.getSpirit() * vipPercent / 100)));
            aBonus.addAll(Bonus.view(Bonus.BONUS_EXP, (int) (campaign.getExp())));
            //            aBonus.addAll(campaignService.eventDropItem(mUser, (int) (campaign.getGold() / resCampaign.getGold())));
            aBonus.addAll(campaignService.eventTetDropItem(mUser, (int) (campaign.getGold() / resCampaign.getGold())));
            float hourIdle = (float) (new Date().getTime() - campaign.getLastTimeReward().getTime()) / DateTime.HOUR_MILLI_SECOND;
            if (campaign.resetAwards()) {
                List<Long> retBonus = Bonus.receiveListItem(mUser, "campaign_awards", aBonus);
                if (retBonus.isEmpty()) {
                    addErrResponse();
                    return;
                }
                if (user.getLevel() > oldLevel) {
                    addResponse(LEVEL_BONUS, getCommonVector(Bonus.receiveListItem(mUser, "level_up", Bonus.viewGem(10 * (user.getLevel() - oldLevel)))));
                    EventType.USER_LEVEL_UP.addEvent(mUser, user.getLevel());
                }
                retBonus.addAll(getLoots());
                if (FunctionType.ADVENTURE.isEnable(mUser)) {
                    int addAdventurePoint = (int) (hourIdle * ConfigAdventure.cfgAdventure.getPointPerHourIdle());
                    retBonus.addAll(Bonus.receiveListItem(mUser, "adventure_point", Bonus.viewMaterial(MaterialType.ADVENTURE_POINT, addAdventurePoint)));
                    Actions.save(user, "adventure", "receive_idle", "hour_idle", hourIdle, "add_value", addAdventurePoint);
                }

                campaign.setLastTimeReward(new Date());
                Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
                builder.addAVector(CommonProto.getCommonVectorProto(retBonus, null));
                addResponse(builder.build());
                getLoots();
                EventType.CAMPAIGN_LOOT.addEvent(mUser);
            } else addErrResponse();
        }
    }

    void status() {
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser, this);
        if (campaign != null) {
            addResponse(campaign.toProto(user.getVip(), user.getServer()));
        }
    }

    void top() {
        addResponse(TopMonitor.getInstance().get(TopType.CAMPAIGN, String.valueOf(user.getServer())));
    }
    //endregion
}
