package monster.controller;

import grep.database.DBJPA;
import grep.helper.Filer;
import grep.helper.GUtil;
import grep.helper.GZip;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.BattleType;
import monster.dao.mapping.BattleLogEntity;
import monster.game.battlehistory.BattleHistory;
import monster.game.battlehistory.entity.BattleHistoryEntity;
import monster.object.BattleInputCache;
import protocol.Pbmethod;

import jakarta.persistence.EntityManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Battle Test or replay ???
 */
public class BattleReplayHandler extends AHandler {


    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(BATTLE_REPLAY, ATK_VERIFY_MODE_REPLAY, BATTLE_HISTORY_REPLAY, NEW_USER_REPLAY);
        actions.forEach(action -> mHandler.put(action, this));
    }

    //
    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);
        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        try {
            switch (actionId) {
                case NEW_USER_REPLAY -> replayNewUser();
                case BATTLE_HISTORY_REPLAY -> replayHistory(getInputInt());
                case BATTLE_REPLAY -> replayBattle(parseCommonInput().getALongList());
                case ATK_VERIFY_MODE_REPLAY -> replayVerifyBattle();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void replayNewUser() {
        try {
            if (mUser.getUData().isNewUserReplayed()) {
                addResponse(Pbmethod.PbListBattleResult.newBuilder().build());
                return;
            }

            if (!mUser.getUData().updateNewUserReplayed()) {
                addErrResponse();
                return;
            }


            long battleId = 21542;
            byte[] data = Filer.readBinFile("battlelogsnotremove/" + battleId);
            if (data == null || data.length < 10) addErrResponse(getLang(Lang.battle_not_found));
            else {
                Pbmethod.PbListBattleResult.Builder builder = Pbmethod.PbListBattleResult.parseFrom(GZip.decompressByte(data)).toBuilder();
                builder.setReplay(true);
                //                    addRawResponse(BATTLE_REPLAY, bui);
                addResponse(builder.build());
                if (mUser != null)
                    addBattleInput(BattleInputCache.builder().values(new ArrayList<>()).battleType(BattleType.MODE_REPLAY).build());
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void replayHistory(int logId) {
        BattleHistoryEntity battleHistory = DBJPA.getUnique("battle_history", BattleHistoryEntity.class, "id", logId);
        if (battleHistory == null) {
            addErrResponse(getLang(Lang.battle_not_found));
            return;
        }

        byte[] data = Filer.readBinFile(BattleHistory.getDataFileName(battleHistory));
        byte[] outputData = Filer.readBinFile(BattleHistory.getOutputDataFileName(battleHistory));
        if (data == null || data.length < 10) {
            addErrResponse(getLang(Lang.battle_not_found));
            return;
        }

        try {
            Pbmethod.PbListBattleResult.Builder builder = Pbmethod.PbListBattleResult.parseFrom(GZip.decompressByte(data)).toBuilder();
            builder.setReplay(true);
            addResponse(builder.build());
            addBattleInput(BattleInputCache.builder().battleType(BattleType.MODE_REPLAY).values(List.of(battleHistory.getBattleId())).outputBytes(GZip.decompressByte(outputData)).build());
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    void replayVerifyBattle() {
        BattleInputCache input = getBattleInputCache(BattleType.MODE_REPLAY);
        if (input != null) {
            if (input.outputBytes != null) addRawResponse(ATK_VERIFY_MODE_REPLAY, input.outputBytes);
            else {
                long battleId = input.values.get(0);
                try {
                    BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
                    if (battleLog == null) addErrResponse(getLang(Lang.battle_not_found));
                    else {
                        byte[] rawOutput = battleLog.getOutputRaw();
                        if (rawOutput != null) addRawResponse(ATK_VERIFY_MODE_REPLAY, GZip.decompressByte(rawOutput));
                        else addResponse(Pbmethod.CommonVector.newBuilder().build());
                    }
                } catch (Exception ex) {
                    Logs.error(GUtil.exToString(ex) + "\nbattleId=" + battleId);
                }
            }
        }
    }

    void replayBattle(List<Long> aLong) {
        long battleId = aLong.get(0);
        BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
        if (battleLog == null) addErrResponse(getLang(Lang.battle_not_found));
        else {
            //            BattleType battleType = BattleType.get(battleLog.getBattleType());
            //            String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
            byte[] data = battleLog.getRawData(); // Filer.readBinFile(battleType.logPath + "/" + tmp + "/" + battleId);
            if (data == null || data.length < 10) addErrResponse(getLang(Lang.battle_not_found));
            else {
                try {
                    Pbmethod.PbListBattleResult.Builder builder = Pbmethod.PbListBattleResult.parseFrom(GZip.decompressByte(data)).toBuilder();
                    builder.setReplay(true);
                    //                    addRawResponse(BATTLE_REPLAY, bui);
                    addResponse(builder.build());
                    if (mUser != null)
                        addBattleInput(BattleInputCache.builder().values(aLong).battleType(BattleType.MODE_REPLAY).build());
                } catch (Exception ex) {
                    Logs.error(ex);
                }
            }
        }
    }

}
