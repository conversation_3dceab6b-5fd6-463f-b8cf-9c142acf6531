package monster.controller.game;

import grep.log.Logs;
import monster.config.CfgWarClan;
import monster.config.lang.Lang;
import monster.config.penum.*;
import monster.controller.AHandler;
import monster.dao.UserDAO;
import monster.dao.WarClanDAO;
import monster.dao.mapping.*;
import monster.object.BattleInputCache;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.protocol.CommonProto;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.monitor.ClanMonitor;
import monster.service.monitor.EventMonitor;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import java.util.*;
import java.util.stream.Collectors;

public class WarClanHandler extends AGame {
    final static String name = "lienminhchien";
    final static String KEY_LOG = "war_clan";
    String KeyWarClanRank = "warclanrank:", KeyWarClanLog = "warclanlog:", KeyBattleLog = "warbattlelog:",
            KeyUserTeam = "warclanteam:", KeyUserListHero = "warclanlisthero:", KeyUserSupport = "warclansup:", KeyClanResults = "warclanres:"; // KeyClanTurnAtk = "warclanturnatk:",
    HashMap<String, WarClanUserEntity> MapUserWarLog = new HashMap<String, WarClanUserEntity>();
    HashMap<String, WarClanLogEntity> MapClanWarLog = new HashMap<String, WarClanLogEntity>();

    List<String> MapAward = new ArrayList<String>();
    long DateGen = -1L;
    long currentWarId = -1;
    WarClanDAO dao = new WarClanDAO();

    public WarClanHandler(Map<String, AGame> mGameHandler, int serverId) {
        super(mGameHandler, serverId);
    }

    @Override
    public void initAction(Map<String, AGame> mGameHandler) {
        List<Integer> actions = Arrays.asList(WAR_CLAN_STATUS, WAR_CLAN_MEMBER, WAR_CLAN_ACTIVITY, WAR_CLAN_CHOOSE_MEMBER,
                WAR_CLAN_ATTACK, WAR_CLAN_REGISTER, WAR_CLAN_GET_TEAM, WAR_CLAN_HISTORY, ATK_VERIFY_WAR_CLAN);
        actions.forEach(action -> mGameHandler.put(getKey(action), this));

        KeyWarClanRank = getKey(KeyWarClanRank);
        KeyWarClanLog = getKey(KeyWarClanLog);
        KeyBattleLog = getKey(KeyBattleLog);
        KeyUserTeam = getKey(KeyUserTeam);
        KeyUserListHero = getKey(KeyUserListHero);
        KeyUserSupport = getKey(KeyUserSupport);
        KeyClanResults = getKey(KeyClanResults);
    }


    public void doAction(AHandler handler) {
        //        if (!CfgServer.isRealServer()) {
        //            handler.addErrResponse(handler.getLang(Lang.war_not_start));
        //            return;
        //        }
        if (!CfgWarClan.isBattleDay() && !CfgWarClan.isPreparedDay() && handler.getActionId() != WAR_CLAN_HISTORY) {
            if (handler.getActionId() == WAR_CLAN_STATUS) {
                handler.addResponse(WAR_CLAN_STATUS, Pbmethod.ListCommonVector.newBuilder().addAVector(handler.getCommonVector(0L)).build());
            } else handler.addErrResponse(handler.getLang(Lang.war_battle_end));
            return;
        }
        int actionId = handler.getActionId();
        MyUser mUser = handler.getMUser();
        byte[] requestData = handler.getRequestData();

        try {
            Calendar ca = Calendar.getInstance();
            switch (actionId) {
                case WAR_CLAN_HISTORY:
                    getHistory(handler, requestData);
                    break;
                default:
                    if ((CfgWarClan.isPreparedDay() && ca.get(Calendar.HOUR_OF_DAY) == CfgWarClan.TimePrepare && ca.get(Calendar.MINUTE) < CfgWarClan.TimePrepareMin)
                            || (!CfgWarClan.isPreparedDay() && ca.get(Calendar.HOUR_OF_DAY) == 23 && ca.get(Calendar.MINUTE) > 55)) {
                        handler.addErrResponse(handler.getLang(Lang.war_in_analysis));
                        return;
                    } else {
                        switch (actionId) {
                            case WAR_CLAN_STATUS -> status(handler, mUser);
                            case WAR_CLAN_REGISTER -> equip(handler, requestData, mUser);// member register team
                            case WAR_CLAN_GET_TEAM -> getTeam(handler, requestData, mUser);// đội hình của người chơi
                            case WAR_CLAN_MEMBER -> getWarClanMember(handler, requestData, mUser);
                            case WAR_CLAN_CHOOSE_MEMBER -> sortMember(handler, requestData, mUser);
                            case WAR_CLAN_ATTACK -> attack(handler, actionId, mUser, requestData);
                            case WAR_CLAN_ACTIVITY -> getWarClanActivity(handler, mUser);
                            case ATK_VERIFY_WAR_CLAN -> handler.getBattleInputCache(BattleType.MODE_WAR_CLAN, true);
                        }
                    }
            }
        } catch (Exception ex) {
            Logs.error(ex);
            handler.addErrResponse();
        }
    }

    /**
     * Lịch sử clan chiến của liên minh
     *
     * @param handler
     * @param requestData
     */
    private void getHistory(AHandler handler, byte[] requestData) {
        int clanId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        List<WarClanHistoryEntity> aHistory = dao.getWarHistory(clanId);
        if (aHistory != null && aHistory.size() > 0) {
            Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
            aHistory.forEach(history -> builder.addAVector(history.toProto(clanId)));
            handler.addResponse(builder.build());
        } else handler.addErrResponse(handler.getLang(Lang.war_no_history));
    }

    private void getTeam(AHandler handler, byte[] requestData, MyUser mUser) {
        int userId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        UserEntity checkUser = new UserDAO().getUser(userId);
        if (checkUser == null) {
            handler.addErrResponse();
            return;
        }
        WarClanUserEntity warUser = getWarUser(checkUser.getId(), checkUser.getClan());
        if (warUser == null) {
            handler.addErrResponse(handler.getLang(Lang.user_not_found));
            return;
        }
        handler.addResponse(WAR_CLAN_GET_TEAM, warUser.toProto(false, 0).build());
    }

    private void equip(AHandler handler, byte[] requestData, MyUser mUser) {
        if (!CfgWarClan.isPreparedDay()) {
            handler.addErrResponse(handler.getLang(Lang.war_end_prepared_time));
            return;
        }
        UserEntity user = mUser.getUser();
        WarClanUserEntity warUser = getWarUser(user.getId(), user.getClan());
        if (warUser == null) {
            handler.addErrResponse(handler.getLang(Lang.war_user_invalid));
            return;
        }
        if (warUser.getTeamStatus() == 1) {
            handler.addErrResponse(handler.getLang(Lang.war_equip_only_one));
            return;
        }

        List<Long> heroIds = CommonProto.parseCommonVector(requestData).getALongList();
        BattleTeam myTeam = BattleTeam.getInstance(mUser, heroIds);
        if (!myTeam.isOk()) {
            handler.addErrResponse(handler.getLang(Lang.err_team_invalid));
            return;
        }

        String newData = myTeam.toString();
        if (warUser.updateTeam(newData)) {
            warUser.setDataHero(newData);
            handler.addResponse(WAR_CLAN_REGISTER, null);
            Actions.save(user, KEY_LOG, "register_team", "heroIds", heroIds.toString());
            handler.addErrResponse(Lang.register_success);
        } else {
            handler.addErrResponse();
        }
    }

    private synchronized void attack(AHandler handler, int actionId, MyUser mUser, byte[] requestData) {
        if (!CfgWarClan.isBattleDay()) {
            handler.addErrResponse(handler.getLang(Lang.war_battle_time_err));
            return;
        }
        try {
            List<Long> aLong = Pbmethod.CommonVector.parseFrom(requestData).getALongList();
            int oppUserId = aLong.get(0).intValue();
            UserEntity user = mUser.getUser();

            WarClanLogEntity myClanLog = getWarClan(user.getClan());
            WarClanUserEntity myWarUser = getWarUser(user.getId(), user.getClan());

            if (myClanLog == null || myWarUser == null || myWarUser.getClanId() != user.getClan() || myWarUser.getStatus() < 1) {
                handler.addErrResponse(handler.getLang(Lang.war_user_invalid));
                return;
            }

            UserDAO uDao = new UserDAO();
            UserEntity oppUser = uDao.getUser(oppUserId);
            if (oppUser == null) {
                handler.addErrResponse(handler.getLang(Lang.war_opp_not_found1));
                return;
            }

            WarClanLogEntity oppClanLog = getWarClan(myClanLog.getClanOpp());
            WarClanUserEntity oppWarUser = getWarUser(oppUserId, myClanLog.getClanOpp());

            if (oppClanLog == null || oppWarUser == null || oppWarUser.getClanId() != myClanLog.getClanOpp()) {
                handler.addErrResponse(String.format(handler.getLang(Lang.war_opp_not_found2), oppUser.getName()));
                return;
            }
            if (myWarUser.getNumberAtk() >= 1) {
                handler.addErrResponse(handler.getLang(Lang.war_number_attack, "1"));
                return;
            }

            // ko dc danh thang minh da danh
            if (myWarUser.getNumberAtk() == 1) {
                String atk1 = myWarUser.getAtk1();
                if (atk1 != null && atk1.length() > 0) {
                    String[] tmp = atk1.split("\\|");
                    String lastAtkName = atk1.substring(0, atk1.length() - tmp[tmp.length - 1].length() - 1);
                    if (oppUser.getName().equals(lastAtkName)) {
                        handler.addErrResponse(handler.getLang(Lang.war_already_atk));
                        return;
                    }
                }
            }

            BattleTeam myHero = myWarUser.getTeam();
            if (aLong.size() > 1) {
                myHero = BattleTeam.getInstance(mUser, aLong.subList(1, aLong.size()));
                if (myHero == null) {
                    handler.addErrResponse(handler.getLang(Lang.err_hero_duplicate));
                    return;
                }
                if (!myHero.isOk()) {
                    handler.addErrResponse(handler.getLang(Lang.err_team_invalid));
                    return;
                }
            }
            BattleTeam oppHero = oppWarUser.getTeam();
            if (oppHero == null) oppHero = UserTeam.getFakeOneTeam();
            BattleResultEntityNew battleResult = BattleBuilder.builder().setMode(BattleType.MODE_WAR_CLAN, BattleMode.NORMAL)
                    .setInfo(user.getId()).setTeam(myHero, oppHero).battle();

            //                    null;//BattleUtil.battle(myHero, oppHero, false).setBattleType(BattleType.WAR_CLAN);
            String title = Lang.getTitle("title_clan_fight");
            //            String title = Lang.instance().isVi() ? "Hạm chiến với " : "In Fleet War with ";
            Pbmethod.PbListBattleResult.Builder builder = battleResult.toProto(mUser.getUser(), oppUser, title + " " + oppUser.getName());
            boolean isWin = battleResult.isWin();
            int percentDie = battleResult.aResult.get(0).getPercentDie(false);
            int flagReceived = isWin ? 3 : 0;
            if (!isWin && percentDie >= 60) flagReceived = 2;
            else if (!isWin && percentDie > 0) flagReceived = 1;
            if (flagReceived > 3) flagReceived = 3;
            if (flagReceived == 3 && battleResult.aResult.get(0).getPercentDie(true) > 0) flagReceived = 2;

            // luu lai vao game Server thay doi
            int flagEarn = 0;
            String mostLost = oppWarUser.getMostLost();
            if (mostLost == null || mostLost.length() == 0) {
                oppWarUser.setMostLost(user.getName() + "|" + flagReceived);
                dao.updateMostLost(oppUserId, oppWarUser.getMostLost());
                flagEarn = flagReceived;
            } else {
                String[] tmp = mostLost.split("\\|");
                int lastFlag = Integer.parseInt(tmp[tmp.length - 1]);
                if (lastFlag < flagReceived) {
                    oppWarUser.setMostLost(user.getName() + "|" + flagReceived);
                    dao.updateMostLost(oppUserId, oppWarUser.getMostLost());
                    flagEarn = flagReceived - lastFlag;
                }
            }

            //            if (flagEarn > 0) {
            myClanLog.addFlag(flagEarn);
            dao.updateClanWarData(myWarUser.getClanId(), CfgWarClan.getWarId(), myClanLog.getFlag(), flagEarn);
            //            }

            if (myWarUser.getNumberAtk() == 0) {
                myWarUser.setAtk1(oppUser.getName() + "|" + flagReceived);
            } else {
                myWarUser.setAtk2(oppUser.getName() + "|" + flagReceived);
            }
            myWarUser.setNumberAtk(myWarUser.getNumberAtk() + 1);
            dao.updateAtk(user.getId(), myWarUser.getAtk1(), myWarUser.getAtk2());
            List<List<Long>> output = new ArrayList<>();
            if (flagReceived > 0) {
                int bonusCoin = CfgWarClan.config.attackCoin[flagReceived - 1];
                output.add(Bonus.receiveListItem(mUser, "warclan_attack", Bonus.viewMaterial(MaterialType.GUILD_COIN, bonusCoin)));
            }
            output.add(List.of((long) flagReceived, (long) myClanLog.getFlag(), (long) flagEarn));
            handler.addResponse(WAR_CLAN_ATTACK, builder.build());

            mUser.getMBattleInput().put(BattleType.MODE_WAR_CLAN, BattleInputCache.builder().battleType(BattleType.MODE_WAR_CLAN).battleResult(battleResult)
                    .output(output).build());

            dao.saveLogAttack(WarClanAttackLogEntity.builder().dateCreated(new Date())
                    .warId(CfgWarClan.getWarId()).atkClanId(myClanLog.getClanId()).defClanId(oppClanLog.getClanId())
                    .atkId(user.getId()).defId(oppUserId)
                    .atkName(user.getName()).defName(oppUser.getName()).atkLevel(user.getLevel()).defLevel(oppUser.getLevel())
                    .atkAvatar(user.getAvatar()).defAvatar(oppUser.getAvatar())
                    .atkFrameId(user.getAvatarFrame()).defFrameId(oppUser.getAvatarFrame())
                    .win(isWin ? 1 : 0).flagReceived(flagReceived).flagEarn(flagEarn)
                    .battleId((int) builder.getBattleId()).build());

            Actions.save(mUser.getUser(), Actions.GGAME, "warclan_flag", "flag", flagReceived, "flagEarn", flagEarn,
                    "oppId", oppUser.getId(), "numberAtk", myWarUser.getNumberAtk());
            EventMonitor.getInstance().addDropItem(user.getId(), EventType.WAR_CLAN_JOIN);
        } catch (Exception ex) {
            Logs.error(ex);
            handler.addErrResponse(handler.getLang(Lang.war_could_not_atk));
        }
    }

    private void status(AHandler handler, MyUser mUser) {
        UserEntity user = mUser.getUser();

        // Trao Thuong
        if (CfgWarClan.isPreparedDay()) {
            if (currentWarId != CfgWarClan.getWarId()) {
                currentWarId = CfgWarClan.getWarId();
                MapAward = new ArrayList<>();
            }
        }

        if (user.getClan() == 0) {
            handler.addErrResponse(handler.getLang(Lang.war_no_clan));
            return;
        }
        WarClanLogEntity log = getWarClan(user.getClan());
        if (log == null) {
            if (CfgWarClan.isPreparedDay() && Calendar.getInstance().get(Calendar.HOUR_OF_DAY) < 10) {
                handler.addErrResponse(handler.getLang(Lang.war_register_time));
            } else {
                handler.addErrResponse(handler.getLang(Lang.war_clan_not_join));
            }

            return;
        }
        WarClanLogEntity logOpp = getWarClan(log.getClanOpp());
        if (logOpp == null && CfgWarClan.isBattleDay()) {
            handler.addErrResponse(handler.getLang(Lang.war_clan_opp_not_join));
            return;
        }

        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(CfgWarClan.isPreparedDay() ? 1L : 2L)
                    .addALong(CfgWarClan.countdown()).addALong(log.getMember()).build());
        }
        {
            builder.addAVector(Pbmethod.CommonVector.newBuilder().addAString(log.getClanName())
                    .addALong(log.getClanId()).addALong(log.getServerId() - 3).addALong(log.getFlag()).addALong(log.getNumberAttack())
                    .addALong(ClanMonitor.getClan(user.getClan()).getAvatar()).build());
        }
        {
            if (logOpp == null && CfgWarClan.isPreparedDay()) {
                builder.addAVector(Pbmethod.CommonVector.newBuilder().addAString("")
                        .addALong(0).addALong(0).addALong(0).addALong(0).addALong(0).build());
            } else {
                builder.addAVector(Pbmethod.CommonVector.newBuilder().addAString(logOpp.getClanName())
                        .addALong(logOpp.getClanId()).addALong(logOpp.getServerId() - 3).addALong(logOpp.getFlag()).addALong(logOpp.getNumberAttack())
                        .addALong(ClanMonitor.getClan(logOpp.getClanId()).getAvatar()).build());
            }
        }
        handler.addResponse(WAR_CLAN_STATUS, builder.build());
    }

    private synchronized void sortMember(AHandler handler, byte[] requestData, MyUser mUser) {
        if (!CfgWarClan.isPreparedDay()) {
            handler.addErrResponse(handler.getLang(Lang.war_prepared_end));
            return;
        }
        if (mUser.getUser().getClanPosition() != ClanPosition.LEADER.value) {
            handler.addErrResponse(handler.getLang(Lang.war_leader_only));
            return;
        }
        WarClanLogEntity log = getWarClan(mUser.getUser().getClan());
        if (log == null) {
            handler.addErrResponse(handler.getLang(Lang.war_clan_not_join));
            return;
        }
        List<Integer> userIds = CommonProto.parseCommonVector(requestData).getALongList()
                .stream().distinct().map(Long::intValue).collect(Collectors.toList());
        List<Integer> aMemberId = log.getAMember().stream().map(WarClanUserEntity::getUserId).collect(Collectors.toList());
        userIds = userIds.stream().filter(value -> aMemberId.contains(value)).distinct().collect(Collectors.toList());

        if (log.getMember() != userIds.size()) {
            handler.addErrResponse(handler.getLang(Lang.war_number_user_invalid));
            return;
        }

        if (dao.sortMember(log.getClanId(), log.getWarId(), userIds)) {
            log.updateMemberStatus(userIds);
            handler.addResponse(null);
            Actions.save(mUser.getUser(), KEY_LOG, "sort", "member", userIds.size(),
                    "ids", userIds.stream().map(value -> String.valueOf(value)).collect(Collectors.joining(",")));
        } else handler.addErrResponse();
    }

    private void getWarClanMember(AHandler handler, byte[] requestData, MyUser mUser) {
        int clanId = (int) CommonProto.parseCommonVector(requestData).getALong(0);
        WarClanLogEntity log = getWarClan(clanId);
        if (log == null) {
            handler.addErrResponse(handler.getLang(Lang.war_clan_not_join));
            return;
        }
        handler.addResponse(WAR_CLAN_MEMBER, log.toProtoAUser(mUser.getUser().getClan() != clanId));

        //        if (CfgWarClan.isPreparedDay() && clanId != mUser.getUser().getClan()) {
        //            handler.addErrResponse(handler.getLang(Lang.err_params));
        //            return;
        //        } else if (CfgWarClan.isBattleDay() && clanId != mUser.getUser().getClan()) {
        //            WarClanLogEntity myLog = getWarClan(mUser.getUser().getClan());
        //            if (myLog == null) {
        //                handler.addErrResponse(handler.getLang(Lang.war_clan_not_join));
        //                return;
        //            }
        //            if (myLog.getClanOpp() != clanId) {
        //                handler.addErrResponse(handler.getLang(Lang.err_params));
        //                return;
        //            }
        //        }
        //        handler.addResponse(WAR_CLAN_MEMBER, log.toProtoAUser(mUser.getUser().getClan() != clanId));
    }

    /**
     * Quá trình tấn công lần này
     *
     * @param handler
     * @param mUser
     */
    public void getWarClanActivity(AHandler handler, MyUser mUser) {
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        UserEntity user = mUser.getUser();
        if (user.getClan() == 0) {
            handler.addErrResponse(handler.getLang(Lang.war_no_clan));
            return;
        }
        List<WarClanAttackLogEntity> aAttack = new WarClanDAO().getWarClanActivity(user.getClan(), CfgWarClan.getWarId());
        if (aAttack == null) {
            handler.addErrResponse(handler.getLang(Lang.err_params));
            return;
        }
        for (int i = 0; i < aAttack.size(); i++) builder.addAVector(aAttack.get(i).toProto());
        handler.addResponse(builder.build());
    }

    public synchronized WarClanUserEntity getWarUser(int userId, int clanId) {
        if (System.currentTimeMillis() - DateGen > (5 * 60 * 1000)) {
            DateGen = System.currentTimeMillis();
            MapUserWarLog = new HashMap<>();
            MapClanWarLog = new HashMap<>();
        }
        String key = DateGen + ":" + userId;
        WarClanUserEntity warUser = MapUserWarLog.get(key);
        if (warUser == null) {
            getWarClan(clanId);
            warUser = MapUserWarLog.get(key);
        }
        return warUser;
    }

    void resetClanInfo(int clanId) {
        if (System.currentTimeMillis() - DateGen > (5 * 60 * 1000)) {
            DateGen = System.currentTimeMillis();
            MapClanWarLog = new HashMap<>();
            MapUserWarLog = new HashMap<>();
        }
        MapClanWarLog.remove(keyClan(clanId));
    }

    private synchronized WarClanLogEntity getWarClan(int clanId) {
        try {
            if (System.currentTimeMillis() - DateGen > (5 * 60 * 1000)) {
                DateGen = System.currentTimeMillis();
                MapClanWarLog = new HashMap<>();
                MapUserWarLog = new HashMap<>();
            }
            String key = keyClan(clanId);
            WarClanLogEntity log = MapClanWarLog.get(key);
            if (log != null) {
                return log;
            }
            log = dao.getClanWarLog(CfgWarClan.getWarId(), clanId);
            if (log == null) {
                return null;
            }
            List<WarClanUserEntity> aMember = dao.getListMember(CfgWarClan.getWarId(), clanId);
            log.setAMember(aMember);
            if (aMember.size() > 0) {
                MapClanWarLog.put(key, log);
                aMember.forEach(member -> MapUserWarLog.put(keyUser(member.getUserId()), member));
            }
            return log;
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }

    private String keyUser(int userId) {
        return String.format("%s:%s", DateGen, userId);
    }

    private String keyClan(int clanId) {
        return String.format("%s:%s", DateGen, clanId);
    }
    //endregion
}