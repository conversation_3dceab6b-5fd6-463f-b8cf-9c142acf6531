package monster.controller;

import grep.database.DBJPA;
import grep.log.Logs;
import monster.config.CfgAlbum;
import monster.config.lang.Lang;
import monster.config.penum.MaterialType;
import monster.controller.logic.AlbumService;
import monster.dao.mapping.UserAlbumEntity;
import monster.dao.mapping.UserMaterialEntity;
import monster.dao.mapping.main.ResAlbumImageEntity;
import monster.object.UserInt;
import monster.protocol.CommonProto;
import monster.service.user.Bonus;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class AlbumHandler extends AHandler {

    public static final String KEY_DATA = "album";
    private UserAlbumEntity image;

    @Override
    public void initAction(Map<Integer, AHandler> mHandler) {
        List<Integer> actions = Arrays.asList(ALBUM_STATUS, ALBUM_GET_FRAGMENT_IMAGE);
        actions.forEach(action -> mHandler.put(action, this));
    }

    @Override
    public AHandler newInstance() {
        return new AlbumHandler();
    }

    @Override
    public void handle(Pbmethod.ResponseData.Builder response, String session, int actionId, byte[] requestData) {
        super.handle(response, session, actionId, requestData);

        if (!validateSession(session)) {
            addResponse(LOGIN_REQUIRE, null);
            return;
        }
        checkTimeMonitor("s");

        try {
            switch (actionId) {
                case ALBUM_STATUS:
                    status();
                    break;
                case ALBUM_GET_FRAGMENT_IMAGE:
                    getFragmentImage();
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    //region Handler service
    void status() {
        checkUnlockImage();
        Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
        {
            UserAlbumEntity image = mUser.getResources().getImage(1, 1);
//            if (image.isFirstInit()) {
//                List<Long> aBonus = Bonus.receiveListItem(mUser, "album_tutorial", Bonus.viewMaterial(MaterialType.ALBUM_CARD, 300));
//                builder.addAVector(Pbmethod.CommonVector.newBuilder().addALong(1).addAllALong(aBonus).build());
//            } else {
            builder.addAVector(getCommonVector(0));
//            }
            image.setFirstInit(false);
        }
        {
            Pbmethod.CommonVector.Builder bonus = Pbmethod.CommonVector.newBuilder();
            for (int i = 0; i < CfgAlbum.bonusIndex.size(); i++) {
                bonus.addALong(CfgAlbum.bonusIndex.get(i)).addALong(CfgAlbum.bonusNumber.get(i));
            }
            builder.addAVector(bonus);
        }
        {
            Pbmethod.CommonVector.Builder finishImage = Pbmethod.CommonVector.newBuilder();
            for (UserAlbumEntity image : mUser.getResources().images) {
                if (image.isFinish()) finishImage.addALong(image.getAlbumId()).addALong(image.getImageId());
            }
            builder.addAVector(finishImage);
        }
        for (UserAlbumEntity image : mUser.getResources().images) {
            builder.addAVector(image.toProto());
        }
        addResponse(builder.build());
    }

    void getFragmentImage() {
        List<Long> aLong = CommonProto.parseCommonVector(requestData).getALongList();
        int albumId = aLong.get(0).intValue();
        int imageId = aLong.get(1).intValue();
        UserAlbumEntity image = mUser.getResources().getImage(albumId, imageId);

        if (image == null) {
            addErrResponse("Chưa mở khóa ảnh này");
            return;
        }

        if (image.isFinish()) {
            addErrResponse("Đã mở ảnh hoàn toàn");
            return;
        }

        UserMaterialEntity userMaterial = mUser.getResources().getMaterial(MaterialType.ALBUM_CARD);
        if (userMaterial.getNumber() < image.getFeeOpenFragment()) {
            addErrResponse(getLang(Lang.err_not_enough_material));
            return;
        }
        int numberOpenFalse = mUser.getUData().getUInt().getValue(UserInt.ALBUM_NUMBER_OPEN_FRAGMENT);
        int index = 0; // mảnh ghép thứ index
        String oldImage = image.getImageData();
        String newImageData = "";
        if (numberOpenFalse == 9) { // force new fragment
            index = oldImage.indexOf("0");
            newImageData = image.getImageData().substring(0, index) + "1" + image.getImageData().substring(index + 1);
        } else {
            index = AlbumService.getRandomFragment(albumId, imageId);
            newImageData = image.getImageData().substring(0, index) + "1" + image.getImageData().substring(index + 1);
        }
        boolean isFinish = AlbumService.isFinish(newImageData);
        int bonusCard = !oldImage.equals(newImageData) ? CfgAlbum.getBonusFragment(AlbumService.numberOpenFragment(newImageData)) : (int) (image.getFeeOpenFragment() * 0.3f);
        int newNumberOpenFalse = !oldImage.equals(newImageData) ? 0 : mUser.getUData().getUInt().getValue(UserInt.ALBUM_NUMBER_OPEN_FRAGMENT) + 1;

        List<Long> bonusData = Bonus.receiveListItem(mUser, KEY_DATA + "_fragment", Bonus.viewMaterial(MaterialType.ALBUM_CARD, -image.getFeeOpenFragment() + bonusCard));
        if (bonusData.isEmpty()) {
            addErrResponse();
            return;
        }

        if (DBJPA.update("user_album", Arrays.asList("image_data", newImageData, "finish", isFinish ? 1 : 0),
                Arrays.asList("user_id", user.getId(), "album_id", albumId, "image_id", imageId))) {
            image.setImageData(newImageData);
            image.setFinish(isFinish);
            List<ResAlbumImageEntity> newUnlocked = isFinish ? AlbumService.getUnlockImage(mUser) : new ArrayList<>();

            // return data
            Pbmethod.ListCommonVector.Builder builder = Pbmethod.ListCommonVector.newBuilder();
            builder.addAVector(Pbmethod.CommonVector.newBuilder()
                    .addALong(albumId).addALong(imageId).addALong(index).addALong(image.getFeeOpenFragment())
                    .build());
            { // unlocked image id
                Pbmethod.CommonVector.Builder unlockInfo = Pbmethod.CommonVector.newBuilder();
                newUnlocked.forEach(unlockedImage -> unlockInfo.addALong(unlockedImage.getAlbumId()).addALong(unlockedImage.getImageId()));
                builder.addAVector(unlockInfo);
            }
            builder.addAVector(getCommonVector(bonusData));
            if (bonusCard == 0) builder.addAVector(Pbmethod.CommonVector.newBuilder().build());
            else builder.addAVector(Pbmethod.CommonVector.newBuilder()
                    .addALong(Bonus.BONUS_MATERIAL).addALong(MaterialType.ALBUM_CARD.type).addALong(MaterialType.ALBUM_CARD.id)
                    .addALong(userMaterial.getNumber()).addALong(bonusCard)
                    .build());
            addResponse(builder.build());

            mUser.getUData().getUInt().setAndUpdateDB(user.getId(), UserInt.ALBUM_NUMBER_OPEN_FRAGMENT, newNumberOpenFalse);
            if (isFinish) {
                mUser.getResources().getMHero().forEach((k, v) -> v.calculatePointHero(mUser));
            }
        } else {
            Bonus.receiveListItem(mUser, KEY_DATA + "_fragment_error", Bonus.viewMaterial(MaterialType.ALBUM_CARD, image.getFeeOpenFragment()));
        }
    }
    //endregion

    //region logic

    /**
     * Đề phòng trong các trường hợp lỗi mở lại các ảnh cho user
     * có lẽ cũng ko nên check thường xuyên
     */
    private void checkUnlockImage() {
        List<UserAlbumEntity> userAlbums = mUser.getResources().images;
        if (userAlbums == null || userAlbums.isEmpty()) { // chưa có ảnh nào, insert default
            UserAlbumEntity uImage = new UserAlbumEntity(user.getId(), 1, 1);
            if (DBJPA.save(uImage)) mUser.getResources().addImage(uImage);
        }
        AlbumService.getUnlockImage(mUser);
    }
    //endregion

    //region Database access
    //endregion
}

