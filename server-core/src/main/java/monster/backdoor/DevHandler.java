package monster.backdoor;

import grep.database.DBJPA;
import grep.log.Logs;
import grep.log.slib_Logger;
import io.undertow.server.HttpHandler;
import io.undertow.server.HttpServerExchange;
import io.undertow.util.Headers;
import monster.config.CfgWarClan;
import monster.util.GameDebug;
import org.slf4j.Logger;

public class DevHandler implements HttpHandler {

    @Override
    public void handleRequest(HttpServerExchange serverExchange) throws Exception {
        serverExchange.getRequestReceiver().receiveFullBytes((exchange, inputByte) -> {
            long curTime = System.currentTimeMillis();
            HttpRequestData requestData = new HttpRequestData(exchange, inputByte);
            String output = "";
            try {
                output = process(requestData);

                exchange.getResponseHeaders().put(Headers.CONTENT_TYPE, "application/json");
                exchange.getResponseSender().send(output);

            } catch (Exception ex) {
                Logs.error(ex);
                output = ex.toString();
            }

            getLogger().info(String.format("%s %s -> %s -> input=<%s>, output=<%s>", System.currentTimeMillis() - curTime, requestData.getIp(), requestData.getUri(), requestData.getInputStr(), output));
        }, (exchange, ex) -> {
            Logs.error(ex);
        });
    }

    private String process(HttpRequestData requestData) {
        String args[] = requestData.getArgs();
        try {
            switch (args[1]) {
                case "album":
                    resetAlbum(requestData, Integer.parseInt(args[2]));
                    break;
                case "war":
                    CfgWarClan.fakeIsPrepared = args[2].equals("attack") ? false : true;
                    requestData.getResult().success(args[2]);
                    break;
                case "logAll":
                    requestData.getResult().success(1, GameDebug.getLogView());
                    break;
                case "logUserDetail":
                    requestData.getResult().success(1, GameDebug.getUserLogView(Integer.parseInt(args[2])));
                    break;
                case "logUserId":
                    requestData.getResult().success(1, GameDebug.listAvailableUser());
                    break;
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return requestData.getResult().toString();
    }

    //region Handler service
    private void resetAlbum(HttpRequestData requestData, int userId) {
        DBJPA.rawSQL("delete from dson.user_album where user_id=" + userId);
        requestData.getResult().error("ok");
    }

    //endregion
    private Logger getLogger() {
        return slib_Logger.access();
    }

}
