package monster.backdoor;

import io.undertow.server.HttpHandler;
import io.undertow.server.HttpServerExchange;
import io.undertow.util.Headers;

public class RoutingHandlers {

    public static HttpHandler plainTextHandler(String value) {
        return new PlainTextHandler(value);
    }

    public static StatusHandler statusHandler() {
        return new StatusHandler();
    }

    public static AdminConfigHandler configHandler() {
        return new AdminConfigHandler();
    }

    public static AdminUserHandler userHandler() {
        return new AdminUserHandler();
    }

    public static RateHandler rateHandler() {
        return new RateHandler();
    }

    public static DevHandler devHandler() {
        return new DevHandler();
    }

    public static HttpGenDataHandler genDataHandler() {
        return new HttpGenDataHandler();
    }

    public static ServerStatusHandler serverStatusHandler() {
        return new ServerStatusHandler();
    }

    public static void notFoundHandler(HttpServerExchange exchange) {
        exchange.setStatusCode(404);
        exchange.getResponseHeaders().put(Headers.CONTENT_TYPE, "text/plain");
        exchange.getResponseSender().send("Page Not Found");
    }
}
