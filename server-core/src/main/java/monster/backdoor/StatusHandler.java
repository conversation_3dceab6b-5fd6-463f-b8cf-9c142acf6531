package monster.backdoor;

import com.google.gson.JsonObject;
import grep.database.DBJPA;
import grep.log.slib_Logger;
import io.undertow.server.HttpHandler;
import io.undertow.server.HttpServerExchange;
import io.undertow.util.Headers;
import monster.cache.memcached.MCache;
import monster.dao.mapping.UserEntity;
import org.slf4j.Logger;

public class StatusHandler implements HttpHandler {

    private int a = 0;

    @Override
    public void handleRequest(HttpServerExchange exchange) throws Exception {
        String output = "";
        HttpRequestData requestData = new HttpRequestData(exchange);
        try {
            output = process(requestData);

            exchange.getResponseHeaders().put(Headers.CONTENT_TYPE, "application/json");
            exchange.getResponseSender().send(output);
        } catch (Exception ex) {
            getLogger().error("handleRequest", ex);
            output = ex.toString();
        }

        getLogger().info(String.format("%s -> %s -> input=<%s>, output=<%s>", requestData.getIp(), requestData.getUri(), requestData.getInputStr(), output));
    }

    private String process(HttpRequestData requestData) {
        String[] args = requestData.getArgs();
        if (args.length <= 1) {
            args = new String[]{"status", "index"};
        }
        try {
            switch (args[1]) {
                case "index":
                    index(requestData);
                    break;
                case "mysql":
                    mysqlStatus(requestData);
                    break;
                case "memcached":
                    if (args.length >= 3 && args[2].equals("reset")) {
                        resetMemcached(requestData);
                    } else memcachedStatus(requestData);
                    break;
            }
        } catch (Exception ex) {
            getLogger().error("process", ex);
        }
        return requestData.getResult().toString();
    }

    //region Handler service
    private void index(HttpRequestData requestData) {
        JsonObject objResult = new JsonObject();
        { // memcached
            String k = "statusMemcached";
            long time = System.currentTimeMillis();

            JsonObject obj = new JsonObject();
            MCache.getInstance().set(k, String.valueOf(System.currentTimeMillis()), 30);
            String v = MCache.getInstance().get(k).toString();
            obj.addProperty("value", v);
            obj.addProperty("time", System.currentTimeMillis() - time);
            objResult.add("memcached", obj);
        }
        { // mysql
            long time = System.currentTimeMillis();

            JsonObject obj = new JsonObject();
            obj.addProperty("value", ((UserEntity) DBJPA.getUnique("user", UserEntity.class, "id", 344888)).getName());
            obj.addProperty("time", System.currentTimeMillis() - time);
            objResult.add("mysql", obj);
        }
        requestData.getResult().success(objResult);
    }

    private void mysqlStatus(HttpRequestData requestData) {
        JsonObject objResult = new JsonObject();
        { // mysql
            long time = System.currentTimeMillis();

            JsonObject obj = new JsonObject();
            obj.addProperty("value", ((UserEntity) DBJPA.getUnique("user", UserEntity.class, "id", 344888)).getName());
            obj.addProperty("time", System.currentTimeMillis() - time);
            objResult.add("mysql", obj);
        }
        requestData.getResult().success(objResult);
    }

    private void memcachedStatus(HttpRequestData requestData) {
        JsonObject objResult = new JsonObject();
        { // memcached
            String k = "statusMemcached";
            long time = System.currentTimeMillis();

            JsonObject obj = new JsonObject();
            MCache.getInstance().set(k, String.valueOf(System.currentTimeMillis()), 30);
            String v = MCache.getInstance().get(k).toString();
            obj.addProperty("value", v);
            obj.addProperty("time", System.currentTimeMillis() - time);
            objResult.add("memcached", obj);
        }
        requestData.getResult().success(objResult);
    }

    private void resetMemcached(HttpRequestData requestData) {
        MCache.getInstance().nextTime = 0;

        memcachedStatus(requestData);
    }

    //endregion

    private Logger getLogger() {
        return slib_Logger.access();
    }

}
