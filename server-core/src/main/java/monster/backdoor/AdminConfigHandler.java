package monster.backdoor;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.Filer;
import grep.helper.GsonUtil;
import grep.log.Logs;
import grep.log.slib_Logger;
import io.undertow.server.HttpHandler;
import io.undertow.server.HttpServerExchange;
import io.undertow.util.Headers;
import monster.backdoor.service.BackdoorService;
import monster.cache.WarClanJob;
import monster.config.*;
import monster.config.penum.BattleType;
import monster.dao.mapping.BattleLogEntity;
import monster.dao.mapping.main.ConfigEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.dao.mapping.main.ResOblivionTowerEntity;
import monster.server.Main;
import monster.server.config.Guice;
import monster.service.Services;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.dependence.IMath;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResHero;
import monster.task.LiveTask;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class AdminConfigHandler implements HttpHandler {

    @Override
    public void handleRequest(HttpServerExchange exchange) throws Exception {
        String output = "";
        HttpRequestData requestData = new HttpRequestData(exchange);
        try {
            output = process(requestData);

            exchange.getResponseHeaders().put(Headers.CONTENT_TYPE, "application/json");
            exchange.getResponseSender().send(output);
        } catch (Exception ex) {
            Logs.error(ex);
            output = ex.toString();
        }

        getLogger().info(String.format("%s -> %s -> input=<%s>, output=<%s>", requestData.getIp(), requestData.getUri(), requestData.getInputStr(), output));
    }

    private String process(HttpRequestData requestData) {
        String[] args = requestData.getArgs();
        try {
            switch (args[1]) {
                case "liveTask":
                    return new LiveTask().doTask(new ArrayList(Arrays.asList(args)).subList(2, args.length));
                case "reloadSysMail":
                    CfgServer.reloadSystemMail();
                    return "Cập nhật thư hệ thống thành công";
                case "reloadSysChat":
                    Services.chatService.reloadSystemChat();
                    return "Cập nhật chat hệ thống thành công";
                case "reloadInvalidChat":
                    CfgChat.aChatInvalid = CfgChat.getList("select k from dson_main.res_chat_invalid");
                    return "Cập nhật server thành công";
                case "reload": // /admin/cfg/reload/config_account
                    if (args.length > 2) return reloadConfig(requestData, args[2]);
                    else Main.initConfig();
                    break;
                case "weight":
                    changeWeight(requestData, args);
                    break;
                case "pointRate":
                    changePointRate(requestData, args);
                    break;
                case "aspen":
                    changeAspenMonster(args);
                    break;
                case "aspenPower":
                    return aspenPower();
                case "towerPower":
                    return towerPower();
                case "heroMax":
                    return Guice.getInstance(BackdoorService.class).resetHeroMax();
                case "battle":
                    return getBattle(requestData, args[2]);
                case "battleoutput":
                    return getBattleOutput(requestData, args[2]);
                case "start": // http://123.30.106.79:6663/admin/cfg/start
                    return startLMC();
                case "attack": // http://123.30.106.79:6663/admin/cfg/attack
                    return attackLMC();
                case "award": // http://123.30.106.79:6663/admin/cfg/award
                    return awardLCM();
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return requestData.getResult().toString();
    }

    //region Handler service

    private String reloadConfig(HttpRequestData requestData, String k) throws Exception {
        ConfigEntity cfg = (ConfigEntity) DBJPA.getUnique(CfgServer.getCfgTable(), ConfigEntity.class, "k", k);
        if (cfg != null) {
            Main.setConfig(cfg.getK(), cfg.getV());
            return String.format("ok %s -> %s", cfg.getK(), cfg.getV());
        }
        return "false config not found";
    }

    private String getBattleOutput(HttpRequestData requestData, String battleId) {
        if (battleId.startsWith("b")) { // b for binary file
            try {
                battleId = battleId.substring(1);
                BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
                String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
                byte[] data = Filer.readBinFile(BattleType.get(battleLog.getBattleType()).logPath + "/" + tmp + "/" + battleId + ".output");
                List<Integer> aInt = new ArrayList<>();
                for (byte value : data) {
                    aInt.add((int) value);
                }
                return aInt.stream().map(v -> String.valueOf(v)).collect(Collectors.joining(","));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "false ^_^";
        } else if (battleId.startsWith("r")) {
            try {
                battleId = battleId.substring(1);
                BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
                String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
                byte[] data = Filer.readBinFile(BattleType.get(battleLog.getBattleType()).logPath + "/" + tmp + "/" + battleId + ".output");

                protocol.Pbmethod.PbListBattleResult builder = protocol.Pbmethod.PbListBattleResult.parseFrom(data);
                return builder.toString();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "false ^_^";
        }
        BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
        String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
        String data = Filer.readFile(BattleType.get(battleLog.getBattleType()).logPath + "/" + tmp + "/" + battleId + ".input");
        return data;
    }

    private String getBattle(HttpRequestData requestData, String battleId) {
        //        System.out.println("battleId = " + battleId);
        if (battleId.startsWith("b")) { // b for binary file
            try {
                battleId = battleId.substring(1);
                BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
                String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
                byte[] data = battleLog.getRawData();// Filer.readBinFile(BattleType.get(battleLog.getBattleType()).logPath + "/" + tmp + "/" + battleId);
                List<Integer> aInt = new ArrayList<>();
                for (byte value : data) {
                    aInt.add((int) value);
                }
                return aInt.stream().map(v -> String.valueOf(v)).collect(Collectors.joining(","));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "false ^_^";
        } else if (battleId.startsWith("r")) {
            try {
                battleId = battleId.substring(1);
                BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
                String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
                byte[] data = Filer.readBinFile(BattleType.get(battleLog.getBattleType()).logPath + "/" + tmp + "/" + battleId);

                protocol.Pbmethod.PbListBattleResult builder = protocol.Pbmethod.PbListBattleResult.parseFrom(data);
                return builder.toString();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "false ^_^";
        }
        BattleLogEntity battleLog = DBJPA.getUnique("battle_log", BattleLogEntity.class, "id", battleId);
        String tmp = DateTime.getDateyyyyMMdd(battleLog.getDateCreated());
        String data = GsonUtil.toJson(battleLog.getInput());//Filer.readFile(BattleType.get(battleLog.getBattleType()).logPath + "/" + tmp + "/" + battleId + ".input");
        return data;
    }

    private String startLMC() {
        CfgWarClan.fakeIsPrepared = true;
        int warId = CfgWarClan.getWarId();
        DBJPA.rawSQL("delete from war_clan_attack_log where war_id=" + warId,
                "delete from war_clan_history where war_id=" + warId,
                "delete from war_clan_log where war_id=" + warId,
                "delete from war_clan_user where war_id=" + warId);
        new WarClanJob().findOpponentTest(warId);
        //        Utils.resetServer();
        return "ok -> " + warId;
    }

    private String attackLMC() {
        CfgWarClan.fakeIsPrepared = false;
        return "ok";
    }

    private String awardLCM() {
        int warId = CfgWarClan.getWarId();
        new WarClanJob().updateWarResult(warId);
        new WarClanJob().updateClanRank();
        return "ok";
    }

    private String index() {
        JsonObject info = new JsonObject();
        info.add("userOnline", UserOnline.getInfo());
        info.add("skill1", new JsonPrimitive(new Gson().toJson(ResHero.getSkill(685))));
        info.add("skill2", new JsonPrimitive(new Gson().toJson(ResHero.getSkill(684))));
        return info.toString();
    }

    private String towerPower() {
        String debug = "";
        int size = CfgOblivion.mTower.size();
        for (int i = 0; i < size; i++) {
            ResOblivionTowerEntity tower = CfgOblivion.getOblivion(i + 1);
            if (tower != null) {
                HeroInfoEntity[] aMonster = tower.getAMonster();
                //            Arrays.stream(aMonster).filter(Objects::nonNull).forEach(monster -> System.out.println(monster.id + " " + monster.point.toString()));
                long power = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> IMath.getPower(monster.point)).sum();
                long atk = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.point.getCurrentAttack()).sum();
                long def = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.point.getCurrentArmor()).sum();
                long hp = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.point.getCurrentHP()).sum();
                debug += String.format("%s %s %s %s %s\n", i + 1, hp, atk, def, power);
            } else debug += String.format("%s null\n", i + 1);
        }
        return debug;
    }

    private String aspenPower() {
        String debug = "";
        List<List<Integer>> monsters = CfgDungeon.config.monster;
        for (int i = 0; i < monsters.size(); i++) {
            ResMonsterEntity[] aMonster = CfgDungeon.getMonster(i + 1);
            long power = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> IMath.getPower(monster.getPoint())).sum();
            long atk = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.getPoint().getCurrentAttack()).sum();
            long def = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.getPoint().getCurrentArmor()).sum();
            long hp = Arrays.stream(aMonster).filter(Objects::nonNull).mapToLong(monster -> monster.getPoint().getCurrentHP()).sum();
            debug += String.format("%s %s %s %s %s\n", i + 1, hp, atk, def, power);
        }
        return debug;
    }

    private void changeAspenMonster(String args[]) {
        JsonArray monster = GsonUtil.parseJsonArray("[" + args[2].replaceAll("]\\[", "],[") + "]");
        JsonArray position = GsonUtil.parseJsonArray("[" + args[2].replaceAll("]\\[", "],[") + "]");
    }

    private void changePointRate(HttpRequestData requestData, String args[]) {
        JsonArray guildBoss = GsonUtil.parseJsonArray("[" + args[2] + "]");
        JsonArray campaign = GsonUtil.parseJsonArray("[" + args[3] + "]");
        JsonArray towerOblivion = GsonUtil.parseJsonArray("[" + args[4] + "]");
        JsonArray island = GsonUtil.parseJsonArray("[" + args[5] + "]");
        JsonArray islandCreep = GsonUtil.parseJsonArray("[" + args[6] + "]");
        JsonArray dungeon = GsonUtil.parseJsonArray("[" + args[7] + "]");
        JsonObject obj = new JsonObject();
        obj.add("guildBoss", guildBoss);
        obj.add("campaign", campaign);
        obj.add("towerOblivion", towerOblivion);
        obj.add("island", island);
        obj.add("islandCreep", islandCreep);
        obj.add("dungeon", dungeon);
        DBJPA.update("dson_main.config", Arrays.asList("v", obj.toString()), Arrays.asList("k", "config_pointRate"));
        CfgPointRate.loadConfig(obj.toString());
        requestData.getResult().success(obj.toString());
    }

    private void changeWeight(HttpRequestData requestData, String args[]) {
        Logs.warn(String.format("change weight = %s", new Gson().toJson(args)));
        IMath.HP_WEIGHT_7 = Integer.parseInt(args[2]);
        IMath.ATTACK_WEIGHT_7 = Integer.parseInt(args[3]);
        IMath.HP_WEIGHT_8 = Integer.parseInt(args[4]);
        IMath.ATTACK_WEIGHT_8 = Integer.parseInt(args[5]);
        IMath.HP_WEIGHT_9 = Integer.parseInt(args[6]);
        IMath.ATTACK_WEIGHT_9 = Integer.parseInt(args[7]);
        String newValue = String.format("{\"hp7\":%s,\"atk7\":%s,\"hp8\":%s,\"atk8\":%s,\"hp9\":%s,\"atk9\":%s}",
                IMath.HP_WEIGHT_7, IMath.ATTACK_WEIGHT_7,
                IMath.HP_WEIGHT_8, IMath.ATTACK_WEIGHT_8,
                IMath.HP_WEIGHT_9, IMath.ATTACK_WEIGHT_9);
        DBJPA.update("dson_main.config", Arrays.asList("v", newValue), Arrays.asList("k", "config_iMath"));
        requestData.getResult().success(String.format("[%s,%s,%s,%s,%s,%s]", IMath.HP_WEIGHT_7, IMath.ATTACK_WEIGHT_7, IMath.HP_WEIGHT_8, IMath.ATTACK_WEIGHT_8, IMath.HP_WEIGHT_9, IMath.ATTACK_WEIGHT_9));
    }

    //endregion
    private Logger getLogger() {
        return slib_Logger.access();
    }

}
