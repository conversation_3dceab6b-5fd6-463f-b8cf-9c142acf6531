package monster.backdoor;

import io.undertow.server.HttpServerExchange;
import lombok.Data;
import monster.protocol.pbentity.TextResult;

@Data
public class HttpRequestData {

    private TextResult result = new TextResult();
    private String uri, ip;
    private String inputStr, outputStr;
    private String args[];

    public HttpRequestData(HttpServerExchange exchange) {
        this.uri = exchange.getRequestURI();
        this.ip = exchange.getSourceAddress().getHostString();
        inputStr = "";
        parseArgs();
    }

    public HttpRequestData(HttpServerExchange exchange, byte[] inputByte) {
        this.uri = exchange.getRequestURI();
        this.ip = exchange.getSourceAddress().getHostString();
        if (inputByte != null && inputByte.length > 0) {
            inputStr = new String(inputByte);
        }
        parseArgs();
    }

    private void parseArgs() {
        if (uri.startsWith("/admin"))
            this.args = uri.substring("/admin/".length()).split("/");
        else
            this.args = uri.substring(1).split("/");
    }
}
