package monster.service.user;

import java.util.ArrayList;
import java.util.List;

import static monster.service.user.Bonus.*;

public class BonusReceive {

    public static List<List<Long>> parse(List<Long> bonus) {
        List<List<Long>> result = new ArrayList<>();
        if (bonus != null && !bonus.isEmpty()) {
            int index = 0;
            while (index < bonus.size()) {
                List<Long> tmp = new ArrayList<>();
                int type = bonus.get(index++).intValue();
                int length = Bonus.mResultLength.containsKey(type) ? Bonus.mResultLength.get(type) - 1 : 0;
                tmp.add((long) type);
                for (int i = index; i < index + length; i++) {
                    tmp.add(bonus.get(i));
                }
                result.add(tmp);
                index += length;
            }
        }
        return result;
    }

    public static List<Long> merge(List<Long> lstBonus) {
        List<List<Long>> ret = new ArrayList<>();
        List<List<Long>> aBonus = parse(lstBonus);
        aBonus.forEach(bonus -> {
            boolean include = false;
            switch (bonus.get(0).intValue()) {
                case BONUS_HERO_SKIN, BONUS_HERO, BONUS_HERO_LEVEL, BONUS_AVATAR, BONUS_AVATAR_FRAME, BONUS_HERO_TEST:
                    ret.add(bonus);
                    break;
                case BONUS_GOLD, BONUS_GEM:
                    for (List<Long> childBonus : ret) {
                        if (childBonus.get(0).longValue() == bonus.get(0).longValue()) { // same type, update lại thông tin sở hữu và thêm tài nguyên
                            childBonus.set(childBonus.size() - 2, bonus.get(bonus.size() - 2));
                            childBonus.set(childBonus.size() - 1, childBonus.get(childBonus.size() - 1) + bonus.get(bonus.size() - 1));
                            include= true;
                            break;
                        }
                    }
                    if (!include) {
                        ret.add(bonus);
                    }
                    break;
                case BONUS_MATERIAL:
                    for (List<Long> childBonus : ret) {
                        if (childBonus.size() == bonus.size()
                                && childBonus.get(1).longValue() == bonus.get(1).longValue()
                                && childBonus.get(2).longValue() == bonus.get(2).longValue()) {
                            // same materialType and same materialId, update lại thông tin sở hữu và thêm tài nguyên
                            childBonus.set(childBonus.size() - 2, bonus.get(bonus.size() - 2));
                            childBonus.set(childBonus.size() - 1, childBonus.get(childBonus.size() - 1) + bonus.get(bonus.size() - 1));
                            include = true;
                            break;
                        }
                    }
                    if (!include) {
                        ret.add(bonus);
                    }
                    break;
                default: {
                    ret.add(bonus);
//                    boolean include = false;
//                    for (List<Long> childBonus : ret) {
//                        if (childBonus.size() == bonus.size()) {
//                            boolean isOk = true;
//                            for (int index = 0; index < bonus.size() - 1; index++) {
//                                if (bonus.get(index).longValue() != childBonus.get(index).longValue()) isOk = false;
//                            }
//                            if (isOk) {
//                                childBonus.set(childBonus.size() - 1, childBonus.get(childBonus.size() - 1) + bonus.get(bonus.size() - 1));
//                                include = true;
//                                break;
//                            }
//                        }
//                    }
//                    if (!include) {
//                        ret.add(bonus);
//                    }
                    break;
                }
            }
        });

        List<Long> results = new ArrayList<>();
        for (List<Long> bonus : ret) results.addAll(bonus);
        return results;
    }

}
