package monster.service.user;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import grep.database.DBJPA;
import monster.config.penum.TeamType;
import monster.dao.UserTeamDAO;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserTeamEntity;
import monster.object.BattleTeam;
import monster.server.config.Guice;
import monster.service.battle.common.entity.HeroInfoEntity;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.concurrent.TimeUnit;

public class UserTeam {

    static UserTeamDAO userTeamDAO = Guice.getInstance(UserTeamDAO.class);

    //region USER TEAM
    static LoadingCache<String, UserTeamEntity> cache = CacheBuilder.newBuilder().maximumSize(10000).expireAfterAccess(1, TimeUnit.DAYS).build(new CacheLoader<String, UserTeamEntity>() {
        @Override
        public UserTeamEntity load(String k) throws Exception {
            String[] keys = k.split("_");
            return DBJPA.getUnique("user_team", UserTeamEntity.class, "user_id", keys[0], "team_id", keys[1]);
        }
    });

    public static BattleTeam getBattleTeam(int userId, TeamType teamType) {
        try {
            UserTeamEntity userTeam = cache.get(getKey(userId, teamType.value));
            if (userTeam != null) return userTeam.getBattleTeam();
        } catch (Exception ex) {
        }
        return null;
    }

    public static UserTeamEntity getTeam(int userId, TeamType teamType) {
        try {
            return cache.get(getKey(userId, teamType.value));
        } catch (Exception ex) {
        }
        return null;
    }

    public static void clearTeamCache(int userId, TeamType teamType) {
        try {
            cache.invalidate(getKey(userId, teamType.value));
        } catch (Exception ex) {
        }
    }

    public static boolean saveOrUpdateUserTeam(UserEntity user, TeamType teamType, BattleTeam team, long realPower) {
        return saveOrUpdateUserTeam(user, teamType, team, realPower, false);
    }

    public static boolean saveOrUpdateUserTeam(UserEntity user, TeamType teamType, BattleTeam team, long realPower, boolean updateLastWinPower) {
        if (teamType.save) {
            HeroInfoEntity[] aHero = team.getAHero();
            UserTeamEntity uTeam = new UserTeamEntity(user.getId(), teamType.value, user.getServer());
            JSONArray arr = new JSONArray();
            for (HeroInfoEntity hero : aHero) {
                JSONObject obj = new JSONObject();
                if (hero != null) {
                    obj.put("id", hero.heroId);
                    obj.put("level", hero.level);
                    obj.put("star", hero.star);
                }
                arr.add(obj);
            }
            uTeam.setInfo(arr.toString());

            long power = team.getPower();
            uTeam.setPower((int) power);
            uTeam.setData(new Gson().toJson(team));
            if (updateLastWinPower) uTeam.setLastWinPower((int) power);
            DBJPA.saveOrUpdate(uTeam);

            cache.put(getKey(uTeam.getUserId(), uTeam.getTeamId()), uTeam);

            if (teamType == TeamType.ARENA_CRYSTAL_ATK) {
                //                if (realPower > user.getRealPower()) {
                //                    DBJPA.rawSQL(String.format("update user set real_power=if(real_power<%s,%s,real_power) where id=%s", realPower, realPower, user.getId()));
                //                    user.setRealPower(realPower);
                //                }
                //                power = Stream.of(team.getAHero()).filter(hero -> hero != null).mapToLong(hero -> IMath.getPower(hero.point)).sum();
                //                if (power > user.getPower()) {
                //                    DBJPA.rawSQL(String.format("update user set power=if(power<%s,%s,power) where id=%s", power, power, user.getId()));
                //                    user.setPower(power);
                //                }
            } else if (teamType == TeamType.ARENA_SWAP_DEF) {

            }
            return true;
        }
        return true;
    }

    static String getKey(int userId, int teamId) {
        return String.format("%s_%s", userId, teamId);
    }

    public static long getSize() {
        return cache.size();
    }

    public static BattleTeam getFakeOneTeam() {
        String data = "{\"aHero\":[{\"id\":10619855,\"heroId\":171,\"team\":1,\"position\":0,\"level\":1,\"star\":4,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15002,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":2,\"type\":\"hurt\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":1083,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":24,\"rand\":4,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":2,\"type\":\"hurt\",\"num\":1.21,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"stun\",\"num\":0.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":0.3}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[113,53,380,175,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[113,53,380,175,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"clazz\":\"CLASS_MAGE\",\"faction\":\"FACTION_WATER\"},{\"id\":211,\"heroId\":182,\"team\":1,\"position\":1,\"level\":1,\"star\":4,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15001,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":1,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":1049,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":13,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":3,\"type\":\"hurt\",\"num\":1.44,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]},{\"obj\":4,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":0,\"type\":\"heal\",\"num\":3.6,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[100,53,460,168,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[100,53,460,168,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"clazz\":\"CLASS_PRIEST\",\"faction\":\"FACTION_FIRE\"},{\"id\":10638932,\"heroId\":405,\"team\":1,\"position\":2,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15001,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":1,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":1105,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":24,\"rand\":3,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":6,\"type\":\"hurt\",\"num\":1.06,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":3,\"pointIndex\":0,\"count\":0,\"type\":\"dotFire\",\"num\":0.75,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"armP\",\"num\":-0.1,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]},{\"obj\":1,\"rand\":0,\"listEffect\":[{\"round\":3,\"pointIndex\":0,\"count\":0,\"type\":\"dotFireB\",\"num\":0.2,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[187,58,2442,206,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[187,58,2442,206,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"clazz\":\"CLASS_MAGE\",\"faction\":\"FACTION_FIRE\"},null,null,null],\"aPet\":[null],\"numberWin\":0}";
        return new Gson().fromJson(data, BattleTeam.class);
    }

    public static BattleTeam getFakeTwoTeam() {
        String data = "{\"aHero\":[null,null,null,null,null,null,{\"id\":23280868,\"heroId\":92,\"team\":1,\"position\":0,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15001,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":1,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":1098,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":24,\"rand\":3,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":6,\"type\":\"hurt\",\"num\":1.28,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":3,\"pointIndex\":0,\"count\":0,\"type\":\"dotBlood\",\"num\":0.2,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[154,42,2114,165,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[154,42,2114,165,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.03,1.0],\"isBoss\":false,\"clazz\":\"CLASS_RANGER\",\"faction\":\"FACTION_LUNISOLAR\"},null,{\"id\":23288654,\"heroId\":150,\"team\":1,\"position\":2,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15005,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":5,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":897,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":12,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":9,\"type\":\"hurt\",\"num\":0.8,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"zsForbid\",\"num\":0.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":0.45}]},{\"obj\":1,\"rand\":0,\"listEffect\":[{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"atkP\",\"num\":0.1,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"decDmg\",\"num\":100.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[185,53,2348,180,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[185,53,2348,180,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.03,1.0],\"isBoss\":false,\"clazz\":\"CLASS_RANGER\",\"faction\":\"FACTION_WATER\"},{\"id\":23280009,\"heroId\":85,\"team\":1,\"position\":3,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15002,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":2,\"type\":\"hurt\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":875,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":13,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":4,\"type\":\"hurt\",\"num\":1.5,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]},{\"obj\":1,\"rand\":0,\"listEffect\":[{\"round\":3,\"pointIndex\":0,\"count\":0,\"type\":\"crit\",\"num\":300.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[167,38,2052,155,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[167,38,2052,155,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"clazz\":\"CLASS_ASSASSIN\",\"faction\":\"FACTION_WATER\"},{\"id\":23283455,\"heroId\":87,\"team\":1,\"position\":4,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15001,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":1,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":920,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":12,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":4,\"type\":\"hurt\",\"num\":0.96,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"msStun\",\"num\":0.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":0.5}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[150,44,1898,151,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[150,44,1898,151,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"clazz\":\"CLASS_MAGE\",\"faction\":\"FACTION_WATER\"},{\"id\":19894670,\"heroId\":128,\"team\":1,\"position\":5,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15003,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":3,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":877,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":18,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":5,\"type\":\"hurt\",\"num\":1.25,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"armP\",\"num\":-0.15,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]},{\"obj\":1,\"rand\":0,\"listEffect\":[{\"round\":6,\"pointIndex\":0,\"count\":0,\"type\":\"hot\",\"num\":3.2,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":2,\"pointIndex\":0,\"count\":0,\"type\":\"siphonArm\",\"num\":0.15,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"number\":120,\"values\":[137,46,2309,148,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[137,46,2309,148,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"clazz\":\"CLASS_WARRIOR\",\"faction\":\"FACTION_WIND\"}],\"aPet\":[{\"userId\":626978,\"petId\":6,\"level\":37,\"tier\":2,\"passive1\":15,\"passive2\":5,\"passive3\":0,\"passive4\":0,\"rune1\":0,\"rune2\":0,\"rune3\":0},null],\"aRelics\":[],\"numberWin\":0}";
        return new Gson().fromJson(data, BattleTeam.class);
    }

    public static BattleTeam getFakeThreeTeam() {
        String data = "{\"aHero\":[{\"id\":97064790,\"heroId\":336,\"team\":1,\"position\":0,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0," +
                "\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15004,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":4,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":100108,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":37,\"rand\":3,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":5,\"type\":\"hurt\",\"num\":1.3,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":0,\"pointIndex\":0,\"count\":0,\"type\":\"energy\",\"num\":-25.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"values\":[193,54,2376,211,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[193,54,2376,211,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"bossType\":0,\"clazz\":\"CLASS_PRIEST\",\"faction\":\"FACTION_WIND\"},null,null,null,null,null,{\"id\":97064791,\"heroId\":336,\"team\":1,\"position\":0,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15004,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":4,\"type\":\"danhthuong\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":100108,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":37,\"rand\":3,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":5,\"type\":\"hurt\",\"num\":1.3,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":0,\"pointIndex\":0,\"count\":0,\"type\":\"energy\",\"num\":-25.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"values\":[193,54,2376,211,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[193,54,2376,211,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"bossType\":0,\"clazz\":\"CLASS_PRIEST\",\"faction\":\"FACTION_WIND\"},null,null,null,null,null,{\"id\":65109650,\"heroId\":328,\"team\":1,\"position\":0,\"level\":1,\"star\":5,\"tier\":0,\"skin\":0,\"levelCalculate\":1,\"voidLevel\":0,\"normalSkill\":{\"id\":15002,\"animation\":99,\"trigger\":1,\"effects\":[{\"obj\":11,\"rand\":0,\"listEffect\":[{\"round\":0,\"pointIndex\":0,\"count\":2,\"type\":\"hurt\",\"num\":1.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"activeSkill\":{\"id\":70069,\"animation\":0,\"trigger\":1,\"effects\":[{\"obj\":24,\"rand\":3,\"listEffect\":[{\"round\":1,\"pointIndex\":0,\"count\":5,\"type\":\"hurt\",\"num\":0.5,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":3,\"pointIndex\":0,\"count\":0,\"type\":\"dotFire\",\"num\":1.1,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":1.0},{\"round\":1,\"pointIndex\":0,\"count\":0,\"type\":\"energyEndRound\",\"num\":-50.0,\"num1\":0.0,\"num2\":0.0,\"num3\":0.0,\"ratio\":0.3}]}],\"power\":0,\"powerBonus\":0,\"isArtifactSkill\":false},\"passiveSkills\":[],\"gems\":[0,0,0,0,0,0],\"type\":0,\"point\":{\"startHp\":-1,\"percentHp\":-1,\"maxHp\":-1,\"cacheAnger\":-1,\"startHpPercent\":-1.0,\"disArm\":false,\"values\":[199,52,2586,196,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"baseValues\":[199,52,2586,196,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},\"addPercent\":[1.0,1.0,1.0,1.0],\"isBoss\":false,\"bossType\":0,\"clazz\":\"CLASS_RANGER\",\"faction\":\"FACTION_FIRE\"},null,null,null,null,null],\"aPet\":[null,null,null],\"numberWin\":0}";
        return new Gson().fromJson(data, BattleTeam.class);
    }


    public static UserTeamEntity getFirstTeam() {
        return userTeamDAO.getFirstUserTeam(TeamType.CAMPAIGN);
    }

    public static void main(String[] args) {
        //        System.out.println(getFakeOneTeam().getAHero().length);
        //        for (HeroInfoEntity aHero : getFakeOneTeam().getAHero()) {
        //            System.out.println(new Gson().toJson(aHero));
        //            if (aHero != null) {
        //                System.out.println(aHero.point.getValues().length);
        //            }
        //        }
    }
    //endregion
}
