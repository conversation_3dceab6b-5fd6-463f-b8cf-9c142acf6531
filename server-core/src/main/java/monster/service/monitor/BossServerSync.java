package monster.service.monitor;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.log.Logs;
import lombok.Data;
import lombok.NonNull;
import monster.config.CfgBossServer;
import monster.config.CfgServer;
import monster.dao.mapping.BossServerEntity;
import monster.dao.mapping.BossServerStatisticEntity;
import monster.server.AppConfig;
import monster.service.user.Actions;

import jakarta.persistence.EntityManager;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BossServerSync {
    @NonNull
    private int server;
    private BossServerEntity aBossServer;
    private Map<Date, BossServerStatisticEntity> mStatistic = new HashMap<>();

    public synchronized BossServerEntity getABossServer(int server) {
        if (server == 0) return null;
        EntityManager session = null;
        //        if (CfgServer.isTestServer() && !AppConfig.isServerRealTest()) aBossServer = null;
        if (CfgServer.isTestServer() || AppConfig.isServerRealTest()) aBossServer = null;
        if (aBossServer == null) {
            try {
                session = DBJPA.getEntityManager();
                List<BossServerEntity> listBoss = session.createNativeQuery("select * from dson.boss_server where server_id=" + server, BossServerEntity.class).getResultList();
                if (!listBoss.isEmpty()) aBossServer = listBoss.get(0);
                else {
                    session.getTransaction().begin();
                    BossServerEntity serverBoss = new BossServerEntity(server);
                    //                    //Tính power của các boss nếu chưa có
                    //                    for (int i = 0; i < CfgBossServer.config.numberBoss; i++) {
                    //                        if (serverBoss.getListBosses().get(i).getPower() == 0) {
                    //                            long power = Arrays.stream(serverBoss.getDataMonsters().get(i)).filter(hero -> hero != null).mapToLong(hero -> IMath.getPower(hero.point)).sum();
                    //                            session.createNativeQuery("update dson_main.res_boss_server set power=" + power + " where id=" + serverBoss.getListBosses().get(i).getId()).executeUpdate();
                    //                        }
                    //                    }
                    session.persist(serverBoss);
                    session.getTransaction().commit();

                    aBossServer = serverBoss;
                }

            } catch (Exception ex) {
                Logs.error(GUtil.exToString(ex));
            } finally {
                DBJPA.closeSession(session);
            }
            if (aBossServer == null) return null;
            aBossServer.initRank();
            for (int i = 0; i < CfgBossServer.config.getNumberBoss(); i++) {
                aBossServer.cacheTopDamage(true, i);
            }
        }

        //Break time
        if (aBossServer.isBreakTime() && aBossServer.getSelfDie() != 1) {
            if (!dbUpdateBossBreak()) {
                return null;
            }
        }

        //Qua ngày, ko phải break time, chưa full thì random monster mới
        if (aBossServer.canRandomNewDay()) {
            List<Integer> aNewHeroKey = aBossServer.getNewRandomHero();

            if (!aBossServer.dbUpdateNewABoss(server, aNewHeroKey)) {
                return null;
            }
            aBossServer.setNewABossServer(aNewHeroKey);
            Actions.save(server, 0, "boss_server", "respawn", "start_time", aBossServer.getStartTime(), "hp", aBossServer.getPercentHp(), "info", aBossServer.getLogInfo());
        }

        return aBossServer;
    }

    public synchronized BossServerStatisticEntity getStatistic(int server, Date date) {
        if (server == 0) return null;
        EntityManager session = null;
        if (mStatistic.get(date) == null) {
            try {
                session = DBJPA.getEntityManager();
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<BossServerStatisticEntity> list = session.createNativeQuery("select * from dson.boss_server_statistic where server_id=" + server + " and date_statistic='" + df.format(date) + "'", BossServerStatisticEntity.class).getResultList();
                BossServerStatisticEntity statistic;
                if (!list.isEmpty()) statistic = list.get(0);
                else {
                    session.getTransaction().begin();
                    statistic = new BossServerStatisticEntity(date, server);
                    session.persist(statistic);
                    session.getTransaction().commit();
                }
                mStatistic.put(date, statistic);
            } catch (Exception ex) {
                Logs.error(GUtil.exToString(ex));
            } finally {
                DBJPA.closeSession(session);
            }
            if (aBossServer != null) {
                aBossServer.initRank();
            }
        }

        return mStatistic.get(date);
    }

    boolean dbUpdateBossBreak() {
        //        Date newStartTime = new Date();
        //        newStartTime.setTime(DateTime.getNextMSHour(CfgBossServer.config.getNumberHourClose()));
        //        newCreateTime.setTime(DateTime.getNextMSFromMin(2));
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //        EntityManager session = null;
        //        try {
        //            session = DBJPA.getEntityManager();
        //            session.getTransaction().begin();
        //            session.createNativeQuery("update boss_server set percent_hp='" + CfgBossServer.str0 + "', create_time='" + df.format(newCreateTime) + "', self_die = 1 where server_id=" + server).executeUpdate();
        //            session.createNativeQuery("update boss_server_statistic set percent_hp = '" + aBossServer.getPercentHp() + "' where server_id=" + server + " and date_statistic = '" + DateTime.getStartOfDay(new Date()) + "'").executeUpdate();
        //            session.getTransaction().commit();
        //        } catch (Exception ex) {
        //            Logs.error(GUtil.exToString(ex));
        //        } finally {
        //            DBJPA.closeSession(session);
        //        }

        String key = "boss_server";
        String sqlUpdateUserBoss = "update user_boss_server set last_season_receive = '" + CfgBossServer.str0 + "', last_total_damage1 = total_damage1, last_total_damage2 = total_damage2" +
                ", last_total_damage3 = total_damage3, last_total_damage4 = total_damage4, last_total_damage5 = total_damage5 where server_id = " + server;

        if (!updateUBoss("update boss_server set percent_hp='" + CfgBossServer.str0 + "', self_die = 1, hero_summoned = '" + CfgBossServer.getDefaultHeroSummoned() + "' where server_id=" + server,
                sqlUpdateUserBoss,
                "update boss_server_statistic set percent_hp = '" + aBossServer.getPercentHp() + "', is_user_kill = 0 where server_id=" + server + " and date_statistic = '" + df.format(DateTime.getStartOfDay(new Date())) + "'",
                String.format("SET @%s=0", key),
                String.format("UPDATE user_boss_server SET last_rank1 = @%s\\:=(@%s +1) where total_damage1 > 0 and server_id =" + server + " ORDER BY total_damage1 DESC limit " + CfgBossServer.config.getNumberTop(), key, key),
                String.format("SET @%s=0", key),
                String.format("UPDATE user_boss_server SET last_rank2 = @%s\\:=(@%s +1) where total_damage2 > 0 and server_id =" + server + " ORDER BY total_damage2 DESC limit " + CfgBossServer.config.getNumberTop(), key, key),
                String.format("SET @%s=0", key),
                String.format("UPDATE user_boss_server SET last_rank3 = @%s\\:=(@%s +1) where total_damage3 > 0 and server_id =" + server + " ORDER BY total_damage3 DESC limit " + CfgBossServer.config.getNumberTop(), key, key),
                String.format("SET @%s=0", key),
                String.format("UPDATE user_boss_server SET last_rank4 = @%s\\:=(@%s +1) where total_damage4 > 0 and server_id =" + server + " ORDER BY total_damage4 DESC limit " + CfgBossServer.config.getNumberTop(), key, key),
                String.format("SET @%s=0", key),
                String.format("UPDATE user_boss_server SET last_rank5 = @%s\\:=(@%s +1) where total_damage5 > 0 and server_id =" + server + " ORDER BY total_damage5 DESC limit " + CfgBossServer.config.getNumberTop(), key, key))) {
            return false;
        }

        CfgBossServer.clearCache(server);

        BossServerStatisticEntity statistic = getStatistic(server, DateTime.getStartOfDay(new Date()));
        statistic.setPercentHp(aBossServer.getPercentHp());
        statistic.setIsUserKill(0);
        aBossServer.setPercentHp(CfgBossServer.str0);
        aBossServer.setSelfDie(1);
        aBossServer.setHeroSummoned(CfgBossServer.getDefaultHeroSummoned());
        //        aBossServer.setStartTime(newStartTime);
        Actions.save(server, 0, "boss_server", "buster", "start_time", aBossServer.getStartTime(), "hp", aBossServer.getPercentHp(), "info", aBossServer.getLogInfo());

        return true;
    }

    private boolean updateUBoss(String... sqls) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            for (String sql : sqls) {
                session.createNativeQuery(sql).executeUpdate();
            }
            session.getTransaction().commit();
            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return false;
    }
}
