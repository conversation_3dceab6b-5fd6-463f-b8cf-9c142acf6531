package monster.service.monitor;

import grep.helper.DateTime;
import monster.dao.mapping.BossServerEntity;
import monster.dao.mapping.BossServerStatisticEntity;
import monster.dao.mapping.BossSilentRingEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class BossMonitor {
    public static Map<Integer, BossServerSync> mBossSync = new HashMap<>();
    public static Map<Integer, BossSilentRingSync> bossSilentRingSyncMapByClanId = new HashMap<>();

    //region Boss server
    private static synchronized BossServerSync getBossSync(int server) {
        if (!mBossSync.containsKey(server)) {
            mBossSync.put(server, new BossServerSync(server));
        }

        return mBossSync.get(server);
    }

    public static BossServerEntity getABossServer(int server) {
        return getBossSync(server).getABossServer(server);
    }

    public static BossServerStatisticEntity getStatistic(int server, Date date) {
        return getBossSync(server).getStatistic(server, DateTime.getStartOfDay(date));
    }
    //endregion

    //region Boss silent ring
    private static synchronized BossSilentRingSync getBossSilentRingSync(int clanId) {
        if (!bossSilentRingSyncMapByClanId.containsKey(clanId)) {
            bossSilentRingSyncMapByClanId.put(clanId, new BossSilentRingSync(clanId));
        }

        return bossSilentRingSyncMapByClanId.get(clanId);
    }

    public static Map<Integer, BossSilentRingEntity> getBossSilentRingMapById(int clanId) {
        return getBossSilentRingSync(clanId).getBossSilentRingMapById();
    }
    //endregion
}
