package monster.service.monitor;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import grep.timer.ITimer;
import grep.timer.TimeCounter;
import grep.timer.TurnInfor;
import monster.config.CfgArenaCrystal;
import monster.config.penum.BattleMode;
import monster.config.penum.BattleType;
import monster.dao.mapping.*;
import monster.object.BattleTeam;
import monster.object.MyUser;
import monster.object.UserResources;
import monster.service.battle.common.entity.HeroInfoEntity;
import monster.service.battle.common.entity.proto.HeroTurnEntity;
import monster.service.battle.dependence.*;
import monster.service.battle.dependence.entity.SimulateHero;
import monster.service.battle.dependence.entity.SimulateResult;
import net.sf.json.JSONArray;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.stream.Collectors;

public class BattleFakeProcess implements ITimer {

    @Override
    public void doExpireTurn(int turnId) {
        try {
            CfgArenaCrystal.clearCache();//Clear cache của các server test -> đồng bộ cập nhật lại đội hình thủ thách đấu
            processBattleFake();
            processBattleLog();
        } catch (Exception ex) {
            Logs.error(ex);
        }
        addTimer();
    }

    public void addTimer() {
        TimeCounter.getInstance().addQueue(new TurnInfor(this, 1, 1));
    }

    static BattleFakeProcess instance;

    public static BattleFakeProcess getInstance() {
        if (instance == null) instance = new BattleFakeProcess();
        return instance;
    }

    public void processBattleFake(){
        List<UserBattleFakeEntity> aLUBattleFake = getUBattleFake();
        if (aLUBattleFake != null && !aLUBattleFake.isEmpty()) {
            //Lặp theo lần giả lập
            for (UserBattleFakeEntity uBattleFake : aLUBattleFake) {
                int userId = uBattleFake.getUserId();
                int groupId = uBattleFake.getGroupId();
                int numberAttack = uBattleFake.getNumberAttack();
                UserEntity dbAttUser = UserOnline.getDbUser(userId);
                List<UserEntity> defUsers = new JSONArray();
                if (groupId > 0) { // chay vao battle fake cua phuong
                    BattleFakeGroupEntity tGroup = dbGetGroup(groupId);
                    if (tGroup == null) continue;
                    defUsers = dbGetUser(tGroup);
                    //Check null
                    if (defUsers == null || dbAttUser == null) {
                        continue;
                    }
                    UserArenaCrystalEntity attUserArena = CfgArenaCrystal.getArenaCrystal(userId);
                    if (attUserArena == null) {
                        continue;
                    }
                    BattleTeam myTeam = attUserArena.getDefTeamEntity();
                    if (myTeam == null) continue;
                    for (UserEntity defUser : defUsers) {
                        List<BattleFakeEntity> aLFake = new ArrayList<>();
                        List<Map<Long, BattleFakeInfoEntity>> aLMFakeInfo = new ArrayList<>();
                        int defId = defUser.getId();
                        BattleFakeAvgEntity aAvg = new BattleFakeAvgEntity(uBattleFake.getId(), defId, 0, 0);
                        if (defId == userId) continue;
                        UserArenaCrystalEntity defUserArena = CfgArenaCrystal.getArenaCrystal(defId);
                        if (!DBJPA.save(aAvg)) {
                            continue;
                        }

                        if (defUserArena == null) continue;

                        BattleTeam defTeam = defUserArena.getDefTeamEntity();
                        if (defTeam == null) {
                            continue;
                        }

                        if (!defTeam.isOk() || !myTeam.isOk()) continue;

                        //Lặp theo số lần đánh
                        for (int i = 0; i < numberAttack; i++) {
                            BattleFakeEntity aFake = new BattleFakeEntity();
                            Map<Long, BattleFakeInfoEntity> aMFakeInfo = new HashMap<>();

                            BattleBuilder battleBuilder = BattleBuilder.builder()
                                    .setMode(BattleType.NONE, BattleMode.NORMAL).setInfo(userId, defId).setTeam(myTeam, defTeam);
                            BattleResultEntityNew battleResult = battleBuilder.battle();
                            battleResult.toProto(dbAttUser, UserOnline.getDbUser(defId),
                                    String.format("Giả lập uBattleId = %s, defUserId = %s lần %s", uBattleFake.getId(), defId, i + 1));
                            SimulateResult simulateResult = battleResult.aResult.get(0);
                            HeroTurnEntity turnEntity = simulateResult.aTurn.stream().max(Comparator.comparing(heroTurnEntity -> heroTurnEntity.round)).orElse(null);
                            int roundEnd = turnEntity == null ? 0 :  turnEntity.round;
                            List<SimulateHero> allHeroes = battleResult.aResult.get(0).team1;
                            allHeroes.addAll(battleResult.aResult.get(0).team2);
                            aFake.setBattleFake(aAvg.getId(), battleResult.battleId, battleResult.isWin() ? 1 : 0, roundEnd);
                            aAvg.setNumWin(aAvg.getNumWin() + aFake.getIsWin());
                            aAvg.setAvgRoundEnd(aAvg.getAvgRoundEnd() + roundEnd);

                            if (!DBJPA.save(aFake)) {
                                continue;
                            }
                            for (SimulateHero hero : allHeroes) { //Lặp theo số hero trong trận
                                BattleFakeInfoEntity aFakeInfo = new BattleFakeInfoEntity(aFake.getBattleId(), hero.id, hero.heroId, hero.team);
                                BattleHeroAnalysis heroAnalysis = hero.getHeroAnalysis();
                                if (heroAnalysis == null) continue;

                                aFakeInfo.setHeroInfo(heroAnalysis.logDamage, heroAnalysis.logDamage - heroAnalysis.logCritDamage,
                                        heroAnalysis.logDOTDamage, heroAnalysis.logHeal, heroAnalysis.cntMaxDamageTurn, heroAnalysis.roundStillAlive,
                                        heroAnalysis.listPercentHpAtStartRound.toString(), heroAnalysis.cntNormalAttack, heroAnalysis.cntActiveSkill,
                                        0, 0, 0, heroAnalysis.cntLostTurn, heroAnalysis.cntStun, heroAnalysis.cntStone,
                                        heroAnalysis.cntFreeze, heroAnalysis.cntWeaken, heroAnalysis.cntFear, heroAnalysis.cntExhaust);
                                if (!DBJPA.save(aFakeInfo)) {
                                    continue;
                                }
                                aMFakeInfo.put(hero.id, aFakeInfo);
                            }
                            aLFake.add(aFake);
                            aLMFakeInfo.add(aMFakeInfo);
                        }
                        aAvg.setAvgRoundEnd(aAvg.getAvgRoundEnd() / numberAttack);
                        if (!dbUpdateAvg(aAvg)) {
                            continue;
                        }
                        setAMAvgInfo(myTeam.getAHero(), aAvg.getId(), aLMFakeInfo, 1, false);
                        setAMAvgInfo(defTeam.getAHero(), aAvg.getId(), aLMFakeInfo, 2, false);
                    }
                    dbUpdateQueue(uBattleFake.getId());
                } else if (groupId == 0) { // chay vao battle tower cua tu
//                        UserArenaCrystalEntity attUserArena = CfgArenaCrystal.getArenaCrystal(userId);
//                        if (attUserArena == null) {
//                            continue;
//                        }
//
//                        BattleTeam myTeam = attUserArena.getDefTeamEntity();
//                        if (myTeam == null) continue;
//                        List<BattleFakeEntity> aLFake = new ArrayList<>();
//                        List<Map<Long, BattleFakeInfoEntity>> aLMFakeInfo = new ArrayList<>();
//                        int towerLevel = uBattleFake.getLevelTower();
//                        BattleFakeAvgEntity aAvg = new BattleFakeAvgEntity(uBattleFake.getId(), 0, uBattleFake.getTower(), uBattleFake.getLevelTower());
//                        HeroInfoEntity[] defTeam = new HeroInfoEntity[6];
//                        if (towerLevel == 1) {
//                            ResOblivionTowerEntity oblivionTowerEntity = CfgOblivion.getOblivion(uBattleFake.getLevelTower());
//                            defTeam = oblivionTowerEntity.getAMonster();
//                        } else {
//                            ResTower2Entity tower2 = CfgOblivion.getTower2(uBattleFake.getLevelTower());
//                            defTeam = tower2.getAMonster();
//                        }
//
//                        if (!DBJPA.save(aAvg)) {
//                            continue;
//                        }
//                        if (defTeam == null) {
//                            continue;
//                        }
//
//                        //Lặp theo số lần đánh
//                        for (int i = 0; i < numberAttack; i++) {
//                            BattleFakeEntity aFake = new BattleFakeEntity();
//                            Map<Long, BattleFakeInfoEntity> aMFakeInfo = new HashMap<>();
//
//                            BattleBuilder battleBuilder = BattleBuilder.builder()
//                                    .setMode(BattleType.NONE, BattleMode.NORMAL).setInfo(userId).setTeam(myTeam, defTeam);
//                            BattleResultEntityNew battleResult = battleBuilder.battle();
//                            battleResult.toProto(dbAttUser, UserOnline.getDbUser(defId),
//                                    String.format("Giả lập uBattleId = %s, defUserId = %s lần %s", uBattleFake.getId(), defId, i + 1));
//
//                            BattleResultEntity battleResult = BattleUtil.battle(BattleType.NONE, myTeam.getAHero(), myTeam.getAPet(), defTeam, null, false);
//                            battleResult.toProto(dbAttUser, null,
//                                    String.format("Giả lập uBattleId = %s, tower level = %s lần %s", uBattleFake.getId(), towerLevel, i + 1), true);
//                            AMode mode = battleResult.getMode();
//                            List<HeroTurnEntity> aTurn = mode.simulate();
//                            int roundEnd = 0;
//                            for (HeroTurnEntity turn : aTurn) {
//                                if (turn.round > roundEnd) roundEnd = turn.round;
//                            }
//                            List<HeroBattleEntity> allHeroes = battleResult.getAllHeroes();
//                            allHeroes = allHeroes.stream().filter(Objects::nonNull).filter(HeroBattleEntity::isHero).collect(Collectors.toList());
//
//                            aFake.setBattleFake(aAvg.getId(), battleResult.battleId, mode.getTeamWin() == 1 ? 1 : 0, roundEnd);
//                            aAvg.setNumWin(aAvg.getNumWin() + aFake.getIsWin());
//                            aAvg.setAvgRoundEnd(aAvg.getAvgRoundEnd() + roundEnd);
//
//                            if (!DBJPA.save(aFake)) {
//                                continue;
//                            }
//                            BattleAnalysis analysis = mode.analysis;
//                            for (HeroBattleEntity hero : allHeroes) { //Lặp theo số hero trong trận
//                                BattleFakeInfoEntity aFakeInfo = new BattleFakeInfoEntity(aFake.getBattleId(), hero.id, hero.heroId, hero.team);
//                                BattleHeroAnalysis heroAnalysis = analysis.get(hero.id);
//                                if (heroAnalysis == null) continue;
//
//                                aFakeInfo.setHeroInfo(hero.logDamage, hero.logDamage - hero.logCritDamage,
//                                        heroAnalysis.cntTotalDamageDot, hero.logHeal, heroAnalysis.cntMaxDamageTurn, heroAnalysis.cntAlive,
//                                        mode.analysis.percentHp.get(hero.id).toString(), heroAnalysis.cntNormalAttack, heroAnalysis.cntActiveSkill,
//                                        0, 0, 0, heroAnalysis.cntLostTurn, heroAnalysis.cntStun, heroAnalysis.cntStone,
//                                        heroAnalysis.cntFreeze, heroAnalysis.cntWeaken, heroAnalysis.cntFear, heroAnalysis.cntExhaust);
//                                if (!DBJPA.save(aFakeInfo)) {
//                                    continue;
//                                }
//                                aMFakeInfo.put(hero.id, aFakeInfo);
//                            }
//                            aLFake.add(aFake);
//                            aLMFakeInfo.add(aMFakeInfo);
//                        }
//                        aAvg.setAvgRoundEnd(aAvg.getAvgRoundEnd() / numberAttack);
//                        aAvg.setTowerLevel(uBattleFake.getLevelTower());
//                        aAvg.setTower(uBattleFake.getTower());
//                        if (!dbUpdateTowerAvg(aAvg)) {
//                            continue;
//                        }
//                        boolean checkdone = dbUpdateQueue(uBattleFake.getId());
//                        setAMAvgInfo(myTeam.getAHero(), aAvg.getId(), aLMFakeInfo, 1, false);
//                        setAMAvgInfo(defTeam, aAvg.getId(), aLMFakeInfo, 2, true);


                } else if (groupId == -1) { // auto battle tower
//                        int towerLevel = uBattleFake.getLevelTower();
//                        int towerLevelTo = uBattleFake.getLevelTowerTo();
//                        int towerType = uBattleFake.getTower();
//                        int num = towerType == 1 ? 6 : 12;
//                        HeroInfoEntity[] defTeam = new HeroInfoEntity[num];
//                        BattleTeam myTeamTower = null;
//                        if (towerType == 1) { // thap 1
//                            UserOblivionEntity atk = dbGetTower1(userId);
//                            if (atk == null) {
//                                dbUpdateFailQueue(uBattleFake.getId(), 2);
//                                break;
//                            }
//                            myTeamTower = getBattleTeamEntity(atk.getTeamLastAttack());
//                        } else {// thap 2
//                            UserTower2Entity atk = dbGetTower2(userId);
//                            if (atk == null) {
//                                dbUpdateFailQueue(uBattleFake.getId(), 2);
//                                break;
//                            }
//                            myTeamTower = getBattleTeamEntity(atk.getTeamLastAttack());
//                        }
//
//                        int levelEnd = towerLevel - 1;
//                        //Lặp theo số lần đánh
//                        for (int i = towerLevel; i <= towerLevelTo; i++) {
//                            levelEnd++;
//                            if (towerType == 1) {
//                                ResOblivionTowerEntity oblivionTowerEntity = CfgOblivion.getOblivion(i);
//                                defTeam = oblivionTowerEntity.getAMonster();
//                            } else {
//                                ResTower2Entity tower2 = CfgOblivion.getTower2(i);
//                                defTeam = tower2.getAMonster();
//                            }
//
//                            if (defTeam == null) {
//                                continue;
//                            }
//                            BattleResultEntity battleResult = BattleUtil.battle(BattleType.NONE, myTeamTower.getAHero(), myTeamTower.getAPet(), defTeam, null, true);
//                            battleResult.toProto(dbAttUser, null,
//                                    String.format("Giả lập uBattleId = %s, tower_level_from = %s tower_level_to %s tower_type %s level_end %s", uBattleFake.getId(), towerLevel, towerLevelTo, towerType, levelEnd), true);
//                            AMode mode = battleResult.getMode();
//                            List<HeroTurnEntity> aTurn = mode.simulate();
//                            int roundEnd = 0;
//                            for (HeroTurnEntity turn : aTurn) {
//                                if (turn.round > roundEnd) roundEnd = turn.round;
//                            }
//
//                            if (!battleResult.isWin() || i == towerLevelTo) {
//                                List<Integer> idHeros = new ArrayList<>();
//                                for (int j = 0; j < myTeamTower.getAHero().length; j++) {
//                                    idHeros.add(myTeamTower.getAHero()[j] != null ? myTeamTower.getAHero()[j].heroId : 0);
//                                }
//                                int status = battleResult.isWin() ? 1 : 0;
//                                idHeros.add(myTeamTower.getAPet()[0] != null ? myTeamTower.getAPet()[0].getPetId() : 0);
//                                BattleFakeTowerEntity aFake = new BattleFakeTowerEntity(uBattleFake.getId(), userId, battleResult.battleId, towerLevel, towerLevelTo, towerType, levelEnd, roundEnd, status, GsonUtil.toJson(idHeros));
//                                dbUpdateQueue(uBattleFake.getId());
//                                if (!DBJPA.save(aFake)) {
//                                    break;
//                                }
//                                break;
//                            }
//                            System.out.println("=====================Done!===================");
//                        }
                }

                //Lặp theo số user thủ
            }
        }
    }

    public void processBattleLog() {
        List<UserBattleGroupEntity> listUserBattleLog = getUBattleGroup();
        if (listUserBattleLog == null || listUserBattleLog.isEmpty()) return;

        for (UserBattleGroupEntity userBattleLog : listUserBattleLog) {
            int groupId = userBattleLog.getGroupId();
            int numberAttack = userBattleLog.getNumberAttack();
            BattleFakeGroupEntity group = dbGetGroup(groupId);
            if (group == null) continue;

            List<UserEntity> listUser = dbGetUser(group);
            //Check null
            if (listUser == null) {
                continue;
            }

            //Duyệt listUser để lấy atkUser
            for (int userIndex = 0; userIndex < listUser.size(); userIndex++) {
                int atkUserId = listUser.get(userIndex).getId();
                UserEntity atkUser = UserOnline.getDbUser(atkUserId);
                if (atkUser == null) {
                    continue;
                }

                UserArenaCrystalEntity atkUserArena = CfgArenaCrystal.getArenaCrystal(atkUserId);
                if (atkUserArena == null) {
                    continue;
                }
                BattleTeam atkTeam = atkUserArena.getDefTeamEntity();
                if (atkTeam == null || !atkTeam.isOk()) continue;

                //Duyệt listUser để lấy defUser
                for (int tmpUserIndex = 0; tmpUserIndex < listUser.size(); tmpUserIndex++) {
                    //Bỏ qua nếu là atkUser
                    if (tmpUserIndex == userIndex) continue;

                    UserEntity defUser = listUser.get(tmpUserIndex);
                    List<BattleGroupEntity> listBattleGroup = new ArrayList<>();
                    List<Map<Long, BattleGroupInfoEntity>> listMapBattleGroupInfo = new ArrayList<>();
                    int defUserId = defUser.getId();
                    UserArenaCrystalEntity defUserArena = CfgArenaCrystal.getArenaCrystal(defUserId);
                    if (defUserArena == null) continue;

                    BattleTeam defTeam = defUserArena.getDefTeamEntity();
                    if (defTeam == null || !defTeam.isOk()) {
                        continue;
                    }

                    BattleGroupAvgEntity aAvg = new BattleGroupAvgEntity(userBattleLog.getId(), atkUserId, defUserId, 0, 0);
                    if (!DBJPA.save(aAvg)) {
                        continue;
                    }

                    //Lặp theo số lần đánh
                    for (int i = 0; i < numberAttack; i++) {
                        BattleGroupEntity battleGroup = new BattleGroupEntity();
                        Map<Long, BattleGroupInfoEntity> mapBattleGroupInfo = new HashMap<>();

                        BattleBuilder battleBuilder = BattleBuilder.builder()
                                .setMode(BattleType.NONE, BattleMode.NORMAL).setInfo(atkUserId, defUserId).setTeam(atkTeam, defTeam);
                        BattleResultEntityNew battleResult = battleBuilder.battle();
                        battleResult.toProto(atkUser, defUser,
                                String.format("Giả lập uBattleId = %s, defUserId = %s lần %s", userBattleLog.getId(), defUserId, i + 1));
                        SimulateResult simulateResult = battleResult.aResult.get(0);
                        int roundEnd = simulateResult.aTurn.stream().max(Comparator.comparing(heroTurnEntity -> heroTurnEntity.round)).get().round;
                        HeroTurnEntity lastTurn = simulateResult.aTurn.get(simulateResult.aTurn.size() - 1);
                        List<SimulateHero> allHeroes = battleResult.aResult.get(0).team1;
                        allHeroes.addAll(battleResult.aResult.get(0).team2);
                        battleGroup.setBattleGroup(aAvg.getId(), battleResult.battleId, battleResult.isWin() ? 1 : 0, roundEnd);
                        aAvg.setNumWin(aAvg.getNumWin() + battleGroup.getIsWin());
                        aAvg.setAvgRoundEnd(aAvg.getAvgRoundEnd() + roundEnd);

                        if (!DBJPA.save(battleGroup)) {
                            continue;
                        }
                        for (SimulateHero hero : allHeroes) { //Lặp theo số hero trong trận
                            BattleGroupInfoEntity battleGroupInfo = new BattleGroupInfoEntity(battleGroup.getBattleId(), hero.id, hero.heroId, hero.team);
                            BattleHeroAnalysis heroAnalysis = hero.getHeroAnalysis();
                            if (heroAnalysis == null) continue;

                            battleGroupInfo.setHeroInfo(heroAnalysis.logDamage, heroAnalysis.logDamage - heroAnalysis.logCritDamage,
                                    heroAnalysis.logDOTDamage, heroAnalysis.logHeal, heroAnalysis.cntMaxDamageTurn, heroAnalysis.roundStillAlive,
                                    heroAnalysis.listPercentHpAtStartRound.toString(), heroAnalysis.cntNormalAttack, heroAnalysis.cntActiveSkill,
                                    0, 0, 0, heroAnalysis.cntLostTurn, heroAnalysis.cntStun, heroAnalysis.cntStone,
                                    heroAnalysis.cntFreeze, heroAnalysis.cntWeaken, heroAnalysis.cntFear, heroAnalysis.cntExhaust);
                            if (!DBJPA.save(battleGroupInfo)) {
                                continue;
                            }
                            mapBattleGroupInfo.put(hero.id, battleGroupInfo);
                        }
                        listBattleGroup.add(battleGroup);
                        listMapBattleGroupInfo.add(mapBattleGroupInfo);
                    }
                    aAvg.setAvgRoundEnd(aAvg.getAvgRoundEnd() / numberAttack);
                    if (!dbUpdateGroupAvg(aAvg)) {
                        continue;
                    }
                    setAvgBattleGroupInfo(atkTeam.getAHero(), aAvg.getId(), listMapBattleGroupInfo, 1, false);
                    setAvgBattleGroupInfo(defTeam.getAHero(), aAvg.getId(), listMapBattleGroupInfo, 2, false);
                }
            }

            dbUpdateQueueBattleGroup(userBattleLog.getId());
        }
    }

    public static BattleTeam getBattleTeam(List<BattleFakeFormationEntity> listFormation, int team) {
        HeroInfoEntity[] aHero = new HeroInfoEntity[BattleConfig.TEAM_HERO_SIZE];
        List<Long> listUserHeroId = listFormation.stream().map(BattleFakeFormationEntity::getId).collect(Collectors.toList());
        List<Integer> listPosition = listFormation.stream().map(BattleFakeFormationEntity::getPosition).collect(Collectors.toList());
        List<UserHeroEntity> listUserHero = getListUserHero(listUserHeroId);
        if (listUserHero != null) {
            for (int i = 0; i < listUserHero.size(); i++) {
                UserHeroEntity userHero = listUserHero.get(i);
                BattleFakeFormationEntity battleFakeFormation = listFormation.stream().filter(tmp -> tmp.getId() == userHero.getId()).findAny().orElse(null);
                if (battleFakeFormation == null) continue;
                int position = battleFakeFormation.getPosition();
                IMath.calculateBaseHeroPoint(userHero.getHeroId(), userHero.getPoint(), userHero.getTier(), userHero.getStar(), userHero.getLevel());
                aHero[position] = listUserHero.get(i).toHeroInfo(team, position);
            }
            return BattleTeam.builder().aHero(aHero).aPet(null).build();

        }

        return null;
    }

    public MyUser getMyUser(int userId) {
        try {
            UserEntity user = UserOnline.getDbUser(userId);
            MyUser mUser = new MyUser(user);
            mUser.setResources(new UserResources(mUser));
//            mUser.getResources().images = doQuery(em -> em.createQuery("select c from UserAlbumEntity c where c.userId=:userId ", UserAlbumEntity.class)
//                    .setParameter("userId", mUser.getUser().getId()).getResultList());
//            mUser.getResources().items = doQuery(em -> em.createQuery("select c from UserItemEntity c where c.userId=:userId ", UserItemEntity.class)
//                    .setParameter("userId", mUser.getUser().getId()).getResultList());
//            mUser.getResources().artifacts = doQuery(em -> em.createQuery("select c from UserArtifactEntity c where c.userId=:userId ", UserArtifactEntity.class)
//                    .setParameter("userId", mUser.getUser().getId()).getResultList());
//            mUser.getResources().gems = doQuery(em -> em.createQuery("select c from UserGemEntity c where c.userId=:userId ", UserGemEntity.class)
//                    .setParameter("userId", mUser.getUser().getId()).getResultList());
//            mUser.getResources().heroSkins = doQuery(em -> em.createQuery("select c from UserHeroSkinEntity c where c.userId=:userId ", UserHeroSkinEntity.class)
//            mUser.getResources().isOk(mUser);
            mUser.getResources().getHeroes();
            mUser.getResources().heroes.stream().forEach(hero -> IMath.calculate(mUser, hero, new ArrayList<>()));
            return mUser;
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }

    public static List<BattleFakeFormationEntity> getListFormation(boolean isAtk) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from dson.battle_fake_formation where is_atk = " + isAtk, BattleFakeFormationEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    public static List<UserHeroEntity> getListUserHero(List<Long> listUserHeroId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery("select * from dson.user_hero where id in " + StringHelper.toDBList(listUserHeroId), UserHeroEntity.class).getResultList();
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    public List<UserBattleFakeEntity> getUBattleFake() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String aStr = getQueueStr(getQueueBattleFake());
            if (aStr == null || "".equals(aStr)) return null;
            List<UserBattleFakeEntity> aLUBattleFake = session.createNativeQuery("select * from user_battle_fake where id in " + aStr, UserBattleFakeEntity.class).getResultList();

            return aLUBattleFake;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    public List<UserBattleGroupEntity> getUBattleGroup() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String aStr = getQueueStr(getQueueBattleGroup());
            if (aStr == null || "".equals(aStr)) return null;
            List<UserBattleGroupEntity> aLUBattleFake = session.createNativeQuery("select * from user_battle_group where id in " + aStr, UserBattleGroupEntity.class).getResultList();

            return aLUBattleFake;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    public BattleTeam getBattleTeamEntity(String team) {
        return new Gson().fromJson(team, BattleTeam.class);
    }

    private String getQueueStr(List<Long> queue) {
        if (queue == null || queue.isEmpty()) return null;
        if (queue.size() == 1) return "(" + queue.get(0) + ")";
        String aStr = queue.toString();
        return "(" + aStr.substring(1, aStr.length() - 1) + ")";
    }

    private List<Long> getQueueBattleFake() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<Long> aLong = session.createNativeQuery("select * from battle_fake_queue").getResultList();

            return aLong;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    private List<Long> getQueueBattleGroup() {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<Long> aLong = session.createNativeQuery("select * from battle_group_queue").getResultList();

            return aLong;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    private UserTower2Entity dbGetTower2(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserTower2Entity> oUser = session.createNativeQuery("select * from dson.user_tower2 where user_id =" + userId, UserTower2Entity.class).getResultList();
            return oUser.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    private BattleFakeGroupEntity dbGetGroup(int groupId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            Query query = session.createNativeQuery("select * from dson_main.battle_fake_group where id=:value", BattleFakeGroupEntity.class);
            query.setParameter("value", groupId);
            List<BattleFakeGroupEntity> aTGroup = query.getResultList();

            return aTGroup.isEmpty() ? null : aTGroup.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    private List<UserEntity> dbGetUser(BattleFakeGroupEntity tGroup) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            String strUserIds = tGroup.getUsers();
            strUserIds = strUserIds.substring(1, strUserIds.length() - 1);
            List<UserEntity> testUser = session.createNativeQuery("select * from dson.user where id in (" + strUserIds + ")", UserEntity.class).getResultList();

            return testUser;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return null;
    }

    private UserOblivionEntity dbGetTower1(int userId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            List<UserOblivionEntity> oUser = session.createNativeQuery("select * from dson.user_oblivion where user_id =" + userId, UserOblivionEntity.class).getResultList();
            return oUser.get(0);
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }
        return null;
    }

    private boolean dbUpdateAvg(BattleFakeAvgEntity aAvg) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update battle_fake_avg set num_win=:par1 , avg_round_end=:par2 where id=:par3");
            query.setParameter("par1", aAvg.getNumWin());
            query.setParameter("par2", aAvg.getAvgRoundEnd());
            query.setParameter("par3", aAvg.getId());
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateGroupAvg(BattleGroupAvgEntity aAvg) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update battle_group_avg set num_win=:par1 , avg_round_end=:par2 where id=:par3");
            query.setParameter("par1", aAvg.getNumWin());
            query.setParameter("par2", aAvg.getAvgRoundEnd());
            query.setParameter("par3", aAvg.getId());
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateTowerAvg(BattleFakeAvgTowerEntity aAvg) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update battle_fake_avg_tower set num_win=:par1 , avg_round_end=:par2, tower_level =:tower_level, tower =:tower  where id=:par3");
            query.setParameter("par1", aAvg.getNumWin());
            query.setParameter("par2", aAvg.getAvgRoundEnd());
            query.setParameter("par3", aAvg.getId());
            query.setParameter("tower_level", aAvg.getTowerLevel());
            query.setParameter("tower", aAvg.getTower());
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateTowerAvg(BattleFakeAvgEntity aAvg) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("update battle_fake_avg set num_win=:par1 , avg_round_end=:par2, tower_level =:tower_level, tower =:tower  where id=:par3");
            query.setParameter("par1", aAvg.getNumWin());
            query.setParameter("par2", aAvg.getAvgRoundEnd());
            query.setParameter("par3", aAvg.getId());
            query.setParameter("tower_level", aAvg.getTowerLevel());
            query.setParameter("tower", aAvg.getTower());
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private void setAMAvgInfo(HeroInfoEntity[] aHero, long avgId,
                              List<Map<Long, BattleFakeInfoEntity>> aLMFakeInfo, int team, boolean isBot) {
        if (aHero == null) return;
        int index = 0;
        for (HeroInfoEntity hero : aHero) {
            if (hero == null) continue;
            long heroId = hero.id;
            int heroKey = hero.heroId;
            BattleFakeAvgInfoEntity aAvgInfo = new BattleFakeAvgInfoEntity(avgId, heroId, heroKey, team);
            if (isBot) {
                aAvgInfo.setHeroId(heroId + index);
                index++; // lam cach nay vi tower co quai chung heroid =) Fk
            }
            aLMFakeInfo.forEach(aMFakeInfo -> {
                aAvgInfo.setAvgDamage(aAvgInfo.getAvgDamage() + aMFakeInfo.get(heroId).getTotalDamage()); //Sát thương tổng TB
                aAvgInfo.setAvgDamageNormal(aAvgInfo.getAvgDamageNormal() + aMFakeInfo.get(heroId).getTotalDamageNormal()); //Sát thương thường TB
                aAvgInfo.setAvgDamageDot(aAvgInfo.getAvgDamageDot() + aMFakeInfo.get(heroId).getTotalDamageDot()); //Sát thương đốt(độc,..) TB
                aAvgInfo.setAvgHpHeal(aAvgInfo.getAvgHpHeal() + aMFakeInfo.get(heroId).getTotalHpHeal()); //Tổng hồi máu TB
                aAvgInfo.setAvgMaxDamageTurn(aAvgInfo.getAvgMaxDamageTurn() + aMFakeInfo.get(heroId).getMaxDamageTurn()); //Sát thương max theo lượt TB
                aAvgInfo.setAvgRoundAlive(aAvgInfo.getAvgRoundAlive() + aMFakeInfo.get(heroId).getRoundAlive()); //Turn còn sống TB
                aAvgInfo.setAvgNumAttackNormal(aAvgInfo.getAvgNumAttackNormal() + aMFakeInfo.get(heroId).getNumAttackNormal()); //Số lần đánh thường TB
                aAvgInfo.setAvgNumActive(aAvgInfo.getAvgNumActive() + aMFakeInfo.get(heroId).getNumActive()); //Số lần skill active TB
                aAvgInfo.setAvgNumPassive1(aAvgInfo.getAvgNumPassive1() + aMFakeInfo.get(heroId).getNumPassive1()); //Số lần skill bị động 1 TB
                aAvgInfo.setAvgNumPassive2(aAvgInfo.getAvgNumPassive2() + aMFakeInfo.get(heroId).getNumPassive2()); //Số lần skill bị động 2 TB
                aAvgInfo.setAvgNumPassive3(aAvgInfo.getAvgNumPassive3() + aMFakeInfo.get(heroId).getNumPassive3()); //Số lần skill bị động 3 TB
                aAvgInfo.setAvgNumLostTurn(aAvgInfo.getAvgNumLostTurn() + aMFakeInfo.get(heroId).getNumLostTurn()); //Số lần mất lượt TB
                aAvgInfo.setAvgNumStun(aAvgInfo.getAvgNumStun() + aMFakeInfo.get(heroId).getNumStun()); //Số lần choáng TB
                aAvgInfo.setAvgNumStone(aAvgInfo.getAvgNumStone() + aMFakeInfo.get(heroId).getNumStone()); //Số lần hóa đá TB
                aAvgInfo.setAvgNumFreeze(aAvgInfo.getAvgNumFreeze() + aMFakeInfo.get(heroId).getNumFreeze()); //Số lần đóng băng TB
                aAvgInfo.setAvgNumFear(aAvgInfo.getAvgNumFear() + aMFakeInfo.get(heroId).getNumFear()); //Số lần hoảng sợ TB
                aAvgInfo.setAvgNumWeaken(aAvgInfo.getAvgNumWeaken() + aMFakeInfo.get(heroId).getNumWeaken()); //Số lần tàn phế TB
                aAvgInfo.setAvgNumExhaust(aAvgInfo.getAvgNumExhaust() + aMFakeInfo.get(heroId).getNumExhaust()); //Số lần suy nhược TB
//            aAvgInfo.setAvgNumMark(aAvgInfo.getAvgNumMark() + aMFakeInfo.get(heroId).getNumMark() / aLMFakeInfo.size()); //Số lần khắc dấu TB
            });

            aAvgInfo.setAvgDamage(aAvgInfo.getAvgDamage() / aLMFakeInfo.size()); //Sát thương tổng TB
            aAvgInfo.setAvgDamageNormal(aAvgInfo.getAvgDamageNormal() / aLMFakeInfo.size()); //Sát thương thường TB
            aAvgInfo.setAvgDamageDot(aAvgInfo.getAvgDamageDot() / aLMFakeInfo.size()); //Sát thương đốt(độc,..) TB
            aAvgInfo.setAvgHpHeal(aAvgInfo.getAvgHpHeal() / aLMFakeInfo.size()); //Tổng hồi máu TB
            aAvgInfo.setAvgMaxDamageTurn(aAvgInfo.getAvgMaxDamageTurn() / aLMFakeInfo.size()); //Sát thương max theo lượt TB
            aAvgInfo.setAvgRoundAlive(aAvgInfo.getAvgRoundAlive() / aLMFakeInfo.size()); //Turn còn sống TB
            aAvgInfo.setAvgNumAttackNormal(aAvgInfo.getAvgNumAttackNormal() / aLMFakeInfo.size()); //Số lần đánh thường TB
            aAvgInfo.setAvgNumActive(aAvgInfo.getAvgNumActive() / aLMFakeInfo.size()); //Số lần skill active TB
            aAvgInfo.setAvgNumPassive1(aAvgInfo.getAvgNumPassive1() / aLMFakeInfo.size()); //Số lần skill bị động 1 TB
            aAvgInfo.setAvgNumPassive2(aAvgInfo.getAvgNumPassive2() / aLMFakeInfo.size()); //Số lần skill bị động 2 TB
            aAvgInfo.setAvgNumPassive3(aAvgInfo.getAvgNumPassive3() / aLMFakeInfo.size()); //Số lần skill bị động 3 TB
            aAvgInfo.setAvgNumLostTurn(aAvgInfo.getAvgNumLostTurn() / aLMFakeInfo.size()); //Số lần mất lượt TB
            aAvgInfo.setAvgNumStun(aAvgInfo.getAvgNumStun() / aLMFakeInfo.size()); //Số lần choáng TB
            aAvgInfo.setAvgNumStone(aAvgInfo.getAvgNumStone() / aLMFakeInfo.size()); //Số lần hóa đá TB
            aAvgInfo.setAvgNumFreeze(aAvgInfo.getAvgNumFreeze() / aLMFakeInfo.size()); //Số lần đóng băng TB
            aAvgInfo.setAvgNumFear(aAvgInfo.getAvgNumFear() / aLMFakeInfo.size()); //Số lần hoảng sợ TB
            aAvgInfo.setAvgNumWeaken(aAvgInfo.getAvgNumWeaken() / aLMFakeInfo.size()); //Số lần tàn phế TB
            aAvgInfo.setAvgNumExhaust(aAvgInfo.getAvgNumExhaust() / aLMFakeInfo.size()); //Số lần suy nhược TB
//            aAvgInfo.setAvgNumMark(aAvgInfo.getAvgNumMark() + aMFakeInfo.get(heroId).getNumMark() / aLMFakeInfo.size()); //Số lần khắc dấu TB
            DBJPA.save(aAvgInfo);
        }
    }

    private void setAvgBattleGroupInfo(HeroInfoEntity[] aHero, long avgId,
                              List<Map<Long, BattleGroupInfoEntity>> aLMFakeInfo, int team, boolean isBot) {
        if (aHero == null) return;
        int index = 0;
        for (HeroInfoEntity hero : aHero) {
            if (hero == null) continue;
            long heroId = hero.id;
            int heroKey = hero.heroId;
            BattleGroupAvgInfoEntity aAvgInfo = new BattleGroupAvgInfoEntity(avgId, heroId, heroKey, team);
            if (isBot) {
                aAvgInfo.setHeroId(heroId + index);
                index++; // lam cach nay vi tower co quai chung heroid =) Fk
            }
            aLMFakeInfo.forEach(aMFakeInfo -> {
                aAvgInfo.setAvgDamage(aAvgInfo.getAvgDamage() + aMFakeInfo.get(heroId).getTotalDamage()); //Sát thương tổng TB
                aAvgInfo.setAvgDamageNormal(aAvgInfo.getAvgDamageNormal() + aMFakeInfo.get(heroId).getTotalDamageNormal()); //Sát thương thường TB
                aAvgInfo.setAvgDamageDot(aAvgInfo.getAvgDamageDot() + aMFakeInfo.get(heroId).getTotalDamageDot()); //Sát thương đốt(độc,..) TB
                aAvgInfo.setAvgHpHeal(aAvgInfo.getAvgHpHeal() + aMFakeInfo.get(heroId).getTotalHpHeal()); //Tổng hồi máu TB
                aAvgInfo.setAvgMaxDamageTurn(aAvgInfo.getAvgMaxDamageTurn() + aMFakeInfo.get(heroId).getMaxDamageTurn()); //Sát thương max theo lượt TB
                aAvgInfo.setAvgRoundAlive(aAvgInfo.getAvgRoundAlive() + aMFakeInfo.get(heroId).getRoundAlive()); //Turn còn sống TB
                aAvgInfo.setAvgNumAttackNormal(aAvgInfo.getAvgNumAttackNormal() + aMFakeInfo.get(heroId).getNumAttackNormal()); //Số lần đánh thường TB
                aAvgInfo.setAvgNumActive(aAvgInfo.getAvgNumActive() + aMFakeInfo.get(heroId).getNumActive()); //Số lần skill active TB
                aAvgInfo.setAvgNumPassive1(aAvgInfo.getAvgNumPassive1() + aMFakeInfo.get(heroId).getNumPassive1()); //Số lần skill bị động 1 TB
                aAvgInfo.setAvgNumPassive2(aAvgInfo.getAvgNumPassive2() + aMFakeInfo.get(heroId).getNumPassive2()); //Số lần skill bị động 2 TB
                aAvgInfo.setAvgNumPassive3(aAvgInfo.getAvgNumPassive3() + aMFakeInfo.get(heroId).getNumPassive3()); //Số lần skill bị động 3 TB
                aAvgInfo.setAvgNumLostTurn(aAvgInfo.getAvgNumLostTurn() + aMFakeInfo.get(heroId).getNumLostTurn()); //Số lần mất lượt TB
                aAvgInfo.setAvgNumStun(aAvgInfo.getAvgNumStun() + aMFakeInfo.get(heroId).getNumStun()); //Số lần choáng TB
                aAvgInfo.setAvgNumStone(aAvgInfo.getAvgNumStone() + aMFakeInfo.get(heroId).getNumStone()); //Số lần hóa đá TB
                aAvgInfo.setAvgNumFreeze(aAvgInfo.getAvgNumFreeze() + aMFakeInfo.get(heroId).getNumFreeze()); //Số lần đóng băng TB
                aAvgInfo.setAvgNumFear(aAvgInfo.getAvgNumFear() + aMFakeInfo.get(heroId).getNumFear()); //Số lần hoảng sợ TB
                aAvgInfo.setAvgNumWeaken(aAvgInfo.getAvgNumWeaken() + aMFakeInfo.get(heroId).getNumWeaken()); //Số lần tàn phế TB
                aAvgInfo.setAvgNumExhaust(aAvgInfo.getAvgNumExhaust() + aMFakeInfo.get(heroId).getNumExhaust()); //Số lần suy nhược TB
//            aAvgInfo.setAvgNumMark(aAvgInfo.getAvgNumMark() + aMFakeInfo.get(heroId).getNumMark() / aLMFakeInfo.size()); //Số lần khắc dấu TB
            });

            aAvgInfo.setAvgDamage(aAvgInfo.getAvgDamage() / aLMFakeInfo.size()); //Sát thương tổng TB
            aAvgInfo.setAvgDamageNormal(aAvgInfo.getAvgDamageNormal() / aLMFakeInfo.size()); //Sát thương thường TB
            aAvgInfo.setAvgDamageDot(aAvgInfo.getAvgDamageDot() / aLMFakeInfo.size()); //Sát thương đốt(độc,..) TB
            aAvgInfo.setAvgHpHeal(aAvgInfo.getAvgHpHeal() / aLMFakeInfo.size()); //Tổng hồi máu TB
            aAvgInfo.setAvgMaxDamageTurn(aAvgInfo.getAvgMaxDamageTurn() / aLMFakeInfo.size()); //Sát thương max theo lượt TB
            aAvgInfo.setAvgRoundAlive(aAvgInfo.getAvgRoundAlive() / aLMFakeInfo.size()); //Turn còn sống TB
            aAvgInfo.setAvgNumAttackNormal(aAvgInfo.getAvgNumAttackNormal() / aLMFakeInfo.size()); //Số lần đánh thường TB
            aAvgInfo.setAvgNumActive(aAvgInfo.getAvgNumActive() / aLMFakeInfo.size()); //Số lần skill active TB
            aAvgInfo.setAvgNumPassive1(aAvgInfo.getAvgNumPassive1() / aLMFakeInfo.size()); //Số lần skill bị động 1 TB
            aAvgInfo.setAvgNumPassive2(aAvgInfo.getAvgNumPassive2() / aLMFakeInfo.size()); //Số lần skill bị động 2 TB
            aAvgInfo.setAvgNumPassive3(aAvgInfo.getAvgNumPassive3() / aLMFakeInfo.size()); //Số lần skill bị động 3 TB
            aAvgInfo.setAvgNumLostTurn(aAvgInfo.getAvgNumLostTurn() / aLMFakeInfo.size()); //Số lần mất lượt TB
            aAvgInfo.setAvgNumStun(aAvgInfo.getAvgNumStun() / aLMFakeInfo.size()); //Số lần choáng TB
            aAvgInfo.setAvgNumStone(aAvgInfo.getAvgNumStone() / aLMFakeInfo.size()); //Số lần hóa đá TB
            aAvgInfo.setAvgNumFreeze(aAvgInfo.getAvgNumFreeze() / aLMFakeInfo.size()); //Số lần đóng băng TB
            aAvgInfo.setAvgNumFear(aAvgInfo.getAvgNumFear() / aLMFakeInfo.size()); //Số lần hoảng sợ TB
            aAvgInfo.setAvgNumWeaken(aAvgInfo.getAvgNumWeaken() / aLMFakeInfo.size()); //Số lần tàn phế TB
            aAvgInfo.setAvgNumExhaust(aAvgInfo.getAvgNumExhaust() / aLMFakeInfo.size()); //Số lần suy nhược TB
//            aAvgInfo.setAvgNumMark(aAvgInfo.getAvgNumMark() + aMFakeInfo.get(heroId).getNumMark() / aLMFakeInfo.size()); //Số lần khắc dấu TB
            DBJPA.save(aAvgInfo);
        }
    }

    private void setAMAvgInfoTower(HeroInfoEntity[] aHero, long avgId,
                                   List<Map<Long, BattleFakeInfoEntity>> aLMFakeInfo, int team, boolean isBot) {
        if (aHero == null) return;
        int index = 0;
        for (HeroInfoEntity hero : aHero) {
            if (hero == null) continue;
            long heroId = hero.id;
            int heroKey = hero.heroId;
            BattleFakeAvgInfoEntity aAvgInfo = new BattleFakeAvgInfoEntity(avgId, heroId, heroKey, team);
            if (isBot) {
                aAvgInfo.setHeroId(heroId + index);
                index++; // lam cach nay vi tower co quai chung heroid =) Fk
            }
//            aLMFakeInfo.forEach(aMFakeInfo -> {
//                aAvgInfo.setAvgDamage(aAvgInfo.getAvgDamage() + aMFakeInfo.get(heroId).getTotalDamage()); //Sát thương tổng TB
//                aAvgInfo.setAvgDamageNormal(aAvgInfo.getAvgDamageNormal() + aMFakeInfo.get(heroId).getTotalDamageNormal()); //Sát thương thường TB
//                aAvgInfo.setAvgDamageDot(aAvgInfo.getAvgDamageDot() + aMFakeInfo.get(heroId).getTotalDamageDot()); //Sát thương đốt(độc,..) TB
//                aAvgInfo.setAvgHpHeal(aAvgInfo.getAvgHpHeal() + aMFakeInfo.get(heroId).getTotalHpHeal()); //Tổng hồi máu TB
//                aAvgInfo.setAvgMaxDamageTurn(aAvgInfo.getAvgMaxDamageTurn() + aMFakeInfo.get(heroId).getMaxDamageTurn()); //Sát thương max theo lượt TB
//                aAvgInfo.setAvgRoundAlive(aAvgInfo.getAvgRoundAlive() + aMFakeInfo.get(heroId).getRoundAlive()); //Turn còn sống TB
//                aAvgInfo.setAvgNumAttackNormal(aAvgInfo.getAvgNumAttackNormal() + aMFakeInfo.get(heroId).getNumAttackNormal()); //Số lần đánh thường TB
//                aAvgInfo.setAvgNumActive(aAvgInfo.getAvgNumActive() + aMFakeInfo.get(heroId).getNumActive()); //Số lần skill active TB
//                aAvgInfo.setAvgNumPassive1(aAvgInfo.getAvgNumPassive1() + aMFakeInfo.get(heroId).getNumPassive1()); //Số lần skill bị động 1 TB
//                aAvgInfo.setAvgNumPassive2(aAvgInfo.getAvgNumPassive2() + aMFakeInfo.get(heroId).getNumPassive2()); //Số lần skill bị động 2 TB
//                aAvgInfo.setAvgNumPassive3(aAvgInfo.getAvgNumPassive3() + aMFakeInfo.get(heroId).getNumPassive3()); //Số lần skill bị động 3 TB
//                aAvgInfo.setAvgNumLostTurn(aAvgInfo.getAvgNumLostTurn() + aMFakeInfo.get(heroId).getNumLostTurn()); //Số lần mất lượt TB
//                aAvgInfo.setAvgNumStun(aAvgInfo.getAvgNumStun() + aMFakeInfo.get(heroId).getNumStun()); //Số lần choáng TB
//                aAvgInfo.setAvgNumStone(aAvgInfo.getAvgNumStone() + aMFakeInfo.get(heroId).getNumStone()); //Số lần hóa đá TB
//                aAvgInfo.setAvgNumFreeze(aAvgInfo.getAvgNumFreeze() + aMFakeInfo.get(heroId).getNumFreeze()); //Số lần đóng băng TB
//                aAvgInfo.setAvgNumFear(aAvgInfo.getAvgNumFear() + aMFakeInfo.get(heroId).getNumFear()); //Số lần hoảng sợ TB
//                aAvgInfo.setAvgNumWeaken(aAvgInfo.getAvgNumWeaken() + aMFakeInfo.get(heroId).getNumWeaken()); //Số lần tàn phế TB
//                aAvgInfo.setAvgNumExhaust(aAvgInfo.getAvgNumExhaust() + aMFakeInfo.get(heroId).getNumExhaust()); //Số lần suy nhược TB
////            aAvgInfo.setAvgNumMark(aAvgInfo.getAvgNumMark() + aMFakeInfo.get(heroId).getNumMark() / aLMFakeInfo.size()); //Số lần khắc dấu TB
//            });

            aAvgInfo.setAvgDamage(aAvgInfo.getAvgDamage() / aLMFakeInfo.size()); //Sát thương tổng TB
            aAvgInfo.setAvgDamageNormal(aAvgInfo.getAvgDamageNormal() / aLMFakeInfo.size()); //Sát thương thường TB
            aAvgInfo.setAvgDamageDot(aAvgInfo.getAvgDamageDot() / aLMFakeInfo.size()); //Sát thương đốt(độc,..) TB
            aAvgInfo.setAvgHpHeal(aAvgInfo.getAvgHpHeal() / aLMFakeInfo.size()); //Tổng hồi máu TB
            aAvgInfo.setAvgMaxDamageTurn(aAvgInfo.getAvgMaxDamageTurn() / aLMFakeInfo.size()); //Sát thương max theo lượt TB
            aAvgInfo.setAvgRoundAlive(aAvgInfo.getAvgRoundAlive() / aLMFakeInfo.size()); //Turn còn sống TB
            aAvgInfo.setAvgNumAttackNormal(aAvgInfo.getAvgNumAttackNormal() / aLMFakeInfo.size()); //Số lần đánh thường TB
            aAvgInfo.setAvgNumActive(aAvgInfo.getAvgNumActive() / aLMFakeInfo.size()); //Số lần skill active TB
            aAvgInfo.setAvgNumPassive1(aAvgInfo.getAvgNumPassive1() / aLMFakeInfo.size()); //Số lần skill bị động 1 TB
            aAvgInfo.setAvgNumPassive2(aAvgInfo.getAvgNumPassive2() / aLMFakeInfo.size()); //Số lần skill bị động 2 TB
            aAvgInfo.setAvgNumPassive3(aAvgInfo.getAvgNumPassive3() / aLMFakeInfo.size()); //Số lần skill bị động 3 TB
            aAvgInfo.setAvgNumLostTurn(aAvgInfo.getAvgNumLostTurn() / aLMFakeInfo.size()); //Số lần mất lượt TB
            aAvgInfo.setAvgNumStun(aAvgInfo.getAvgNumStun() / aLMFakeInfo.size()); //Số lần choáng TB
            aAvgInfo.setAvgNumStone(aAvgInfo.getAvgNumStone() / aLMFakeInfo.size()); //Số lần hóa đá TB
            aAvgInfo.setAvgNumFreeze(aAvgInfo.getAvgNumFreeze() / aLMFakeInfo.size()); //Số lần đóng băng TB
            aAvgInfo.setAvgNumFear(aAvgInfo.getAvgNumFear() / aLMFakeInfo.size()); //Số lần hoảng sợ TB
            aAvgInfo.setAvgNumWeaken(aAvgInfo.getAvgNumWeaken() / aLMFakeInfo.size()); //Số lần tàn phế TB
            aAvgInfo.setAvgNumExhaust(aAvgInfo.getAvgNumExhaust() / aLMFakeInfo.size()); //Số lần suy nhược TB
//            aAvgInfo.setAvgNumMark(aAvgInfo.getAvgNumMark() + aMFakeInfo.get(heroId).getNumMark() / aLMFakeInfo.size()); //Số lần khắc dấu TB
            DBJPA.save(aAvgInfo);
        }
    }

    private boolean dbUpdateQueue(long uBattleFakeId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("delete from battle_fake_queue where user_battle_fake_id =:par1");
            query.setParameter("par1", uBattleFakeId);
            query.executeUpdate();
            query = session.createNativeQuery("update user_battle_fake set is_done = 1 where id =:par1");
            query.setParameter("par1", uBattleFakeId);
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateQueueBattleGroup(long uBattleLogId) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("delete from battle_group_queue where user_battle_group_id =:par1");
            query.setParameter("par1", uBattleLogId);
            query.executeUpdate();
            query = session.createNativeQuery("update user_battle_group set is_done = true where id =:par1");
            query.setParameter("par1", uBattleLogId);
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

    private boolean dbUpdateFailQueue(long uBattleFakeId, int log) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            session.getTransaction().begin();
            Query query = session.createNativeQuery("delete from battle_fake_queue where user_battle_fake_id =:par1");
            query.setParameter("par1", uBattleFakeId);
            query.executeUpdate();
            query = session.createNativeQuery("update user_battle_fake set is_done = " + log + " where id =:par1 ");
            query.setParameter("par1", uBattleFakeId);
            query.executeUpdate();
            session.getTransaction().commit();

            return true;
        } catch (Exception ex) {
            Logs.error(GUtil.exToString(ex));
        } finally {
            DBJPA.closeSession(session);
        }

        return false;
    }

}
