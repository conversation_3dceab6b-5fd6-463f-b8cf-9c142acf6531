package monster.service.monitor;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.GUtil;
import grep.log.Logs;
import grep.timer.ITimer;
import grep.timer.TimeCounter;
import grep.timer.TurnInfor;
import monster.cache.CacheStore;
import monster.cache.CacheStoreBeans;
import monster.config.penum.TopType;
import monster.dao.mapping.TopHeroUserEntity;
import monster.dao.mapping.TopUserEntity;
import protocol.Pbmethod.PbListUser;

import jakarta.persistence.EntityManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class TopMonitor implements ITimer {

    final String KEY_DATA = "cache_top_";
    Map<String, PbListUser> mTop = new HashMap<>();
    CacheStore<String> cacheStore5m = CacheStoreBeans.getMinute(String.class, 5);

    public void clearCache(TopType topType, String... keyCache) {
        TopCacheInfo cacheInfo = new TopCacheInfo(topType, keyCache);
        mTop.remove(cacheInfo.keyCache);
    }

    public PbListUser get(TopType topType, String... keyCache) {
        return get(new TopCacheInfo(topType, keyCache));
    }

    private synchronized PbListUser get(TopCacheInfo cacheInfo) {
        if (!mTop.containsKey(cacheInfo.keyCache)) {
            if (cacheTopUser(cacheInfo)) {
                return mTop.get(cacheInfo.keyCache);
            }
            return PbListUser.newBuilder().build();
        }
        if (cacheStore5m.get(KEY_DATA + cacheInfo.keyCache) == null) {
            cacheStore5m.add(KEY_DATA + cacheInfo.keyCache, "1");
            CompletableFuture.supplyAsync(() -> cacheTopUser(cacheInfo));
        }
        return mTop.get(cacheInfo.keyCache);
    }

    boolean cacheTopUser(TopCacheInfo cacheInfo) {
        if (cacheInfo.topType == TopType.HERO_POWER) {
            List<TopHeroUserEntity> aUser = dbGetTopHero(cacheInfo.sql);
            if (aUser != null && aUser.size() > 0) {
                mTop.put(cacheInfo.keyCache, toTopHeroProto(aUser));
                cacheStore5m.add(KEY_DATA + cacheInfo.keyCache, "1");
                return true;
            }
            return false;
        } else {
            List<TopUserEntity> aUser = dbGetTop(cacheInfo.sql);
            if (aUser != null && aUser.size() > 0) {
                mTop.put(cacheInfo.keyCache, toTopProto(aUser));
                cacheStore5m.add(KEY_DATA + cacheInfo.keyCache, "1");
                return true;
            }
            return false;
        }

    }

    PbListUser toTopProto(List<TopUserEntity> aUser) {
        PbListUser.Builder builder = PbListUser.newBuilder();
        aUser.forEach(user -> builder.addAUser(user.toProto()));
        return builder.build();
    }

    PbListUser toTopHeroProto(List<TopHeroUserEntity> aUser) {
        PbListUser.Builder builder = PbListUser.newBuilder();
        aUser.forEach(user -> builder.addAUser(user.toProto()));
        return builder.build();
    }


    @Override
    public void doExpireTurn(int turnId) {
        try {
            if (counter % 30 == 0) {
                counter = 1;
                topArenaCrystalCrown();
            } else counter++;
        } catch (Exception ex) {
            Logs.error(ex);
        }
        addTimer();
    }

    void topArenaCrystalCrown() {
        try {

        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    int counter = 0;

    public void addTimer() {
        TimeCounter.getInstance().addQueue(new TurnInfor(this, 1, 60));
    }

    static TopMonitor instance;

    public static TopMonitor getInstance() {
        if (instance == null) instance = new TopMonitor();
        return instance;
    }

    //region Database
    List<TopUserEntity> dbGetTop(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery(sql, TopUserEntity.class).getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    List<TopHeroUserEntity> dbGetTopHero(String sql) {
        EntityManager session = null;
        try {
            session = DBJPA.getEntityManager();
            return session.createNativeQuery(sql, TopHeroUserEntity.class).getResultList();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(GUtil.exToString(ex));
        } finally {
            closeSession(session);
        }
        return null;
    }

    void closeSession(EntityManager session) {
        DBJPA.closeSession(session);
    }
    //endregion

    class TopCacheInfo {
        String sql, keyCache;
        TopType topType;

        public TopCacheInfo(TopType topType, String... keyCache) {
            this.keyCache = String.format("%s_%s_%s", KEY_DATA, topType.name, new Gson().toJson(keyCache));
            this.topType = topType;
            switch (keyCache.length) {
                case 0:
                    this.sql = topType.sql;
                    break;
                case 1:
                    this.sql = String.format(topType.sql, keyCache[0]);
                    break;
                case 2:
                    this.sql = String.format(topType.sql, keyCache[0], keyCache[1]);
                    break;
            }
        }

    }
}
