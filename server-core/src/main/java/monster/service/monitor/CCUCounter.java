package monster.service.monitor;

import grep.helper.DateTime;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.config.CfgServer;
import monster.object.MyUser;
import monster.task.dbcache.CCUCache;
import redis.clients.jedis.Jedis;

import java.util.*;

public class CCUCounter {

    static Map<Integer, CCUCounter> mInstance = new HashMap<>();

    public static CCUCounter getInstance(int serverId) {
        if (!mInstance.containsKey(serverId)) mInstance.put(serverId, new CCUCounter(serverId));
        return mInstance.get(serverId);
    }

    public CCUCounter(int serverId) {
        this.serverId = serverId;
    }

    int serverId;
    String curKey = "";
    List<String> aKey = new ArrayList<>();

    // insert into dson.user(id, name, last_login, last_action) values (7485006, '', now(), 1) on duplicate key update last_action=VALUES(last_action);
    public void count(MyUser mUser) {
        if (System.currentTimeMillis() - mUser.getUser().getLastAction() > 60000) {
            mUser.getUser().setLastAction(System.currentTimeMillis());
            //            DBJPA.update("user", Arrays.asList("last_action", System.currentTimeMillis()), Arrays.asList("id", mUser.getUser().getId()));
            CCUCache.getInstance().addQueueLastAction(mUser.getUser().getId(), System.currentTimeMillis());
        }
        if (System.currentTimeMillis() - mUser.getLastSaveSession() > 10000) {
            Logs.logSession("%s\t%s".formatted(mUser.getUser().getId(), mUser.getUser().getLoginTime() == 0 ? "first_" + mUser.getSession() : mUser.getSession()));
            mUser.setLastSaveSession(System.currentTimeMillis());
        }
        if (!CfgServer.isRealServer()) return;
        if (!mUser.getKeyCCU().equals(getCurKey())) {
            mUser.setKeyCCU(curKey);
            pfAdd(curKey, mUser.getUser().getId());
        }
    }

    private synchronized String getCurKey() {
        String tmp = getKey();
        if (!tmp.equals(curKey)) {
            if (curKey.length() > 0) savePFCount(curKey);
            curKey = tmp;
            aKey.clear();
        }
        return curKey;
    }

    private String savePFCount(String key) {
        Jedis jedis = null;
        try {
            jedis = JCache.getInstance().getPool().getResource();
            long count = jedis.pfcount(key);
            String hours = key.substring(key.indexOf(":") + 1);
            //            DBJPA.insert(CfgServer.DB_MAIN + "cache_ccu", Arrays.asList("server_id", "date_created", "hours", "online"),
            //                    Arrays.asList(String.valueOf(serverId), DateTime.getDateyyyyMMddCross(new Date()), hours, String.valueOf(count)));
            CCUCache.getInstance().addQueueCCU(serverId, DateTime.getDateyyyyMMddCross(new Date()), hours, count);
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            try {
                JCache.getInstance().returnResource(jedis);
            } catch (Exception ex) {
            }
        }
        return null;
    }

    private String pfAdd(String key, int userId) {
        Jedis jedis = null;
        try {
            jedis = JCache.getInstance().getPool().getResource();
            jedis.pfadd(key, String.valueOf(userId));
            if (!aKey.contains(key)) {
                aKey.add(key);
                jedis.expire(key, 10800);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        } finally {
            try {
                JCache.getInstance().returnResource(jedis);
            } catch (Exception ex) {
            }
        }
        return null;
    }

    /**
     * Key get by minute and serverId -> minute * number of server key ?
     * ccu:1:1321
     */
    private String getKey() {
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        int min = (Calendar.getInstance().get(Calendar.MINUTE) / 5) * 5;
        return String.format("%s:%s:%s", serverId, hour, min);
    }

    public static void main(String[] args) {
        System.out.println(getInstance(5).getKey());
    }
}
