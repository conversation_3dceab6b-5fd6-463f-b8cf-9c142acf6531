package monster.service.event;

import java.util.Date;

public class TimeEvent implements EventCounter {

    Date fromTime;
    long secondBetweenEvent;

    /**
     * @param fromTime         ngày bắt đầu
     * @param timeBetweenEvent thời gian giữa 2 event (second)
     */
    public TimeEvent(Date fromTime, long timeBetweenEvent) {
        this.fromTime = fromTime;
        this.secondBetweenEvent = timeBetweenEvent;
    }

    @Override
    public int lastEventId() {
        return getEventId() - 1;
    }

    @Override
    public int getEventId() {
        long timePass = (System.currentTimeMillis() - fromTime.getTime()) / 1000; // second
        return (int) (timePass / secondBetweenEvent) + 1;
    }

    @Override
    public long countdownToEndEvent() {
        long timePass = (System.currentTimeMillis() - fromTime.getTime()) / 1000; // second
        long timePassInEvent = timePass % secondBetweenEvent;
        return secondBetweenEvent - timePassInEvent;
    }

    public Date getDayStartCurrentEvent() {
        int numberEventPass = getEventId() - 1;
        long timePass = numberEventPass * secondBetweenEvent;
        return new Date(fromTime.getTime() + timePass * 1000);
    }

    public Date getDayEndCurrentEvent() {
        return new Date(getDayStartCurrentEvent().getTime() + secondBetweenEvent * 1000);
    }
}
