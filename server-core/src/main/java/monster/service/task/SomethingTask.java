package monster.service.task;

import grep.database.DBJPA;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.task.worker.JobCounter;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;

import java.util.Arrays;

@DisallowConcurrentExecution
public class SomethingTask extends JobCounter implements Job {

    @Override
    protected void executeJob() {
        try {
            System.out.println("xxxxxxxxxxxxxxxx");
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private void maintenanceInProgress() {
        String value = DBJPA.getUniqueColumn("dson_main.config_api", Arrays.asList("k", "maintenance_in_progress"), "v");
        CfgServer.maintenanceInProgress = !StringHelper.isEmpty(value) && value.equals("1");
    }

}
