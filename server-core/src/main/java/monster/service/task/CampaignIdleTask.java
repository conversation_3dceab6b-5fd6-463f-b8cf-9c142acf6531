package monster.service.task;

import grep.log.Logs;
import monster.server.config.Guice;
import monster.service.common.IdleService;
import monster.task.worker.JobCounter;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;

/**
 * <PERSON><PERSON><PERSON> ch<PERSON>y job bất đồng bộ như này thì trong game làm sao update được tiến độ
 */
@DisallowConcurrentExecution
public class CampaignIdleTask extends JobCounter implements Job {

    IdleService idleService = Guice.getInstance(IdleService.class);
    static int count = 0;

    @Override
    protected void executeJob() {
        try {
            if (++count % 60 == 0) Logs.warn("CampaignIdleTask count=" + count);
            idleService.processIdleBattle();
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

}
