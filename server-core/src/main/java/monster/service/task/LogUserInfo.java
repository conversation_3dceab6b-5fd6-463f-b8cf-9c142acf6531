package monster.service.task;

import grep.helper.DateTime;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.dao.mongo.mapping.UserLogInfoPojo;
import monster.object.MyUser;
import monster.service.Services;

public class LogUserInfo {

    public static LogUserInfo instance = new LogUserInfo();

    public static LogUserInfo getInstance() {
        return instance;
    }

    public void checkLogUserInfo(MyUser mUser) {
        try {
            if (!mUser.getDailyCache().isRedisLogin()) {
                mUser.getDailyCache().setRedisLogin(true);
                String kCache = "UserLogin:" + DateTime.getDateyyyyMMdd();
                JCache.getInstance().sadd(kCache, String.valueOf(mUser.getUser().getId()));
                JCache.getInstance().expire(kCache, DateTime.DAY_SECOND * 2);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public void logUserInfo(MyUser mUser) {
        String kCache = String.format("LogUserInfo:%s", DateTime.getDateyyyyMMdd());
        if (!JCache.getInstance().sismember(kCache, String.valueOf(mUser.getUser().getId()))) {
            long moneyCharge = Services.daoUser.getTotalMoneyCharge(mUser.getUser().getId());
            int campaignLevel = Services.userService.getCampaign(mUser).getLevel();

            if (Services.mongoUser.addLog(UserLogInfoPojo.builder()
                    .userId(mUser.getUser().getId()).serverId(mUser.getUser().getServer()).level(mUser.getUser().getLevel())
                    .money(moneyCharge)
                    .campaign(campaignLevel)
                    .power(mUser.getUser().getPower())
                    .build())) {
                JCache.getInstance().sadd(kCache, String.valueOf(mUser.getUser().getId()));
                JCache.getInstance().expire(kCache, DateTime.DAY_SECOND);
            }
        }
    }

}
