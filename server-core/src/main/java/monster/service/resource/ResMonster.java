package monster.service.resource;

import com.google.gson.Gson;
import grep.database.DBJPA;
import monster.config.CfgServer;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.dao.mapping.main.ResMonsterTeam;
import monster.dao.mapping.main.ResPetEntity;
import monster.object.BattleTeam;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ResMonster {

    private static List<ResMonsterEntity> aMonster = new ArrayList<>();
    public static Map<Integer, ResMonsterEntity> mMonster = new HashMap<>();
    private static Map<Integer, ResPetEntity> mPet = new HashMap<>();

    public static Map<Integer, List<ResMonsterTeam>> mMonsterTeamByFeature;
    public static Map<Integer, ResMonsterTeam> mMonsterTeam;

    public static ResMonsterTeam getTeam(int id) {
        return mMonsterTeam.get(id);
    }

    public static ResMonsterTeam getMonsterTeam(int id) {
        ResMonsterTeam team = mMonsterTeam.get(id);
        if (team != null && team.getPower() == 0) {
            BattleTeam battleTeam = team.getBattleTeam();
            team.setPower(battleTeam.getPower());
            team.setAvatar(battleTeam.getAvatar());
        }
        return team;
    }

    public static ResMonsterEntity getMonster(int id) {
        ResMonsterEntity monster = mMonster.get(id);
        Gson gson = new Gson();
        return gson.fromJson(gson.toJson(monster), ResMonsterEntity.class);
    }

    public static List<ResMonsterEntity> getListMonster(List<Integer> lstId) {
        List<ResMonsterEntity> arr = new ArrayList<>();
        for (int i = 0; i < lstId.size(); i++) {
            arr.add(mMonster.get(lstId.get(i)));
        }
        return arr.size() != lstId.size() ? null : arr;
    }

    public static ResPetEntity getPet(int id) {
        return mPet.get(id);
    }

    public static void init() {
        mMonster.clear();
        aMonster = DBJPA.getList(CfgServer.DB_MAIN + "res_monster", ResMonsterEntity.class);
        aMonster.forEach(resMonster -> mMonster.put(resMonster.getId(), resMonster));

        List<ResMonsterTeam> monsterTeams = DBJPA.getList(CfgServer.DB_MAIN + "res_monster_team", ResMonsterTeam.class);
        mMonsterTeam = monsterTeams.stream().collect(Collectors.toMap(ResMonsterTeam::getId, Function.identity()));
        mMonsterTeamByFeature = monsterTeams.stream().collect(Collectors.groupingBy(ResMonsterTeam::getFeatureId));

        mPet.clear();
        List<ResPetEntity> aPet = DBJPA.getList(CfgServer.DB_MAIN + "res_pet", ResPetEntity.class);
        for (ResPetEntity pet : aPet) {
            pet.init();
            mPet.put(pet.getId(), pet);
        }
    }

    public static void lazyInit() {
        aMonster.forEach(resMonster -> resMonster.init());
    }
}
