package monster.service.resource;

import grep.database.DBJPA;
import monster.config.CfgServer;
import monster.dao.mapping.ResInventoryEntity;
import monster.dao.mapping.main.ResItemEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResMaterial {
    static Map<String, ResInventoryEntity> mInventory = new HashMap<>();
    static List<ResInventoryEntity> aInventory = new ArrayList<>();

    public static ResInventoryEntity getInventory(int inventoryId) {
        return mInventory.get(inventoryId);
    }

    public static void init() {
        aInventory = DBJPA.getList(CfgServer.DB_MAIN + "res_inventory", ResInventoryEntity.class);
        mInventory.clear();
        aInventory.forEach(inventory -> mInventory.put(inventory.getKey(), inventory));
    }
}
