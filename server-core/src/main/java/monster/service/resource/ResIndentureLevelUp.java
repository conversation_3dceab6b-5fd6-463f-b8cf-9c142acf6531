package monster.service.resource;

import grep.database.DBJPA;
import monster.dao.mapping.main.ResIndentureLevelUpEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResIndentureLevelUp {
    public static Map<Integer, ResIndentureLevelUpEntity> resIndentureLevelUp = new HashMap<>();

    public static void init() {
        String sql = "select * from dson_main.res_indenture_level_up";
        List<ResIndentureLevelUpEntity> listResIndenture = DBJPA.getQueryList(sql, ResIndentureLevelUpEntity.class);
        resIndentureLevelUp.clear();
        if (listResIndenture != null) {
            for (ResIndentureLevelUpEntity rIndentureLevelUp : listResIndenture) {
                resIndentureLevelUp.put(rIndentureLevelUp.getId(), rIndentureLevelUp);
            }
        }
    }
}
