package monster.service.resource;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import monster.game.royalpalace.entity.ResFactionSkillUpgradeEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResFactionSkillUpgrade {
    public static Map<Integer, ResFactionSkillUpgradeEntity> resFactionSkillUpgrade = new HashMap<>();

    public static void init() {
        String sql = "select * from dson_main.res_faction_skill_upgrade_royal_palace";
        List<ResFactionSkillUpgradeEntity> resFactionSkillUpgradeEntity = DBJPA.getQueryList(sql, ResFactionSkillUpgradeEntity.class);
        resFactionSkillUpgrade.clear();
        if (resFactionSkillUpgradeEntity != null) {
            for (ResFactionSkillUpgradeEntity factionSkillUpgrade : resFactionSkillUpgradeEntity) {
                resFactionSkillUpgrade.put(factionSkillUpgrade.getId(), factionSkillUpgrade);
            }
        }
    }
    public static List<Long> getFeeUpgrade(int faction, int levelFactionSkill) {
        for (ResFactionSkillUpgradeEntity upgradeEntity : resFactionSkillUpgrade.values()) {
            if (upgradeEntity.getFaction() == faction && upgradeEntity.getLevelTier() == levelFactionSkill + 1) {
                return GsonUtil.strToListLong(upgradeEntity.getFeeUpgrade());
            }
        }
        return null;
    }
    public static int getFeeHero5Star(int faction, int levelFactionSkill) {
        for (ResFactionSkillUpgradeEntity upgradeEntity : resFactionSkillUpgrade.values()) {
            if (upgradeEntity.getFaction() == faction && upgradeEntity.getLevelTier() == levelFactionSkill + 1) {
                return upgradeEntity.getFeeHero5StarUpgrade();
            }
        }
        return -1;
    }

    public static int getLevelUnlock(int faction, int levelFactionSkill) {
        for (ResFactionSkillUpgradeEntity upgradeEntity : resFactionSkillUpgrade.values()) {
            if (upgradeEntity.getFaction() == faction && upgradeEntity.getLevelTier() == levelFactionSkill + 1) {
                return upgradeEntity.getLevelUnlock();
            }
        }
        return -1;
    }

    public static List<Integer> getStatsBonus(int faction, int levelFactionSkill) {
        for (ResFactionSkillUpgradeEntity upgradeEntity : resFactionSkillUpgrade.values()) {
            if (upgradeEntity.getFaction() == faction && upgradeEntity.getLevelTier() == levelFactionSkill) {
                return GsonUtil.strToListInt(upgradeEntity.getStatsBonus());
            }
        }
        return new ArrayList<>();
    }

    public static List<Integer> getSkillPassive(int faction, int levelFactionSkill) {
        for (ResFactionSkillUpgradeEntity upgradeEntity : resFactionSkillUpgrade.values()) {
            if (upgradeEntity.getFaction() == faction && upgradeEntity.getLevelTier() == levelFactionSkill) {
               return GsonUtil.strToListInt(upgradeEntity.getSkillPassive());
            }
        }
        return new ArrayList<>();
    }
}
