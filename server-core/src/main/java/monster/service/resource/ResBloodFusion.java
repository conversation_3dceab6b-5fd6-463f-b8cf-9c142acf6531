package monster.service.resource;

import grep.database.DBJPA;
import monster.config.CfgServer;
import monster.game.bloodmagic.entity.ResBloodFusionEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResBloodFusion {
    public static Map<Integer, ResBloodFusionEntity> mBloodFusion = new HashMap<>();
    public static List<ResBloodFusionEntity> aBloodFusion = new ArrayList<>();

    public static void init() {
        aBloodFusion = DBJPA.getList(CfgServer.DB_MAIN + "res_blood_fusion", ResBloodFusionEntity.class);
        mBloodFusion.clear();
        aBloodFusion.forEach(bloodFusion -> mBloodFusion.put(bloodFusion.getId(), bloodFusion));
    }
    public static String getName(int id) {
        return mBloodFusion.get(id).getName();
    }

}
