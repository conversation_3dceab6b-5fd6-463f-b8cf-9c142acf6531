package monster.service.resource;

import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import monster.config.CfgServer;
import monster.config.penum.MarketType;
import monster.config.penum.MaterialType;
import monster.dao.mapping.UserCampaignEntity;
import monster.dao.mapping.main.MarketDetailEntity;
import monster.dao.mapping.main.MarketEntity;
import monster.dao.mapping.main.ShopNewDetailEntity;
import monster.service.Services;
import monster.service.user.Bonus;

import java.util.*;
import java.util.stream.Collectors;

public class SuperMarket {

    public static Map<Integer, MarketEntity> mMarket = new HashMap<>();
    static Map<Integer, MarketDetailEntity> mMarketDetail = new HashMap<>();
    static Map<Integer, List<MarketDetailEntity>> mListMarketDetail = new HashMap<>();
    static Map<Integer, List<ShopNewDetailEntity>> mListShopNewDetail = new HashMap<>();

    public static MarketDetailEntity getItem(int detailId) {
        return mMarketDetail.get(detailId);
    }

    public static List<MarketDetailEntity> getShopDetails(MarketType marketType) {
        var marketDetails = marketType.showNumber > 0 ? getListShopRandomItem(marketType.id) : getListShopItem(marketType.id);
        return marketDetails;
    }

    public static List<Long> getDefaultCountdown() {
        List<Long> aLong = new ArrayList<>();
        while (aLong.size() < MarketType.maxId) aLong.add(0L);
        mMarket.forEach((k, v) -> {
            if (v.getEnable() == 1 && v.getType() == MarketType.SHOP_TYPE_REFRESH_DAILY)
                aLong.set(v.getId() - 1, (long) DateTime.getTimeKey());
            if (v.getEnable() == 1 && v.getType() == MarketType.SHOP_TYPE_REFRESH_MONTH)
                aLong.set(v.getId() - 1, (long) Calendar.getInstance().get(Calendar.MONTH));
            if (v.getEnable() == 1 && v.getTimeRefresh() > 0)
                aLong.set(v.getId() - 1, System.currentTimeMillis());
        });
        return aLong;
    }

    public static List<MarketDetailEntity> getListShopItem(int marketId) {
        List<MarketDetailEntity> aDetail = mListMarketDetail.get(marketId);
        return aDetail.stream().filter(MarketDetailEntity::isShow).collect(Collectors.toList());
    }

    public static List<ShopNewDetailEntity> getListShopCry(int marketLevel) {
        List<ShopNewDetailEntity> aDetail = mListShopNewDetail.get(marketLevel);
        mListShopNewDetail.get(0).stream().forEach(item -> {
            if (!aDetail.contains(item)) aDetail.add(item);
        });
        return aDetail.stream().filter(ShopNewDetailEntity::isShow).collect(Collectors.toList());
    }

    public static List<MarketDetailEntity> getListShopRandomItem(int marketId) {
        List<MarketDetailEntity> results = new ArrayList<>();
        MarketEntity market = mMarket.get(marketId);
        List<MarketDetailEntity> aMarketDetail = mListMarketDetail.get(marketId);
        Random rand = new Random();
        while (results.size() < market.getShowNumber()) {
            float randFloat = rand.nextFloat() * 100;
            for (int index = 0; index < aMarketDetail.size(); index++) {
                if (randFloat < aMarketDetail.get(index).getPercent()) {
                    results.add(aMarketDetail.get(index));
                    break;
                }
            }
        }
        return results;
    }

    public static MarketEntity getMarket(int id) {
        return mMarket.get(id);
    }

    public static void init() {
        mMarket.clear();
        mListMarketDetail.clear();
        mMarketDetail.clear();
        mListShopNewDetail.clear();

        List<MarketEntity> aMarket = DBJPA.getList(CfgServer.DB_MAIN + "market", MarketEntity.class);
        aMarket.forEach(marketEntity -> mMarket.put(marketEntity.getId(), marketEntity));
        aMarket.forEach(marketEntity -> mListMarketDetail.put(marketEntity.getId(), new ArrayList<>()));

        List<MarketDetailEntity> aMarketDetail = DBJPA.getList(CfgServer.DB_MAIN + "market_detail", MarketDetailEntity.class);
        aMarketDetail.forEach(marketDetail -> mListMarketDetail.get(marketDetail.getMarketId()).add(marketDetail));
        aMarketDetail.forEach(marketDetail -> mMarketDetail.put(marketDetail.getId(), marketDetail));
        mListMarketDetail.get(MarketType.ALTAR.id).sort(Comparator.comparing(MarketDetailEntity::getId));
        mListMarketDetail.get(MarketType.MARKETPLACE.id).sort(Comparator.comparing(MarketDetailEntity::getItemOrder));
        mListMarketDetail.get(MarketType.SHOP_GUILD_NEW.id).sort(Comparator.comparing(MarketDetailEntity::getItemOrder));

        mListMarketDetail.forEach((marketId, aDetail) -> {
            for (int i = 1; i < aDetail.size(); i++) {
                aDetail.get(i).setPercent(aDetail.get(i).getPercent() + aDetail.get(i - 1).getPercent());
            }
        });
        List<ShopNewDetailEntity> aShopCryDetail = DBJPA.getList(CfgServer.DB_MAIN + "shop_new_detail", ShopNewDetailEntity.class);
        aShopCryDetail.forEach(shopNewDetail -> mListShopNewDetail.put(shopNewDetail.getMarketLevel(), new ArrayList<>()));
        aShopCryDetail.forEach(shopNewDetail -> mListShopNewDetail.get(shopNewDetail.getMarketLevel()).add(shopNewDetail));

        for (MarketEntity market : mMarket.values()) {
            MarketType marketType = MarketType.get(market.getId());
            if (marketType != null) {
                marketType.setType(market.getType());
                marketType.enable = market.getEnable() == 1;
                marketType.version = market.getVersion();
                marketType.showNumber = market.getShowNumber();
                marketType.timeRefresh = market.getTimeRefresh();
            }
        }
    }
}
