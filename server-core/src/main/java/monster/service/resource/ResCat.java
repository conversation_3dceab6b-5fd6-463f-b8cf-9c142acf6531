package monster.service.resource;

import grep.database.DBJPA;
import monster.config.CfgServer;
import monster.game.raisecat.entity.ResFoodCatEntity;
import monster.game.raisecat.entity.ResInfoCatEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResCat {
    public static Map<Integer, ResInfoCatEntity> mInfoCat = new HashMap<>();
    public static List<ResInfoCatEntity> aInfoCatMap = new ArrayList<>();
    public static Map<Integer, ResFoodCatEntity> mFoodCat = new HashMap<>();
    public static List<ResFoodCatEntity> aFoodCat = new ArrayList<>();

    public static void init() {
        aInfoCatMap = DBJPA.getList(CfgServer.DB_MAIN + "res_info_cat", ResInfoCatEntity.class);
        aFoodCat = DBJPA.getList(CfgServer.DB_MAIN + "res_food_cat", ResFoodCatEntity.class);
        mInfoCat.clear();
        mFoodCat.clear();
        aInfoCatMap.forEach(infoCat -> mInfoCat.put(infoCat.getId(), infoCat));
        aFoodCat.forEach(foodCat -> mFoodCat.put(foodCat.getLevelFood(), foodCat));
    }

    public static ResInfoCatEntity getInfoCatBySlotId(int slotId) {
        return aInfoCatMap.stream().filter(item -> item.getSlotId() == slotId).findFirst().orElse(null);
    }

    public static ResFoodCatEntity getInfoFoodById(int foodId) {
        return aFoodCat.stream().filter(foodCat -> foodCat.getItemConsumeId() == foodId).findFirst().orElse(null);
    }

}
