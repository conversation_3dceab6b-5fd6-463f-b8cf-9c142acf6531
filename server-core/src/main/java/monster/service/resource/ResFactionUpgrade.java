package monster.service.resource;

import grep.database.DBJPA;
import grep.helper.GsonUtil;
import monster.game.royalpalace.entity.ResFactionUpgradeEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResFactionUpgrade {
    public static Map<Integer, ResFactionUpgradeEntity> resFactionUpgrade = new HashMap<>();

    public static void init() {
        String sql = "select * from dson_main.res_faction_upgrade_royal_palace";
        List<ResFactionUpgradeEntity> resFactionUpgradeEntity = DBJPA.getQueryList(sql, ResFactionUpgradeEntity.class);
        resFactionUpgrade.clear();
        if (resFactionUpgradeEntity != null) {
            for (ResFactionUpgradeEntity factionUpgrade : resFactionUpgradeEntity) {
                resFactionUpgrade.put(factionUpgrade.getId(), factionUpgrade);
            }
        }
    }

    public static ResFactionUpgradeEntity getLevelFaction(int level) {
        return resFactionUpgrade.get(level);
    }

    public static String feeFactionUpgrade(int level, int faction) {
        for (ResFactionUpgradeEntity upgradeEntity : resFactionUpgrade.values()) {
            if (upgradeEntity.getLevel() == level && faction == 1) {
                return upgradeEntity.getFeeUpgradeFaction1();
            }
            if (upgradeEntity.getLevel() == level && faction == 2) {
                return upgradeEntity.getFeeUpgradeFaction2();
            }
            if (upgradeEntity.getLevel() == level && faction == 3) {
                return upgradeEntity.getFeeUpgradeFaction3();
            }
            if (upgradeEntity.getLevel() == level && faction == 4) {
                return upgradeEntity.getFeeUpgradeFaction4();
            }
            if (upgradeEntity.getLevel() == level && faction == 5) {
                return upgradeEntity.getFeeUpgradeFaction5();
            }
        }
        return null;
    }

}
