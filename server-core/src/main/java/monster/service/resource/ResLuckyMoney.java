package monster.service.resource;

import monster.config.CfgServer;
import monster.game.chat.entity.ResLuckyMoneyEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import grep.database.DBJPA;

public class ResLuckyMoney {
    public static Map<Integer, ResLuckyMoneyEntity> resLuckyMoneyEntity = new HashMap<>();

    public static void init() {
        List<ResLuckyMoneyEntity> listResLuckyMoney = DBJPA.getList(CfgServer.DB_MAIN + "res_lucky_money", ResLuckyMoneyEntity.class);
        if(listResLuckyMoney != null) {
            for (ResLuckyMoneyEntity res : listResLuckyMoney) {
                resLuckyMoneyEntity.put(res.getId(), res);
            }
        }
    }
}
