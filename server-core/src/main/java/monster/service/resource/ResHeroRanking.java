package monster.service.resource;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import grep.database.DBJPA;
import grep.log.Logs;
import monster.cache.redis.JCache;
import monster.config.CfgServer;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.main.ResHeroRankingEntity;
import monster.game.ranking.TopTask;
import monster.game.ranking.entity.HeroServerRankingEntity;
import monster.object.CacheHeroRank;
import monster.task.dbcache.TopHeroPowerCache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResHeroRanking {

    public static Map<Integer, ResHeroRankingEntity> mHeroRanking = new HashMap<>();
    public static int maxSize;

    public static void init() {
        List<ResHeroRankingEntity> aHeroRanking = DBJPA.getList(CfgServer.DB_MAIN + "res_hero_ranking", ResHeroRankingEntity.class);
        mHeroRanking.clear();
        for (ResHeroRankingEntity rank : aHeroRanking) {
            mHeroRanking.put(rank.getId(), rank);
        }
        maxSize = aHeroRanking.size();
    }

    public static void addRank(UserEntity user, long number, int cluster) {
        try {
            HeroServerRankingEntity serverRanking = getHeroServerRank(cluster);
            if (serverRanking != null) {
                List<Long> dataRank = serverRanking.getRankingData();
                if (dataRank.isEmpty()) initDefault(user, number, cluster);
                else {
                    for (int i = 1; i <= maxSize; i++) {
                        ResHeroRankingEntity res = mHeroRanking.get(i);
                        if (number < res.getPoint()) break;
                        int index = (i - 1) * 3;
                        if (dataRank.size() < index || dataRank.get(index) == 0) {
                            TopHeroPowerCache.getInstance().addQueue(new CacheHeroRank(i, user, number, cluster));
                        }
                    }
                }
            } else { // Chưa có data thì add k cần check
                initDefault(user, number, cluster);
            }
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    private static void initDefault(UserEntity user, long number, int cluster) {
        for (int i = 1; i <= maxSize; i++) {
            ResHeroRankingEntity res = mHeroRanking.get(i);
            if (number < res.getPoint()) break;
            TopHeroPowerCache.getInstance().addQueue(new CacheHeroRank(i, user, number, cluster));
        }
    }

    public static HeroServerRankingEntity getHeroServerRank(int cluster) {
        String data = JCache.getInstance().getValue(TopTask.getKeyHeroServerRank(cluster));
        if (data == null) {
            return null;
        }
        return new Gson().fromJson(data, new TypeToken<HeroServerRankingEntity>() {
        }.getType());
    }
}
