package monster.service.resource;

import grep.database.DBJPA;
import grep.helper.NumberUtil;
import lombok.Getter;
import monster.config.CfgServer;
import monster.dao.mapping.main.ResGemQualityEntity;
import monster.dao.mapping.main.ResGemSetEntity;
import monster.dao.mapping.main.ResGemSummonEntity;

import java.util.*;


public class ResGem {

    static Map<Integer, ResGemSummonEntity> mSummon = new HashMap<>();
    static Map<Integer, ResGemSetEntity> mSet = new HashMap<>();
    static Map<Integer, List<ResGemQualityEntity>> mQuality = new HashMap<Integer, List<ResGemQualityEntity>>() {{
        put(1, new ArrayList<>());
        put(2, new ArrayList<>());
        put(3, new ArrayList<>());
        put(4, new ArrayList<>());
        put(5, new ArrayList<>());
        put(6, new ArrayList<>());
    }};

    public static ResGemSummonEntity getGemSummon(int id) {
        return mSummon.get(id);
    }

    public static ResGemSetEntity getGemSet(int id) {
        if(mSet.containsKey(id))
            return mSet.get(id);
        return null;
    }

    public static int randomKeyReleased(){
        Set<Integer> keySet = mSet.keySet();
        List<Integer> keyList = new ArrayList<>(keySet);
        int size = keyList.size();
        int randIdx = new Random().nextInt(size);
        Integer randomKey = keyList.get(randIdx);
        return randomKey;
    }

    public static ResGemQualityEntity getRandomQuality(int id) {
        List<ResGemQualityEntity> qualityEntities = mQuality.get(id);


        return qualityEntities.get(NumberUtil.getRandom(qualityEntities.size()));
    }

    public static void init() {
        List<ResGemSummonEntity> summons = DBJPA.getList(CfgServer.DB_MAIN + "res_gem_summon", ResGemSummonEntity.class);
        summons.forEach(summon -> {
            mSummon.put(summon.id, summon);
            summon.init();
        });

        List<ResGemSetEntity> sets = DBJPA.getQueryList("select * from dson_main.res_gem_set where `release` = 1", ResGemSetEntity.class);
        sets.forEach(gemSet -> mSet.put(gemSet.id, gemSet));

        List<ResGemQualityEntity> qualities = DBJPA.getList(CfgServer.DB_MAIN + "res_gem_quality", ResGemQualityEntity.class);

        qualities.forEach(quality -> mQuality.get(quality.quality).add(quality));
    }
}
