package monster.service.battle.common.entity;

import com.google.gson.Gson;
import monster.dao.mapping.main.ResHeroEntity;
import monster.server.Constans;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.config.TriggerType;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.resource.ResHero;
import protocol.Pbmethod;

import java.util.ArrayList;
import java.util.List;

public class HeroInfoEntity implements HeroBattleInterface {
    public long id;
    public int heroId, team, position;
    public int level, star, skin, levelCalculate, voidLevel;
    public int exclusiveSkillLevel;
    private List<Integer> eSkills;
    public SkillEntity normalSkill, activeSkill;
    public List<SkillEntity> passiveSkills;
    public List<Long> gems;
    public int type = HeroType.TYPE_NORMAL;
    public Point point;
    public float[] addPercent = new float[]{1f, 1f, 1f, 1f};
    public boolean isBoss = false;
    public int bossType = 0;
    public HeroType clazz, faction, gender, species, side;

    @Override
    public HeroInfoEntity toHeroInfo(int team, int position) {
        HeroInfoEntity hero = new HeroInfoEntity();
        hero.id = id;
        hero.heroId = heroId;
        hero.team = team;
        hero.position = position;
        hero.level = level;
        hero.star = star;
        hero.skin = skin;
        hero.levelCalculate = level;
        hero.voidLevel = voidLevel;
        hero.normalSkill = normalSkill;
        hero.activeSkill = activeSkill;
        hero.passiveSkills = passiveSkills;
        hero.gems = gems;
        hero.type = type;
        hero.point = getPoint().cloneInstance();
        hero.isBoss = isBoss;
        hero.bossType = bossType;
        hero.addPercent = addPercent.clone();
        hero.clazz = clazz;
        hero.faction = faction;
        hero.gender = gender;
        hero.species = species;
        hero.side = side;
        return hero;
    }

    @Override
    public Point getPoint() {
        return point;
    }

    public HeroInfoEntity setLevel(int level) {
        this.level = level;
        return this;
    }

    //GD yêu cầu tính năng afk, story, lãnh địa thì cứ lấy theo star show, ko cần qualify
    public HeroInfoEntity setStar(int star) {
        this.star = star;
        ResHeroEntity resHero = ResHero.getHero(heroId);
        if (resHero != null) this.star = resHero.getQualifiedStar(star);
        return this;
    }

    public void setHeroInfo(List<Integer> eSkills) {
        ResHeroEntity resHero = ResHero.getHero(heroId);
        faction = resHero.getHeroFaction();
        clazz = resHero.getHeroClass();

        SkillEntity changeSkill = null;
        passiveSkills = resHero.getSkills(Math.max(resHero.getStar(), star), level, voidLevel, eSkills, skin);
        // chỗ này lấy skill đang sai
        if (passiveSkills.isEmpty()) return;

        activeSkill = passiveSkills.get(0);
        passiveSkills.remove(0);
        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
            SkillEntity skill = passiveSkills.get(i);
            if (skill.trigger != TriggerType.IMMEDIATELY.value) continue;

            if (skill.isAddPassive()) {
                passiveSkills.remove(i);
                passiveSkills.add(ResHero.getChangeSkill1(skill.id));
            }
        }

        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
            SkillEntity skill = passiveSkills.get(i);
            if (skill.trigger != TriggerType.IMMEDIATELY.value) continue;

            if (skill.isChangeCombat1()) {
                passiveSkills.remove(i);
                changeSkill = ResHero.getChangeSkill1(skill.id);
            }
        }
        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
            SkillEntity skill = passiveSkills.get(i);
            if (skill.trigger != TriggerType.IMMEDIATELY.value) continue;

            passiveSkills.remove(i);
            if (skill.isChangeCombat() && changeSkill == null) changeSkill = ResHero.getChangeSkill(skill.id);
        }

        if (changeSkill == null) changeSkill = resHero.getNormalSkill();
        normalSkill = changeSkill == null ? Constans.normalSkill : changeSkill;
    }

    public List<Integer> getListSkillId() {
        List<Integer> listSkill = new ArrayList<>();
        if (normalSkill != null) listSkill.add(normalSkill.getId());
        if (activeSkill != null) listSkill.add(activeSkill.getId());
        if (passiveSkills != null) for (SkillEntity passiveSkill : passiveSkills) {
            if (passiveSkill != null) listSkill.add(passiveSkill.getId());
        }
        return listSkill;
    }

    public Pbmethod.PbTeamHeroInfo.Builder protoTeamHero() {
        return Pbmethod.PbTeamHeroInfo.newBuilder().setId(id).setHeroId(heroId).setLevel(level).setStar(star).setSkin(skin)
                .addAllPoint(point.getCurrentValues());
    }

    public Pbmethod.PbTeamHeroInfo.Builder protoTeamHeroTower(int heroId) {
        return Pbmethod.PbTeamHeroInfo.newBuilder().setId(heroId).setHeroId(heroId).setLevel(level).setStar(star).setSkin(skin);
    }

    public HeroInfoEntity cloneHero() {
        return new Gson().fromJson(new Gson().toJson(this), HeroInfoEntity.class);
    }

    public int getShowPercentHp() {
        if (point.getStartHpPercent() > 0 && point.getStartHpPercent() < 1) return 1;
        return (int) point.getStartHpPercent();
    }

    public Pbmethod.PbHero.Builder toProto() {
        protocol.Pbmethod.PbHero.Builder pbHero = protocol.Pbmethod.PbHero.newBuilder();
        pbHero.setLevel(level);
        pbHero.addExclusiveSkill(0);
        pbHero.setStar(star);
        pbHero.setId(id);
        pbHero.setHeroId(heroId).setSkinUse(skin);
        pbHero.addInfo(getShowPercentHp());
        //        pbHero.setPower(IMath.getPower(level, point, eSkills));
        pbHero.setPower(IMath.getPower(point));
        pbHero.addAllPoint(point.getListValue());
        for (float percent : addPercent) pbHero.addAddPercent(percent);
        return pbHero;
    }

    public List<Float> getListAddPercent() {
        List<Float> values = new ArrayList<>();
        for (float percent : addPercent) values.add(percent);
        return values;
    }

    @Override
    public String toString() {
        return String.format("team=%s, pos=%s, heroId=%s, id=%s, star=%s, level=%s, hp=%s", team, position, heroId, id, star, level, point.getCurrentHP());
    }
}
