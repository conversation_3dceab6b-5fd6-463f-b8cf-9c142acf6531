package monster.service.battle.common.entity.proto;

import monster.protocol.pbentity.ListPbJEffect;
import monster.protocol.pbentity.PbJHeroAttack;
import monster.protocol.pbentity.PbJHeroTurn;
import monster.service.battle.common.entity.HeroBattleEntity;
import protocol.Pbmethod;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class HeroTurnEntity implements Serializable {
    public int round, team, position, anger;
    public boolean isCC;
    public List<AttackInfoEntity> aAttack = new ArrayList<>();

    public HeroTurnEntity(int round) {
        this.round = round + 1;
    }

    public HeroTurnEntity(HeroBattleEntity hero, int round) {
        this.round = round + 1;
        this.team = hero.team;
        this.position = hero.position;
    }

    public boolean isTurnEndRound() {
        return team == 0;
    }

    public Pbmethod.PbHeroTurn.Builder toProto() {
        Pbmethod.PbHeroTurn.Builder builder = Pbmethod.PbHeroTurn.newBuilder();
        builder.setRound(round).setTeam(team).setPosition(position).setAnger(anger);

        if (!aAttack.isEmpty()) {
            Pbmethod.PbHeroAttack.Builder pbHeroAttack = aAttack.get(0).toProto();

            Pbmethod.ListPbEffect.Builder listPbEffect = Pbmethod.ListPbEffect.newBuilder();
            aAttack.get(0).toListEffectProto(listPbEffect);

            for (int i = 1; i < aAttack.size(); i++) {

                pbHeroAttack.addAListEffect(listPbEffect);
                builder.addAAttack(pbHeroAttack);

                pbHeroAttack = aAttack.get(i).toProto();
                listPbEffect = Pbmethod.ListPbEffect.newBuilder();
                aAttack.get(i).toListEffectProto(listPbEffect);
            }
            pbHeroAttack.addAListEffect(listPbEffect);
            builder.addAAttack(pbHeroAttack);
        }
        return builder;
    }

    public PbJHeroTurn toPbj() {
        PbJHeroTurn aJHeroTurn = new PbJHeroTurn();
        aJHeroTurn.setRound(round).setTeam(team).setPosition(position).setAnger(anger);

        if (!aAttack.isEmpty()) {
            PbJHeroAttack aJHeroAttack = aAttack.get(0).toPbJ();

            ListPbJEffect listPbJEffect = new ListPbJEffect();
            aAttack.get(0).toListEffectPbj(listPbJEffect);

            for (int i = 1; i < aAttack.size(); i++) {
                if (aAttack.get(i).aTarget.isEmpty()) {
                    aAttack.get(i).toListEffectPbj(listPbJEffect);
                } else {
                    aJHeroAttack.addAListEffect(listPbJEffect);
                    aJHeroTurn.addAAttack(aJHeroAttack);

                    aJHeroAttack = aAttack.get(i).toPbJ();
                    listPbJEffect = new ListPbJEffect();
                    aAttack.get(i).toListEffectPbj(listPbJEffect);
                }
            }
            aJHeroAttack.addAListEffect(listPbJEffect);
            aJHeroTurn.addAAttack(aJHeroAttack);
        }
        return aJHeroTurn;
    }

    @Override
    public String toString() {
        String output = String.format("round=%s, team=%s, position=%s\n", round, team, position);
        for (AttackInfoEntity attack : aAttack) output += "   " + attack.toString() + "\n";
        return output;
    }
}
