package monster.service.battle.common.config;

import monster.service.battle.dependence.Point;

import java.util.*;

public enum SkillEffectType {
    COUNTER_ATTACK("addHurt"), // phản đòn
    ATTACK_ENEMY_POINT("enemyHurt"), // đánh theo chỉ số công của đối thủ
    COUNTER_ATTACK_2("addHurt2"), // phản đòn gây phản đòn
    COUNTER_ATTACK_BELOW_ARMOR("addHurtd"),
    ARMOR_BY_NUMBER("armNumber", true),
    ARMOR_PER_LEVEL("armP", true),
    ARMOR_PER_LEVEL_POS("armPos"),
    ATTACK_PER_LEVEL_NUMBER_BURN_ENEMY("atkPBurnEnemy", true),
    ATTACK_PER_LEVEL("atkP", true),
    ATTACK_PER_LEVEL_POS("atkPos", true),
    ARMOR_BREAK("brk", true),
    ARMOR_BREAK1("brk1", true),
    ANTI_ARMOR_BREAK("antibrk", true),
    CRITICAL_STRIKE_MARK("cImpress", true),
    //    COUNTER_ATTACK("changeCombat"),
    ASSASSIN("ck"),
    ASSASSIN_POISON("ckDotPoison", true),
    ASSASSIN_SILENCE("ckForbid", true),
    ASSASSIN_ATTACK("ckHurt"),
    ASSASSIN_STONE("ckStone", true),
    ASSASSIN_STUN("ckStun", true),
    CRIT("crit", true),
    CRIT_POS("critPos", true),
    CRIT_TIME("critTime", true),
    REDUCE_DAMAGE("decDmg", true),
    DOT_BLOOD("dotBlood", true),
    dotBlood1("dotBlood1", true),
    DOT_BLOOD_EXTRA_DAMAGE("dotBloodB", true),
    DOT_FIRE("dotFire", true),
    dotFire1("dotFire1", true),
    DOT_FIRE_EXTRA_DAMAGE("dotFireB", true),
    DOT_POISON("dotPoison", true),
    dotPoison1("dotPoison1", true),
    DOT_POISON_EXTRA_DAMAGE("dotPoisonB", true),
    ANGER("energy", true),
    ANGER_ENDROUND("energyEndRound", true, BattleEffectType.EFFECT_ANGER_END_ROUND),
    LIGHTNING_MARK_SKEREI("fImpress", true),
    SILENCE("forbid", true),
    FEAR("fear", true),
    IMMUNE_FEAR("fearless", true),
    IMMUNE_SILENCE("forbidless"),
    GAIN_IMMUNE_CONTROL("free", true),
    ANTI_IMMUNE_CONTROL("antifree", true),
    MAGE("fs"),
    MAGE_DOT_BLOOD("fsDotBlood", true),
    MAGE_SILENCE("fsForbid"),
    MAGE_STUN("fsStun", true),
    HEAL_BY_ATTACK("heal", true),
    HEAL_BY_MAX_HP("healP", true),
    PRECISION("hit", true),
    PRECISION_POS("hitPos", true),
    CONTINUE_HEAL("hot", true), // hồi màu cuối lượt theo chỉ số công
    CONTINUE_HEAL_2("hot2", true), // hồi máu cuối lượt theo chỉ số máu tối đa
    HP_PER_LEVEL("hpP", true),
    HP_PER_LEVEL_POS("hpPos", true),
    ATTACK("hurt"),
    ATTACK_NO_CRIT("addHurt3"),
    ATTACK_BY_PERCENT_HP("hurtPercentHp"),
    ATTACK_NORMAL("danhthuong"),
    MORE_DAMAGE_IF_HIGHER_HP("hurtH"),
    FREEZE("ice", true),
    EXTRA_DAMAGE_IF_FREEZE("iceB"),
    IMMUNE_FREEZE("iceless"),
    DOT_BLOOD_EXACT_DAMAGE("mdotBlood", true), // PET gây đốt không tính giáp
    DOT_POISON_EXACT_DAMAGE("mdotPoison", true), // PET gây đốt không tính giáp
    EXACT_DAMAGE("mhurt"), // PET gây dam không tính giáp
    DODGE_BLOCK("miss", true),
    DODGE_BLOCK_POS("missPos"),
    PRIEST("ms"),
    PRIEST_SILENCE("msForbid", true),
    PRIEST_ATTACK("msHurt"), // extra damage to priest
    PRIEST_STUN("msStun", true), // stun if priest
    WATCHER_MARK_MORE_DAMAGE_TAKEN("oImpress", true),
    DAMAGE_PERCENT_MAX_HP_ABOVE_CONDITION("phurt"),
    DAMAGE_PERCENT_MAX_HP_BELOW_CONDITION("phurtH"),
    ROUND_MARK_DAMAGE("rImpress", true),
    ROUND_MARK_DAMAGE2("rImpress2", true),
    REVIVE("revive", true),
    STEAL_ARMOR("siphonArm", true),
    STEAL_ATTACK("siphonAtk", true),
    HEAL_HP_FROM_DAMAGE("siphonHp", true),
    SKILL_DAMAGE_NUMBER_BURN_ENEMY("sklPBurnEnemy", true),
    SKILL_DAMAGE("sklP", true),
    SPEED("spd", true),
    STONE("stone", true),
    EXTRA_DAMAGE_IF_STONE("stoneB"),
    IMMUNE_STONE("stoneless"),
    STUN("stun", true),
    EXTRA_DAMAGE_IF_STUN("stunB"),
    EXTRA_DAMAGE_IF_FEAR("fearB"),
    EXTRA_DAMAGE_IF_FORBID("forbidB"),
    STUN_IF_BELOW_HP("stunH", true),
    IMMUNE_STUN("stunless"),
    HOLY_DAMAGE("trueAtk", true),
    WEAKEN("weak", true),
    RANGER("yx"),
    RANGER_ATKP("yxAtkP", true),
    RANGER_DOT_BLOOD("yxDotBlood", true), // extra bleed damage to ranger
    RANGER_ATTACK("yxHurt"),
    RANGER_STUN("yxStun", true),

    WARRIOR("zs"),
    WARRIOR_ARMP("zsArmP", true),
    WARRIOR_FIRE("zsDotFire", true), // extra burning damage to warrior
    WARRIOR_SILENCE("zsForbid", true),
    WARRIOR_ATTACK("zsHurt"),
    WARRIOR_ICE("zsIce", true),
    WARRIOR_STUN("zsStun", true), //  stun if warrior

    FRONT_DOT_BLOOD("frontDotBlood", true), // chảy máu nếu hàng trước
    BACK_CRIT_TIME("backCritTime"), // sát thương chí mạng nếu hàng sau
    HEAL_BY_BLOCK_DAMAGE("healBlock", true), // hồi máu theo damage block
    DEATH_MARK("deathMark", true, BattleEffectType.EFFECT_DEATH_MARK), // khắc dấu tử ấn
    DEATH_MARK1("deathMark1", true, BattleEffectType.EFFECT_DEATH_MARK1), // khắc dấu tử ấn
    DEATH_MARK_B("deathMarkB", true), // đánh vào tử ấn đau hơn
    DEATH_MARK_B1("deathMarkB1", true), // đánh vào tử ấn đau hơn

    POINT_CRIT_TIME_REDUCE("critTimeReduce", true),
    POINT_CRIT_TIME_REDUCE2("critTimeReduce2", true),
    POINT_ATTACK_MORE_DAMAGE_IF_HIGH_HP("atkMoreDamageIfHighHp"),
    POINT_MORE_HEAL("moreHeal", true),
    POINT_HEAL_LOST_HP_PER_ROUND("healLostHpPerRound"),
    POINT_BUFF_ATK_PER_ROUND_ALIVE_HERO("buffAtkPerRoundAliveHero"),
    POINT_HEAL_BY_CRIT_ATTACK("healByCritAtk"),
    POINT_MORE_DAMAGE_NOT_CRIT("moreDamageNotCrit"),
    POINT_REDUCE_DAMAGE_A("reduceDamageA", true), // giảm sát thương đốt, độc, chảy máu
    IMMORTAL("immortal", true), // bất tử
    IMMORTAL2("immortal2", true), // bất tử 2
    IMMORTAL_BY_OPP_TURN("immortalByOppTurn", true), // bất tử
    END_ROUND_SKILL("endRoundSkill", true, BattleEffectType.EFFECT_SKILL_END_ROUND), // sub skill at end round
    ENERGY_SEAL("energySeal", true), // khiên năng lượng
    ENERGY_SEAL_2("energySeal2", true), // khiên năng lượng 2
    ENERGY_SEAL_3("energySeal3", true), // khiên năng lượng 3
    ENERGY_SEAL_4("energySeal4", true), // khiên năng lượng 4
    ENERGY_SEAL_5("energySeal5", true), // khiên năng lượng 5
    ENERGY_SEAL_6("energySeal6", true), // khiên năng lượng 6
    ENERGY_SEAL_7("energySeal7", true), // khiên năng lượng 7
    ENERGY_SEAL_8("energySeal8", true), // khiên năng lượng 8
    SHIELD_BY_MAX_HP("shieldByMaxHp", true), // khiên năng lượng theo máu tối đa của người buff
    SHIELD_BY_MAX_HP1("shieldByMaxHp1", true), // khiên năng lượng theo máu tối đa của người buff
    SHIELD_BY_MAX_HP2("shieldByMaxHp2", true), // khiên năng lượng theo máu tối đa của người buff
    SHIELD_BY_MAX_HP3("shieldByMaxHp3", true), // khiên năng lượng theo máu tối đa của người buff
    COPY_ENEMY_ATTACK("copyEnemyAtk", true), // copy công đối thủ
    REMOVE_ACC_EFFECT("removeAccEffect", true), // xóa các hiệu ứng CC
    MORE_TURN_AFTER_ACTIVE_SKILL("moreTurnAfterActiveSkill"), // thêm lượt sau khi sử dụng kỹ năng
    TRUY_SAT("truysat", true), // thêm lượt sau khi sử dụng kỹ năng
    SUY_NHUOC("suynhuoc", true), // thêm lượt sau khi sử dụng kỹ năng
    DIVIDE_DEC_DAME("divideDecDame", true),// chia đều giảm sát thương cùng hàng
    COUNTER_SHEILD("counterShield", true),// hiệu ứng khiên
    COPY_ENEMY_ATTACK2("copyEnemyAtk2", true), // copy công đối thủ
    IMMORTAL_ENEMY_DARK_LIGHT_FACTION("immortalWithDarkLight", true), // Bất tử trong đội hình có hệ hiếm
    IMMORTAL_ENEMY_NORMAL_FACTION("immortalWithNormalFaction", true), // Bất từ trong đội hình có hệ thường

    BRK_PER_LEVEL_NUMBER_STONE_ENEMY("brkStoneEnemy", true), //buff phá giáp theo số tướng bị hóa đá
    ANGER_PER_STONE_ENEMY("energyPStoneEnemy", true), // buff nộ theo số tướng bị hóa đá
    ANGER_PER_STUN_ENEMY("energyPStunEnemy", true), // buff nộ theo số tướng bị hóa đá
    SPD_PER_STONE_ENEMY("spdPStoneEnemy", true), // buff tốc theo số tướng bị hóa đá
    SPD_PER_STUN_ENEMY("spdPStunEnemy", true), // buff tốc theo số tướng bị hóa đá
    STONE_ATK_EXACT_DAMAGE("stoneAtk", true),//thêm sát thương lên địch bị hóa đá
    ATTACK_PER_LEVEL_NUMBER_STONE_ENEMY("atkPStoneEnemy", true), // buff atk theo số tướng bị hóa đá
    HP_PER_LEVEL_NUMBER_STONE_ENEMY("hpStoneEnemy"), // buff hp theo số tướng bị hóa đá
    REDUCE_CONTROL_IMMUNE("freeReduce", true),// giảm chỉ số miễn chống
    ATTACK_FRONT_LINE_ALLY("atkFrontLineAlly", true, BattleEffectType.EFFECT_ATTACK_FRONT_LINE_ALLY), // tấn công 1 đồng đội hàng trước
    ATTACK_FRONT_LINE_ALLY2("atkFrontLineAlly2", true, BattleEffectType.EFFECT_ATTACK_FRONT_LINE_ALLY2), // tấn công 1 đồng đội hàng trước
    FASCINATE("fascinate", true, BattleEffectType.EFFECT_FASCINATE), // mất kiểm soát kỹ năng bản thân, tấn công thường vào 1 đồng minh ngẫu nhiên
    SURVIVAL("survival", true), // Đánh dấu lượng máu hiện tại của đồng minh, khi dấu ấn hết hiệu lực, nó hồi phục máu cho đồng minh về lượng đã đánh dấu , nếu đồng minh chết trước khi dấu ấn hết hạn, hồi sinh cho họ, hiệu quả hồi sinh chỉ tác dụng 1 lần mồi trận. vô hiệu nếu không có hero nào của đội còn sống trên trận.
    STEAL_HP_3_ROUND("stealHp3Round", true), // tấn công 1 đồng đội hàng trước
    CAM_LO("camlo", true, BattleEffectType.EFFECT_CAM_LO), //có 70% cơ hội hồi sinh cho người có dấu ấn với 80% máu. Cam Lộ không cộng dồn và không tương tác với các tướng có nội tại hồi sinh)
    HURT_DEC_ENEMY_LIVE("hurtDivideEnemyLive"),// chia đều sát thương cho số tướng còn sống
    HURT_HP_DEC_ENEMY_LIVE("hurtHPDivideEnemyLive"),// chia đều sát thương cho số tướng còn sống
    LOAN_KHONG("loankhong", true, BattleEffectType.EFFECT_LOAN_KHONG),// loạn không -> khá loạn
    CC_BY_HIT("ccByHit", true, BattleEffectType.EFFECT_CC_BY_HIT),// giống loạn không -> khá loạn
    LOAN_KHONG2("loankhong2", true, BattleEffectType.EFFECT_LOAN_KHONG2),// loạn không -> khá loạn
    HOANG_LOAN("hoangloan", true), // giảm % nhận hồi nộ từ mọi nguồn
    // Something new skills
    BUFF_POINT("buffpoint", true),// buff chỉ số -> pointIndex
    BUFF_POINT_X_FACTION("buffpointXFaction", true), //  buff chỉ số x theo số tướng hệ -> pointIndex, num1 là hệ gì
    ATTACK_PER_LEVEL_NUMBER_CONTROL_ENEMY("atkPControlEnemy", true),//tăng dame theo số tướng bi khống chế
    SKILL_PER_LEVEL_NUMBER_CONTROL_ENEMY("sklPControlEnemy", true),//tăng sát thương kĩ năng khi địch bị khống chế
    MAX_DAMAGE_SUFFER_BY_HP("maxDamageSufferByHp", true, BattleEffectType.EFFECT_MAX_DAMAGE_SUFFER), // damage tối đa chịu đựng theo máu tối đa
    MAX_DAMAGE_SUFFER_BY_HP2("maxDamageSufferByHp2", true, BattleEffectType.EFFECT_MAX_DAMAGE_SUFFER2), // damage tối đa chịu đựng theo máu tối đa 2
    MAX_DAMAGE_SUFFER_BY_HP3("maxDamageSufferByHp3", true, BattleEffectType.EFFECT_MAX_DAMAGE_SUFFER3), // damage tối đa chịu đựng theo máu tối đa 3 -> giống 1
    PHA_CAM("phacam", true, BattleEffectType.EFFECT_PHA_CAM), //  tăng % giới hạn giảm sát thương tối đa
    REDUCE_DAMAGE_FROM_NORMAL_ATTACK("reduceDamageNormalAttack", true), // giảm % damage từ đòn đánh thường
    REDUCE_DAMAGE_FROM_ACTIVE_SKILL("reduceDamageActiveSkill", true), // giảm % damage từ đòn đánh nộ
    DODGE("dodge", true), // né tránh sát thương (trừ các sát thương cuối lượt)
    DOT_MAX_HP("dotmaxHP", true), // đốt theo máu tối đa của địch
    DOT_ENERGY("dotEnergy", true), // đốt theo điểm nộ con lại
    ENERGY_HURT("energyHurt"), // gây damage theo điểm nộ
    TRANSFER_CONTROL("transferControl"), // di dời khống chế
    DE_BUFF_HURT("debuffHurt"),
    EXACT_DAMAGE_BY_HERO("trueHpHurt"), // HERO gây dam không tính giáp
    BONUS_HURT("bonusHurt"),
    bonusHurt1("bonusHurt1", true, BattleEffectType.bonusHurt1),
    COUNTER_MARK("counterMark", true),
    trueEnemyHighestHphurt("trueEnemyHighestHphurt"), // gây sát thương trực tiếp tương đương x% ("num") máu tối đa của kẻ địch có máu tối đa cao nhất cho obj, bị cap bởi công bản thân ( "num1" )
    trueEnemyHPhurt("trueEnemyHPhurt"), // gây sát thương trực tiếp tương đương x% máu tối đa của obj, bị cap bởi công bản thân ( "num1" )
    LOST_HP_HURT("lostHpHurt"), // gây damage theo công của bản thân và máu mất của đối thủ
    DIS_ARM("disarm", true), // vô hiệu hóa attack
    HOPE("hopeMark", true, BattleEffectType.EFFECT_HOPE_MARK), // dấu ấn hi vọng không thể chết
    HOPE2("hopeMark2", true, BattleEffectType.EFFECT_HOPE_MARK2), // dấu ấn hi vọng không thể chết 2
    BUFF_BLOCKER("buffBlocker", true, BattleEffectType.EFFECT_BUFF_BLOCKER), // Không thể nhận các Chỉ số có lợi, Dấu ấn có lợi, Dấu ấn đặc biệt
    LINK("linkMark", true, BattleEffectType.EFFECT_LINK),
    DOT_TRUE_ATK("dotTrueAtk", true),
    DOT_TRUE_HP("dotTrueHp", true),
    FIRE_MARK("fireMark", true), // trong thời gian dấu ấn tồn tại, bất cứ khi nào bị gây sát thương ( bao gồm đánh thường, nộ, các passive gây sát thương thường) ( ngoại trừ ST cuối lượt) sử dụng skill có id ở num cho kẻ tấn công
    ICE_MARK("iceMark", true), // băng Phong: không thể hành động đồng thời bị vô hiệu ngẫu nhiên num ( số lượng) skill bị động ( không bao gồm các skill + point_trigger 23 buff chỉ số hero)
    DEAL_DMG_SHIELD("dealDmgShield", true, BattleEffectType.DMG_SHIELD), // trong thời gian dấu ấn tồn tại, num% sát thương nhận phải sẽ chuyển sang người tạo ra dấu ấn ( nhận giúp sát thương mỗi khi đồng đội bị tấn công)
    DEAL_DMG_SHIELD1("dealDmgShield1", true, BattleEffectType.DMG_SHIELD1), // trong thời gian dấu ấn tồn tại, num% sát thương nhận phải sẽ chuyển sang người tạo ra dấu ấn ( nhận giúp sát thương mỗi khi đồng đội bị tấn công)
    HURT_LOW_HP_MORE_DMG("hurtLowHpMoreDmg"), // Gây sát thương num, với mỗi 1% máu mất của hero tấn công sát thương tăng thêm num1% ( DBZ có effect nay)
    BUFF_HOLY_DAMAGE_BY_NUMBER_CONTROLLED_ENEMY("trueAtkControlEnemy", true),
    BUFF_MAX_HP("hpPMax", true),
    HURT_MAX_HP("maxHpHurt"),
    HURT_MAX_HP1("maxHpHurt1"),
    DEBUFF_BLOCKER("debuffBlocker", true),
    oppress("oppress", true, BattleEffectType.EFFECT_MARK_OPPRESS), // dấu ấn,không thể hành động, không thể hồi nộ từ mọi nguồn. (thêm skill cho khớp với op - chưa làm)
    oppressLowHp("oppressLowHp", true, BattleEffectType.EFFECT_MARK_OPPRESS), // oppress nếu máu mục tiêu thấp hơn bản thân. (thêm skill cho khớp với op - chưa làm)
    breakImmortal("breakImmortal", true),
    breakImmortalByHp("breakImmortalByHp", true), // mối num% máu tối đa của bản thân bị tổn thất, tăng num1%  breakImmortal  cho bản thân.
    HURT_BY_SPEED("hurtMoreBySpd"),
    ONOKI_MARK("onokiMark", true),
    HASHIRAMA_MARK("hashiramaMark", true),
    REDUCE_END_ROUND_DMG("reduceDamageEndRound", true, BattleEffectType.EFFECT_REDUCE_END_ROUND_DMG),
    HASHIRAMA_REVIVAL("hashiramaRevival", true),
    ANTI_CRIT("rescrit", true),
    BONUS_ANGER("bonusAnger", true),
    BONUS_DAMAGE_DOT("bonusDot", true),
    MORE_TURN("moreTurn", true), // Obj được xóa num số lượng hiệu ứng xấu (bao gồm cả dấu ấn bất lợi và hiệu ứng giảm chỉ số) và được hành động ngay lập tức,
    SOUL_BURN("soulBurn", true),
    CONTROL_BLOCK("controlBlock", true),
    SOUL_BURN_MARK("soulBurnMark", true),
    MORE_DAMAGE_ACTIVE_SKILL("buffDmgActive", true),
    MORE_TURN_AFTER_NORMAL_SKILL("moreTurnAfterNormalSkill"), // thêm lượt sau khi danh thuong
    hurtAndReduceMaxHp("hurtAndReduceMaxHp"),
    //    reduceRound("reduceRound", true), // giảm num số round duy trì các hiệu ứng buff có lợi trên obj bao gồm cả dấu ấn có lợi. (ví dụ : Buff khiên tồn tại 5 round, config num = 3 thì số round tồn tại giảm xuống còn 2)
    buffDmgMoreHP("buffDmgMoreHP", true, BattleEffectType.buffDmgMoreHP), // dấu ấn vào bản thân, tăng num% sát thương bản thân gây ra với tất cả mục tiêu có máu cao hơn bản thân.
    immuneDiseased("immuneDiseased", true),
    hurtLowHpMoreDmgForceCrit("hurtLowHpMoreDmgForceCrit"), // Gây sát thương num, với mỗi 1% máu mất của hero tấn công sát thương tăng thêm num1%, đòn tấn công chắc chắn gây chí mạng. (kết hợp của 2 effect bên DBZ)
    atkP2("atkP2", true), // Tăng/giảm num% Công cho obj, không thể bị xóa, có thể stack cộng dồn. (clone DBZ)
    kisameMark("kisameMark", true), // Dấu ấn lên obj, khi obj có dấu ấn này sẽ giảm num% sát thương AOE phải nhận, và đầu mỗi lượt đấu giảm num1 Nộ toàn phe địch.
    taunt("taunt", true), // không thể sử dụng kỹ năng Nộ, bắt buộc đánh thường vào người tạo ra dấu ấn, nếu người tạo ra dấu ấn bị chết, dấu ấn sẽ vô hiệu, có thể bị xóa. (clone bên DBZ)
    healByHurt("healByHurt", true), // done Hồi máu bản thân theo num% sát thương từ đòn đánh thường và Tuyệt kỹ vừa gây ra (clone bên DBZ)
    hopeMark1("hopeMark1", true, BattleEffectType.EFFECT_HOPE_MARK1), //Khi dấu ấn tồn tại, máu không tụt xuống dưới 1, chỉ biến mất sau khi hành động.
    hurtIfHigherHp("hurtIfHigherHp"), //gây num st nếu đối thủ có máu hiện tại cao hơn bản thân
    killAddRoundForHopeMark1("killAddRoundForHopeMark1", true, BattleEffectType.killAddRoundForHopeMark1),
    atkAndBrkByDot("atkAndBrkByDot", true),
    atkAndBrkByKashimoMark("atkAndBrkByKashimoMark", true),
    preHopeMark1("preHopeMark1", true, BattleEffectType.EFFECT_PRE_HOPE_MARK1), //Dấu ấn vào obj, khi lần đầu tiên máu tụt xuống dưới 1 sẽ giữ lại ở 1 và trigger skill id ở num.
    moreDot("moreDot", true),
    selfDie("selfDie"),
    removeGoodEffect("removeGoodEffect", true),
    removeBadEffect("removeBadEffect", true),
    removeControlEffect("removeControlEffect", true),
    removeGoodMark("removeGoodMark", true),
    removeBadMark("removeBadMark", true),
    removeDot("removeDot", true),
    removeBadAndControlAndDot("removeBadAndControlAndDot", true),
    FIGHT_BACK("fightBack", true, BattleEffectType.EFFECT_FIGHT_BACK), // dấu ấn phản kích
    takeMoreDmgFromAbyss("takeMoreDmgFromAbyss", true), // dấu ấn phản kích
    takeMoreDmgFromForest("takeMoreDmgFromForest", true), // dấu ấn phản kích
    takeMoreDmgFromDark("takeMoreDmgFromDark", true), // dấu ấn phản kích
    takeMoreDmgFromLight("takeMoreDmgFromLight", true), // dấu ấn phản kích
    dotMark("dotMark", true, BattleEffectType.dotMark), //dấu ấn vào obj, khi obj có dấu ấn này nhận thêm num% sát thương cuối lượt từ các effect có trong bảng DoT ở config battle.
    removeControlAndDotPerRound("removeControlAndDotPerRound", true),
    hate("hate", true, BattleEffectType.hate),//dấu ấn vào obj, mỗi khi obj gây sát thương dạng hurt sẽ được tăng num% trueAtk và num1% free,
    // nếu là hành động bị động thì đc bonus thêm num2% chỉ số ở num và num1
    skillEndTurn("skillEndTurn", true, BattleEffectType.skillEndTurn),//dấu ấn buff vào obj, khi kết thúc lượt của hero bât kì kể cả obj,
    // nếu obj gây sát thương dạng hurt, obj sẽ có num1% tỉ lệ trigger skill id ở num (không bao gồm st dot, không bao gồm Nộ của obj)
    background("background", true, BattleEffectType.background),
    triggerNormalByTarget("triggerNormalByTarget", true, BattleEffectType.triggerNormalByTarget),
    //Dấu ấn buff vào obj, có num% kích hoạt đòn đánh thường của obj, khi kẻ địch tấn công dựa vào số target để nhân với cái base num tỉ lệ kích hoạt dấu ấn. (không xóa khi chết)
    akazaMark("akazaMark", true, BattleEffectType.akazaMark), //dấu ấn vào obj, tăng num% sát thương gây ra (dmg đầu ra)  với kẻ địch bị choáng, không cộng dồn (xin ID luôn)
    shinobuMark("shinobuMark", true), //Dấu ấn vào obj dưới dạng stack, mỗi khi bị gây st bởi đánh thường, nộ, bị động thì xóa 1 stack và hồi phục num% máu tối đa
    muichirouMark("muichirouMark", true),
    //dấu ấn vào bản thân mỗi khi có đồng đội bị đánh thường, nộ, phản kích thì tăng num stack,
    // mỗi stack tăng num1% xuyên thủ và num2% st chuẩn (brk và trueAtk) cộng dồn tối đa num3 stack (dấu ấn ko biến mất khi chết)
    sklPByEnemyLive("sklPByEnemyLive", true), //dấu ấn vào bản thân, có cộng đồn, trả về icon sklP, tăng (num - num1*số kẻ địch còn sống)% ST Kỹ Năng (sklP)
    hurtSelfPercentLostHP("hurtSelfPercentLostHP"),
    bonusDmgPerDotType("bonusDmgPerDotType", true),
    //Cuối lượt, nhận sát thương bằng num%/num1%/num2% công dựa trên số lượng loại hiệu ứng DOT địch đang chịu (1/2/3 loại) (thiêu đốt, độc, chảy máu). Sát thương trực tiếp vào máu (bỏ qua shield)
    removeAccPDotEnemy("removeAccPDotEnemy", true),
    triggerActive("triggerActive", true, BattleEffectType.triggerActive), //Dấu ấn buff vào obj, khi obj dùng Nộ có num% cơ hội kích hoạt skill ID ở num2,
    // nếu kỹ năng Nộ tiêu diệt được mục tiêu tỉ lệ kích hoạt tăng lên thành num1%
    triggerActive1("triggerActive1", true, BattleEffectType.triggerActive1),
    onlySingleTarget("onlySingleTarget", true, BattleEffectType.onlySingleTarget), //trong thời gian dấu ấn tồn tại, không thể bị chọn làm mục tiêu bởi các kỹ năng có số lượng obj >1 của kẻ địch,
    // Hiệu quả này vô hiệu nếu bản thân là người còn sống duy nhất của phe ta trên chiến trường hoặc toàn bộ các thành viên cùng phe có trạng thái này.
    onlySingleTarget1("onlySingleTarget1", true, BattleEffectType.onlySingleTarget1), //Giống onlySingleTarget, khác ở chỗ tăng num% Công
    // khi có đồng đội bị chết và không biến mất khi chết
    finalDmg("finalDmg", true),
    finalDecDmg("finalDecDmg", true),
    healByMaxHP("healByMaxHP", true),
    sleep("sleep", true, BattleEffectType.sleep),
    buffDamageAoe("buffDamageAoe", true),
    buffSingleAttack("buffSingleAttack", true),
    shieldByAtk("shieldByAtk", true),
    shieldByAtk1("shieldByAtk1 ", true),
    immuneControlStack("immuneControlStack", true, BattleEffectType.immuneControlStack),
    //Dạng dấu ấn stack, obj có dấu ấn sẽ không bị khống chế bởi các effect trong list controlEffect. mỗi lần kháng khống chế sẽ trừ 1 stack.
    moreDamagePercentHp("moreDamagePercentHp", true, BattleEffectType.moreDamagePercentHp),
    moreDotPBurnEnemy("moreDotPBurnEnemy", true),
    soulMark("soulMark", true),//Dạng dấu ấn, có cộng dồn, ko thể bị xóa và cướp, chết không bị mất. Tăng obj num% ST Nộ với mỗi tầng đang có
    soulMarkPEnemy("soulMarkPEnemy", true, BattleEffectType.soulMarkPEnemy),//Dạng dấn ấn, khi obj chạy normal skill, tăng cho obj num stack soulMark với mỗi mục tiêu bị đánh
    soulMarkPower("soulMarkPower", true),
    takeDmgByAllyBeAttacked("takeDmgByAllyBeAttacked", true, BattleEffectType.takeDmgByAllyBeAttacked),
    hurtPierceArmor("hurtPierceArmor"),
    energyKillEnemy("energyKillEnemy", true),
    decDmgByEnergy("decDmgByEnergy", true),
    trueAtkByEnergy("trueAtkByEnergy", true),
    shieldByPoisonStack("shieldByPoisonStack", true),
    moreDotTurn("moreDotTurn", true),
    poisonous("poisonous", true, BattleEffectType.poisonous),
    accShield("accShield", true),
    moreDmgAccEnemy("moreDmgAccEnemy", true, BattleEffectType.moreDmgAccEnemy),
    atkPSpd("atkPSpd", true), //buff, Tăng num% Công với mỗi 1 Tốc Độ
    meimeiMark("meimeiMark", true), //Tăng num% atk, num1% trueAtk
    moreDmgLowHp("moreDmgLowHp", true, BattleEffectType.moreDmgLowHp),//dấu ấn lên obj, obj tăng num% ST lên mục tiêu dưới num1% máu tối đa
    moreDmgBySpd("moreDmgBySpd", true, BattleEffectType.moreDmgBySpd),
    weaken("weaken", true, BattleEffectType.weaken), // dấu ấn lên obj theo dạng stack có cộng dồn, mỗi stack giảm num% sát thương obj gây ra và tăng num1% sát thương obj nhận vào, tối đa num2 stack"
    debuffByDot3("debuffByDot3", true),
    bonusHurtIfKillEnemy("bonusHurtIfKillEnemy", true, BattleEffectType.bonusHurtIfKillEnemy),
    atkPForever("atkPForever", true),
    reduceSingleAttack("reduceSingleAttack", true), //giảm num% sát thương đơn mục tiêu nhận vào (dùng đc ở cả trigger 23),
    hurtSoulMarkPEnemy("hurtSoulMarkPEnemy"),
    unableBeTarget("unableBeTarget", true, BattleEffectType.unableBeTarget), //Không thể bị chọn làm mục tiêu khi có đồng đội còn sống.
    immuneHurtPercentHp("immuneHurtPercentHp", true, BattleEffectType.immuneHurtPercentHp), //Khi có dấu ấn này, không phải nhận sát thương từ các hiệu ứng hurtPercentHp, hurtSelfPercentLostHP
    debuffTakeMoreDmg("debuffTakeMoreDmg", true, BattleEffectType.debuffTakeMoreDmg),
    dmgAndDot("dmgAndDot"),
    buffAtkByAtkEnemy("buffAtkByAtkEnemy", true),
    //tăng Atk (tăng cả chỉ số input) bằng num% tổng input Atk của toàn bộ kẻ địch (dùng để tăng ngầm dmg của boss)
    //VD: tổng atk kẻ địch bằng 1500, num = 500 -> base Atk của obj = 750
    hurtByTeamAtk("hurtByTeamAtk"),
    buffClanBoss("buffClanBoss", true, BattleEffectType.buffClanBoss),
    buffFire("buffFire", true),
    buffWind("buffWind", true),
    buffWater("buffWater", true),
    buffLight("buffLight", true),
    buffDark("buffDark", true),
    excited3("excited3", true, BattleEffectType.excited3),
    buffType("buffType", true, BattleEffectType.buffType),
    hurtByDmgSuffered("hurtByDmgSuffered"),
    chosoMark("chosoMark", true),
    limitChosoMark("limitChosoMark", true, BattleEffectType.limitChosoMark),
    takeMoreDmgByFaction("takeMoreDmgByFaction", true, BattleEffectType.takeMoreDmgByFaction),
    critBlock("critBlock", true),
    hurtToji("hurtToji"),
    tojiMark("tojiMark", true),
    kashimoMark("kashimoMark", true),
    hurtBonusDmgToKashimoMark("hurtBonusDmgToKashimoMark"),
    stealGoodEffect("stealGoodEffect", true),
    regenerative("regenerative", true, BattleEffectType.regenerative),
    critDamageReduce("critDamageReduce", true),
    basicAttack("basicAttack", true),
    basicAttackReduce("basicAttackReduce", true),
    activeAttack("activeAttack", true),
    activeAttackReduce("activeAttackReduce", true),
    healOverToShield("healOverToShield", true),
    teemoMark("teemoMark", true, BattleEffectType.teemoMark),
    hanaMark("hanaMark", true, BattleEffectType.hanaMark),
    ;
    public String value;
    public boolean isBuffEffect;
    //Các effect cần code riêng thì đừng khai báo biến này
    public BattleEffectType easyBattleEffect;

    SkillEffectType(String value) {
        this.value = value;
        this.isBuffEffect = false;
        this.easyBattleEffect = null;
    }

    SkillEffectType(String value, boolean buffEffect) {
        this.value = value;
        this.isBuffEffect = buffEffect;
        this.easyBattleEffect = null;
    }

    SkillEffectType(String value, boolean buffEffect, BattleEffectType easyBattleEffect) {
        this.value = value;
        this.isBuffEffect = buffEffect;
        this.easyBattleEffect = easyBattleEffect;
    }

    public static Map<String, List<SkillEffectType>> mBuffByClass = new HashMap<>();
    public static Map<SkillEffectType, BattleEffectType> lookupBattleEffect = new HashMap<>();
    public static Map<SkillEffectType, BattleEffectType> lookupIncPointBattleEffect = new HashMap<>();
    public static Map<SkillEffectType, BattleEffectType> lookupDecPointBattleEffect = new HashMap<>();

    public static Map<String, Integer> mEffectPointIndexByClass = new HashMap<String, Integer>() {{
        put(WARRIOR_ARMP.value, Point.ARMOR);
        put(RANGER_ATKP.value, Point.ATTACK);
    }};

    public static Map<String, Integer> mEffectPointIndex = new HashMap<String, Integer>() {{
        put(ATTACK_PER_LEVEL.value, Point.ATTACK);
        put(atkPForever.value, Point.ATTACK);
        put(atkPSpd.value, Point.ATTACK);
        put(ATTACK_PER_LEVEL_NUMBER_CONTROL_ENEMY.value, Point.ATTACK);
        put(atkP2.value, Point.ATTACK);
        put(ARMOR_PER_LEVEL.value, Point.ARMOR);
        put(HP_PER_LEVEL.value, Point.HP);
        put(SPEED.value, Point.SPEED);
        put(REDUCE_DAMAGE.value, Point.REDUCE_DAMAGE);
        put(ARMOR_BREAK.value, Point.ARMOR_BREAK);
        put(ARMOR_BREAK1.value, Point.ARMOR_BREAK);
        put(ANTI_ARMOR_BREAK.value, Point.ANTI_ARMOR_BREAK);
        put(CRIT.value, Point.CRIT);
        put(ANGER.value, Point.ANGER);
        put(SKILL_DAMAGE.value, Point.SKILL_DAMAGE);
        put(GAIN_IMMUNE_CONTROL.value, Point.CONTROL_IMMUNE);
        put(CRIT_TIME.value, Point.CRIT_DAMAGE);
        put(ANTI_IMMUNE_CONTROL.value, Point.ANTI_CONTROL_IMMUNE);
        put(PRECISION.value, Point.PRECISION);
        put(HOLY_DAMAGE.value, Point.HOLY_DAMAGE);
        put(trueAtkByEnergy.value, Point.HOLY_DAMAGE);
        put(DODGE_BLOCK.value, Point.BLOCK);
        put(critBlock.value, Point.critBlock);
        put(basicAttack.value, Point.basicAttack);
        put(basicAttackReduce.value, Point.basicAttackReduce);
        put(activeAttack.value, Point.activeAttack);
        put(activeAttackReduce.value, Point.activeAttackReduce);
        //
        put(ATTACK_PER_LEVEL_POS.value, Point.ATTACK);
        put(CRIT_POS.value, Point.CRIT);
        put(HP_PER_LEVEL_POS.value, Point.HP);
        put(PRECISION_POS.value, Point.PRECISION);
        put(ARMOR_PER_LEVEL_POS.value, Point.ARMOR);
        put(DODGE_BLOCK_POS.value, Point.BLOCK);
        put(tojiMark.value, Point.ANTI_ARMOR_BREAK);
        put(kashimoMark.value, Point.SPEC_MORE_HEAL);

        //
        put(takeMoreDmgFromAbyss.value, Point.SPEC_TAKE_MORE_DAMAGE_FROM_FACTION_ABYSS);
        put(takeMoreDmgFromForest.value, Point.SPEC_TAKE_MORE_DAMAGE_FROM_FACTION_FOREST);
        put(takeMoreDmgFromDark.value, Point.SPEC_TAKE_MORE_DAMAGE_FROM_FACTION_DARK);
        put(takeMoreDmgFromLight.value, Point.SPEC_TAKE_MORE_DAMAGE_FROM_FACTION_LIGHT);

        put(ASSASSIN.value, Point.SPEC_ADD_DAMAGE_AGAINST_ASSASIN);
        put(MAGE.value, Point.SPEC_ADD_DAMAGE_AGAINST_MAGE);
        put(PRIEST.value, Point.SPEC_ADD_DAMAGE_AGAINST_PRIEST);
        put(RANGER.value, Point.SPEC_ADD_DAMAGE_AGAINST_RANGER);
        put(WARRIOR.value, Point.SPEC_ADD_DAMAGE_AGAINST_WARRIOR);
        put(IMMUNE_SILENCE.value, Point.SPEC_IMMUNE_SILENCE);
        put(IMMUNE_FEAR.value, Point.SPEC_IMMUNE_FEAR);
        put(IMMUNE_STONE.value, Point.SPEC_IMMUNE_STONE);
        put(IMMUNE_STUN.value, Point.SPEC_IMMUNE_STUN);
        put(IMMUNE_FREEZE.value, Point.SPEC_IMMUNE_FREEZE);
        put(DOT_FIRE_EXTRA_DAMAGE.value, Point.SPEC_ADD_DAMAGE_AGAINST_BURN_FOE);
        put(DOT_POISON_EXTRA_DAMAGE.value, Point.SPEC_ADD_DAMAGE_AGAINST_POISON_FOE);
        put(DOT_BLOOD_EXTRA_DAMAGE.value, Point.SPEC_ADD_DAMAGE_AGAINST_BLOOD_FOE);
        put(EXTRA_DAMAGE_IF_STUN.value, Point.SPEC_ADD_DAMAGE_AGAINST_STUN_FOE);
        put(EXTRA_DAMAGE_IF_FEAR.value, Point.SPEC_ADD_DAMAGE_AGAINST_FEAR_FOE);
        put(EXTRA_DAMAGE_IF_FORBID.value, Point.SPEC_ADD_DAMAGE_AGAINST_FORBID_FOE);
        put(EXTRA_DAMAGE_IF_STONE.value, Point.SPEC_ADD_DAMAGE_AGAINST_STONE_FOE);
        put(EXTRA_DAMAGE_IF_FREEZE.value, Point.SPEC_ADD_DAMAGE_AGAINST_FREEZE_FOE);
        put(DEATH_MARK_B.value, Point.SPEC_ADD_DAMAGE_AGAINST_DEATH_MARK);
        put(DEATH_MARK_B1.value, Point.SPEC_ADD_DAMAGE_AGAINST_DEATH_MARK1);
        put(STONE_ATK_EXACT_DAMAGE.value, Point.SPEC_ADD_DAMAGE_AGAINST_STONE_FOE);
        //
        put(POINT_CRIT_TIME_REDUCE.value, Point.SPEC_CRIT_TIME_REDUCE);
        put(POINT_CRIT_TIME_REDUCE2.value, Point.SPEC_CRIT_TIME_REDUCE2);
        put(POINT_ATTACK_MORE_DAMAGE_IF_HIGH_HP.value, Point.SPEC_ATTACK_MORE_DAMAGE_IF_HIGH_HP);
        put(POINT_MORE_HEAL.value, Point.SPEC_MORE_HEAL);
        put(POINT_HEAL_LOST_HP_PER_ROUND.value, Point.SPEC_HEAL_LOST_HP_PER_ROUND);
        put(POINT_BUFF_ATK_PER_ROUND_ALIVE_HERO.value, Point.SPEC_BUFF_ATK_PER_ROUND_ALIVE_HERO);
        put(POINT_HEAL_BY_CRIT_ATTACK.value, Point.SPEC_HEAL_BY_CRIT_ATTACK);
        put(POINT_MORE_DAMAGE_NOT_CRIT.value, Point.SPEC_MORE_DAMAGE_NOT_CRIT);
        put(POINT_REDUCE_DAMAGE_A.value, Point.SPEC_REDUCE_DAMAGE_A);
        //
        put(MORE_TURN_AFTER_ACTIVE_SKILL.value, Point.SPEC_CHANCE_MORE_TURN_AFTER_ACTIVE_SKILL);
        put(DODGE.value, Point.DODGE);
        put(BUFF_MAX_HP.value, Point.HP);
        put(DEBUFF_BLOCKER.value, Point.SPEC_DEBUFF_BLOCK);
        put(breakImmortal.value, Point.BREAK_IMMORTAL);
        put(ANTI_CRIT.value, Point.ANTI_CRIT);
        put(BONUS_ANGER.value, Point.SPEC_MORE_ANGER);
        put(BONUS_DAMAGE_DOT.value, Point.SPEC_MORE_DAMAGE_A);
        put(moreDotPBurnEnemy.value, Point.SPEC_MORE_DAMAGE_A);
        put(healByHurt.value, Point.SPEC_HEAL_BY_HURT);
        put(moreDot.value, Point.SPEC_ADD_DOT_DAMAGE_TAKEN);
        put(removeControlAndDotPerRound.value, Point.SPEC_REMOVE_CONTROL_DOT_PER_ROUND);
        put(finalDmg.value, Point.SPEC_MORE_FINAL_DMG);
        put(finalDecDmg.value, Point.SPEC_TAKE_LESS_FINAL_DMG);
        put(energyKillEnemy.value, Point.ANGER);
        put(decDmgByEnergy.value, Point.REDUCE_DAMAGE);
        put(reduceSingleAttack.value, Point.reduceSingleAttack);
    }};

    //region lookup
    static Map<String, SkillEffectType> lookup = new HashMap<>();

    static {
        for (SkillEffectType target : SkillEffectType.values()) lookup.put(target.value, target);

        mBuffByClass.put(HeroType.CLASS_ASSASSIN.name, Arrays.asList(ASSASSIN_ATTACK, ASSASSIN_POISON, ASSASSIN_SILENCE, ASSASSIN_STONE, ASSASSIN_STUN));
        mBuffByClass.put(HeroType.CLASS_MAGE.name, Arrays.asList(MAGE_DOT_BLOOD, MAGE_SILENCE, MAGE_STUN));
        mBuffByClass.put(HeroType.CLASS_PRIEST.name, Arrays.asList(PRIEST_STUN, PRIEST_ATTACK, PRIEST_SILENCE));
        mBuffByClass.put(HeroType.CLASS_RANGER.name, Arrays.asList(RANGER_ATKP, RANGER_ATTACK, RANGER_DOT_BLOOD, RANGER_STUN));
        mBuffByClass.put(HeroType.CLASS_WARRIOR.name, Arrays.asList(WARRIOR_ARMP, WARRIOR_ATTACK, WARRIOR_FIRE, WARRIOR_ICE, WARRIOR_SILENCE, WARRIOR_STUN));
        mBuffByClass.put("", new ArrayList<>());
        mBuffByClass.forEach((key, values) -> mBuffByClass.get("").addAll(values));

        setLookupBattleEffect(BattleEffectType.EFFECT_STUN, Arrays.asList(STUN, STUN_IF_BELOW_HP, ASSASSIN_STUN, MAGE_STUN, RANGER_STUN, WARRIOR_STUN, PRIEST_STUN));
        setLookupBattleEffect(BattleEffectType.EFFECT_SILENCE, Arrays.asList(SILENCE, ASSASSIN_SILENCE, MAGE_SILENCE, WARRIOR_SILENCE, PRIEST_SILENCE));
        setLookupBattleEffect(BattleEffectType.EFFECT_STONE, Arrays.asList(STONE, ASSASSIN_STONE));
        setLookupBattleEffect(BattleEffectType.EFFECT_FREEZE, Arrays.asList(FREEZE, WARRIOR_ICE, ICE_MARK));
        setLookupBattleEffect(BattleEffectType.EFFECT_FASCINATE, Arrays.asList(FASCINATE));
        setLookupBattleEffect(BattleEffectType.EFFECT_DEBUFF_BLOCKER, Arrays.asList(DEBUFF_BLOCKER));
        setLookupBattleEffect(BattleEffectType.EFFECT_WEAKEN, Arrays.asList(WEAKEN));
        setLookupBattleEffect(BattleEffectType.kisameMark, Arrays.asList(kisameMark));
        setLookupBattleEffect(BattleEffectType.taunt, Arrays.asList(taunt));
        setLookupBattleEffect(BattleEffectType.EFFECT_SURVIVAL, Arrays.asList(SURVIVAL));
        setLookupBattleEffect(BattleEffectType.EFFECT_ICE_MARK, Arrays.asList(ICE_MARK));
        setLookupBattleEffect(BattleEffectType.EFFECT_DIS_ARM, Arrays.asList(DIS_ARM));
        setLookupBattleEffect(BattleEffectType.EFFECT_COUNTER_MARK, Arrays.asList(COUNTER_MARK));
        setLookupBattleEffect(BattleEffectType.EFFECT_REDUCE_DAMAGE_NORMAL_ATTACK, Arrays.asList(REDUCE_DAMAGE_FROM_NORMAL_ATTACK));
        setLookupBattleEffect(BattleEffectType.EFFECT_REDUCE_DAMAGE_ACTIVE_SKILL, Arrays.asList(REDUCE_DAMAGE_FROM_ACTIVE_SKILL));
        setLookupBattleEffect(BattleEffectType.EFFECT_COUNTER_SHIELD, Arrays.asList(COUNTER_SHEILD));
        setLookupBattleEffect(BattleEffectType.EFFECT_SUY_NHUOC, Arrays.asList(SUY_NHUOC));
        setLookupBattleEffect(BattleEffectType.EFFECT_TRUY_SAT, Arrays.asList(TRUY_SAT));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL, Arrays.asList(ENERGY_SEAL));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_2, Arrays.asList(ENERGY_SEAL_2));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_3, Arrays.asList(ENERGY_SEAL_3));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_4, Arrays.asList(ENERGY_SEAL_4));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_5, Arrays.asList(ENERGY_SEAL_5));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_6, Arrays.asList(ENERGY_SEAL_6));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_7, Arrays.asList(ENERGY_SEAL_7));
        setLookupBattleEffect(BattleEffectType.EFFECT_ENERGY_SEAL_8, Arrays.asList(ENERGY_SEAL_8));
        setLookupBattleEffect(BattleEffectType.EFFECT_SHIELD_BY_MAX_HP, Arrays.asList(SHIELD_BY_MAX_HP));
        setLookupBattleEffect(BattleEffectType.EFFECT_SHIELD_BY_MAX_HP1, Arrays.asList(SHIELD_BY_MAX_HP1));
        setLookupBattleEffect(BattleEffectType.EFFECT_SHIELD_BY_MAX_HP2, Arrays.asList(SHIELD_BY_MAX_HP2));
        setLookupBattleEffect(BattleEffectType.EFFECT_SHIELD_BY_MAX_HP, Arrays.asList(SHIELD_BY_MAX_HP3));
        setLookupBattleEffect(BattleEffectType.shieldByPoisonStack, Arrays.asList(shieldByPoisonStack));
        setLookupBattleEffect(BattleEffectType.accShield, Arrays.asList(accShield));
        setLookupBattleEffect(BattleEffectType.shieldByAtk, Arrays.asList(shieldByAtk));
        setLookupBattleEffect(BattleEffectType.shieldByAtk1, Arrays.asList(shieldByAtk1));
        setLookupBattleEffect(BattleEffectType.EFFECT_HOANG_LOAN, Arrays.asList(HOANG_LOAN));
        setLookupBattleEffect(BattleEffectType.EFFECT_WATCHER_MARK_MORE_DAMAGE_TAKEN, Arrays.asList(WATCHER_MARK_MORE_DAMAGE_TAKEN));
        setLookupBattleEffect(BattleEffectType.EFFECT_FEAR, Arrays.asList(FEAR));
        setLookupBattleEffect(BattleEffectType.EFFECT_LIGHTNING_MARK_SKEREI, Arrays.asList(LIGHTNING_MARK_SKEREI));
        setLookupBattleEffect(BattleEffectType.EFFECT_CRITICAL_STRIKE_MARK, Arrays.asList(CRITICAL_STRIKE_MARK));
        setLookupBattleEffect(BattleEffectType.EFFECT_DOT_TRUE_ATK, Arrays.asList(DOT_TRUE_ATK));
        setLookupBattleEffect(BattleEffectType.EFFECT_DOT_TRUE_HP, Arrays.asList(DOT_TRUE_HP));
        setLookupBattleEffect(BattleEffectType.EFFECT_DOT_FIRE, Arrays.asList(DOT_FIRE, DOT_MAX_HP, DOT_ENERGY, dotFire1));
        setLookupBattleEffect(BattleEffectType.EFFECT_DOT_BLOOD, Arrays.asList(DOT_BLOOD, dotBlood1));
        setLookupBattleEffect(BattleEffectType.EFFECT_DOT_POISON, Arrays.asList(DOT_POISON, dotPoison1));
        setLookupBattleEffect(BattleEffectType.EFFECT_EXACT_DOT_BLOOD, Arrays.asList(DOT_BLOOD_EXACT_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_EXACT_DOT_POISON, Arrays.asList(DOT_POISON_EXACT_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_EXTRA_BURNING, Arrays.asList(DOT_FIRE_EXTRA_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_EXTRA_BLEED, Arrays.asList(DOT_BLOOD_EXTRA_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_EXTRA_POISON, Arrays.asList(DOT_POISON_EXTRA_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_ROUND_MARK, Arrays.asList(ROUND_MARK_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_ROUND_MARK2, Arrays.asList(ROUND_MARK_DAMAGE2));
        setLookupBattleEffect(BattleEffectType.EFFECT_EXTRA_STONE, Arrays.asList(STONE_ATK_EXACT_DAMAGE));
        setLookupBattleEffect(BattleEffectType.EFFECT_ONOKI_MARK, Arrays.asList(ONOKI_MARK));
        setLookupBattleEffect(BattleEffectType.EFFECT_HASHIRAMA_MARK, Arrays.asList(HASHIRAMA_MARK));
        setLookupBattleEffect(BattleEffectType.EFFECT_HASHIRAMA_REVIVAL, Arrays.asList(HASHIRAMA_REVIVAL));
        setLookupBattleEffect(BattleEffectType.BUFF_MORE_DAMAGE_ACTIVE_SKILL, Arrays.asList(MORE_DAMAGE_ACTIVE_SKILL));
        setLookupBattleEffect(BattleEffectType.SKILL_DAMAGE_NUMBER_BURN_ENEMY, Arrays.asList(SKILL_DAMAGE_NUMBER_BURN_ENEMY));
        setLookupBattleEffect(BattleEffectType.ATTACK_PER_LEVEL_NUMBER_BURN_ENEMY, Arrays.asList(ATTACK_PER_LEVEL_NUMBER_BURN_ENEMY));
        setLookupBattleEffect(BattleEffectType.atkAndBrkByDot, Arrays.asList(atkAndBrkByDot));
        setLookupBattleEffect(BattleEffectType.atkAndBrkByKashimoMark, Arrays.asList(atkAndBrkByKashimoMark));
        setLookupBattleEffect(BattleEffectType.shinobuMark, Arrays.asList(shinobuMark));
        setLookupBattleEffect(BattleEffectType.muichirouMark, Arrays.asList(muichirouMark));
        setLookupBattleEffect(BattleEffectType.bonusDmgPerDotType, Arrays.asList(bonusDmgPerDotType));
        setLookupBattleEffect(BattleEffectType.moreDotPBurnEnemy, Arrays.asList(moreDotPBurnEnemy));
        setLookupBattleEffect(BattleEffectType.soulMarkPower, Arrays.asList(soulMarkPower));
        setLookupBattleEffect(BattleEffectType.soulMark, Arrays.asList(soulMark));
        setLookupBattleEffect(BattleEffectType.meimeiMark, Arrays.asList(meimeiMark));
        setLookupBattleEffect(BattleEffectType.reduceSingleAttack, Arrays.asList(reduceSingleAttack));
        setLookupBattleEffect(BattleEffectType.buffFire, Arrays.asList(buffFire));
        setLookupBattleEffect(BattleEffectType.buffWind, Arrays.asList(buffWind));
        setLookupBattleEffect(BattleEffectType.buffWater, Arrays.asList(buffWater));
        setLookupBattleEffect(BattleEffectType.buffLight, Arrays.asList(buffLight));
        setLookupBattleEffect(BattleEffectType.buffDark, Arrays.asList(buffDark));
        setLookupBattleEffect(BattleEffectType.chosoMark, Arrays.asList(chosoMark));
        setLookupBattleEffect(BattleEffectType.tojiMark, Arrays.asList(tojiMark));
        setLookupBattleEffect(BattleEffectType.kashimoMark, Arrays.asList(kashimoMark));
        setLookupBattleEffect(BattleEffectType.stealGoodEffect, Arrays.asList(stealGoodEffect));
        setLookupBattleEffect(BattleEffectType.healOverToShield, Arrays.asList(healOverToShield));
        //
        setLookupPointBattleEffect(BattleEffectType.BUFF_ATTACK_INC, BattleEffectType.BUFF_ATTACK_DEC,
                Arrays.asList(ATTACK_PER_LEVEL, atkPSpd, ATTACK_PER_LEVEL_NUMBER_CONTROL_ENEMY, atkP2, ATTACK_PER_LEVEL_POS));
        setLookupPointBattleEffect(BattleEffectType.BUFF_HOLY_DAMAGE_INC, BattleEffectType.BUFF_HOLY_DAMAGE_DEC, Arrays.asList(HOLY_DAMAGE, trueAtkByEnergy));
        setLookupPointBattleEffect(BattleEffectType.BUFF_MORE_HEAL_INC, BattleEffectType.BUFF_MORE_HEAL_DEC, Arrays.asList(POINT_MORE_HEAL));
        setLookupPointBattleEffect(BattleEffectType.BUFF_SKILL_DAMAGE_INC, BattleEffectType.BUFF_SKILL_DAMAGE_DEC, Arrays.asList(SKILL_DAMAGE, sklPByEnemyLive));
        setLookupPointBattleEffect(BattleEffectType.BUFF_ARMOR_INC, BattleEffectType.BUFF_ARMOR_DEC, Arrays.asList(ARMOR_PER_LEVEL, ARMOR_PER_LEVEL_POS));
        setLookupPointBattleEffect(BattleEffectType.BUFF_SPEED_INC, BattleEffectType.BUFF_SPEED_DEC, Arrays.asList(SPEED, SPD_PER_STUN_ENEMY, SPD_PER_STONE_ENEMY));
        setLookupPointBattleEffect(BattleEffectType.BUFF_REDUCE_DAMAGE_INC, BattleEffectType.BUFF_REDUCE_DAMAGE_DEC, Arrays.asList(REDUCE_DAMAGE, decDmgByEnergy));
        setLookupPointBattleEffect(BattleEffectType.BUFF_ARMOR_BREAK_INC, BattleEffectType.BUFF_ARMOR_BREAK_DEC, Arrays.asList(ARMOR_BREAK));
        setLookupPointBattleEffect(BattleEffectType.BUFF_ARMOR_BREAK_INC1, BattleEffectType.BUFF_ARMOR_BREAK_DEC1, Arrays.asList(ARMOR_BREAK1));
        setLookupPointBattleEffect(BattleEffectType.BUFF_ANTI_ARMOR_BREAK_INC, BattleEffectType.BUFF_ANTI_ARMOR_BREAK_DEC, Arrays.asList(ANTI_ARMOR_BREAK));
        setLookupPointBattleEffect(BattleEffectType.BUFF_CRIT_INC, BattleEffectType.BUFF_CRIT_DEC, Arrays.asList(CRIT, CRIT_POS));
        setLookupPointBattleEffect(BattleEffectType.BUFF_CRIT_DAMAGE_INC, BattleEffectType.BUFF_CRIT_DAMAGE_DEC, Arrays.asList(CRIT_TIME));
        setLookupPointBattleEffect(BattleEffectType.BUFF_ANGER_INC, BattleEffectType.BUFF_ANGER_DEC, Arrays.asList(ANGER));
        setLookupPointBattleEffect(BattleEffectType.BUFF_CONTROL_IMMUNE_INC, BattleEffectType.BUFF_CONTROL_IMMUNE_DEC, Arrays.asList(GAIN_IMMUNE_CONTROL));
        setLookupPointBattleEffect(BattleEffectType.BUFF_ANTI_CONTROL_IMMUNE_INC, BattleEffectType.BUFF_ANTI_CONTROL_IMMUNE_DEC, Arrays.asList(ANTI_IMMUNE_CONTROL));
        setLookupPointBattleEffect(BattleEffectType.BUFF_PRECISION_INC, BattleEffectType.BUFF_PRECISION_DEC, Arrays.asList(PRECISION, PRECISION_POS));
        setLookupPointBattleEffect(BattleEffectType.BUFF_BLOCK_INC, BattleEffectType.BUFF_BLOCK_DEC, Arrays.asList(DODGE_BLOCK, DODGE_BLOCK_POS));
        setLookupPointBattleEffect(BattleEffectType.buffDamageAoeInc, BattleEffectType.buffDamageAoeDec, Arrays.asList(buffDamageAoe));
        setLookupPointBattleEffect(BattleEffectType.buffSingleAttackInc, BattleEffectType.buffSingleAttackDec, Arrays.asList(buffSingleAttack));
        setLookupPointBattleEffect(BattleEffectType.atkPForeverInc, BattleEffectType.atkPForeverDec, Arrays.asList(atkPForever));
        setLookupPointBattleEffect(BattleEffectType.critBlockInc, BattleEffectType.critBlockDec, Arrays.asList(critBlock));
    }

    static void setLookupBattleEffect(BattleEffectType effect, List<SkillEffectType> skillEffects) {
        skillEffects.forEach(value -> lookupBattleEffect.put(value, effect));
    }

    static void setLookupPointBattleEffect(BattleEffectType incPointEffect, BattleEffectType decPointEffect, List<SkillEffectType> skillEffects) {
        skillEffects.forEach(skillEffect -> {
            lookupIncPointBattleEffect.put(skillEffect, incPointEffect);
            lookupDecPointBattleEffect.put(skillEffect, decPointEffect);
        });
    }

    public static SkillEffectType get(String value) {
        return lookup.get(value);
    }

    public static BattleEffectType getBattleEffect(String effectType, float num) {
        SkillEffectType skillEffectType = get(effectType);
        if (skillEffectType == null) return null;

        return getBattleEffect(skillEffectType, num);
    }

    public static BattleEffectType getBattleEffect(SkillEffectType skillEffectType, float num) {
        if (lookupBattleEffect.get(skillEffectType) != null) return lookupBattleEffect.get(skillEffectType);

        return num > 0 ? lookupIncPointBattleEffect.get(skillEffectType) : lookupDecPointBattleEffect.get(skillEffectType);
    }

    public static Integer buffPointIndex(String value, String clazz) {
        if (mEffectPointIndex.containsKey(value)) return mEffectPointIndex.get(value);
        if (mBuffByClass.get(clazz).contains(lookup.get(value))) {
            return mEffectPointIndexByClass.get(value);
        }
        return null;
    }
    //endregion


}

