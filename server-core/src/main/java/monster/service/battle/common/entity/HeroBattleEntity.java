package monster.service.battle.common.entity;

import monster.config.CfgBattle;
import monster.config.CfgServer;
import monster.dao.mapping.main.ResBattleEffectEntity;
import monster.dao.mapping.main.ResHeroEntity;
import monster.protocol.pbentity.PbJBattleHero;
import monster.server.Constans;
import monster.service.battle.common.AMode;
import monster.service.battle.common.config.BattleEffectType;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.config.SkillEffectType;
import monster.service.battle.common.config.TriggerType;
import monster.service.battle.common.entity.proto.AttackInfoEntity;
import monster.service.battle.common.entity.proto.EffectEntity;
import monster.service.battle.common.entity.proto.TargetEntity;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.BattleHeroAnalysis;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.monitor.Telegram;
import monster.service.resource.ResHero;
import protocol.Pbmethod;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static monster.service.battle.dependence.BattleConfig.HP_END_ROUND;
import static monster.service.battle.dependence.BattleConfig.STACK_COUNTDOWN_ROUND;

public class HeroBattleEntity {

    //region Variables
    public List<HeroBattleEntity> myTeam, oppTeam;
    public HeroBattleEntity myHeroBreath, oppHeroBreath;
    public long id;
    public int position, team, level, tier, star, skin, levelCalculate, voidLevel;
    public Point point = new Point(), startBattlePoint = new Point();
    public AMode mode;
    public boolean roundAttack = false, isBoss = false;
    public int bossType = 0;
    public HeroType faction, clazz;
    public int heroId;
    public SkillEntity activeSkill, normalSkill;
    public List<SkillEntity> passiveSkills;
    public float buffSkillDamage = 0;
    public long damageBlock, stealHp = 0;
    public int immortal = 0, immortal2 = 0, immortalByOppTurn = 0, numberKill = 0, numberFireMarkActivated = 0,
            summonedByPosition = -1, roundBeSummoned = -1, rateToTriggerNormalSkill = 0, numberTrigger59 = 0, numberHanaMark = 0;
    public EffectController effectController;
    public boolean isAlreadyGotStunEffect = false, isSecondaryHero = false, needRemoveAndTriggerPreHopeMark1 = false, isFoughtBack = false,
            needToProcessSkillEndTurn = false, isHopeMark2 = false, needRemoveRegenerative = false, isBlockIncreaseAngerFromAttack = false;
    public long turnDamageHp; // tổng damage hp nhận được trong 1 lượt hiệu ứng
    public List<Integer> disablePassiveSkill = new ArrayList<>();
    public List<Integer> listSkillNeedToProcess = new ArrayList<>();
    public long damageTakenByTurn = 0, causedDamageInTurn = 0, lastDamageTaken = 0, causedDamageInRound = 0;
    private List<BattleHeroAnalysis> listHeroAnalysis = new ArrayList<>();
    //endregion

    public void init() {
        //        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
        //            SkillEntity skill = passiveSkills.get(i);
        //            if (skill != null && skill.trigger == TriggerType.IMMEDIATELY.value) {
        //                passiveSkills.remove(i);
        //            }
        //        }
    }

    public void setHeroInfo(List<Integer> eSkills) {
        ResHeroEntity resHero = ResHero.getHero(heroId);
        faction = resHero.getHeroFaction();
        clazz = resHero.getHeroClass();

        SkillEntity changeSkill = null;
        passiveSkills = resHero.getSkills(Math.max(resHero.getStar(), star), level, voidLevel, eSkills, skin);
        activeSkill = passiveSkills.get(0);
        passiveSkills.remove(0);

        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
            SkillEntity skill = passiveSkills.get(i);
            if (skill.trigger != TriggerType.IMMEDIATELY.value) continue;

            if (skill.isAddPassive()) {
                passiveSkills.remove(i);
                passiveSkills.add(ResHero.getChangeSkill1(skill.id));
            }
        }

        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
            SkillEntity skill = passiveSkills.get(i);
            if (skill.trigger != TriggerType.IMMEDIATELY.value) continue;

            if (skill.isChangeCombat1()) {
                passiveSkills.remove(i);
                changeSkill = ResHero.getChangeSkill1(skill.id);
            }
        }
        for (int i = passiveSkills.size() - 1; i >= 0; i--) {
            SkillEntity skill = passiveSkills.get(i);
            if (skill.trigger != TriggerType.IMMEDIATELY.value) continue;

            passiveSkills.remove(i);
            if (skill.isChangeCombat() && changeSkill == null) changeSkill = ResHero.getChangeSkill(skill.id);
        }

        if (changeSkill == null) changeSkill = resHero.getNormalSkill();
        normalSkill = changeSkill == null ? Constans.normalSkill : changeSkill;
    }

    public List<Integer> getListSkillId() {
        List<Integer> listSkill = new ArrayList<>();
        if (activeSkill != null) listSkill.add(activeSkill.getId());
        if (normalSkill != null) listSkill.add(normalSkill.getId());
        if (passiveSkills != null) for (SkillEntity passiveSkill : passiveSkills) {
            if (passiveSkill != null) listSkill.add(passiveSkill.getId());
        }
        return listSkill;
    }

    public void setMyTeam(List<HeroBattleEntity> aTeam) {
        this.myTeam = aTeam;
    }

    public void setOppTeam(List<HeroBattleEntity> aTeam) {
        this.oppTeam = aTeam;
    }

    public List<EffectEntity> extraDamage(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, String skillEffect, long attack, boolean isLastHit, int round, int numberHit, Set<HeroBattleEntity> listHeroNeedToActiveFireMark, boolean isLastHurtEffect) {
        return beAttack(atkInfo, atkHero, skillEffect, attack, false, false, 0, isLastHit, 0, false, round, numberHit, listHeroNeedToActiveFireMark, -1, false, false, isLastHurtEffect);
    }

    public List<EffectEntity> beAttack(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, String skillEffect, long attack, boolean isActiveTrigger, boolean isLastHit, boolean isFightingBack, int round, int numberHit, Set<HeroBattleEntity> listHeroNeedToActiveFireMark, boolean isLastHurtEffect) {
        return beAttack(atkInfo, atkHero, skillEffect, attack, isActiveTrigger, true, 0, isLastHit, 0, isFightingBack, round, numberHit, listHeroNeedToActiveFireMark, -1, false, false, isLastHurtEffect);
    }

    public List<EffectEntity> beAttack(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, String skillEffect, long attack, boolean isActiveTrigger, boolean addTarget, int forceCrit, boolean isLastHit, int percentPierceArmor, boolean isFightingBack, int round, int numberHit, Set<HeroBattleEntity> listHeroNeedToActiveFireMark, int fixTypeHp, boolean isBonusDamage, boolean isSharingDamage, boolean isLastHurtEffect) {
        if (!this.isHero()) return new ArrayList<>();

        List<EffectEntity> aBattleEffect = new ArrayList<>();
        if (!mode.currentTurn.isTurnEndRound() && mode.nextRand() < this.getDodge(atkHero)) {
            mode.addHeroDodge(this);
            if (isActiveTrigger && isLastHit) {
                if (isLastHurtEffect && !atkHero.isBlockIncreaseAngerFromAttack) {
                    addAnger(10);
                    aBattleEffect.add(getResultEffectAnger(null, 10, false));
                }
                if (!atkInfo.isPassiveSkill()) mode.addHeroBeAttack(this);
                if (atkInfo.isActiveSkill()) mode.addHeroBeSkillDamage(this);
            }
            //            aBattleEffect.add(new EffectEntity(this, BattleEffectType.BUFF_HP.value, point.getHP(), -1, BattleConfig.HP_NORMAL));
            aBattleEffect.add(getResultEffectHpDec(-1, BattleConfig.HP_NORMAL, false, isFightingBack, true, 0, false, false));
            return aBattleEffect;
        }

        attack = checkHanaMarkToDecDmg(attack, numberHit);
        Object[] objects = IMath.calculateBattleDamage(mode, atkInfo, atkHero, this, attack, forceCrit, percentPierceArmor);
        boolean isCrit = (boolean) objects[0];
        boolean isBlock = (boolean) objects[1];
        boolean isKhacHe = (boolean) objects[2];
        long damageTaken = (long) objects[3];
        if (atkHero.team == 1 && atkHero.heroId == 21) {
            debug("----------------damage sau cong thuc: " + damageTaken);
        }
        int addAnger = 0;
        if (isActiveTrigger) {
            addAnger = isCrit ? 20 : 10;
            if (isCrit) {
                mode.addHeroBeCrit(this);
            }
        }
        if (!atkInfo.isPassiveSkill()) {
            if (isCrit) {
                mode.addHeroBeCrit(this);
                //                mode.addHeroWhoCrit(atkHero, this);
            }
            //            else mode.addHeroBeAttackNoCrit(this);
        }
        if (isBlock) {
            damageBlock += damageTaken;
            mode.addHeroBeBlock(this);
        }

        damageTaken = checkReduceDamage(atkInfo, damageTaken, numberHit);
        if (atkHero.team == 1 && atkHero.heroId == 21) {
            debug("----------------damage maxdamagesuffer, reduceNormal, reduceActive: " + damageTaken);
        }
        long[] damageInfo = calculateDamageToSeal(atkInfo, atkHero, damageTaken, aBattleEffect, false, isSharingDamage, false, isLastHit, isLastHurtEffect);
        damageTaken = damageInfo[0];
        long damageToSeal = damageInfo[1];
        if (atkHero.team == 1 && atkHero.heroId == 21) {
            debug("----------------damageToSeal: " + damageToSeal);
        }
        long counterDamage = damageInfo[2];
        if (addTarget) atkInfo.aTarget.add(new TargetEntity(this));
        long[] resultEffectHp = addHp(atkInfo, atkHero, -damageTaken, aBattleEffect, false, isLastHit, false, true, isSharingDamage, isLastHurtEffect);
        long effectHp = resultEffectHp[0] - damageToSeal;
        if (atkHero.team == 1 && atkHero.heroId == 21) {
            debug("----------------damage tru mau: " + resultEffectHp[0]);
        }
        if (isCrit) {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(BattleConfig.HP_CRIT, resultEffectHp[1], fixTypeHp),
                    isBlock, isFightingBack, false, resultEffectHp[2], false, isBonusDamage));
            aBattleEffect.addAll(effectController.removeBattleEffect(BattleEffectType.EFFECT_CRITICAL_STRIKE_MARK));
            addLogCrit(atkHero, effectHp);
        } else if (isKhacHe) {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(BattleConfig.HP_KHAC_HE_TANG_DAME, resultEffectHp[1], fixTypeHp),
                    isBlock, isFightingBack, false, resultEffectHp[2], false, isBonusDamage));
        } else {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(BattleConfig.HP_NORMAL, resultEffectHp[1], fixTypeHp),
                    isBlock, isFightingBack, false, resultEffectHp[2], false, isBonusDamage));
        }
        if (isActiveTrigger && isLastHit) {
            if (isLastHurtEffect && !atkHero.isBlockIncreaseAngerFromAttack) {
                addAnger(addAnger);
                aBattleEffect.add(getResultEffectAnger(atkHero, addAnger, false));
            }
            if (!atkInfo.isPassiveSkill()) mode.addHeroBeAttack(this);
            if (atkInfo.isActiveSkill()) mode.addHeroBeSkillDamage(this);
        }
        if (!isHeroAndAlive()) mode.addHeroDie(this, atkHero, aBattleEffect);

        // counter damage 1 and 2
        if (counterDamage > 0)
            aBattleEffect.addAll(atkHero.beCounterAttack(atkInfo, this, skillEffect, (int) counterDamage, isLastHit, false, round, 1, listHeroNeedToActiveFireMark, fixTypeHp, true, isSharingDamage, isLastHurtEffect));

        if (isActiveTrigger && point.getCurrentHP() > 0) {
            int bonusDamage = checkBonusBeAttack(atkHero, attack, atkInfo.isActiveSkill());
            if (bonusDamage > 0) {
                List<EffectEntity> listEffect = beAttack(atkInfo, atkHero, skillEffect, bonusDamage, false, false, IMath.CRIT_NORMAL, false, percentPierceArmor, false, round, 1, listHeroNeedToActiveFireMark, fixTypeHp, true, isSharingDamage, isLastHurtEffect);
                aBattleEffect.addAll(listEffect);
            }
        }
        if (isActiveTrigger && point.getCurrentHP() > 0) {
            int bonusDamage = checkBonusBeAttackNoCrit(atkHero, attack, atkInfo.isActiveSkill());
            if (bonusDamage > 0)
                aBattleEffect.addAll(beAttack(atkInfo, atkHero, skillEffect, bonusDamage, false, false, IMath.CRIT_FALSE, false, percentPierceArmor, false, round, 1, listHeroNeedToActiveFireMark, fixTypeHp, true, isSharingDamage, isLastHurtEffect));
        }
        if (!atkInfo.isPassiveSkill()) {
            if (isHeroAndDead() && atkHero.point.getCurrentValue(Point.SPEC_HEAL_MAX_HP_BY_KILL) > 0 && atkHero.canHeal()) { // tướng chết
                long effectHeal = atkHero.point.getMaxHp() * atkHero.point.getCurrentValue(Point.SPEC_HEAL_MAX_HP_BY_KILL) / 1000;
                long reduceHeal = atkHero.checkAndGetReduceHeal(effectHeal, atkInfo);
                effectHeal = atkHero.point.addHp(Math.max(0, effectHeal - reduceHeal));
                mode.totalHealBySingleSkillEffectCount += effectHeal;
                atkHero.addLogHeal(effectHeal);
                //                aBattleEffect.add(new EffectEntity(++mode.countEffect, atkHero, BattleEffectType.BUFF_HP_INC, atkHero.point.getHP(), newEffectHp, BattleConfig.HP_NORMAL));
            }
        }
        if (!atkInfo.isPassiveSkill() && isCrit && atkHero.point.getCurrentValue(Point.SPEC_HEAL_BY_CRIT_ATTACK) > 0 && atkHero.isHeroAndAlive() && atkHero.canHeal()) {
            long effectHeal = Math.abs(effectHp) * atkHero.point.getCurrentValue(Point.SPEC_HEAL_BY_CRIT_ATTACK) / 1000;
            long reduceHeal = atkHero.checkAndGetReduceHeal(effectHeal, atkInfo);
            effectHeal = atkHero.point.addHp(Math.max(0, effectHeal - reduceHeal));
            mode.totalHealBySingleSkillEffectCount += effectHeal;
            atkHero.addLogHeal(effectHeal);
        }

        if (atkHero.isHeroAndAlive() && atkHero.point.getCurrentValue(Point.SPEC_HUT_MAU) > 0 && atkHero.canHeal()) {
            long effectValue = Math.abs(effectHp) * atkHero.point.getCurrentValue(Point.SPEC_HUT_MAU) / 1000;
            effectValue = atkHero.point.addHp(effectValue);
            mode.totalHealBySingleSkillEffectCount += effectValue;
        }

        if (!atkInfo.isPassiveSkill() && atkHero.point.getCurrentValue(Point.SPEC_HEAL_BY_HURT) > 0 && atkHero.isHeroAndAlive() && atkHero.canHeal()) {
            long effectHeal = Math.abs(effectHp) * atkHero.point.getCurrentValue(Point.SPEC_HEAL_BY_HURT) / 1000;
            long reduceHeal = atkHero.checkAndGetReduceHeal(effectHeal, atkInfo);
            effectHeal = atkHero.point.addHp(Math.max(0, effectHeal - reduceHeal));
            atkHero.addLogHeal(effectHeal);
            mode.totalHealBySingleSkillEffectCount += effectHeal;
        }
        if (this.numberFireMarkActivated < BattleConfig.MAX_NUMBER_ACTIVE_FIRE_MARK && atkHero.team != this.team) {
            listHeroNeedToActiveFireMark.add(this);
        }

        if (isLastHit) aBattleEffect.addAll(this.effectController.removeBattleEffect(BattleEffectType.sleep));

        return aBattleEffect;
    }

    public List<EffectEntity> beCounterAttack(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, String skillEffect, long attack, boolean isLastHit, int round, int numberHit, Set<HeroBattleEntity> listHeroNeedToActiveFireMark, boolean isLastHurtEffect) {
        return beCounterAttack(atkInfo, atkHero, skillEffect, attack, isLastHit, true, round, numberHit, listHeroNeedToActiveFireMark, -1, false, false, isLastHurtEffect);
    }

    public List<EffectEntity> beCounterAttack(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, String skillEffect, long attack, boolean isLastHit, boolean addTarget, int round, int numberHit, Set<HeroBattleEntity> listHeroNeedToActiveFireMark, int fixTypeHp, boolean isBonusDamage, boolean isSharingDamage, boolean isLastHurtEffect) {
        if (!this.isHero()) return new ArrayList<>();

        List<EffectEntity> aBattleEffect = new ArrayList<>();
        if (!mode.currentTurn.isTurnEndRound() && mode.nextRand() < this.getDodge(atkHero)) {
            mode.addHeroDodge(this);
            if (addTarget) atkInfo.aTarget.add(new TargetEntity(this));
            aBattleEffect.add(getResultEffectHpDec(-1, BattleConfig.HP_NORMAL, false, true, true, 0, false, false));
            return aBattleEffect;
        }

        attack = checkHanaMarkToDecDmg(attack, numberHit);
        Object[] objects = IMath.calculateBattleDamage(mode, atkInfo, atkHero, this, attack, IMath.CRIT_FALSE, 0);
        boolean isCrit = (boolean) objects[0];
        boolean isBlock = (boolean) objects[1];
        boolean isKhacHe = (boolean) objects[2];
        long damageTaken = (long) objects[3];

        if (isBlock) {
            damageBlock += damageTaken;
            mode.addHeroBeBlock(this);
        }

        damageTaken = checkReduceDamage(atkInfo, damageTaken, numberHit);

        long[] damageInfo = calculateDamageToSeal(atkInfo, atkHero, damageTaken, aBattleEffect, false, isSharingDamage, false, isLastHit, isLastHurtEffect);
        damageTaken = damageInfo[0];
        long damageToSeal = damageInfo[1];
        long counterDamage = damageInfo[2];

        if (addTarget) atkInfo.aTarget.add(new TargetEntity(this));
        long[] resultEffectHp = addHp(atkInfo, atkHero, -damageTaken, aBattleEffect, false, isLastHit, false, true, isSharingDamage, isLastHurtEffect);
        long effectHp = resultEffectHp[0] - damageToSeal;
        if (isCrit) {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(BattleConfig.HP_CRIT, resultEffectHp[1], fixTypeHp),
                    isBlock, true, false, resultEffectHp[2], false, isBonusDamage));
            aBattleEffect.addAll(effectController.removeBattleEffect(BattleEffectType.EFFECT_CRITICAL_STRIKE_MARK));
        } else if (isKhacHe) {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(BattleConfig.HP_KHAC_HE_TANG_DAME, resultEffectHp[1], fixTypeHp),
                    isBlock, true, false, resultEffectHp[2], false, isBonusDamage));
        } else {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(BattleConfig.HP_NORMAL, resultEffectHp[1], fixTypeHp),
                    isBlock, true, false, resultEffectHp[2], false, isBonusDamage));
        }

        if (!isHeroAndAlive()) mode.addHeroDie(this, atkHero, aBattleEffect);

        // counter damage
        if (counterDamage > 0) {
            aBattleEffect.addAll(atkHero.beCounterAttack(atkInfo, this, skillEffect, (int) counterDamage, isLastHit, false, round, 1, listHeroNeedToActiveFireMark, fixTypeHp, true, isSharingDamage, isLastHurtEffect));
        }


        if (addTarget && point.getCurrentHP() > 0) {
            int bonusDamage = checkBonusBeAttack(atkHero, attack, atkInfo.isActiveSkill());
            if (bonusDamage > 0) {
                aBattleEffect.addAll(beCounterAttack(atkInfo, atkHero, skillEffect, bonusDamage, isLastHit, false, round, 1, listHeroNeedToActiveFireMark, fixTypeHp, true, isSharingDamage, isLastHurtEffect));
            }
        }
        if (addTarget && point.getCurrentHP() > 0) {
            int bonusDamage = checkBonusBeAttackNoCrit(atkHero, attack, atkInfo.isActiveSkill());
            if (bonusDamage > 0) {
                aBattleEffect.addAll(beCounterAttack(atkInfo, atkHero, skillEffect, bonusDamage, isLastHit, false, round, 1, listHeroNeedToActiveFireMark, fixTypeHp, true, isSharingDamage, isLastHurtEffect));
            }
        }

        if (this.numberFireMarkActivated < BattleConfig.MAX_NUMBER_ACTIVE_FIRE_MARK && atkHero.team != this.team) {
            listHeroNeedToActiveFireMark.add(this);
        }

        if (!atkInfo.isPassiveSkill()) {
            if (isHeroAndDead() && atkHero.point.getCurrentValue(Point.SPEC_HEAL_MAX_HP_BY_KILL) > 0 && atkHero.canHeal()) { // tướng chết
                long effectHeal = atkHero.point.getMaxHp() * atkHero.point.getCurrentValue(Point.SPEC_HEAL_MAX_HP_BY_KILL) / 1000;
                long reduceHeal = atkHero.checkAndGetReduceHeal(effectHeal, atkInfo);
                effectHeal = atkHero.point.addHp(Math.max(0, effectHeal - reduceHeal));
                mode.totalHealBySingleSkillEffectCount += effectHeal;
                atkHero.addLogHeal(effectHeal);
                //                aBattleEffect.add(new EffectEntity(++mode.countEffect, atkHero, BattleEffectType.BUFF_HP_INC, atkHero.point.getHP(), newEffectHp, BattleConfig.HP_NORMAL));
            }
        }

        if (atkHero.isHeroAndAlive() && atkHero.point.getCurrentValue(Point.SPEC_HUT_MAU) > 0 && atkHero.canHeal()) {
            long effectValue = Math.abs(effectHp) * atkHero.point.getCurrentValue(Point.SPEC_HUT_MAU) / 1000;
            effectValue = atkHero.point.addHp(effectValue);
            mode.totalHealBySingleSkillEffectCount += effectValue;
        }

        if (isLastHit) aBattleEffect.addAll(effectController.removeBattleEffect(BattleEffectType.sleep));

        return aBattleEffect;
    }

    long checkReduceDamage(AttackInfoEntity atkInfo, long damageTaken, int numberHit) {
        long newDamageTaken = damageTaken;
        if (atkInfo != null) {
            if (atkInfo.isNormalSkill() && effectController.contain(BattleEffectType.EFFECT_REDUCE_DAMAGE_NORMAL_ATTACK)) {
                long number = effectController.countValueEffect(BattleEffectType.EFFECT_REDUCE_DAMAGE_NORMAL_ATTACK);
                if (number > 0) newDamageTaken = newDamageTaken * (1000 - number) / 1000;
            }
            if (atkInfo.isActiveSkill() && effectController.contain(BattleEffectType.EFFECT_REDUCE_DAMAGE_ACTIVE_SKILL)) {
                long number = effectController.countValueEffect(BattleEffectType.EFFECT_REDUCE_DAMAGE_ACTIVE_SKILL);
                if (number > 0) newDamageTaken = newDamageTaken * (1000 - number) / 1000;
            }
        }
        return newDamageTaken > 0 ? newDamageTaken : 0;
    }

    long checkReduceDamageEndRound(int typeHp, long damageTaken) {
        if (typeHp != BattleConfig.HP_END_ROUND) return damageTaken;

        damageTaken = (long) ((damageTaken / Math.abs(damageTaken)) * Math.abs(damageTaken) * (1 + (float) effectController.countValueEffect(BattleEffectType.EFFECT_REDUCE_END_ROUND_DMG) / 1000));
        return damageTaken;
    }

    int checkBonusBeAttackNoCrit(HeroBattleEntity atkHero, long attack, boolean isActiveSkill) {
        long addAttack = 0;
        if (isActiveSkill && effectController.contain(BattleEffectType.EFFECT_LIGHTNING_MARK_SKEREI) && ResHero.HERO_SKEREI.contains(atkHero.heroId)) {
            addAttack += attack * effectController.countValueEffect(BattleEffectType.EFFECT_LIGHTNING_MARK_SKEREI) / 1000;
        }
        return (int) addAttack;
    }

    int checkBonusBeAttack(HeroBattleEntity atkHero, long attack, boolean isActiveSkill) {
        long addAttack = 0;
        if (effectController.contain(BattleEffectType.EFFECT_WATCHER_MARK_MORE_DAMAGE_TAKEN)) {
            addAttack += attack * effectController.countValueEffect(BattleEffectType.EFFECT_WATCHER_MARK_MORE_DAMAGE_TAKEN) / 1000;
        }
        if (effectController.contain(BattleEffectType.EFFECT_WEAKEN)) {
            addAttack += attack * 0.5;
        }
        if (clazz != null && faction != null) {
            addAttack += attack * (atkHero.point.getMoreDamageAgainstClassAndFaction(clazz.name, faction.name) + this.point.getTakeMoreDamageFromFaction(faction.name)) / 1000;
        } else {
            Telegram.sendError("clazz and faction null hero=" + this);
        }
        for (BattleEffectType effectType : BattleEffectType.aEffectExtra) {
            if (effectController.contain(effectType)) {
                addAttack += attack * atkHero.point.getMoreDamageAgainstEffect(effectType) / 1000;
            }
        }

        // Các hiệu ứng gây thêm dam buff trong trận
        if (effectController.contain(BattleEffectType.EFFECT_DOT_BLOOD) || effectController.contain(BattleEffectType.EFFECT_EXACT_DOT_BLOOD)) {
            addAttack += attack * atkHero.effectController.countValueEffect(BattleEffectType.EFFECT_EXTRA_BLEED) / 1000;
        }
        if (effectController.contain(BattleEffectType.EFFECT_DOT_POISON) || effectController.contain(BattleEffectType.EFFECT_EXACT_DOT_POISON)) {
            addAttack += attack * atkHero.effectController.countValueEffect(BattleEffectType.EFFECT_EXTRA_POISON) / 1000;
        }
        if (effectController.contain(BattleEffectType.EFFECT_DOT_FIRE) || effectController.contain(BattleEffectType.EFFECT_EXACT_DOT_FIRE)) {
            addAttack += attack * atkHero.effectController.countValueEffect(BattleEffectType.EFFECT_EXTRA_BURNING) / 1000;
        }
        if (effectController.contain(BattleEffectType.EFFECT_STONE) && atkHero.effectController.contain(BattleEffectType.EFFECT_EXTRA_STONE)) {
            addAttack += attack * atkHero.effectController.countValueEffect(BattleEffectType.EFFECT_EXTRA_STONE) / 1000;
        }
        return (int) addAttack;
    }

    /*
     * damage gây ra do đốt, độc chỉ xảy ra vào cuối round
     * nổ khiên không ảnh hưởng tới ai
     */
    public List<EffectEntity> beExactDamage(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, long damageTaken, boolean isLastHit, boolean isLastHurtEffect, int typeHp, int numberHit, boolean... info) {
        if (!this.isHero()) return new ArrayList<>();

        boolean hasReduceDamageA = info.length > 0 && info[0];
        List<EffectEntity> aBattleEffect = new ArrayList<>();
        if (!mode.currentTurn.isTurnEndRound() && mode.nextRand() < this.getDodge(atkHero)) {
            mode.addHeroDodge(this);
            aBattleEffect.add(getResultEffectHpDec(-1, BattleConfig.HP_NORMAL, false, true, true, 0, false, false));
            return aBattleEffect;
        }
        if (hasReduceDamageA && typeHp == BattleConfig.HP_END_ROUND && point.getCurrentValue(Point.SPEC_REDUCE_DAMAGE_A) > 0) {
            long reducePoint = Math.min(point.getCurrentValue(Point.SPEC_REDUCE_DAMAGE_A), 990);
            damageTaken -= damageTaken * reducePoint / 1000;
        }

        if (typeHp == BattleConfig.HP_END_ROUND)
            damageTaken += damageTaken * atkHero.point.getCurrentValue(Point.SPEC_MORE_DAMAGE_A) / 1000;

        long[] damageInfo = calculateDamageToSeal(atkInfo, atkHero, damageTaken, aBattleEffect, false, false, typeHp == HP_END_ROUND, isLastHit, isLastHurtEffect);
        damageTaken = damageInfo[0];
        long damageToSeal = damageInfo[1];
        long[] resultEffectHp = addHp(atkInfo, atkHero, -damageTaken, aBattleEffect, typeHp == HP_END_ROUND, isLastHit, false, true, false, isLastHurtEffect);
        long effectHp = resultEffectHp[0] - damageToSeal;
        if (effectHp != 0) {
            aBattleEffect.add(0, getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(typeHp, resultEffectHp[1], -1), false, false, false, resultEffectHp[2], false, false));
        }
        if (!isHeroAndAlive()) mode.addHeroDie(this, atkHero, aBattleEffect);
        return aBattleEffect;
    }

    // beExactDamage + maxDamageSufferByHp
    public List<EffectEntity> beExactDamage2(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, long damageTaken, int typeHp, int numberHit, boolean isSharingDmg, boolean isBonusDmg, boolean isLastHit, boolean isLastHurtEffect) {
        List<EffectEntity> aBattleEffect = new ArrayList<>();
        if (!mode.currentTurn.isTurnEndRound() && mode.nextRand() < this.getDodge(atkHero)) {
            mode.addHeroDodge(this);
            aBattleEffect.add(getResultEffectHpDec(-1, BattleConfig.HP_NORMAL, false, true, true, 0, false, false));
            return aBattleEffect;
        }
        long[] damageInfo = calculateDamageToSeal(atkInfo, atkHero, damageTaken, aBattleEffect, false, false, typeHp == HP_END_ROUND, isLastHit, isLastHurtEffect);
        damageTaken = damageInfo[0];
        long damageToSeal = damageInfo[1];
        long[] resultEffectHp = addHp(atkInfo, atkHero, -damageTaken, aBattleEffect, typeHp == HP_END_ROUND, isLastHit, false, true, isSharingDmg, isLastHurtEffect);
        long effectHp = resultEffectHp[0] - damageToSeal;
        if (effectHp != 0) {
            aBattleEffect.add(getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(typeHp, resultEffectHp[1], -1), false, false, false, resultEffectHp[2], false, isBonusDmg));
        }
        if (!isHeroAndAlive()) mode.addHeroDie(this, atkHero, aBattleEffect);
        return aBattleEffect;
    }


    /**
     * các damage đốt, độc, chảy máu không chịu ảnh hưởng của hiệu ứng maxDamageSufferByHp
     * values
     * [0]: có phải damage DOT không
     * [1]: có check heroDie không
     * [2]: có phải true damage ko?
     */
    public List<EffectEntity> beDamage(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, long attack, boolean isLastHurtEffect, int typeHp, boolean isLastHit, boolean... values) {
        if (!this.isHero()) return new ArrayList<>();

        List<EffectEntity> aBattleEffect = new ArrayList<>();
        if (!mode.currentTurn.isTurnEndRound() && mode.nextRand() < this.getDodge(atkHero)) {
            mode.addHeroDodge(this);
            aBattleEffect.add(getResultEffectHpDec(-1, BattleConfig.HP_NORMAL, false, true, true, 0, false, false));
            return aBattleEffect;
        }

        boolean isDOT = !(values.length == 0 || !values[0]);
        boolean isIgnoreShield = values.length >= 2 && values[1];
        long damageTaken = isDOT ? IMath.calculateDOTDamage(mode, atkHero, this, attack) : IMath.calculateSpecDamage(mode, atkHero, this, attack);
        //        long damageTaken = IMath.calculateSpecDamage(mode, atkHero, this, attack);

        // E skill: giảm sát thương các hiệu ứng (đốt, độc, chảy máu -> là các hiệu ứng gây damage cuối vòng)
        if (typeHp == BattleConfig.HP_END_ROUND) {
            if (point.getCurrentValue(Point.SPEC_REDUCE_DAMAGE_A) > 0) {
                long reducePoint = Math.min(point.getCurrentValue(Point.SPEC_REDUCE_DAMAGE_A), 990);
                damageTaken -= damageTaken * reducePoint / 1000;
            }
            damageTaken -= damageTaken * this.point.getCurrentValue(Point.SPEC_REDUCE_DAMAGE_DOT) / 1000;
        }

        if (typeHp == BattleConfig.HP_END_ROUND)
            damageTaken += damageTaken * (this.point.getCurrentValue(Point.SPEC_ADD_DOT_DAMAGE_TAKEN) + (atkHero != null ? atkHero.point.getCurrentValue(Point.SPEC_MORE_DAMAGE_A) : 0)) / 1000;

        long[] damageInfo = new long[3];
        if (!isIgnoreShield) {
            damageInfo = calculateDamageToSeal(atkInfo, atkHero, damageTaken, aBattleEffect, isDOT, false, typeHp == HP_END_ROUND, isLastHit, isLastHurtEffect);
            damageTaken = damageInfo[0];
        }
        long damageToShield = damageInfo[1];

        if (!isDOT) {// không phải DOT damage thì mới giảm
            damageTaken = checkReduceDamage(null, damageTaken, 1);
        }

        long[] resultEffectHp = addHp(atkInfo, atkHero, -damageTaken, aBattleEffect, typeHp == HP_END_ROUND, isLastHit, isDOT, false, false, isLastHurtEffect);
        long effectHp = resultEffectHp[0] - damageToShield;
        if (effectHp != 0) {
            EffectEntity resultEffect = getResultEffectHpDec(effectHp, BattleConfig.checkTypeHpResist(typeHp, resultEffectHp[1], -1), false, false, false, resultEffectHp[2], false, false);
            aBattleEffect.add(0, resultEffect);
        }
        if (!isHeroAndAlive() && (values.length < 2 || values[1])) mode.addHeroDie(this, atkHero, aBattleEffect);
        return aBattleEffect;
    }

    public void countBuffForAnalysis(HeroBattleEntity atkHero, BattleEffectType battleEffectType) {
        if (CfgBattle.listControlEffect.contains(battleEffectType)) atkHero.getHeroAnalysis().countControl++;
        if (CfgBattle.listGoodMark.contains(battleEffectType) || CfgBattle.listGoodEffect.contains(battleEffectType))
            atkHero.getHeroAnalysis().countGoodEffectGoodMark++;
        if (CfgBattle.listBadMark.contains(battleEffectType) || CfgBattle.listBadEffect.contains(battleEffectType))
            atkHero.getHeroAnalysis().countBadEffectBadMark++;
    }

    //region Buff Effect
    public List<EffectEntity> beBuff(AttackInfoEntity attackInfo, HeroBattleEntity atkHero, SkillEntity.SingleSkillEffect singleSkillEffect, int round) {
        if (mode.countBuffEffect++ == 5000) {
            Telegram.sendNotify("too many countBuffEffect=" + mode.countBuffEffect);
        }
        List<EffectEntity> resultEffect = new ArrayList<>();
        SkillEffectType effectType = SkillEffectType.get(singleSkillEffect.type);

        BattleEffectType easyBattleEffect = effectType.easyBattleEffect;
        if (easyBattleEffect != null) {
            if (!isBuffSuccess(atkHero, this, easyBattleEffect, singleSkillEffect.type, singleSkillEffect.ratio, singleSkillEffect.num, resultEffect))
                return resultEffect;

            EffectCount effectCount = effectController.addBattleEffect(atkHero, easyBattleEffect, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1, (long) singleSkillEffect.num2, (long) singleSkillEffect.num3, (long) singleSkillEffect.num4);
            if (effectCount != null) {
                EffectEntity effectEntity = effectController.getResultEffect(effectCount);
                resultEffect.add(effectEntity);
            }
            countBuffForAnalysis(atkHero, easyBattleEffect);
            return resultEffect;
        }

        BattleEffectType battleEffectType = getBattleEffect(effectType, singleSkillEffect.num);
        if (!isBuffSuccess(atkHero, this, battleEffectType, singleSkillEffect.type, singleSkillEffect.ratio, singleSkillEffect.num, resultEffect))
            return resultEffect;
        countBuffForAnalysis(atkHero, battleEffectType);
        switch (effectType) {
            case shieldToMaxHp: {
                long convertValue = (long) (this.getTotalShieldHp() * singleSkillEffect.num / 1000);
                long addMaxHp = convertValue;

                if (convertValue <= 0) break;
                for (BattleEffectType shieldEffect : BattleEffectType.listShieldEffect) {
                    if (!this.effectController.contain(shieldEffect)) continue;

                    EffectCount effectCount = this.effectController.get(shieldEffect);
                    for (int i = effectCount.countNumber() - 1; i >= 0; i--) {
                        long shieldHp = effectCount.getValue(i);
                        long newShieldHp = shieldHp - convertValue;
                        if (newShieldHp < 0) {
                            convertValue = Math.abs(newShieldHp);
                            effectCount.remove(i);
                            if (effectCount.countNumber() <= 0) {
                                resultEffect.addAll(this.effectController.removeBattleEffect(shieldEffect));
                                break;
                            }
                        } else {
                            convertValue = 0;
                            effectCount.setValue(i, newShieldHp);
                            break;
                        }

                        if (convertValue <= 0) break;
                    }
                }
                addMaxHp -= convertValue;
                if (addMaxHp <= 0) break;

                float percentHp = this.point.percentHpFloat();
                long newMaxHp = this.point.getMaxHp() + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - this.point.getCurrentHP();

                EffectCount effectCount = this.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_MAX_HP_INC, "hpPMax", singleSkillEffect.round, addMaxHp);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) this.point.setHPAndMaxHP(newHp, newMaxHp);
                    resultEffect.add(new EffectEntity(effectCount.id, this, BattleEffectType.BUFF_MAX_HP_INC, addHp, newHp, addMaxHp, newMaxHp));
                }
                break;
            }
            case healOverToShield: {
                if (!this.canHeal()) break;

                long effectHeal = (long) (atkHero.point.getMaxHp() * singleSkillEffect.num / 1000);
                long lostHp = Math.max(0, this.point.getMaxHp() - this.point.getCurrentHP());
                long overHeal = 0;
                boolean isShield = false;
                if (effectHeal > lostHp) {//Lượng hồi máu phụ trội
                    overHeal = effectHeal - lostHp;
                    effectHeal = lostHp;
                    isShield = true;
                }

                if (effectHeal > 0) {
                    long reduceHeal = this.checkAndGetReduceHeal(effectHeal, attackInfo);
                    long effectHp = this.point.addHp(Math.max(0, effectHeal - reduceHeal));
                    if (effectHp > 0) {
                        resultEffect.add(this.getResultEffectHpInc(atkHero, effectHp));
                        atkHero.addLogHeal(effectHp);
                    } else isShield = false;
                }

                if (isShield) {
                    long effectShield = (long) (overHeal * singleSkillEffect.num1 / 1000);
                    if (effectShield > 0) {
                        EffectCount effectCount = this.effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectShield);
                        if (effectCount != null) {
                            EffectEntity effectEntity = this.effectController.getResultEffect(effectCount);
                            resultEffect.add(effectEntity);
                        }
                    }
                }

                break;
            }
            case stealGoodEffect: {
                int numberSteal = (int) singleSkillEffect.num;
                while (numberSteal > 0) {
                    List<BattleEffectType> listBattleEffectType = CfgBattle.listGoodEffect.stream().filter(goodEffect -> this.effectController.contain(goodEffect)).collect(Collectors.toList());
                    if (listBattleEffectType.isEmpty()) break;

                    for (BattleEffectType goodEffect : listBattleEffectType) {
                        EffectCount stealEffectCount = this.effectController.get(goodEffect);
                        int randomIndex = mode.nextRand() % stealEffectCount.countNumber();
                        EffectCount newEffectCount = atkHero.effectController.addBattleEffect(stealEffectCount.atkHero, stealEffectCount.type, stealEffectCount.skillType,
                                stealEffectCount.getValue(randomIndex), stealEffectCount.getValue2(randomIndex), stealEffectCount.getValue3(randomIndex), stealEffectCount.getValue4(randomIndex));
                        Integer pointIndex = SkillEffectType.buffPointIndex(stealEffectCount.skillType, atkHero.clazz == null ? "" : atkHero.clazz.name);
                        if (newEffectCount != null) {
                            if (pointIndex != null && newEffectCount.needToChangeHeroPoint()) {
                                atkHero.point.add(Point.CURRENT_VALUES_INDEX, pointIndex, stealEffectCount.getValue(randomIndex));
                            }
                            resultEffect.add(atkHero.effectController.getResultEffect(newEffectCount));
                        }
                        if (pointIndex != null) {
                            this.effectController.recalculateHeroPoint(stealEffectCount, randomIndex, false);
                        }
                        stealEffectCount.remove(randomIndex);
                        if (stealEffectCount.countNumber() <= 0)
                            resultEffect.addAll(this.effectController.removeBattleEffect(stealEffectCount.type));
                        resultEffect.add(this.effectController.getResultEffect(stealEffectCount));
                        if (--numberSteal <= 0) break;
                    }
                }

                break;
            }
            case chosoMark: {
                if (this.effectController.contain(battleEffectType)) {
                    EffectCount effectCount = this.effectController.get(battleEffectType);
                    long limitStack = effectCount.getValue1(0) + this.effectController.countValueEffect(BattleEffectType.limitChosoMark);
                    if (effectCount.countNumber() >= limitStack) break;
                }

                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1, (long) singleSkillEffect.num2, (long) singleSkillEffect.num3, (long) singleSkillEffect.num4);
                if (effectCount != null) {
                    EffectEntity effectEntity = effectController.getResultEffect(effectCount);
                    resultEffect.add(effectEntity);

                    checkChosoMark(this, effectCount, resultEffect);
                }

                break;
            }
            case buffFire: {
                long addAttack = this.point.addAttack(singleSkillEffect.num / 1000);

                float percentHp = this.point.percentHpFloat();
                long addMaxHp = (long) (this.point.getCalculatedValue(Point.HP) * singleSkillEffect.num1 / 1000);
                long newMaxHp = this.point.getCalculatedValue(Point.HP) + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - this.point.getCurrentHP();

                long addFinalDmg = (long) singleSkillEffect.num2;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, addAttack, addMaxHp, addFinalDmg);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) {
                        this.point.addAttack(-addAttack);
                    } else {
                        this.point.setHPAndMaxHP(newHp, newMaxHp);
                        this.point.add(Point.CURRENT_VALUES_INDEX, Point.SPEC_MORE_FINAL_DMG, addFinalDmg);
                        resultEffect.add(new EffectEntity(effectCount.id, this, battleEffectType, addHp, newHp, addMaxHp, newMaxHp));
                    }
                }
                break;
            }
            case buffWind: {
                long addCrit = this.point.addCrit((long) singleSkillEffect.num);

                float percentHp = this.point.percentHpFloat();
                long addMaxHp = (long) (this.point.getCalculatedValue(Point.HP) * singleSkillEffect.num1 / 1000);
                long newMaxHp = this.point.getCalculatedValue(Point.HP) + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - this.point.getCurrentHP();

                long addFinalDmg = (long) singleSkillEffect.num2;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, addCrit, addMaxHp, addFinalDmg);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) {
                        this.point.addCrit(-addCrit);
                    } else {
                        this.point.setHPAndMaxHP(newHp, newMaxHp);
                        this.point.add(Point.CURRENT_VALUES_INDEX, Point.SPEC_MORE_FINAL_DMG, addFinalDmg);
                        resultEffect.add(new EffectEntity(effectCount.id, this, battleEffectType, addHp, newHp, addMaxHp, newMaxHp));
                    }
                }
                break;
            }
            case buffWater: {
                long addArmorBreak = this.point.addArmorBreak((long) singleSkillEffect.num);

                float percentHp = this.point.percentHpFloat();
                long addMaxHp = (long) (this.point.getCalculatedValue(Point.HP) * singleSkillEffect.num1 / 1000);
                long newMaxHp = this.point.getCalculatedValue(Point.HP) + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - this.point.getCurrentHP();

                long addFinalDmg = (long) singleSkillEffect.num2;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, addArmorBreak, addMaxHp, addFinalDmg);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) {
                        this.point.addArmorBreak(-addArmorBreak);
                    } else {
                        this.point.setHPAndMaxHP(newHp, newMaxHp);
                        this.point.add(Point.CURRENT_VALUES_INDEX, Point.SPEC_MORE_FINAL_DMG, addFinalDmg);
                        resultEffect.add(new EffectEntity(effectCount.id, this, battleEffectType, addHp, newHp, addMaxHp, newMaxHp));
                    }
                }
                break;
            }
            case buffLight: {
                long addHolyDamage = this.point.addHolyDamage((long) singleSkillEffect.num);

                float percentHp = this.point.percentHpFloat();
                long addMaxHp = (long) (this.point.getCalculatedValue(Point.HP) * singleSkillEffect.num1 / 1000);
                long newMaxHp = this.point.getCalculatedValue(Point.HP) + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - this.point.getCurrentHP();

                long addFinalDmg = (long) singleSkillEffect.num2;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, addHolyDamage, addMaxHp, addFinalDmg);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) {
                        this.point.addHolyDamage(-addHolyDamage);
                    } else {
                        this.point.setHPAndMaxHP(newHp, newMaxHp);
                        this.point.add(Point.CURRENT_VALUES_INDEX, Point.SPEC_MORE_FINAL_DMG, addFinalDmg);
                        resultEffect.add(new EffectEntity(effectCount.id, this, battleEffectType, addHp, newHp, addMaxHp, newMaxHp));
                    }
                }
                break;
            }
            case buffDark: {
                long addReduceDamage = this.point.addReduceDamage((long) singleSkillEffect.num);

                float percentHp = this.point.percentHpFloat();
                long addMaxHp = (long) (this.point.getCalculatedValue(Point.HP) * singleSkillEffect.num1 / 1000);
                long newMaxHp = this.point.getCalculatedValue(Point.HP) + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - this.point.getCurrentHP();

                long addFinalDmg = (long) singleSkillEffect.num2;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, addReduceDamage, addMaxHp, addFinalDmg);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) {
                        this.point.addReduceDamage(-addReduceDamage);
                    } else {
                        this.point.setHPAndMaxHP(newHp, newMaxHp);
                        this.point.add(Point.CURRENT_VALUES_INDEX, Point.SPEC_MORE_FINAL_DMG, addFinalDmg);
                        resultEffect.add(new EffectEntity(effectCount.id, this, battleEffectType, addHp, newHp, addMaxHp, newMaxHp));
                    }
                }
                break;
            }
            case buffDamageAoe: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case buffAtkByAtkEnemy: {
                long effectValue = (long) (this.oppTeam.stream().filter(HeroBattleEntity::isHeroAndAlive).mapToLong(oppHero -> oppHero.point.getCalculatedValue(Point.ATTACK)).sum()
                        * singleSkillEffect.num / 1000);
                this.point.addAttack(effectValue);
                this.point.setCalculatedValue(Point.ATTACK, this.point.getRealCurrentAttack());
                break;
            }
            case debuffByDot3: {
                if (!this.effectController.contain(BattleEffectType.EFFECT_DOT_POISON)
                        && !this.effectController.contain(BattleEffectType.EFFECT_EXACT_DOT_POISON))
                    break;

                long effectValue = (long) (this.point.getCalculatedValue(Point.ATTACK) * -Math.abs(singleSkillEffect.num) / 1000);
                effectValue = this.point.addAttack(effectValue);
                EffectCount effectCount = this.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ATTACK_DEC, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                resultEffect.add(this.effectController.getResultEffect(effectCount));
                break;
            }
            case meimeiMark: {
                long addAttack = (long) singleSkillEffect.num * this.point.getCalculatedValue(Point.ATTACK) / 1000;
                long addHolyDmg = (long) singleSkillEffect.num1;
                EffectCount effectCount = this.effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, addAttack, addHolyDmg);
                if (effectCount != null) {
                    this.point.addAttack(addAttack);
                    this.point.addHolyDamage(addHolyDmg);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case moreDotTurn: {
                CfgBattle.listDot3.forEach(tmpBattleEffectType -> {
                    EffectCount effectCount = this.effectController.get(tmpBattleEffectType);
                    if (effectCount == null) return;

                    effectCount.addRound((int) singleSkillEffect.num);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                });
                break;
            }
            case soulMark: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) {
                    this.point.addSkillDamage((long) singleSkillEffect.num);
                    this.checkSoulMark();
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case soulMarkPower: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round);
                if (effectCount != null) {
                    effectCount.listRequire = singleSkillEffect.listRequire;
                    effectCount.listPointIndex = singleSkillEffect.listPointIndex;
                    effectCount.listPointValue = singleSkillEffect.listPointValue;
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case healByMaxHP: {
                if (atkHero.point.getCurrentHP() > 0 && this.point.getCurrentHP() > 0) {
                    long effectValue = this.point.addHp((long) (singleSkillEffect.num * atkHero.point.getCalculatedValue(Point.HP) / 1000));
                    resultEffect.add(this.getResultEffectHpInc(atkHero, effectValue));
                    atkHero.addLogHeal(effectValue);
                }
                break;
            }
            case removeAccPDotEnemy: {
                if (mode.nextRand() >= singleSkillEffect.num) break;

                int numberOppHeroHasDot3 = (int) atkHero.oppTeam.stream().filter(oppHero -> {
                    if (!oppHero.isHeroAndAlive()) return false;
                    for (BattleEffectType dot3Effect : CfgBattle.listDot3) {
                        if (oppHero.effectController.contain(dot3Effect)) return true;
                    }

                    return false;
                }).count();
                if (numberOppHeroHasDot3 == 0) break;

                resultEffect.addAll(effectController.removeNumberEffectFromList(CfgBattle.listControlEffect, (int) singleSkillEffect.num1 * numberOppHeroHasDot3));
                long effectAttack = (long) (numberOppHeroHasDot3 * singleSkillEffect.num2 * this.point.getCalculatedValue(Point.ATTACK) / 1000);
                long effectArmor = (long) (numberOppHeroHasDot3 * singleSkillEffect.num3 * this.point.getCalculatedValue(Point.ARMOR) / 1000);
                effectAttack = this.point.addAttack(effectAttack);
                effectArmor = this.point.addArmorBreak(effectArmor);
                BattleEffectType atkBattleEffectType = effectAttack >= 0 ? BattleEffectType.BUFF_ATTACK_INC : BattleEffectType.BUFF_ATTACK_DEC;
                BattleEffectType armorBattleEffectType = effectArmor >= 0 ? BattleEffectType.BUFF_ARMOR_INC : BattleEffectType.BUFF_ARMOR_DEC;
                EffectCount atkEffectCount = this.effectController.addBattleEffect(atkHero, atkBattleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectAttack);
                EffectCount armorEffectCount = this.effectController.addBattleEffect(atkHero, armorBattleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectArmor);
                resultEffect.add(this.effectController.getResultEffect(atkEffectCount));
                resultEffect.add(this.effectController.getResultEffect(armorEffectCount));
            }
            case muichirouMark: {
                if (this.effectController.contain(battleEffectType) && this.effectController.get(battleEffectType).countNumber() >= CfgBattle.config.skill.maxMuichirouStack)
                    break;

                long effectBrk = (long) singleSkillEffect.num1;
                long effectHolyDmg = (long) singleSkillEffect.num2;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, effectBrk, effectHolyDmg);
                if (effectCount != null) {
                    resultEffect.add(effectController.getResultEffect(effectCount));
                    if (effectCount.needToChangeHeroPoint()) {
                        this.point.addArmorBreak(effectBrk);
                        this.point.addHolyDamage(effectHolyDmg);
                    }
                    mode.debug("effectCount.countNumber()2: " + effectCount.countNumber());
                }
                break;
            }
            case shinobuMark: {
                if (this.effectController.contain(battleEffectType) && this.effectController.get(battleEffectType).countNumber() >= CfgBattle.config.skill.maxShinobuStack)
                    break;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1, (long) singleSkillEffect.num2, (long) singleSkillEffect.num3);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case atkAndBrkByDot: {
                int numberDotFireStack = atkHero.oppTeam.stream().filter(HeroBattleEntity::isHeroAndAlive).mapToInt(oppHero -> {
                    EffectCount dotFireEffectCount = oppHero.effectController.get(BattleEffectType.EFFECT_DOT_FIRE);
                    return dotFireEffectCount == null ? 0 : dotFireEffectCount.countNumber();
                }).sum();
                if (numberDotFireStack <= 0) break;

                long effectAttack = (long) (numberDotFireStack * singleSkillEffect.num * this.point.getCalculatedValue(Point.ATTACK) / 1000);
                long effectBrk = (long) (numberDotFireStack * singleSkillEffect.num1);
                effectAttack = this.point.addAttack(effectAttack);
                effectBrk = this.point.addArmorBreak(effectBrk);
                BattleEffectType atkBattleEffectType = effectAttack >= 0 ? BattleEffectType.BUFF_ATTACK_INC : BattleEffectType.BUFF_ATTACK_DEC;
                BattleEffectType brkBattleEffectType = effectBrk >= 0 ? BattleEffectType.BUFF_ARMOR_BREAK_INC : BattleEffectType.BUFF_ARMOR_BREAK_DEC;
                EffectCount effectCount = this.effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round);
                EffectCount atkEffectCount = this.effectController.addBattleEffect(atkHero, atkBattleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectAttack);
                EffectCount brkEffectCount = this.effectController.addBattleEffect(atkHero, brkBattleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectBrk);
                EffectEntity atkResultEffect = effectController.getResultEffect(atkEffectCount);
                EffectEntity brkResultEffect = effectController.getResultEffect(brkEffectCount);
                resultEffect.add(effectController.getResultEffect(effectCount));
                resultEffect.add(atkResultEffect);
                resultEffect.add(brkResultEffect);
                break;
            }
            case atkAndBrkByKashimoMark: {
                int numberDotFireStack = atkHero.oppTeam.stream().filter(HeroBattleEntity::isHeroAndAlive).mapToInt(oppHero -> {
                    EffectCount dotFireEffectCount = oppHero.effectController.get(BattleEffectType.kashimoMark);
                    return dotFireEffectCount == null ? 0 : dotFireEffectCount.countNumber();
                }).sum();
                if (numberDotFireStack <= 0) break;

                long effectAttack = (long) (numberDotFireStack * singleSkillEffect.num * this.point.getCalculatedValue(Point.ATTACK) / 1000);
                long effectBrk = (long) (numberDotFireStack * singleSkillEffect.num1);
                effectAttack = this.point.addAttack(effectAttack);
                effectBrk = this.point.addArmorBreak(effectBrk);
                BattleEffectType atkBattleEffectType = effectAttack >= 0 ? BattleEffectType.BUFF_ATTACK_INC : BattleEffectType.BUFF_ATTACK_DEC;
                BattleEffectType brkBattleEffectType = effectBrk >= 0 ? BattleEffectType.BUFF_ARMOR_BREAK_INC : BattleEffectType.BUFF_ARMOR_BREAK_DEC;
                EffectCount effectCount = this.effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round);
                EffectCount atkEffectCount = this.effectController.addBattleEffect(atkHero, atkBattleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectAttack);
                EffectCount brkEffectCount = this.effectController.addBattleEffect(atkHero, brkBattleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectBrk);
                EffectEntity atkResultEffect = effectController.getResultEffect(atkEffectCount);
                EffectEntity brkResultEffect = effectController.getResultEffect(brkEffectCount);
                resultEffect.add(effectController.getResultEffect(effectCount));
                resultEffect.add(atkResultEffect);
                resultEffect.add(brkResultEffect);
                break;
            }
            //            case HOPE1: {
            //                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
            //                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
            //                break;
            //            }
            case ANGER_ENDROUND: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_ANGER_END_ROUND, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case MORE_DAMAGE_ACTIVE_SKILL: {
                if (this.effectController.countValueEffect(BattleEffectType.BUFF_MORE_DAMAGE_ACTIVE_SKILL) >= singleSkillEffect.num1)
                    break;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_MORE_DAMAGE_ACTIVE_SKILL, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case kisameMark: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.kisameMark, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case taunt: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.taunt, singleSkillEffect.type, singleSkillEffect.round, atkHero.position, atkHero.team);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case SURVIVAL: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_SURVIVAL, singleSkillEffect.type, singleSkillEffect.round, point.getCurrentHP());
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ICE_MARK: {
                disablePassiveSkill.clear();
                int numberDisable = (int) singleSkillEffect.num;
                List<Integer> skillIds = passiveSkills.stream().map(skillEntity -> skillEntity.id).collect(Collectors.toList());
                if (passiveSkills.size() <= numberDisable) disablePassiveSkill.addAll(skillIds);
                else {
                    while (skillIds.size() > numberDisable) {
                        skillIds.remove(mode.nextRand() % skillIds.size());
                    }
                    disablePassiveSkill.addAll(skillIds);
                }
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_ICE_MARK, singleSkillEffect.type, singleSkillEffect.round, 0);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DIS_ARM: {
                if (mode.battleType.isDisarmEnable()) {
                    this.point.setDisArm(true);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DIS_ARM, singleSkillEffect.type, singleSkillEffect.round);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case COUNTER_MARK: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_COUNTER_MARK, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case REDUCE_DAMAGE_FROM_NORMAL_ATTACK: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_REDUCE_DAMAGE_NORMAL_ATTACK, singleSkillEffect.type, singleSkillEffect.round, (long) (singleSkillEffect.num * 1000));
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case REDUCE_DAMAGE_FROM_ACTIVE_SKILL: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_REDUCE_DAMAGE_ACTIVE_SKILL, singleSkillEffect.type, singleSkillEffect.round, (long) (singleSkillEffect.num * 1000));
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ATTACK_PER_LEVEL_NUMBER_CONTROL_ENEMY: {
                int number = countControlEnemy();
                for (int i = 0; i < number; i++) {
                    long pAttack = (long) singleSkillEffect.num;
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, pAttack);
                    if (effectCount != null) {

                        if (effectCount.needToChangeHeroPoint()) point.addAttack(pAttack);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case BUFF_HOLY_DAMAGE_BY_NUMBER_CONTROLLED_ENEMY: {
                int number = countControlEnemy();
                for (int i = 0; i < number; i++) {
                    long effectValue = (long) singleSkillEffect.num;
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) point.addHolyDamage(effectValue);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case SKILL_PER_LEVEL_NUMBER_CONTROL_ENEMY: {
                int number = countControlEnemy();
                for (int i = 0; i < number; i++) {
                    long effectValue = (long) singleSkillEffect.num;
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) point.addSkillDamage(effectValue);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case sklPByEnemyLive: {
                int numberEnemyAlive = (int) this.oppTeam.stream().filter(HeroBattleEntity::isHeroAndAlive).count();
                long effectValue = (long) (singleSkillEffect.num - numberEnemyAlive * singleSkillEffect.num1);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) point.addSkillDamage(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case STEAL_HP_3_ROUND: {
                stealHp = (int) (point.getCurrentHP() * singleSkillEffect.num);
                point.addHp(-stealHp);
                resultEffect.add(getResultEffectHpDec(-stealHp, HP_END_ROUND, false, false, false, 0, false, false));
                break;
            }
            case HP_PER_LEVEL_NUMBER_STONE_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_STONE)).count();
                long addHp = (long) (startBattlePoint.getCurrentValue(Point.HP) * number * singleSkillEffect.num);
                long effectHp = point.addHp(addHp);
                EffectEntity effectEntity = this.getResultEffectHpInc(atkHero, effectHp);
                resultEffect.add(effectEntity);
                break;
            }
            case REDUCE_CONTROL_IMMUNE: {
                long effectValue = (long) singleSkillEffect.num;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, -effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) point.reduceControlImmune(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case SPD_PER_STONE_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_STONE)).count();
                long effectValue = (long) singleSkillEffect.num * number;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) point.addSpeed(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                mode.sortHeroSpeed();
                break;
            }
            case SPD_PER_STUN_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && (hero.effectController.contain(BattleEffectType.EFFECT_STUN) || hero.effectController.contain(BattleEffectType.EFFECT_STUN2))).count();
                long effectValue = (long) singleSkillEffect.num * number;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) point.addSpeed(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                mode.sortHeroSpeed();
                break;
            }
            case ANGER_PER_STONE_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_STONE)).count();
                long effectValue = (long) singleSkillEffect.num * number;
                point.addAnger(effectValue);
                resultEffect.add(this.getResultEffectAnger(atkHero, effectValue, true));
                break;
            }
            case ANGER_PER_STUN_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && (hero.effectController.contain(BattleEffectType.EFFECT_STUN) || hero.effectController.contain(BattleEffectType.EFFECT_STUN2))).count();
                long effectValue = (long) singleSkillEffect.num * number;
                point.addAnger(effectValue);
                resultEffect.add(this.getResultEffectAnger(atkHero, effectValue, true));
                break;
            }
            case ATTACK_PER_LEVEL_NUMBER_STONE_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_STONE)).count();
                for (int i = 0; i < number; i++) {
                    long effectValue = (long) singleSkillEffect.num;
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) point.addAttack(effectValue);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case BRK_PER_LEVEL_NUMBER_STONE_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_STONE)).count();
                for (int i = 0; i < number; i++) {
                    long effectValue = (long) singleSkillEffect.num;
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) point.addArmorBreak(effectValue);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case IMMORTAL_ENEMY_DARK_LIGHT_FACTION: {
                for (HeroBattleEntity hero : oppTeam) {
                    //                    if (hero.isHero())
                    //                        if (hero.faction == HeroType.FACTION_LUNISOLAR || hero.faction == HeroType.FACTION_WATER) {
                    //                            point.set(Point.SPEC_IMMORTAL_FOREVER, 1);
                    //                            break;
                    //                        }
                }
                break;
            }
            case IMMORTAL_ENEMY_NORMAL_FACTION: {
                for (HeroBattleEntity hero : oppTeam) {
                    //                    if (hero.isHero())
                    //                        if (hero.faction != HeroType.FACTION_LUNISOLAR && hero.faction != HeroType.FACTION_WATER) {
                    //                            point.set(Point.SPEC_IMMORTAL_FOREVER, 1);
                    //                            break;
                    //                        }
                }
                break;
            }
            case COUNTER_SHEILD: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_COUNTER_SHIELD, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DIVIDE_DEC_DAME: {
                List<HeroBattleEntity> sameRow = myTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.isSameLine(atkHero)).collect(Collectors.toList());
                long effectValue = (long) singleSkillEffect.num / sameRow.size();
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) point.addReduceDamage(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case SUY_NHUOC: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_SUY_NHUOC, singleSkillEffect.type, singleSkillEffect.round);
                if (effectCount != null) {
                    resultEffect.add(effectController.getResultEffect(effectCount));
                    atkHero.getHeroAnalysis().addControlEffect(this, BattleEffectType.EFFECT_SUY_NHUOC); //Count
                }

                break;
            }
            case TRUY_SAT: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_TRUY_SAT, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case REMOVE_ACC_EFFECT, removeControlEffect: {
                resultEffect.addAll(effectController.removeNumberEffectFromList(CfgBattle.listControlEffect, (int) singleSkillEffect.num));
                break;
            }
            case removeGoodEffect: {
                resultEffect.addAll(effectController.removeNumberEffectFromList(CfgBattle.listGoodEffect, (int) singleSkillEffect.num));
                break;
            }
            case removeBadEffect: {
                List<EffectEntity> listEffect = effectController.removeNumberEffectFromList(CfgBattle.listBadEffect, (int) singleSkillEffect.num);
                resultEffect.addAll(listEffect);
                break;
            }
            case removeGoodMark: {
                resultEffect.addAll(effectController.removeNumberEffectFromList(CfgBattle.listGoodMark, (int) singleSkillEffect.num));
                break;
            }
            case removeBadMark: {
                resultEffect.addAll(effectController.removeNumberEffectFromList(CfgBattle.listBadMark, (int) singleSkillEffect.num));
                break;
            }
            case removeDot: {
                resultEffect.addAll(effectController.removeNumberEffectFromList(CfgBattle.listDot, (int) singleSkillEffect.num));
                break;
            }
            case removeBadAndControlAndDot: {
                List<BattleEffectType> listBattleEffect = new ArrayList<>(CfgBattle.listBadEffect);
                listBattleEffect.addAll(CfgBattle.listControlEffect);
                listBattleEffect.addAll(CfgBattle.listDot);
                resultEffect.addAll(effectController.removeNumberEffectFromList(listBattleEffect, (int) singleSkillEffect.num));
                break;
            }
            case STEAL_ATTACK: {
                long effectValue = (long) (singleSkillEffect.num * point.getRealCurrentAttack());
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ATTACK_INC, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) {
                        point.addAttack(effectValue);
                        atkHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.TOTAL_STEAL_ATTACK, effectValue);
                    }
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case COPY_ENEMY_ATTACK2: {
                long effectValue = (long) singleSkillEffect.num * point.getRealCurrentAttack();
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ATTACK_INC, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) {
                        point.addAttack(effectValue);
                        atkHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.TOTAL_COPY_ATTACK2, effectValue);
                    }
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case COPY_ENEMY_ATTACK: {
                long effectValue = oppTeam.stream().filter(hero -> hero.isHeroAndAlive()).mapToLong(hero -> (long) (hero.point.getRealCurrentAttack() * singleSkillEffect.num)).sum();
                if (effectValue > 0) {
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ATTACK_INC, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) {
                            point.addAttack(effectValue);
                            atkHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, Point.TOTAL_COPY_ATTACK2, effectValue);
                        }
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case ENERGY_SEAL:
            case ENERGY_SEAL_2:
            case ENERGY_SEAL_3:
            case ENERGY_SEAL_4:
            case ENERGY_SEAL_5:
            case ENERGY_SEAL_6:
            case ENERGY_SEAL_7:
            case ENERGY_SEAL_8: {
                long sealHp = (long) (point.getCalculatedValue(Point.HP) * singleSkillEffect.num);
                long sealAtk = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num1);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, sealHp, sealAtk);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case SHIELD_BY_MAX_HP:
            case SHIELD_BY_MAX_HP1:
            case SHIELD_BY_MAX_HP2: {
                long sealHp = (long) (atkHero.point.getCalculatedValue(Point.HP) * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, sealHp);
                if (effectCount != null) {
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case SHIELD_BY_MAX_HP3: {
                long sealHp = (long) (this.point.getMaxHp() * singleSkillEffect.num);
                EffectCount effectCount = this.effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, sealHp);
                if (effectCount != null) {
                    resultEffect.add(this.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case shieldByAtk:
            case shieldByAtk1: {
                long sealHp = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, sealHp);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case shieldByPoisonStack: {
                int numberPoisonStack = this.oppTeam.stream()
                        .filter(heroBattle -> heroBattle.isHeroAndAlive() && heroBattle.effectController.contain(BattleEffectType.EFFECT_DOT_POISON))
                        .mapToInt(heroBattle -> heroBattle.effectController.get(BattleEffectType.EFFECT_DOT_POISON).countNumber()).sum();
                long sealHp = (long) (atkHero.point.getCalculatedValue(Point.HP) * singleSkillEffect.num * numberPoisonStack);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, sealHp);
                if (effectCount != null) {
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case accShield: {
                long sealHp = (long) (this.point.getCalculatedValue(Point.HP) * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, sealHp, (long) singleSkillEffect.num1, (long) singleSkillEffect.num2, (long) singleSkillEffect.num3, (long) singleSkillEffect.num4);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case HOANG_LOAN: {
                long effectValue = (long) (singleSkillEffect.num * 1000);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_HOANG_LOAN, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case SKILL_DAMAGE_NUMBER_BURN_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_DOT_FIRE)).count();
                for (int i = 0; i < number; i++) {
                    long effectValue = (long) (singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) point.addSkillDamage(effectValue);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case ATTACK_PER_LEVEL_NUMBER_BURN_ENEMY: {
                long number = oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.effectController.contain(BattleEffectType.EFFECT_DOT_FIRE)).count();
                for (int i = 0; i < number; i++) {
                    long effectValue = (long) (singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) point.addAttack(effectValue);
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case IMMORTAL: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_IMMORTAL, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) {
                    immortal = (int) singleSkillEffect.num;
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case IMMORTAL2: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_IMMORTAL2, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) {
                    immortal2 = (int) singleSkillEffect.num;
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case IMMORTAL_BY_OPP_TURN: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_IMMORTAL_BY_OPP_TURN, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) {
                    immortalByOppTurn = (int) singleSkillEffect.num;
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case CONTINUE_HEAL: { // Chỉ số công của người buff
                if (point.getCurrentHP() > 0) {
                    long effectValue = (long) (singleSkillEffect.num * atkHero.point.getRealCurrentAttack());
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_CONTINUE_HEAL, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint() && this.canHeal()) {
                            long reduceHeal = this.checkAndGetReduceHeal(effectValue, attackInfo);
                            long effectHp = this.point.addHp(Math.max(0, effectValue - reduceHeal));
                            resultEffect.add(this.getResultEffectHpInc(atkHero, effectHp));
                            atkHero.addLogHeal(effectHp);
                        }
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case CONTINUE_HEAL_2: { // máu tối đa của bản thân
                if (point.getCurrentHP() > 0) {
                    long effectValue = (long) (singleSkillEffect.num * point.getCalculatedValue(Point.HP));
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_CONTINUE_HEAL, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) {
                        if (effectCount.needToChangeHeroPoint()) {
                            long effectHp = point.addHp(effectValue);
                            resultEffect.add(this.getResultEffectHpInc(atkHero, effectHp));
                            atkHero.addLogHeal(effectHp);
                        }
                        resultEffect.add(effectController.getResultEffect(effectCount));
                    }
                }
                break;
            }
            case REVIVE: {
                if (!point.isRevived() && point.getCurrentHP() <= 0) {
                    long newHp = (long) (point.getCalculatedValue(Point.HP) * singleSkillEffect.num);
                    revive(resultEffect, newHp);
                }
                break;
            }
            case WATCHER_MARK_MORE_DAMAGE_TAKEN: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_WATCHER_MARK_MORE_DAMAGE_TAKEN, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num * 1000);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case RANGER_DOT_BLOOD: {
                if (HeroType.CLASS_RANGER == clazz) {
                    long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_BLOOD, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case FRONT_DOT_BLOOD: {
                if (isFrontLine()) {
                    long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_BLOOD, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case MAGE_DOT_BLOOD: {
                if (HeroType.CLASS_MAGE == clazz) {
                    long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_BLOOD, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case WARRIOR_FIRE: {
                if (HeroType.CLASS_WARRIOR == clazz) {
                    long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_FIRE, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case WEAKEN: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_WEAKEN, singleSkillEffect.type, singleSkillEffect.round);
                if (effectCount != null) {
                    resultEffect.add(effectController.getResultEffect(effectCount));
                    this.getHeroAnalysis().addControlEffect(this, BattleEffectType.EFFECT_WEAKEN);
                }
                break;
            }
            case FEAR: {
                if (!isBoss) {
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_FEAR, singleSkillEffect.type, singleSkillEffect.round);
                    if (effectCount != null) {
                        resultEffect.add(effectController.getResultEffect(effectCount));
                        this.getHeroAnalysis().addControlEffect(this, BattleEffectType.EFFECT_FEAR);
                    }
                }
                break;
            }
            case LIGHTNING_MARK_SKEREI: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_LIGHTNING_MARK_SKEREI, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num * 1000);
                if (effectCount != null) {
                    resultEffect.add(effectController.getResultEffect(effectCount));
                    this.getHeroAnalysis().addControlEffect(this, BattleEffectType.EFFECT_LIGHTNING_MARK_SKEREI);
                }
                break;
            }
            case CRITICAL_STRIKE_MARK: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_CRITICAL_STRIKE_MARK, singleSkillEffect.type, 15, (long) singleSkillEffect.num * 1000);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case STEAL_ARMOR: {
                long effectValue = (long) (singleSkillEffect.num * point.getCurrentArmor());
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ARMOR_INC, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) atkHero.point.addArmor(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case HEAL_BY_BLOCK_DAMAGE: {
                if (this.canHeal() && damageBlock > 0) {
                    long effectValue = this.point.addHp((int) (singleSkillEffect.num * damageBlock / 2));
                    resultEffect.add(this.getResultEffectHpInc(atkHero, effectValue));
                    atkHero.addLogHeal(effectValue);
                }
                break;
            }
            case HEAL_HP_FROM_DAMAGE: {
                if (atkHero.point.getCurrentHP() > 0) {
                    long effectValue = atkHero.point.addHp((int) (singleSkillEffect.num * atkHero.point.getRealCurrentAttack()));
                    resultEffect.add(this.getResultEffectHpInc(atkHero, effectValue));
                    atkHero.addLogHeal(effectValue);
                }
                break;
            }
            case HEAL_BY_ATTACK: {
                if (point.getCurrentHP() > 0) {
                    long effectValue = point.addHp((int) (singleSkillEffect.num * atkHero.point.getCurrentValue(Point.ATTACK)));
                    resultEffect.add(this.getResultEffectHpInc(atkHero, effectValue));
                    atkHero.addLogHeal(effectValue);
                }
                break;
            }
            case HEAL_BY_MAX_HP: {
                if (point.getCurrentHP() > 0) {
                    long effectValue = point.addHp((int) (singleSkillEffect.num * point.getCalculatedValue(Point.HP)));
                    resultEffect.add(this.getResultEffectHpInc(atkHero, effectValue));
                    atkHero.addLogHeal(effectValue);
                }
                break;
            }
            case DOT_TRUE_ATK: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_TRUE_ATK, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_TRUE_HP: {
                long effectAttack = (long) (point.getMaxHp() * singleSkillEffect.num);
                long maxAttack = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num1 / 100);
                effectAttack = effectAttack < maxAttack || maxAttack == 0 ? effectAttack : maxAttack;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_TRUE_HP, singleSkillEffect.type, singleSkillEffect.round, effectAttack);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_FIRE: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_FIRE, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_MAX_HP: {
                long effectAttack = (long) (point.getMaxHp() * singleSkillEffect.num);
                long maxAttack = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num1 / 100);
                effectAttack = effectAttack < maxAttack || maxAttack == 0 ? effectAttack : maxAttack;
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_FIRE, singleSkillEffect.type, singleSkillEffect.round, effectAttack);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_ENERGY: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num * point.getCurrentAnger());
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_FIRE, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_BLOOD: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_BLOOD, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case dotPoison1:
            case dotFire1:
            case dotBlood1: {
                long effectValue = (long) (atkHero.myTeam.stream().filter(HeroBattleEntity::isHeroAndNotPet).mapToLong(hero -> hero.point.getRealCurrentAttack()).sum()
                        * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case bonusDmgPerDotType: {
                long effectValue = switch (this.countDotEffect()) {
                    case 0 -> 0;
                    case 1 -> (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                    case 2 -> (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num1);
                    default -> (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num2);
                };
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ASSASSIN_POISON: {
                if (HeroType.CLASS_ASSASSIN == clazz) {
                    long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_POISON, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            case DOT_POISON: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DOT_POISON, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_BLOOD_EXACT_DAMAGE: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_EXACT_DOT_BLOOD, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_POISON_EXACT_DAMAGE: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_EXACT_DOT_POISON, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_FIRE_EXTRA_DAMAGE: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_EXTRA_BURNING, singleSkillEffect.type, singleSkillEffect.round, (long) (singleSkillEffect.num * 1000));
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_BLOOD_EXTRA_DAMAGE: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_EXTRA_BLEED, singleSkillEffect.type, singleSkillEffect.round, (long) (singleSkillEffect.num * 1000));
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case DOT_POISON_EXTRA_DAMAGE: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_EXTRA_POISON, singleSkillEffect.type, singleSkillEffect.round, (long) (singleSkillEffect.num * 1000));
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ROUND_MARK_DAMAGE: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_ROUND_MARK, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ROUND_MARK_DAMAGE2: {
                long effectValue = (long) (atkHero.point.getRealCurrentAttack() * singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_ROUND_MARK2, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case STUN_IF_BELOW_HP: {
                if (point.getCurrentHP() > atkHero.point.getCurrentHP() && point.getCurrentValue(Point.SPEC_IMMUNE_STUN) == 0) {
                    this.isAlreadyGotStunEffect = true;
                    EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_STUN, singleSkillEffect.type, singleSkillEffect.round);
                    if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                    this.getHeroAnalysis().addControlEffect(this, BattleEffectType.EFFECT_STUN);
                    if (!attackInfo.isPassiveSkill()) mode.listHeroBeControlled.add(this);
                }
                break;
            }
            case STONE_ATK_EXACT_DAMAGE: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_EXTRA_STONE, singleSkillEffect.type, singleSkillEffect.round, (long) (singleSkillEffect.num * 1000));
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ARMOR_BY_NUMBER: {
                long effectValue = point.addArmor((long) singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case ONOKI_MARK: {
                if (this.effectController.contain(BattleEffectType.EFFECT_ONOKI_MARK)) {
                    EffectCount effectCount = this.effectController.get(BattleEffectType.EFFECT_ONOKI_MARK);
                    if (effectCount.countNumber() >= 10) break;
                }

                long effectSpeed = point.addSpeed((int) singleSkillEffect.num);
                long effectHolyDamage = point.addHolyDamage((int) singleSkillEffect.num1);
                long effectArmorBreak = point.addArmorBreak((int) singleSkillEffect.num2);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_ONOKI_MARK, singleSkillEffect.type, singleSkillEffect.round, effectSpeed, effectHolyDamage, effectArmorBreak);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case HASHIRAMA_MARK: {
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_HASHIRAMA_MARK, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, (long) singleSkillEffect.num1, (long) singleSkillEffect.num2);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case HASHIRAMA_REVIVAL: {
                int numberHeroSameFaction = (int) this.myTeam.stream().filter(ally -> ally.isHero() && ally.faction.equals(this.faction)).count();
                EffectCount effectCount = effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_HASHIRAMA_REVIVAL, singleSkillEffect.type, singleSkillEffect.round, (long) singleSkillEffect.num, numberHeroSameFaction);
                if (effectCount != null) resultEffect.add(effectController.getResultEffect(effectCount));
                break;
            }
            case atkP2: {
                long effectValue = (long) (singleSkillEffect.num);
                EffectCount effectCount = effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) point.addAttack(effectValue);
                    resultEffect.add(effectController.getResultEffect(effectCount));
                }
                break;
            }
            default:
                Integer buffPointIndex = SkillEffectType.buffPointIndex(singleSkillEffect.type, clazz == null ? "" : clazz.name);
                if (buffPointIndex != null) {
                    buffPoint(attackInfo, this, atkHero, resultEffect, buffPointIndex, singleSkillEffect);
                } else {
                    if (battleEffectType != null) {
                        if ((battleEffectType == BattleEffectType.EFFECT_SILENCE && point.getCurrentValue(Point.SPEC_IMMUNE_SILENCE) > 0) || (battleEffectType == BattleEffectType.EFFECT_STONE && point.getCurrentValue(Point.SPEC_IMMUNE_STONE) > 0) || (battleEffectType == BattleEffectType.EFFECT_STUN && point.getCurrentValue(Point.SPEC_IMMUNE_STUN) > 0) || (battleEffectType == BattleEffectType.EFFECT_FREEZE && point.getCurrentValue(Point.SPEC_IMMUNE_FREEZE) > 0))
                            return new ArrayList<>();
                        //                            if (mode.nextRand() < singleSkillEffect.ratio) {
                        this.getHeroAnalysis().addControlEffect(this, battleEffectType);
                        if (BattleEffectType.aStunEffect.contains(battleEffectType)) {
                            if (!attackInfo.isPassiveSkill()) mode.listHeroBeControlled.add(this);
                            if (battleEffectType.equals(BattleEffectType.EFFECT_STUN))
                                this.isAlreadyGotStunEffect = true;
                        }
                        EffectCount effectCount = this.effectController.addBattleEffect(atkHero, battleEffectType, singleSkillEffect.type, singleSkillEffect.round);
                        resultEffect.add(this.effectController.getResultEffect(effectCount));
                        //                            }
                    }
                }
        }

        if (CfgBattle.listDot.contains(battleEffectType)) {
            this.myTeam.stream().filter(ally -> ally.isHeroAndAlive() && ally.effectController.contain(BattleEffectType.chosoMark)).forEach(ally -> {
                EffectCount effectCount = ally.effectController.get(BattleEffectType.chosoMark);
                if (effectCount != null) {
                    long limitStack = effectCount.getValue1(0) + ally.effectController.countValueEffect(BattleEffectType.limitChosoMark);
                    if (effectCount.countNumber() >= limitStack) return;

                    effectCount = ally.effectController.addBattleEffect(effectCount);
                    EffectEntity effectEntity = ally.effectController.getResultEffect(effectCount);
                    resultEffect.add(effectEntity);

                    checkChosoMark(ally, effectCount, resultEffect);
                }
            });

        }
        return resultEffect;
    }

    public static void buffPoint(AttackInfoEntity attackInfo, HeroBattleEntity defHero, HeroBattleEntity atkHero, List<EffectEntity> resultEffect, int buffPointIndex, SkillEntity.SingleSkillEffect singleSkillEffect) {
        String type = singleSkillEffect.type;
        float num = singleSkillEffect.num, num1 = singleSkillEffect.num1, num2 = singleSkillEffect.num2, num3 = singleSkillEffect.num3;
        int round = singleSkillEffect.round;
        // buff index
        BattleEffectType battleEffectType = SkillEffectType.getBattleEffect(type, num);
        switch (buffPointIndex) {
            case Point.SPEC_CRIT_TIME_REDUCE:
            case Point.critBlock:
            case Point.reduceSingleAttack: {
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.add(Point.CURRENT_VALUES_INDEX, buffPointIndex, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SPEC_ADD_DOT_DAMAGE_TAKEN: {
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_SPEC_ADD_DOT_DAMAGE_TAKEN, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.add(Point.CURRENT_VALUES_INDEX, buffPointIndex, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SPEC_MORE_DAMAGE_A: {
                long effectValue = (long) num;
                if (type.equals(SkillEffectType.moreDotPBurnEnemy.value)) {
                    effectValue = (long) num * atkHero.oppTeam.stream().filter(tmpHero -> tmpHero.isHeroAndAlive() && tmpHero.effectController.contain(BattleEffectType.EFFECT_DOT_FIRE)).count();
                }
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, buffPointIndex, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SPEC_MORE_ANGER: {
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_SPEC_MORE_ANGER, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, buffPointIndex, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.ANTI_CRIT: {
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ANTI_CRIT, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, buffPointIndex, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SPEC_REDUCE_DAMAGE_A:
                defHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, buffPointIndex, (long) (num * 1000));
                break;
            case Point.SPEC_CRIT_TIME_REDUCE2: {
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_CRIT_TIME_REDUCE2, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, buffPointIndex, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SPEC_MORE_HEAL: {
                if (battleEffectType == null) break;

                long effectValue = (long) (num * 1000);
                if (battleEffectType.equals(BattleEffectType.kashimoMark))
                    effectValue = (long) -Math.abs(num);
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue, (int) num1, (int) num2, (int) num3);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) {
                        defHero.point.addMoreHeal((long) effectValue);
                    }
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SKILL_DAMAGE: {
                if (battleEffectType == null) break;

                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addSkillDamage(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.ATTACK: {
                if (battleEffectType == null) break;

                long addAttack = (long) (defHero.point.getCalculatedValue(Point.ATTACK) * num / 1000);
                if (type.equals(SkillEffectType.atkPSpd.value))
                    addAttack = (long) num * defHero.point.getCalculatedValue(Point.ATTACK) * defHero.point.getCurrentSpeed() / 1000;
                long effectValue = defHero.point.addAttack(addAttack);
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) defHero.point.addAttack(-effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                } else defHero.point.addAttack(-effectValue);
                break;
            }
            case Point.ARMOR: {
                if (battleEffectType == null) break;

                long effectValue = defHero.point.addArmor(num);
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) defHero.point.addArmor(-effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                } else defHero.point.addArmor(-effectValue);
                break;
            }
            case Point.ANGER: {
                if (type.equals(SkillEffectType.energyKillEnemy.value) && !defHero.mode.listHeroDieMapByAtkHero.containsKey(defHero))
                    break;

                long effectValue = (long) num;
                defHero.point.addAnger(effectValue);
                resultEffect.add(defHero.getResultEffectAnger(atkHero, effectValue, true));
                break;
            }
            case Point.HP: {
                long addMaxHp = 0;
                int effectRound = round;
                if (type.equals(SkillEffectType.hpPMaxByPoison.value)) {
                    long numberEnemyHasPoison = atkHero.oppTeam.stream().filter(enemy -> enemy.isHeroAndAlive() && enemy.effectController.contain(BattleEffectType.EFFECT_DOT_POISON)).count();
                    addMaxHp = (long) (defHero.startBattlePoint.getMaxHp() * num * numberEnemyHasPoison / 1000);
                }
                if (addMaxHp <= 0) break;

                float percentHp = defHero.point.percentHpFloat();
                long newMaxHp = defHero.point.getMaxHp() + addMaxHp;
                long newHp = (long) (newMaxHp * percentHp / 100);
                long addHp = newHp - defHero.point.getCurrentHP();
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, effectRound, addMaxHp);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.setHPAndMaxHP(newHp, newMaxHp);
                    resultEffect.add(new EffectEntity(effectCount.id, defHero, battleEffectType, addHp, newHp, addMaxHp, newMaxHp));
                    if (addHp < 0 && addMaxHp < 0) {
                        if (atkHero != null) atkHero.addLogDamage(addHp, false);
                        defHero.addLogDef(addHp);
                    }
                }
                break;
            }
            case Point.SPEED: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addSpeed(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                defHero.mode.sortHeroSpeed();
                break;
            }
            case Point.ANTI_ARMOR_BREAK: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                if (battleEffectType.equals(BattleEffectType.tojiMark)) effectValue = (long) Math.abs(num);
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue, (long) num1, (long) num2, (long) num3);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addAntiArmorBreak(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.ARMOR_BREAK: {
                if (battleEffectType == null) break;
                long effectValue = defHero.point.addArmorBreak((long) num);
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (!effectCount.needToChangeHeroPoint()) defHero.point.addArmorBreak(-effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.PRECISION: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addPrecision(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.BLOCK: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;

                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addBlock(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.CRIT: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addCrit(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.CRIT_DAMAGE: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addCritDamage(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.REDUCE_DAMAGE: {
                if (battleEffectType == null) break;
                if (type.equals(SkillEffectType.decDmgByEnergy.value) && defHero.point.getCurrentAnger() < num1) break;

                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addReduceDamage(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.CONTROL_IMMUNE: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addControlImmune(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.ANTI_CONTROL_IMMUNE: {
                if (battleEffectType == null) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addAntiControlImmune(effectValue);
                    EffectEntity effectEntity = defHero.effectController.getResultEffect(effectCount);
                    resultEffect.add(effectEntity);
                }
                break;
            }
            case Point.HOLY_DAMAGE: {
                if (battleEffectType == null) break;
                if (type.equals(SkillEffectType.trueAtkByEnergy.value) && defHero.point.getCurrentAnger() < num1) break;
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, battleEffectType, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addHolyDamage(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.SPEC_ADD_DAMAGE_AGAINST_DEATH_MARK, Point.SPEC_ADD_DAMAGE_AGAINST_DEATH_MARK1:
                defHero.point.addNotNegative(buffPointIndex, (int) (num * 1000));
                break;
            case Point.SPEC_DEBUFF_BLOCK: {
                long effectValue = 1;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, BattleEffectType.EFFECT_DEBUFF_BLOCKER, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint())
                        defHero.point.addNotNegative(Point.SPEC_DEBUFF_BLOCK, effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
            case Point.BREAK_IMMORTAL: {
                long effectValue = (long) num;
                EffectCount effectCount = defHero.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_BREAK_IMMORTAL, type, round, effectValue);
                if (effectCount != null) {
                    if (effectCount.needToChangeHeroPoint()) defHero.point.addBreakImmortal(effectValue);
                    resultEffect.add(defHero.effectController.getResultEffect(effectCount));
                }
                break;
            }
        }
    }

//    public void addMaxHp(HeroBattleEntity atkHero, long effectMaxHp, List<EffectEntity> resultEffect, String skillType, boolean... needAddHp) {
//        EffectCount effectCount = this.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_MAX_HP, skillType, 15, effectMaxHp);
//        if (effectCount != null) {
//            resultEffect.addAll(this.checkThenRemoveSameEffectBefore(effectCount.type));
//            if (effectCount.needToChangeHeroPoint()) {
//                this.point.addMaxHp(effectMaxHp);
//                if (this.point.getCurrentHP() > 0 && needAddHp.length == 0) {
//                    effectMaxHp = this.point.addHp(effectMaxHp);
//                    resultEffect.add(new EffectEntity(++mode.effectEntityId, this, BattleEffectType.BUFF_HP_INC, this.point.getCurrentHP(), effectMaxHp));
//                }
//            }
//            EffectEntity effectEntity = new EffectEntity(effectCount.id, this, BattleEffectType.BUFF_MAX_HP, effectMaxHp, this.point.getCurrentHP(), effectMaxHp, this.point.getCalculatedValue(Point.HP));
//            resultEffect.add(effectEntity);
//        }
//    }

    public static boolean isBuffSuccess(HeroBattleEntity atkHero, HeroBattleEntity defHero, BattleEffectType battleEffect, String skillType, float ratio, float num, List<EffectEntity> resultEffect) {
        if (defHero.effectController.contain(BattleEffectType.EFFECT_BUFF_BLOCKER)) { // Không thể nhận các Chỉ số có lợi, Dấu ấn có lợi, Dấu ấn đặc biệt
            Integer pointIndex = SkillEffectType.mEffectPointIndex.get(skillType);
            if (pointIndex != null && num > 0) return false;
        }
        if (defHero.effectController.contain(BattleEffectType.EFFECT_DEBUFF_BLOCKER) || defHero.point.getCurrentValue(Point.SPEC_DEBUFF_BLOCK) > 0) { // không thể nhận các hiệu ứng giảm chỉ số ( giảm công, giảm thủ,.....)
            Integer pointIndex = SkillEffectType.mEffectPointIndex.get(skillType);
            if (pointIndex != null && num < 0) return false;
        }


        int finalRatio = (int) (ratio * 1000);
        if (battleEffect != null) {
            if (BattleEffectType.aCCEffect.contains(battleEffect) || List.of(BattleEffectType.EFFECT_FASCINATE, BattleEffectType.taunt, BattleEffectType.EFFECT_MARK_OPPRESS).contains(battleEffect)) {
                long controlImmune = defHero.point.getCurrentControlImmune();
                long antiControlImmune = atkHero.point.getCurrentAntiControlImmune();
                switch (battleEffect) {
                    case EFFECT_STUN:
                        controlImmune += defHero.point.getCurrentValue(Point.SPEC_CONTROL_IMMUNE_STUN);
                        break;
                    case EFFECT_FREEZE:
                    case EFFECT_ICE_MARK:
                        controlImmune += defHero.point.getCurrentValue(Point.SPEC_CONTROL_IMMUNE_FREEZE);
                        break;
                    case EFFECT_STONE:
                        controlImmune += defHero.point.getCurrentValue(Point.SPEC_CONTROL_IMMUNE_STONE);
                        break;
                    case EFFECT_FEAR:
                        controlImmune += defHero.point.getCurrentValue(Point.SPEC_CONTROL_IMMUNE_FEAR);
                        break;
                    case EFFECT_SILENCE:
                        controlImmune += defHero.point.getCurrentValue(Point.SPEC_CONTROL_IMMUNE_SILENCE);
                        break;
                }
                finalRatio = (int) (1000 * (ratio * (1f - (controlImmune - antiControlImmune) / 1000f)));
            }
        }

        if (defHero.mode.nextRand() >= finalRatio) return false;

        if (CfgBattle.listControlEffect.contains(battleEffect) && defHero.effectController.contain(BattleEffectType.immuneControlStack)) {
            resultEffect.addAll(defHero.effectController.removeBattleEffect(BattleEffectType.immuneControlStack, 1));
            return false;
        }

        return isBuffSuccessSpecify(atkHero, defHero, battleEffect, num);
    }

    public static boolean isBuffSuccessSpecify(HeroBattleEntity atkHero, HeroBattleEntity defHero, BattleEffectType battleEffect, float num) {
        List<BattleEffectType> listEffectHasStackLimitByNum1 = Arrays.asList(BattleEffectType.tojiMark, BattleEffectType.kashimoMark);
        if (listEffectHasStackLimitByNum1.contains(battleEffect)) {
            EffectCount effectCount = defHero.effectController.get(battleEffect);
            if (effectCount != null)
                return effectCount.countNumber() < effectCount.getValue1(0);
        }

        return true;
    }

    BattleEffectType getBattleEffect(SkillEffectType effect, float num) {
        if (SkillEffectType.mBuffByClass.get("").contains(SkillEffectType.get(effect.value))) { // buff hiệu ứng theo clazz
            if (SkillEffectType.mBuffByClass.get(clazz.name).contains(SkillEffectType.get(effect.value))) {
                return SkillEffectType.getBattleEffect(effect, num);
            }
            return null;
        }
        return SkillEffectType.getBattleEffect(effect, num);
    }
    //endregion

    //region Proto
    public Pbmethod.PbBattleHero.Builder toProto() {
        Pbmethod.PbBattleHero.Builder builder = Pbmethod.PbBattleHero.newBuilder();
        builder.setHeroId(heroId);
        builder.setPos(position).setSkin(skin);
        builder.setLevel(level);
        builder.setStar(star);
        builder.setTier(tier);
        builder.addAllPoint(point.getListLongValue());
        return builder;
    }

    public PbJBattleHero toPbJ() {
        PbJBattleHero hero = new PbJBattleHero();
        hero.setHeroId(heroId);
        hero.setPos(position);
        hero.setLevel(level);
        hero.setStar(star);
        hero.setTier(tier);
        hero.addAllPoint(point.getListValue());

        return hero;
    }
    //endregion

    //region Hero Status
    public boolean isHeroAndAlive() {
        return isHero() && (point.getCurrentHP() > 0 || this.position >= BattleConfig.TEAM_HERO_SIZE);
    }

    public boolean isHeroAndDead() {
        return isHero() && point.getCurrentHP() <= 0;
    }

    public boolean canHeal() {
        //        return !effectController.contain(BattleEffectType.EFFECT_DISEASED);
        return point.getCurrentHP() > 0;
    }

    public boolean isHero() {
        return team > 0;
//        return team > 0 && heroId > 0;
    }

    public boolean isHeroAndNotPet() {
        return team != 0 && position != 6;
    }

    public boolean isTransferHero() {
        //        return true;
        List<SkillEntity> listTriggerSkill = getTriggerSkill(TriggerType.BE_CROWD_CONTROLLED);
        return !listTriggerSkill.isEmpty();
    }

    public boolean isSameLine(HeroBattleEntity hero) {
        if ((hero.position == 0 || hero.position == 1) && (position == 0 || position == 1)) {
            return true;
        } else if (hero.position != 0 && hero.position != 1 && position != 0 && position != 1) {
            return true;
        }
        return false;
    }

    public boolean isFrontLine() {
        if (position < 3 && this.couldTarget()) return true;
        for (int i = 0; i < 3; i++) {
            if (i < this.myTeam.size() && this.myTeam.get(i).couldTarget()) return false;
        }
        return true;
    }

    public boolean isBackLine() {
        if (position >= 3 && this.couldTarget()) return true;
        for (int i = 3; i < 6; i++) {
            if (this.myTeam.get(i).couldTarget()) {
                return false;
            }
        }
        return true;
    }

    public boolean hasFrontLineAlive() {
        return myTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.isFrontLine()).findFirst().orElse(null) != null;
    }

    public boolean sameHero(HeroBattleEntity hero) {
        return hero.team == team && hero.position == position;
    }

    public boolean couldTarget() {
        return isHero() && point.getCurrentHP() > 0 && (this.myTeam.stream().noneMatch(ally -> ally.isHeroAndAlive() && !ally.sameHero(this))
                || !this.effectController.contain(BattleEffectType.unableBeTarget));
    }

    public boolean couldDoActiveSkill() {
        return !effectController.contain(BattleEffectType.EFFECT_SILENCE)
                && !effectController.contain(BattleEffectType.taunt);
    }

    public boolean couldProcessEffect(int kind) {
        if (kind == 1) {
            return couldCounterAttack();
        }
        return true;
    }

    public boolean couldCounterAttack() {
        return isHero() && point.getCurrentHP() > 0
                && !effectController.contain(BattleEffectType.EFFECT_STUN)
                && !effectController.contain(BattleEffectType.EFFECT_STUN2)
                && !effectController.contain(BattleEffectType.EFFECT_FREEZE)
                && !effectController.contain(BattleEffectType.EFFECT_STONE)
                && !effectController.contain(BattleEffectType.EFFECT_MARK_OPPRESS)
                && !effectController.contain(BattleEffectType.sleep)
                && !effectController.contain(BattleEffectType.EFFECT_ICE_MARK);
    }

    public boolean couldAttack() {
        return isHero() && point.getCurrentHP() > 0 && !roundAttack && !isControlled();
    }

    public boolean isControlled() {
        return effectController.contain(BattleEffectType.EFFECT_STUN) ||
                effectController.contain(BattleEffectType.EFFECT_STUN2) ||
                effectController.contain(BattleEffectType.EFFECT_ICE_MARK) ||
                effectController.contain(BattleEffectType.EFFECT_FREEZE) ||
                effectController.contain(BattleEffectType.EFFECT_STONE) ||
                effectController.contain(BattleEffectType.EFFECT_MARK_OPPRESS) ||
                effectController.contain(BattleEffectType.sleep) ||
                (effectController.contain(BattleEffectType.EFFECT_FEAR) && point.getCurrentAnger() < 100) ||
                (effectController.contain(BattleEffectType.EFFECT_FEAR) && effectController.contain(BattleEffectType.EFFECT_SILENCE) && point.getCurrentAnger() >= 100);
    }

    public boolean isAbleToCauseDamage(int triggerValue) {
        //pet
        if (this.position == 6) return true;

        TriggerType triggerType = TriggerType.get(triggerValue);
        return isHero() && (triggerType.equals(TriggerType.SELF_DIE) || point.getCurrentHP() > 0) &&
                !effectController.contain(BattleEffectType.EFFECT_STUN) &&
                !effectController.contain(BattleEffectType.EFFECT_STUN2) &&
                !effectController.contain(BattleEffectType.EFFECT_ICE_MARK) &&
                !effectController.contain(BattleEffectType.EFFECT_FREEZE) &&
                !effectController.contain(BattleEffectType.sleep) &&
                !effectController.contain(BattleEffectType.EFFECT_STONE);
    }
    //endregion

    public List<SkillEntity> getTriggerSkill(TriggerType trigger) {
        if (passiveSkills == null) {
            return new ArrayList<>();
        }

        List<SkillEntity> aTriggerSkill = new ArrayList<>();
        for (SkillEntity passive : passiveSkills) {
            if (passive.trigger == trigger.value) aTriggerSkill.add(passive);
        }
        return aTriggerSkill;
    }

    public float getSkillDamage(boolean isActiveSkill) {
        if (isActiveSkill) {
            return buffSkillDamage + (float) (point.getSkillDamage()) / 1000;
        }
        return (float) point.getSkillDamage() / 1000;
    }

    public void addLogDef(long value) {
        getHeroAnalysis().logDef += Math.abs(value);
    }

    public void addLogCrit(HeroBattleEntity atkHero, long effectHp) {
        if (atkHero != null) atkHero.getHeroAnalysis().logCritDamage += Math.abs(effectHp);
    }

    public void addLogHeal(long value) {
        getHeroAnalysis().logHeal += Math.abs(value);
    }

    public void addLogDamage(long value, boolean isDOTDamage) {
        getHeroAnalysis().logDamage += Math.abs(value);
        causedDamageInTurn += Math.abs(value);
        causedDamageInRound += Math.abs(value);
        if (isDOTDamage) getHeroAnalysis().logDOTDamage += Math.abs(value);
        else getHeroAnalysis().logNotDOTDamage += Math.abs(value);
        if (team == 1) mode.totalDamageByTeam1 += Math.abs(value);
    }

    public BattleHeroAnalysis getHeroAnalysis() {
        for (int i = 0; i <= mode.battleIndex; i++) {
            if (i > listHeroAnalysis.size() - 1) listHeroAnalysis.add(new BattleHeroAnalysis());
        }
        return listHeroAnalysis.get(mode.battleIndex);
    }

    @Override
    public String toString() {
        return String.format("team=%s, pos=%s, heroId=%s, id=%s", team, position, heroId, id);
    }

    public long[] addHp(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, long damageTaken, List<EffectEntity> aBattleEffect, boolean isHpEndRound, boolean isLastHit, boolean isDOTDamage, boolean needCheckCountHitBlock, boolean isSharingDamage, boolean isLastHurtEffect) {
        long[] result = new long[3];
        if (immortal > 0 && atkHero != null && !atkHero.breakImmortal()) {
            if (isLastHit) immortal--;
            if (immortal <= 0)
                aBattleEffect.addAll(effectController.removeBattleEffect(BattleEffectType.EFFECT_IMMORTAL));
            result[2] = 1;
            return result;
        }
        if (immortal2 > 0 && atkHero != null && !atkHero.breakImmortal()) {
            if (isLastHit) immortal2--;
            if (immortal2 <= 0) {
                aBattleEffect.addAll(effectController.removeBattleEffect(BattleEffectType.EFFECT_IMMORTAL2));
            }
            result[2] = 1;
            return result;
        }

        if (immortalByOppTurn > 0 && atkHero != null && !atkHero.breakImmortal()) {
            result[2] = 1;
            return result;
        }

        for (Map.Entry<BattleEffectType, Boolean> entry : BattleEffectType.mMaxDamageSuffer.entrySet()) {
            BattleEffectType battleEffectType = entry.getKey();
            boolean isIgnoreHpEndRound = entry.getValue();
            if (!effectController.contain(battleEffectType) || (isIgnoreHpEndRound && isHpEndRound)) continue;

            long maxDamageSufferByTurn = this.point.getMaxHp() * effectController.countValueEffect(battleEffectType) / 1000;
            if (this.damageTakenByTurn >= maxDamageSufferByTurn) {
                damageTaken = -1;
            } else {
                damageTaken = -Math.min(Math.abs(damageTaken), maxDamageSufferByTurn - this.damageTakenByTurn);
                damageTaken = -Math.max(Math.abs(damageTaken), 1);
            }
        }

        if (damageTaken < 0 && effectController.contain(BattleEffectType.DMG_SHIELD)) { // đỡ damage hộ đồng đội
            if (!isSharingDamage) {
                HeroBattleEntity shareHero = effectController.get(BattleEffectType.DMG_SHIELD).atkHero;
                if (!shareHero.sameHero(this) && shareHero.isHeroAndAlive()) {
                    int percent = (int) effectController.countValueEffect(BattleEffectType.DMG_SHIELD);
                    long shareDamage = Math.abs(damageTaken * percent / 1000);
                    if (shareDamage > Math.abs(damageTaken)) shareDamage = Math.abs(damageTaken + 1);
                    damageTaken = -(Math.abs(damageTaken) - shareDamage);
                    aBattleEffect.addAll(shareHero.beExactDamage2(atkInfo, atkHero, shareDamage, BattleConfig.HP_SPECIAL, 1, true, true, isLastHit, isLastHurtEffect));
                }
            }
        }

        if (damageTaken < 0 && effectController.contain(BattleEffectType.DMG_SHIELD1)) { // đỡ damage hộ đồng đội
            if (!isSharingDamage && !isHpEndRound && !isDOTDamage) {
                HeroBattleEntity shareHero = effectController.get(BattleEffectType.DMG_SHIELD1).atkHero;
                if (!shareHero.sameHero(this) && shareHero.isHeroAndAlive()) {
                    int percent = (int) effectController.countValueEffect(BattleEffectType.DMG_SHIELD1);
                    long shareDamage = Math.abs(damageTaken * percent / 1000);
                    if (shareDamage > Math.abs(damageTaken)) shareDamage = Math.abs(damageTaken + 1);
                    damageTaken = -(Math.abs(damageTaken) - shareDamage);
                    aBattleEffect.addAll(shareHero.beAttack(atkInfo, atkHero, "hurt", shareDamage, false, false, 0, isLastHit, 0, false, mode.getRound(), 1, new HashSet<>(), BattleConfig.HP_SPECIAL, true, true, isLastHurtEffect));
                }
            }
        }

        if (damageTaken < 0 && !isSharingDamage && !isHpEndRound && !isDOTDamage) {
            List<HeroBattleEntity> listAllyWhoTakeDmg = this.myTeam.stream().filter(ally -> ally.isHeroAndAlive() && !ally.sameHero(this) && ally.effectController.contain(BattleEffectType.takeDmgByAllyBeAttacked)).collect(Collectors.toList());
            for (HeroBattleEntity allyWhoTakeDmg : listAllyWhoTakeDmg) {
                int percent = (int) allyWhoTakeDmg.effectController.countValueEffect(BattleEffectType.takeDmgByAllyBeAttacked);
                long bonusDamage = Math.abs(damageTaken * percent / 1000);
                aBattleEffect.addAll(allyWhoTakeDmg.beAttack(atkInfo, atkHero, "hurt", bonusDamage, false, false, 0, isLastHit, 0, false, mode.getRound(), 1, new HashSet<>(), BattleConfig.HP_SPECIAL, true, true, isLastHurtEffect));
            }
        }

        damageTaken = checkEffectWeaken(damageTaken, atkHero);
        long effectHp = point.addHp(damageTaken);
        this.damageTakenByTurn += Math.abs(effectHp);

        if (this.effectController.contain(BattleEffectType.regenerative)) {
            if (point.getCurrentHP() <= 0 && atkHero != null && !atkHero.breakImmortal()) {
                point.setCurrentValue(Point.HP, 1);
                result[2] = 1;
                if (!needRemoveRegenerative) needRemoveRegenerative = true;
            }
        } else if (effectController.contain(BattleEffectType.EFFECT_HOPE_MARK)) {
            if (point.getCurrentHP() <= 0 && atkHero != null && !atkHero.breakImmortal()) {
                point.setCurrentValue(Point.HP, 1);
                result[2] = 1;
            }
        } else if (effectController.contain(BattleEffectType.EFFECT_HOPE_MARK2)) {
            if (point.getCurrentHP() <= 0 && atkHero != null && !atkHero.breakImmortal()) {
                point.setCurrentValue(Point.HP, 1);
                result[2] = 1;
                if (!isHopeMark2) {
                    EffectCount effectCount = this.effectController.get(BattleEffectType.EFFECT_HOPE_MARK2);
                    //Tăng công
                    long effectValue = this.point.getCalculatedValue(Point.ATTACK) * effectCount.getValue1(0) / 1000;
                    effectValue = this.point.addAttack(effectValue);
                    EffectCount otherEffectCount = this.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ATTACK_INC, SkillEffectType.ATTACK_PER_LEVEL.value, AMode.MAX_ROUND, effectValue);
                    if (otherEffectCount != null) {
                        if (!otherEffectCount.needToChangeHeroPoint()) this.point.addAttack(-effectValue);
                        aBattleEffect.add(this.effectController.getResultEffect(otherEffectCount));
                    } else this.point.addAttack(-effectValue);
                    //Tăng xuyên thủ
                    effectValue = this.point.addArmorBreak((long) effectCount.getValue2(0));
                    otherEffectCount = this.effectController.addBattleEffect(atkHero, BattleEffectType.BUFF_ARMOR_BREAK_INC, SkillEffectType.ARMOR_BREAK.value, AMode.MAX_ROUND, effectValue);
                    if (otherEffectCount != null) {
                        if (!otherEffectCount.needToChangeHeroPoint()) this.point.addArmorBreak(-effectValue);
                        aBattleEffect.add(this.effectController.getResultEffect(otherEffectCount));
                    }
                    this.isHopeMark2 = true;
                }
            }
        } else if (effectController.contain(BattleEffectType.EFFECT_PRE_HOPE_MARK1)) {
            if (point.getCurrentHP() <= 0 && atkHero != null && !atkHero.breakImmortal()) {
                point.setCurrentValue(Point.HP, 1);
                result[2] = 1;
                needRemoveAndTriggerPreHopeMark1 = true;
            }
        } else if (effectController.contain(BattleEffectType.EFFECT_HOPE_MARK1)) {
            if (point.getCurrentHP() <= 0 && atkHero != null && !atkHero.breakImmortal()) {
                point.setCurrentValue(Point.HP, 1);
                result[2] = 1;
            }
        }

        // Log for statistics
        mode.addTurnDamage(effectHp);

        if (atkHero != null) atkHero.addLogDamage(effectHp, isDOTDamage);
        this.addLogDef(effectHp);
        lastDamageTaken = effectHp;
        //        if (effectController.contain(BattleEffectType.EFFECT_DAMAGE_MARK)) addLogDamageMark(effectHp);

        result[0] = effectHp;

        checkBuffClanBoss(aBattleEffect);
        return result;
    }

    private void checkHopeMark2(HeroBattleEntity atkHero, List<EffectEntity> aBattleEffect) {
        if (this.isHopeMark2) {

        }
    }

    public void checkBuffClanBoss(List<EffectEntity> aBattleEffect) {
        if (this.mode.listRequiredDamage == null || this.mode.listRequiredDamage.isEmpty() || !this.effectController.contain(BattleEffectType.buffClanBoss))
            return;
        EffectCount effectCount = this.effectController.get(BattleEffectType.buffClanBoss);
        int lastIndex = (int) effectCount.getValue2(0);
        for (int i = lastIndex; i < this.mode.listRequiredDamage.size(); i++) {
            long requiredDamage = this.mode.listRequiredDamage.get(i);
            if (this.getHeroAnalysis().logDef < requiredDamage) break;


            long addHolyDamage = effectCount.getValue(0);
            EffectCount atkEffectCount = this.effectController.addBattleEffect(
                    this, BattleEffectType.BUFF_HOLY_DAMAGE_INC1, SkillEffectType.HOLY_DAMAGE.value, AMode.MAX_ROUND * 10, addHolyDamage);
            if (atkEffectCount != null) {
                this.point.addHolyDamage(addHolyDamage);
                EffectEntity effectEntity = this.effectController.getResultEffect(atkEffectCount);
                aBattleEffect.add(effectEntity);
            }
            effectCount.setValue2(0, i + 1);
        }
    }

    private long[] calculateDamageToSeal(AttackInfoEntity atkInfo, HeroBattleEntity atkHero, long damageTaken, List<EffectEntity> aBattleEffect, boolean isDOTDamage, boolean isSharingDamage, boolean isHpEndRound, boolean isLastHit, boolean isLastHurtEffect) {
        //        if (immortal > 0 || immortal2 > 0 || (immortalByOppTurn > 0 || effectController.contain(BattleEffectType.EFFECT_IMMORTAL_ROUND) || immortalTurn > 0)) {
        //            return new long[3];
        //        }
        if (immortal > 0 || immortal2 > 0 || immortalByOppTurn > 0) {
            return new long[3];
        }

        long damageToSeal = 0;
        long counterDamage = 0;
        List<EffectCount> listShieldEffectCount = new ArrayList<>();
        for (BattleEffectType shieldEffect : BattleEffectType.listShieldEffect) {
            if (!effectController.contain(shieldEffect)) continue;

            listShieldEffectCount.add(effectController.get(shieldEffect));
        }

        if (!isSharingDamage && !isHpEndRound && !isDOTDamage) {
            List<HeroBattleEntity> listAllyWhoTakeDmg = this.myTeam.stream().filter(ally -> ally.isHeroAndAlive() && !ally.sameHero(this) && ally.effectController.contain(BattleEffectType.takeDmgByAllyBeAttacked)).collect(Collectors.toList());
            for (HeroBattleEntity allyWhoTakeDmg : listAllyWhoTakeDmg) {
                int percent = (int) allyWhoTakeDmg.effectController.countValueEffect(BattleEffectType.takeDmgByAllyBeAttacked);
                long bonusDamage = Math.abs(damageTaken * percent / 1000);
                aBattleEffect.addAll(allyWhoTakeDmg.beAttack(atkInfo, atkHero, "hurt", bonusDamage, false, false, 0, isLastHit, 0, false, mode.getRound(), 1, new HashSet<>(), BattleConfig.HP_SPECIAL, true, true, isLastHurtEffect));
            }
        }

        //Khi nhận sát thương cần ưu tiên trừ effect được buff trước.
        listShieldEffectCount = listShieldEffectCount.stream().sorted(Comparator.comparing(effectCount -> effectCount.startRound)).collect(Collectors.toList());
        for (int i = 0; i < listShieldEffectCount.size(); i++) {
            EffectCount effectCount = listShieldEffectCount.get(i);
            if (damageTaken > 0) {
                long value = Math.min(effectCount.getValue(0), damageTaken);
                damageToSeal += value;
                effectCount.setValue(0, effectCount.getValue(0) - value);
                damageTaken -= value;
                if (List.of(BattleEffectType.EFFECT_ENERGY_SEAL, BattleEffectType.EFFECT_ENERGY_SEAL_2, BattleEffectType.EFFECT_ENERGY_SEAL_3).contains(effectCount.type)
                        && effectCount.getValue(0) <= 0) counterDamage += effectCount.getValue1(0);
            }
            //accShield thì ko xóa ở đây
            if (effectCount.getValue(0) <= 0 && !effectCount.type.equals(BattleEffectType.accShield)) {
                aBattleEffect.addAll(effectController.removeBattleEffect(effectCount.type));
            }
        }


        // Log for statistics
        mode.addTurnDamage(damageToSeal);
        if (atkHero != null) atkHero.addLogDamage(damageToSeal, isDOTDamage);
        addLogDef(damageToSeal);

        return new long[]{damageTaken, damageToSeal, counterDamage};
    }

    public void cntLostTurn(BattleEffectType battleEffect) {
        if (BattleEffectType.aCCEffect.contains(battleEffect) && !mode.currentTurn.isCC && !roundAttack) {
            mode.currentTurn.isCC = true;
            this.getHeroAnalysis().cntLostTurn++;
        }
    }

    public boolean hasToAtkTeammate() {
        if (isPet()) return false;

        if (isFrontLine()) {
            if (effectController.contain(BattleEffectType.EFFECT_ATTACK_FRONT_LINE_ALLY) || effectController.contain(BattleEffectType.EFFECT_ATTACK_FRONT_LINE_ALLY2)) {
                int otherPos = position == 0 ? 1 : 0;
                return myTeam.get(otherPos).isHeroAndAlive();
            }
        }
        if (effectController.contain(BattleEffectType.EFFECT_FASCINATE)) {
            HeroBattleEntity aliveHero = myTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.position != position).findFirst().orElse(null);
            return aliveHero != null;
        }
        return false;
    }

    public boolean isPet() {
        return position == BattleConfig.TEAM_INPUT - 1;
    }

    public boolean hasToAttackHeroWhoTauntMe() {
        return getHeroTauntMe() != null;
    }

    public HeroBattleEntity getHeroTauntMe() {
        if (!effectController.contain(BattleEffectType.taunt)) return null;
        EffectCount effectCount = effectController.get(BattleEffectType.taunt);
        HeroBattleEntity heroTauntMe = null;
        for (int i = 0; i < effectCount.countNumber(); i++) {
            int tauntPosition = (int) effectCount.getValue(i);
            for (HeroBattleEntity oppHero : oppTeam) {
                if (oppHero.position == tauntPosition && oppHero.isHeroAndAlive()) heroTauntMe = oppHero;
            }
        }
        return heroTauntMe;
    }

    public boolean forceNormalSkill() {
        if (effectController.contain(BattleEffectType.taunt) && hasToAttackHeroWhoTauntMe()) return true;
        if (isFrontLine()) {
            if (effectController.contain(BattleEffectType.EFFECT_ATTACK_FRONT_LINE_ALLY) || effectController.contain(BattleEffectType.EFFECT_ATTACK_FRONT_LINE_ALLY2)) {
                int otherPos = position == 0 ? 1 : 0;
                return myTeam.get(otherPos).isHeroAndAlive();
            }
        }
        return false;
    }

    public List<HeroBattleEntity> getAllyHasEffectLink() {
        return myTeam.stream().filter(hero -> hero.isHeroAndAlive() && !sameHero(hero) && hero.effectController.contain(BattleEffectType.EFFECT_LINK)).collect(Collectors.toList());
    }

    public long addAnger(long addAnger) {
        if (addAnger <= 0) return point.addAnger(addAnger);
        if (effectController.contain(BattleEffectType.EFFECT_MARK_OPPRESS)) return 0;
        //        long effectValue = effectController.countValueEffect(BattleEffectType.EFFECT_HOANG_LOAN); // chỉ sổ giảm buff nộ
        //        addAnger -= addAnger * effectValue / 1000;
        float addPercent = -(effectController.contain(BattleEffectType.EFFECT_HOANG_LOAN) ? CfgBattle.config.skill.hoangloan : 0);
        addPercent += (float) effectController.countValueEffect(BattleEffectType.BUFF_SPEC_MORE_ANGER) / 1000;
        addAnger += addAnger * addPercent;
        return point.addAnger(addAnger > 0 ? addAnger : 0);
    }

    private int countControlEnemy() {
        int number = (int) oppTeam.stream().filter(hero -> hero.isHeroAndAlive() && (hero.effectController.contain(BattleEffectType.EFFECT_STONE) || hero.effectController.contain(BattleEffectType.EFFECT_STUN) || hero.effectController.contain(BattleEffectType.EFFECT_SILENCE) || hero.effectController.contain(BattleEffectType.EFFECT_ICE_MARK) || hero.effectController.contain(BattleEffectType.EFFECT_FREEZE))).count();
        return number;
    }

    public void resetTurnDamageHp() {
        turnDamageHp = 0;
    }

    public long checkMaxDamageSuffer(long damage, int numberHit) {
        AtomicLong maxDamage = new AtomicLong(damage);
        List<BattleEffectType> listBattleEffectType = new ArrayList<>(List.of(BattleEffectType.EFFECT_MAX_DAMAGE_SUFFER,
                BattleEffectType.EFFECT_MAX_DAMAGE_SUFFER2,
                BattleEffectType.EFFECT_MAX_DAMAGE_SUFFER3));
        listBattleEffectType.stream().filter(battleEffectType -> this.effectController.contain(battleEffectType)).forEach(battleEffectType -> {
            EffectCount effectCount = this.effectController.get(battleEffectType);
            maxDamage.set(Math.min(maxDamage.get(), this.point.getMaxHp() * effectCount.getValue(0) / 1000 / numberHit));
        });

        return maxDamage.get();
    }

    public boolean couldUsePassiveSkill(int id) {
        if (disablePassiveSkill.contains(id)) {
            //            List<EffectCount> effectCounts = effectController.get(BattleEffectType.EFFECT_ICE_MARK);
            //            if (!effectCounts.isEmpty()) {
            //                EffectCount count = effectCounts.get(0);
            //                if (count.value > 0) return false;
            //            }
            return false;
        }
        return true;
    }

    public void revive(List<EffectEntity> battleEffect, long newHp) {
        point.setCurrentValue(Point.REVIVED, 1);
        if (newHp > point.getCalculatedValue(Point.HP)) newHp = point.getCalculatedValue(Point.HP);
        point.setCurrentValue(Point.HP, newHp);
        battleEffect.add(new EffectEntity(++mode.effectEntityId, this, BattleEffectType.EFFECT_REVIVE, newHp, point.getCalculatedValue(Point.HP), this.point.getCurrentAnger()));
        battleEffect.addAll(effectController.forceRemoveAllEffect());
        mode.clearTeamWinWhenRevive();
    }

    public boolean breakImmortal() {
        long rand = mode.nextRand();
        return rand < point.getBreakImmortal() + effectController.getBreakImmortalByHpPercent();
    }

    public int getRow() {
        return (position >= BattleConfig.TEAM_HERO_SIZE / 2) ? 1 : 0;
    }

    public int getLine() {
        return getLine(this.position);
    }

    public static int getLine(int position) {
        return position % 3;
    }

    public EffectEntity getResultEffectHpDec(long addHp, int typeHp, boolean isBlock, boolean isFightBack, boolean isDodge, long isImmortal, boolean isSuicide, boolean isBonusDamage) {
        EffectEntity effectEntity = new EffectEntity(++mode.effectEntityId, this, BattleEffectType.BUFF_HP_DEC, isBonusDamage, point.getCurrentHP(), addHp, typeHp, getTotalShieldHp(), isBlock ? 1 : 0, isFightBack ? 1 : 0, isDodge ? 1 : 0, isImmortal, isSuicide ? 1 : 0);
        return effectEntity;
    }

    public EffectEntity getResultEffectHpInc(HeroBattleEntity atkHero, long addHp) {
        int typeHeal = 0;
        if (atkHero != null && (CfgBattle.config.heroSpecialHeal.contains(atkHero.heroId) || CfgBattle.config.heroSpecialHeal.contains(atkHero.skin)))
            typeHeal = 1;
        return new EffectEntity(++mode.effectEntityId, this, BattleEffectType.BUFF_HP_INC, point.getCurrentHP(), addHp, typeHeal);
    }

    public EffectEntity getResultEffectAnger(HeroBattleEntity atkHero, long addAnger, boolean isBuff) {
        if (addAnger < 0 && isBuff && atkHero != null) atkHero.getHeroAnalysis().pointCC += 1;
        return new EffectEntity(++mode.effectEntityId, this, addAnger > 0 ? BattleEffectType.BUFF_ANGER_INC : BattleEffectType.BUFF_ANGER_DEC, addAnger, point.getCurrentAnger(), isBuff ? 1 : 0);
    }

    public long checkAndGetReduceHeal(long addHp, AttackInfoEntity attackInfo) {
        //        long reduceHp = Math.abs(addHp * this.effectController.countValueEffect(BattleEffectType.EFFECT_REDUCE_HEAL) / 1000);
        //        if (reduceHp > 0) {
        //            EffectCount effectCount = this.effectController.get(BattleEffectType.EFFECT_REDUCE_HEAL).get(0);
        //            this.beAttack(attackInfo, effectCount.atkHero, "", reduceHp, false, false, false, 0, 1);
        //            effectCount.value2 += reduceHp;
        //        }
        //
        //        return reduceHp;

        return 0;
    }

    public long getTotalShieldHp() {
        long result = 0;
        for (BattleEffectType battleEffectType : BattleEffectType.listShieldEffect) {
            result += effectController.countValueEffect(battleEffectType);
        }
        return result;
    }

    public boolean isSummonHero() {
        return this.summonedByPosition != -1 || this.roundBeSummoned != -1;
    }

    public boolean isAbleToTrigger13() {
        return true;
    }

    private List<EffectEntity> checkThenRemoveSameEffectBefore(BattleEffectType battleEffectType) {
        ResBattleEffectEntity resBattleEffect = ResHero.getResBattleEffect(battleEffectType.value);
        if (resBattleEffect.getType() == STACK_COUNTDOWN_ROUND) return new ArrayList<>();

        List<EffectEntity> resultEffect = new ArrayList<>();
        for (List<Integer> listSameBattleEffectNeedToReplace : CfgBattle.config.listOfListSameBattleEffectNeedToReplace) {
            if (!listSameBattleEffectNeedToReplace.contains(battleEffectType.value)) continue;

            for (Integer sameBattleEffect : listSameBattleEffectNeedToReplace) {
                BattleEffectType sameBattleEffectType = BattleEffectType.get(sameBattleEffect);
                if (sameBattleEffectType.equals(battleEffectType)) continue;
                resultEffect.addAll(this.effectController.removeBattleEffect(sameBattleEffectType));
            }
        }

        return resultEffect;
    }

    private int countDotEffect() {
        int count = 0;
        if (this.effectController.contain(BattleEffectType.EFFECT_DOT_FIRE)) count++;
        if (this.effectController.contain(BattleEffectType.EFFECT_DOT_BLOOD)) count++;
        if (this.effectController.contain(BattleEffectType.EFFECT_DOT_POISON)) count++;

        return count;
    }

    public void checkSoulMark() {
        if (!this.effectController.contain(BattleEffectType.soulMark) || !this.effectController.contain(BattleEffectType.soulMarkPower))
            return;

        EffectCount effectCount = this.effectController.get(BattleEffectType.soulMark);
        EffectCount soulMarkPowerEffectCount = this.effectController.get(BattleEffectType.soulMarkPower);
        for (int j = 0; j < soulMarkPowerEffectCount.listRequire.size(); j++) {
            int require = soulMarkPowerEffectCount.listRequire.get(j);
            int pointIndex = soulMarkPowerEffectCount.listPointIndex.get(j);
            int value = soulMarkPowerEffectCount.listPointValue.get(j);
            if (effectCount.countNumber() != require) continue;

            this.point.add(Point.CURRENT_VALUES_INDEX, pointIndex, value);
        }
    }

    private void checkChosoMark(HeroBattleEntity heroWhoHasChosoMark, EffectCount chosoMarkEffectCount, List<EffectEntity> resultEffect) {
        if (chosoMarkEffectCount.countNumber() == chosoMarkEffectCount.getValue2(0)) {
            long moreHealValue = 1000;
            EffectCount moreHealEffectCount = heroWhoHasChosoMark.effectController.addBattleEffect(chosoMarkEffectCount.atkHero, BattleEffectType.BUFF_MORE_HEAL_INC, SkillEffectType.POINT_MORE_HEAL.value, AMode.MAX_ROUND, moreHealValue);
            if (moreHealEffectCount != null) {
                if (moreHealEffectCount.needToChangeHeroPoint()) {
                    heroWhoHasChosoMark.point.addMoreHeal(moreHealValue);
                }
                resultEffect.add(heroWhoHasChosoMark.effectController.getResultEffect(moreHealEffectCount));
            }
        }

        if (chosoMarkEffectCount.countNumber() == chosoMarkEffectCount.getValue3(0)) {
            EffectCount hopeMarkEffectCount = heroWhoHasChosoMark.effectController.addBattleEffect(chosoMarkEffectCount.atkHero, BattleEffectType.EFFECT_HOPE_MARK, SkillEffectType.HOPE.value, 3);
            if (hopeMarkEffectCount != null) {
                resultEffect.add(heroWhoHasChosoMark.effectController.getResultEffect(hopeMarkEffectCount));
            }
        }
    }

    private long checkHanaMarkToDecDmg(long attack, int numberHit) {
        attack = checkHanaMarkToDecDmg(BattleEffectType.hanaMark, attack, numberHit);
        attack = checkHanaMarkToDecDmg(BattleEffectType.hanaMark1, attack, numberHit);
        return attack;
    }

    private long checkHanaMarkToDecDmg(BattleEffectType battleEffectType, long attack, int numberHit) {
        if (this.effectController.contain(battleEffectType)) {
            EffectCount effectCount = this.effectController.get(battleEffectType);
            long totalAttack = attack * numberHit;
            if (totalAttack <= this.point.getMaxHp() * effectCount.getTotalValue() / 1000) return attack;

            totalAttack -= totalAttack * Math.abs(effectCount.getTotalValue1()) / 1000;
            effectCount.setValue4(0, 1);
            return totalAttack / numberHit;
        }

        return attack;
    }

    private long checkEffectWeaken(long damageTaken, HeroBattleEntity atkHero) {
        if (damageTaken > 0) return damageTaken;
        int total = 0;
        if (atkHero != null && atkHero.effectController.contain(BattleEffectType.weaken)) { // giảm sát thương gây ra
            EffectCount effectCount = atkHero.effectController.get(BattleEffectType.weaken);
            int limitStack = (int) effectCount.getValue3(0);
            for (int i = 0; i < Math.min(effectCount.countNumber(), limitStack); i++) {
                total -= effectCount.getValue(i);
            }
        }
        if (effectController.contain(BattleEffectType.weaken)) { // tăng sát thương nhận vào
            EffectCount effectCount = effectController.get(BattleEffectType.weaken);
            int limitStack = (int) effectCount.getValue3(0);
            for (int i = 0; i < Math.min(effectCount.countNumber(), limitStack); i++) {
                total -= effectCount.getValue(i);
            }
        }
        return Math.min(damageTaken + damageTaken * total / 1000, -1);
    }

    public long getReduceSingleAttack() {
        return Math.min(800, this.effectController.countValueEffect(BattleEffectType.reduceSingleAttack) + this.point.getCurrentValue(Point.reduceSingleAttack));
    }

    public long getDodge(HeroBattleEntity atkHero) {
        long dodge = this.point.getCurrentValue(Point.DODGE);
        if (atkHero != null && HeroType.isKhacHe(this.faction, atkHero.faction))
            dodge += this.effectController.countValue2Effect(BattleEffectType.buffType);
        if (atkHero != null) dodge += atkHero.effectController.countValueEffect(BattleEffectType.teemoMark);
        return dodge;
    }

    public void debug(String string) {
        if (CfgServer.isTestServer()) System.out.println(string);
    }
}
