package monster.service.battle.common.entity;

import com.google.gson.Gson;
import lombok.Getter;
import monster.service.battle.common.config.SkillEffectType;

import java.util.List;

@Getter
public class SkillEntity {
    public int id, animation;
    public int trigger;
    public List<SkillEffect> effects;
    public long power;
    public long powerBonus;
    public List<ChangeSkillCondition> listChangeSkillCondition;
    public boolean isArtifactSkill = false;

    public class SingleSkillEffect {
        public int round, pointIndex, count;
        public String type;
        public float num, num1, num2, num3, num4, ratio;
        public List<Integer> listRequire, listPointIndex, listPointValue, listEffectId;

        @Override
        public String toString() {
            return "num = " + num + ", num1 = " + num1 + ", num2 = " + num2 + ", num3 = " + num3 + ", num4 = " + num4 + ", count = " + count + ", round = " + round + ", ratio = " + ratio;
        }
    }

    public class SkillEffect {
        public int obj, rand;
        public List<SingleSkillEffect> listEffect;
    }

    public SkillEntity cloneInstance() {
        Gson gson = new Gson();
        return gson.fromJson(gson.toJson(this), SkillEntity.class);
    }

    public boolean isChangeCombat() {
        if (effects == null) return false;

        for (SkillEffect skillEffect : effects) {
            for (SingleSkillEffect singleSkillEffect : skillEffect.listEffect) {
                if (singleSkillEffect.type.equals("changeCombat")) return true;
            }
        }
        return false;
    }

    public class ChangeSkillCondition {
        public int skillId;
        public List<Integer> conditionValue;
    }

    @Override
    public String toString() {
        return "id = " + id;
    }

    public boolean hasAttackSkill() {
        return effects.stream().anyMatch(skillEffect -> skillEffect.listEffect.stream().anyMatch(singleSkillEffect -> {
            SkillEffectType skillEffectType = SkillEffectType.get(singleSkillEffect.type);
            return skillEffectType != null && !skillEffectType.isBuffEffect;
        }));
    }
}
