package monster.service.battle.common;

import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import monster.config.CfgServer;
import monster.config.penum.BattleType;
import monster.dao.mapping.main.ResHeroEntity;
import monster.service.battle.common.config.BattleEffectType;
import monster.service.battle.common.config.HeroType;
import monster.service.battle.common.config.SkillEffectType;
import monster.service.battle.common.config.TriggerType;
import monster.service.battle.common.entity.EffectController;
import monster.service.battle.common.entity.EffectCount;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.battle.common.entity.proto.AttackInfoEntity;
import monster.service.battle.common.entity.proto.EffectEntity;
import monster.service.battle.common.entity.proto.HeroTurnEntity;
import monster.service.battle.common.service.TargetService;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.IMath;
import monster.service.battle.dependence.Point;
import monster.service.battle.dependence.entity.SimulateResult;
import monster.service.monitor.Telegram;
import monster.service.resource.ResHero;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public abstract class AMode {
    int[][] distance = new int[][]{{1, 2, 3, 4, 5, 6},// 0
            {2, 1, 3, 5, 4, 6},// 1
            {3, 2, 1, 6, 5, 4},// 2
            {1, 2, 3, 4, 5, 6},// 3
            {2, 1, 3, 5, 4, 6},// 4
            {3, 2, 1, 6, 5, 4},// 5
    };

    public int battleIndex = 0;
    public String strTeam1 = "";
    public String strTeam2 = "";
    public BattleType battleType = BattleType.NONE; // common
    public String debug = "";
    public TargetService targetService = new TargetService(this);
    public List<HeroTurnEntity> aHeroTurn = new ArrayList<>();
    public HeroTurnEntity currentTurn, camloHeroTurn = new HeroTurnEntity(0);
    public Map<TriggerType, List<HeroBattleEntity>> mTrigger = new HashMap<>();

    // Turn info
    public List<HeroBattleEntity> heroBeSkillDamage = new ArrayList<>();
    public List<HeroBattleEntity> heroBeAttack = new ArrayList<>();
    public List<HeroBattleEntity> heroBeCrit = new ArrayList<>();
    public List<HeroBattleEntity> heroBeAttackNoCrit = new ArrayList<>();
    public List<HeroBattleEntity> heroBlock = new ArrayList<>();
    public List<HeroBattleEntity> heroDie = new ArrayList<>();
    public List<HeroBattleEntity> heroDie2 = new ArrayList<>();
    public Set<HeroBattleEntity> heroDieTriggered = new HashSet<>();
    public Map<HeroBattleEntity, List<HeroBattleEntity>> listHeroDieMapByAtkHero = new HashMap<>();
    public List<HeroBattleEntity> heroBelow80HP = new ArrayList<>();
    public List<HeroBattleEntity> heroBelow50HP = new ArrayList<>();
    public List<HeroBattleEntity> heroBelow30HP = new ArrayList<>();
    public List<HeroBattleEntity> heroLostHPInTurn = new ArrayList<>();
    public Map<HeroBattleEntity, Integer> heroBeBuffOrDebuff = new HashMap<>();
    public List<HeroBattleEntity> listHeroDodge = new ArrayList<>();
    public List<HeroBattleEntity> listHeroMoreTurn = new ArrayList<>();
    public HeroBattleEntity forceAttackHero = null;
    public List<HeroBattleEntity> listHeroBeControlled = new ArrayList<>();
    public List<Map<HeroBattleEntity, Long>> listCounterDamageOnHitMapByHitIndex = new ArrayList<>();
    public Map<HeroBattleEntity, List<Integer>> listSkillNeedToProcessMapByHero = new HashMap<>();

    //những target hero cần check 1 vài thứ như Excited, Fightback,...
    public Set<HeroBattleEntity> listTargetHeroNeedToCheckSomething = new HashSet<>();

    public HeroBattleEntity atkHero;
    public long totalDamageByTurn, totalDamageByTeam1;
    public long atkPower = 0, defPower = 0, highestTotalDamage = 0;
    public int countEffect, countBuffEffect, countSkill = 0;
    public int effectEntityId = 0;

    //    public BattleAnalysis analysis = new BattleAnalysis();

    List<HeroBattleEntity> atkTeam = new ArrayList<>(6);
    List<HeroBattleEntity> defTeam = new ArrayList<>(6);
    List<HeroBattleEntity> aHero = new ArrayList<>();

    // các biến hộ trợ cho relic
    // các biến hộ trợ cho relic
    public int damageEndRound = 0, killerBelowHp = 0;
    public float energySealBelow15 = 0, energySealWhenOppActiveSkill = 0;
    public long totalHealBySingleSkillEffectCount = 0;
    public boolean showBattleId = false;

    @Getter
    @Setter
    HeroBattleEntity atkPet, defPet;
    @Getter
    HeroBattleEntity atkBreath, defBreath;
    public long randomSeed;
    public List<Long> listRequiredDamage = new ArrayList<>();
    int teamWin = 0, turnIndex = 0;
    @Getter
    int round = 0;
    public static final int MAX_ROUND = 15;
    public boolean isTeam1AttackFirst = true;

    public transient Random random;

    public AMode() {

    }

    public abstract List<HeroTurnEntity> simulate();

    public abstract void processSkill(HeroBattleEntity hero, SkillEntity skill, int skillType, HeroBattleEntity fixTarget, boolean... info);

    public abstract void processTriggerSkill(HeroBattleEntity hero, TriggerType trigger);

    public void setAtkBreath(HeroBattleEntity atkBreath) {
        atkBreath.team = 1;
        this.atkBreath = atkBreath;
    }

    public void setDefBreath(HeroBattleEntity defBreath) {
        defBreath.team = 2;
        this.defBreath = defBreath;
    }

    public int setTeam(boolean isAtkTeam, HeroBattleEntity[] team, boolean isPVP) {
        List<HeroBattleEntity> aTeam = isAtkTeam ? atkTeam : defTeam;
        for (int i = 0; i < team.length; i++) {
            if (team[i] != null) {
                aTeam.add(team[i]);
                team[i].init();
                team[i].team = isAtkTeam ? 1 : 2;
                team[i].position = i % BattleConfig.TEAM_HERO_SIZE;
                team[i].mode = this;
            } else aTeam.add(new HeroBattleEntity());
        }

        int value = IMath.checkBonusFormation(isAtkTeam, aTeam, isPVP);

        for (HeroBattleEntity heroBattleEntity : team) {
            if (heroBattleEntity != null) {
                heroBattleEntity.startBattlePoint = heroBattleEntity.point.cloneInstance();
                heroBattleEntity.mode = this;
            }
        }

        return value;
    }

    public int getTeam(boolean isAtkTeam, HeroBattleEntity[] team, boolean isBotTeam) {
        List<HeroBattleEntity> aTeam = isAtkTeam ? atkTeam : defTeam;
        return IMath.checkBonusFormation(isAtkTeam, aTeam, isBotTeam);
    }

    boolean isEndBattle() {
        boolean team1Loose = true, team2Loose = true;
        for (HeroBattleEntity hero : atkTeam) {
            if (hero.point.getCurrentHP() > 0) {
                team1Loose = false;
                break;
            }
        }
        for (HeroBattleEntity hero : defTeam) {
            if (hero.point.getCurrentHP() > 0) {
                team2Loose = false;
                break;
            }
        }
        if (team1Loose && team2Loose) teamWin = 2;
        else teamWin = team1Loose ? 2 : (team2Loose ? 1 : 0);
        return teamWin > 0;
    }

    void initHeroInfo(HeroBattleEntity hero) {
        if (hero != null && hero.isHero()) {
            if (hero.position < 6) {
                if (hero.clazz == null || hero.faction == null) {
                    ResHeroEntity resHero = ResHero.getHero(hero.heroId);
                    if (resHero == null) {
                        debug("Error init heroBattle id = " + hero.id + " heroId = " + hero.heroId + ", team =" + hero.team + ", pos = " + hero.position);
                        Telegram.sendNotify("Error init heroBattle id = " + hero.id + " heroId = " + hero.heroId + ", team =" + hero.team + ", pos = " + hero.position);
                    } else {
                        hero.clazz = resHero.getHeroClass();
                        hero.faction = resHero.getHeroFaction();
                    }
                }
                aHero.add(hero); // no target for pet
            } else if (hero.position == 6) {
                hero.faction = HeroType.FACTION_NULL;
            }
            hero.setMyTeam(hero.team == 1 ? atkTeam : defTeam);
            hero.setOppTeam(hero.team == 1 ? defTeam : atkTeam);
            hero.myHeroBreath = hero.team == 1 ? atkBreath : defBreath;
            hero.oppHeroBreath = hero.team == 1 ? defBreath : atkBreath;
            hero.effectController = new EffectController(hero);
            hero.mode = this;
            hero.point.setCurrentValue(Point.HP, Math.min(hero.point.getCurrentHP(), hero.point.getCalculatedValue(Point.HP)));
            if (hero.point.getCurrentValue(Point.SPEC_IMMORTAL_FOREVER) == 1) {
                hero.point.setCurrentValue(Point.SPEC_IMMORTAL_FOREVER, 0);
                for (HeroBattleEntity tmpHero : hero.oppTeam) {
                    if (tmpHero.isHero()) {
                        //                        if (tmpHero.faction == HeroType.FACTION_LUNISOLAR) {
                        //                            hero.point.set(Point.SPEC_IMMORTAL_FOREVER, 1);
                        //                            break;
                        //                        }
                    }
                }
            } else if (hero.point.getCurrentValue(Point.SPEC_IMMORTAL_FOREVER) == 2) {
                hero.point.setCurrentValue(Point.SPEC_IMMORTAL_FOREVER, 0);
                for (HeroBattleEntity tmpHero : hero.oppTeam) {
                    if (tmpHero.isHero()) {
                        //                        if (tmpHero.faction != HeroType.FACTION_LUNISOLAR) {
                        //                            hero.point.set(Point.SPEC_IMMORTAL_FOREVER, 1);
                        //                            break;
                        //                        }
                    }
                }
            }

            //            showTeamAnger(atkTeam, 0);
            //            showTeamAnger(defTeam, 0);
        }
    }

    void initBattle() {
        this.round = 0;
        this.turnIndex = 0;
        //
        for (HeroBattleEntity hero : atkTeam) initHeroInfo(hero);
        for (HeroBattleEntity hero : defTeam) {
            initHeroInfo(hero);
        }

        initHeroInfo(atkPet);
        initHeroInfo(defPet);

        if (List.of(BattleType.MODE_CHALLENGE).contains(battleType)) {
            atkBreath = null;
            defBreath = null;
        } else {
            atkBreath.position = BattleConfig.TEAM_INPUT;
            defBreath.position = BattleConfig.TEAM_INPUT;
        }
        initHeroInfo(atkBreath);
        initHeroInfo(defBreath);
        //

        sortHeroSpeed();
        isTeam1AttackFirst = aHero.get(0).team == 1;
        aHero.stream().filter(hero -> hero.passiveSkills != null).forEach(hero -> hero.passiveSkills.forEach(passiveSkill -> addTrigger(hero, passiveSkill)));
        if (atkBreath != null && atkBreath.passiveSkills != null)
            atkBreath.passiveSkills.forEach(passiveSkill -> addTrigger(atkBreath, passiveSkill));
        if (defBreath != null && defBreath.passiveSkills != null)
            defBreath.passiveSkills.forEach(passiveSkill -> addTrigger(defBreath, passiveSkill));
    }

    public void sortHeroSpeed() {
        aHero.sort((hero1, hero2) -> (int) (hero2.point.getCurrentSpeed() - hero1.point.getCurrentSpeed()));
        this.turnIndex = -1;
    }

    void addTrigger(HeroBattleEntity hero, SkillEntity passive) {
        TriggerType trigger = TriggerType.get(passive.trigger);
        if (!mTrigger.containsKey(trigger)) mTrigger.put(trigger, new ArrayList<>());
        if (!mTrigger.get(trigger).contains(hero)) mTrigger.get(trigger).add(hero);
    }

    protected void showTeam() {
        atkTeam.stream().filter(hero -> hero.isHero()).forEach(hero ->
                {
                    strTeam1 += hero.toString();
                    addDebugString(String.format("team %s, pos %s, id %s, heroId %s, level %s star %s skin %s, point %s \n", hero.team, hero.position, hero.id, hero.heroId, hero.level, hero.star, hero.skin, hero.point.toString()));
                    debug(String.format("team %s, pos %s, id %s, heroId %s, level %s star %s skin %s, point %s", hero.team, hero.position, hero.id, hero.heroId, hero.level, hero.star, hero.skin, hero.point.toString()));
                }
        );

        if (atkPet != null)
            debug(String.format("pet id %s, level %s -> %s", atkPet.heroId, atkPet.level, atkPet.point.toString()));
        if (atkBreath != null && atkBreath.isHero() && atkBreath.passiveSkills != null)
            debug(String.format("atk breath, skills -> %s", atkBreath.passiveSkills.stream().map(SkillEntity::getId).collect(Collectors.toList())));

        addDebugString("-----\n");
        debug("---");
        defTeam.stream().filter(hero -> hero.isHero()).forEach(hero ->
                {
                    strTeam2 += hero.toString();
                    addDebugString(String.format("team %s, pos %s, id %s, heroId %s, level %s star %s skin %s, point %s \n", hero.team, hero.position, hero.id, hero.heroId, hero.level, hero.star, hero.skin, hero.point.toString()));
                    debug(String.format("team %s, pos %s, id %s, heroId %s, level %s star %s skin %s, point %s", hero.team, hero.position, hero.id, hero.heroId, hero.level, hero.star, hero.skin, hero.point.toString()));
                }
        );
        if (defPet != null)
            debug(String.format("pet id %s, level %s -> %s ", defPet.id, defPet.level, defPet.point.toString()));
        if (defBreath != null && defBreath.isHero() && defBreath.passiveSkills != null)
            debug(String.format("def breath, skills -> %s", defBreath.passiveSkills.stream().map(SkillEntity::getId).collect(Collectors.toList())));
    }

    public SimulateResult setEndBattleInfo(SimulateResult simulate) {
        simulate.showBattleId = showBattleId;
        simulate.teamWin = getTeamWin();
        simulate.isTeam1AttackFirst = isTeam1AttackFirst;

        int index = 0;
        for (HeroBattleEntity hero : atkTeam) {
            if (hero != null && hero.isHero()) {
                if (highestTotalDamage < Math.abs(hero.getHeroAnalysis().logDamage)) {
                    highestTotalDamage = Math.abs(hero.getHeroAnalysis().logDamage);
                }
                if (index < simulate.team1.size())
                    simulate.team1.get(index).percentHp = Math.abs(hero.point.percentHpFloat());
                if (hero.isSecondaryHero) {
                    simulate.team1.stream().filter(simulateHero -> simulateHero.pos == hero.position).findFirst()
                            .ifPresent(simulateHero -> simulateHero.heroAnalysis.merge(hero.getHeroAnalysis()));
                }

                index++;
            }
        }
        index = 0;
        for (HeroBattleEntity hero : defTeam) {
            if (hero != null && hero.isHero()) {
                if (index < simulate.team2.size()) {
                    simulate.team2.get(index).percentHp = Math.abs(hero.point.percentHpFloat());
                }
                if (hero.isSecondaryHero) {
                    simulate.team2.stream().filter(simulateHero -> simulateHero.pos == hero.position).findFirst()
                            .ifPresent(simulateHero -> simulateHero.heroAnalysis.merge(hero.getHeroAnalysis()));
                }

                index++;
            }

        }
        simulate.addTeam(atkBreath);
        simulate.addTeam(defBreath);
        simulate.atkPercentHp = getListPercentHp(true);
        simulate.defPercentHp = getListPercentHp(false);
        simulate.numberRound = getRound();
        simulate.highestTotalDamage = highestTotalDamage;
        if (atkPet != null) simulate.setPet(atkPet);
        if (defPet != null) simulate.setPet(defPet);
        return simulate;
    }

    public List<Long> getListAtkHeroLive() {
        List<Long> aliveHeroInfo = new ArrayList<>();
        boolean isKilledAllEnemy = defTeam.stream().filter(hero -> hero.position < BattleConfig.TEAM_HERO_SIZE).noneMatch(HeroBattleEntity::isHeroAndAlive);
        if (isKilledAllEnemy) {
            List<HeroBattleEntity> listAtkHero = atkTeam.stream().filter(hero -> hero.isHeroAndAlive() && hero.position < BattleConfig.TEAM_HERO_SIZE).collect(Collectors.toList());
            listAtkHero.forEach(hero -> {
                aliveHeroInfo.add((long) hero.position);
                aliveHeroInfo.add(hero.point.percentHpLong());
                aliveHeroInfo.add(hero.point.getCurrentAnger());
            });
        }
        return aliveHeroInfo;
    }

    public List<Long> getListDefHeroLive2() {
        List<Long> defHerosLive = new ArrayList<>();
        for (HeroBattleEntity hero : defTeam) {
            if (hero.isHeroAndAlive()) { // position - realhp - anger
                defHerosLive.add((long) hero.position);
                defHerosLive.add((long) (hero.point.percentHpFloat() * 100));
            }
        }
        return defHerosLive;
    }

    public List<Long> getListDefHeroLive() {
        List<Long> defHerosLive = new ArrayList<>();
        for (HeroBattleEntity hero : defTeam) {
            if (hero.isHeroAndAlive()) { // position - realhp - anger
                defHerosLive.add((long) hero.position);
                defHerosLive.add(hero.point.percentHpLong());
                defHerosLive.add(hero.point.getCurrentAnger());
            }
        }
        return defHerosLive;
    }

    public boolean isClearDefTeam() {
        for (HeroBattleEntity hero : defTeam) {
            if (hero.isHeroAndAlive()) {
                return false;
            }
        }
        return true;
    }

    protected List<HeroBattleEntity> getAliveHero() {
        return aHero.stream().filter(hero -> hero.isHeroAndAlive()).collect(Collectors.toList());
    }

    public int getNumberDie(boolean team1) {
        List<HeroBattleEntity> team = team1 ? atkTeam : defTeam;
        return (int) team.stream().filter(hero -> hero.isHero() && hero.point.getCurrentHP() == 0).count();
    }

    public int getPercentDie(boolean team1) {
        List<HeroBattleEntity> team = team1 ? atkTeam : defTeam;
        int numberDie = (int) team.stream().filter(hero -> hero.isHero() && hero.point.getCurrentHP() == 0).count();
        int numberAlive = (int) team.stream().filter(hero -> hero.isHero() && hero.point.getCurrentHP() > 0).count();
        return numberAlive == 0 ? 100 : numberDie * 100 / (numberAlive + numberDie);
    }

    public int getTeamWin() {
        return teamWin;
    }

    public void clearTeamWinWhenRevive() {
        teamWin = 0;
    }

    int randIndex = 0;

    public int nextRand() {
        if (random == null) random = new Random(randomSeed);
        return random.nextInt(1000);
    }

    public void addHeroDodge(HeroBattleEntity hero) {
        if (!this.listHeroDodge.contains(hero))
            this.listHeroDodge.add(hero);
    }

    public void addHeroBeBlock(HeroBattleEntity hero) {
        if (!this.heroBlock.contains(hero)) this.heroBlock.add(hero);
    }

    public void addHeroDie(HeroBattleEntity hero, HeroBattleEntity atkHero, List<EffectEntity> battleEffects) {
        if (!this.heroDie2.contains(hero)) this.heroDie2.add(hero);
        if (atkHero != null) {
            if (!this.listHeroDieMapByAtkHero.containsKey(atkHero))
                this.listHeroDieMapByAtkHero.put(atkHero, new ArrayList<>());
            if (!this.listHeroDieMapByAtkHero.get(atkHero).contains(hero))
                this.listHeroDieMapByAtkHero.get(atkHero).add(hero);
        }
        if (this.heroDie.contains(hero)) return;
        // Hồi sinh trong trường hợp có hiệu ứng REVIVE
        if (!hero.point.isRevived() && hero.effectController.contain(BattleEffectType.EFFECT_SURVIVAL)) {
            EffectCount effectCount = hero.effectController.get(BattleEffectType.EFFECT_SURVIVAL);
            long newHp = effectCount.getValue(0);
            hero.revive(battleEffects, newHp);
            hero.immortalByOppTurn = 2;
            return;
        }
        this.heroDie.add(hero);
        if (battleEffects != null) {
            if (atkHero != null) {
                atkHero.numberKill++;
                for (int i = battleEffects.size() - 1; i >= 0; i--) {
                    EffectEntity tmp = battleEffects.get(i);
                    if (tmp.getEffectId() == BattleEffectType.EFFECT_KILLER.value && tmp.team == atkHero.team && tmp.position == atkHero.position) {
                        battleEffects.remove(i);
                    }
                }
                battleEffects.add(new EffectEntity(++effectEntityId, atkHero, BattleEffectType.EFFECT_KILLER, atkHero.team, atkHero.position, atkHero.numberKill));
            }

            hero.oppTeam.stream().filter(HeroBattleEntity::isHeroAndAlive).forEach(enemyHero -> {
                if (enemyHero.effectController.contain(BattleEffectType.taunt)) {
                    EffectCount effectCount = enemyHero.effectController.get(BattleEffectType.taunt);
                    int tauntHeroPosition = (int) effectCount.getValue(0);
                    int tauntHeroTeam = (int) effectCount.getValue1(0);
                    if (tauntHeroPosition == hero.position && tauntHeroTeam == hero.team) {
                        battleEffects.addAll(enemyHero.effectController.removeBattleEffect(BattleEffectType.taunt));
                    }
                }
            });
            hero.myTeam.stream().filter(ally -> ally.isHeroAndAlive() && ally.effectController.contain(BattleEffectType.onlySingleTarget1)).forEach(ally -> {
                EffectCount effectCount = ally.effectController.get(BattleEffectType.onlySingleTarget1);
                long addAttack = ally.point.getCalculatedValue(Point.ATTACK) * ally.effectController.countValueEffect(BattleEffectType.onlySingleTarget1) / 1000;
                addAttack = ally.point.addAttack(addAttack);
                EffectCount atkEffectCount = ally.effectController.addBattleEffect(effectCount.atkHero, BattleEffectType.atkPForeverInc, SkillEffectType.atkPForever.value, AMode.MAX_ROUND, addAttack);
                if (atkEffectCount != null && atkEffectCount.needToChangeHeroPoint())
                    battleEffects.add(ally.effectController.getResultEffect(atkEffectCount));
                else ally.point.addAttack(-addAttack);
            });
        }
    }

    public void addHeroBeAttack(HeroBattleEntity hero) {
        if (!heroBeAttack.contains(hero)) heroBeAttack.add(hero);
    }

    public void addHeroBeBuffOrDebuff(HeroBattleEntity hero) {
        if (!heroBeBuffOrDebuff.containsKey(hero)) heroBeBuffOrDebuff.put(hero, 1);
        else heroBeBuffOrDebuff.put(hero, heroBeBuffOrDebuff.get(hero) + 1);
    }

    public void addHeroBeSkillDamage(HeroBattleEntity hero) {
        if (!heroBeSkillDamage.contains(hero)) heroBeSkillDamage.add(hero);
    }

    public void addHeroBeCrit(HeroBattleEntity hero) {
        if (!heroBeCrit.contains(hero)) heroBeCrit.add(hero);
    }

    public void addHeroBeAttackNoCrit(HeroBattleEntity hero) {
        if (!heroBeAttackNoCrit.contains(hero)) heroBeAttackNoCrit.add(hero);
    }

    public void addListSkillNeedToProcessMapByHero(HeroBattleEntity hero, int skillId) {
        if (!listSkillNeedToProcessMapByHero.containsKey(hero))
            listSkillNeedToProcessMapByHero.put(hero, new ArrayList<>());
        listSkillNeedToProcessMapByHero.get(hero).add(skillId);
    }

    void clearHeroCache(HeroBattleEntity atkHero) {
        this.heroDie.clear();
        this.heroDie2.clear();
        this.heroDieTriggered.clear();
        this.heroBeCrit.clear();
        this.heroBeAttackNoCrit.clear();
        this.heroBeAttack.clear();
        this.heroBeSkillDamage.clear();
        this.listHeroBeControlled.clear();
        this.heroBeBuffOrDebuff.clear();
        this.heroLostHPInTurn.clear();
        this.heroBlock.clear();
        this.atkHero = atkHero;
        this.totalDamageByTurn = 0;
        this.listTargetHeroNeedToCheckSomething.clear();
        this.listHeroDieMapByAtkHero.clear();
        this.listHeroDodge.clear();
        clearHeroCache(atkTeam);
        clearHeroCache(defTeam);
    }

    private void clearHeroCache(List<HeroBattleEntity> team) {
        team.stream().filter(HeroBattleEntity::isHero).forEach(hero -> {
            hero.isFoughtBack = false;
            hero.damageBlock = 0;
            hero.numberFireMarkActivated = 0;
            hero.needRemoveAndTriggerPreHopeMark1 = false;
            hero.needToProcessSkillEndTurn = false;
            hero.rateToTriggerNormalSkill = 0;
            hero.causedDamageInTurn = 0;
            hero.numberTrigger59 = 0;
            hero.damageTakenByTurn = 0;
            if (hero.effectController.contain(BattleEffectType.hanaMark)) {
                EffectCount effectCount = hero.effectController.get(BattleEffectType.hanaMark);
                effectCount.setValue4(0, 0);
            }
        });
    }

    public void addTurn(HeroTurnEntity turn) {
        aHeroTurn.add(turn);
    }

    public List<HeroBattleEntity> getAtkTeam() {
        return atkTeam;
    }

    public List<HeroBattleEntity> getDefTeam() {
        return defTeam;
    }

    public boolean isAllOpponentDie() {
        for (int i = 0; i < defTeam.size(); i++) {
            if (defTeam.get(i).isHero() && defTeam.get(i).point.getCurrentHP() > 0) {
                return false;
            }
        }
        return true;
    }

    public void addTurnDamage(long damage) {
        this.totalDamageByTurn += Math.abs(damage);
    }


    public void standardizedHero(List<HeroBattleEntity> aHero) {
        HeroBattleEntity tmpHero = aHero.stream().filter(HeroBattleEntity::isHero).findAny().orElse(null);
        HeroBattleEntity pet = tmpHero == null ? null : (tmpHero.team == 1 ? atkPet : defPet);
        if (pet != null) {
            aHero.stream().filter(HeroBattleEntity::isHero).forEach(hero -> {
                for (int pointIndex = 0; pointIndex < 13; pointIndex++) {
                    if (pointIndex == Point.ARMOR)
                        hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, pointIndex, hero.point.getCurrentValue(pointIndex) * pet.point.getCurrentValue(pointIndex) / 1000);
                    else
                        hero.point.addNotNegative(Point.CURRENT_VALUES_INDEX, pointIndex, pet.point.getValue(Point.CURRENT_VALUES_INDEX, pointIndex));
                }
                hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.ATTACK,
                        hero.point.getCurrentValue(Point.ATTACK) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_ATTACK));
                hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.HP,
                        hero.point.getCurrentValue(Point.HP) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_HP));
                hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.SPEED,
                        hero.point.getCurrentValue(Point.SPEED) + (int) pet.point.getValue(Point.CURRENT_VALUES_INDEX, Point.FIX_SPEED));
            });
        }
        aHero.stream().filter(HeroBattleEntity::isHero).forEach(hero -> {
            hero.point.setCalculatedValue();

            if (hero.point.getMaxHp() <= 0)
                hero.point.setMaxHp(hero.point.getCalculatedValue(Point.HP));
            else hero.point.setCalculatedValue(Point.HP, hero.point.getMaxHp());


            if (hero.point.getStartHp() >= 0 && hero.point.getStartHp() < hero.point.getValue(Point.CALCULATED_VALUES_INDEX, Point.HP)) {
                hero.point.setCurrentValue(Point.HP, hero.point.getStartHp());
            } else if (hero.point.getStartHpPercent() >= 0) {
                BigDecimal bigDecimal = new BigDecimal(hero.point.getCurrentValue(Point.HP));
                bigDecimal = bigDecimal.multiply(BigDecimal.valueOf(hero.point.getStartHpPercent())).divide(BigDecimal.valueOf(100));
                long newHp = bigDecimal.longValue();
                if (hero.point.getStartHpPercent() > 0 && newHp == 0) newHp = 1;
                hero.point.setValue(Point.CURRENT_VALUES_INDEX, Point.HP, newHp);
            } else hero.point.setStartHp(hero.point.getValue(Point.CURRENT_VALUES_INDEX, Point.HP));
        });
        if (tmpHero != null && tmpHero.team == 1) {
            atkPower = aHero.stream().mapToLong(hero -> IMath.getPower(hero.point)).sum();
        } else {
            defPower = aHero.stream().mapToLong(hero -> IMath.getPower(hero.point)).sum();
        }
    }

    protected void increasePetEnergy(int team) {
        HeroBattleEntity pet = team == 1 ? atkPet : defPet;
        if (pet != null) pet.point.addAnger(1);
    }

    protected void addPetEnergyEffect(List<EffectEntity> aBattleEffect) {
        if (atkPet != null)
            aBattleEffect.add(atkPet.getResultEffectAnger(atkPet, 0, false));
        if (defPet != null)
            aBattleEffect.add(defPet.getResultEffectAnger(defPet, 0, false));
    }

    protected AttackInfoEntity getLastAttack() {
        if (currentTurn != null && !currentTurn.aAttack.isEmpty())
            return currentTurn.aAttack.get(currentTurn.aAttack.size() - 1);
        return new AttackInfoEntity();
    }

    public List<Long> getListPercentHp(boolean isTeam1) {
        List<HeroBattleEntity> team = isTeam1 ? atkTeam : defTeam;
        return team.stream().filter(heroBattle -> heroBattle.isHero() && !heroBattle.isSummonHero()).map(hero -> hero.point.percentHpLong()).toList();
    }

    public void debug(String msg) {
        if (CfgServer.isTestServer()) {
            System.out.println(msg);
        }
    }

    public void addDebugString(String msg) {
        if (CfgServer.isTestServer()) {
            this.debug += msg;
        }
    }

    public void showTeamHp(List<HeroBattleEntity> team, int index) {
        team.stream().filter(HeroBattleEntity::isHero).forEach(hero -> {
            debug("-----" + index + "------hero:" + hero + ", hp: " + hero.point.getCurrentHP() + ", maxHp: " + hero.point.getMaxHp());
        });
    }

    public void showTeamAtk(List<HeroBattleEntity> team, int index) {
        team.stream().filter(HeroBattleEntity::isHero).forEach(hero -> {
            debug("-----" + index + "------hero:" + hero + ", atk: " + hero.point.getRealCurrentAttack());
        });
    }

    public void showTeamAnger(List<HeroBattleEntity> team, int index) {
        team.stream().filter(HeroBattleEntity::isHero).forEach(hero -> {
            debug("-----" + index + "------hero:" + hero + ", anger: " + hero.point.getCurrentAnger());
        });
    }
}
