package monster.service.battle.common.entity.proto;

import monster.protocol.pbentity.ListPbJEffect;
import monster.protocol.pbentity.PbJHeroAttack;
import monster.service.battle.common.config.BattleEffectType;
import monster.service.battle.common.entity.HeroBattleEntity;
import monster.service.battle.dependence.BattleConfig;
import protocol.Pbmethod;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class AttackInfoEntity implements Serializable {
    public int team, position, anger;
    public int skillIndex, skillType = BattleConfig.SKILL_PASSIVE;

    public List<TargetEntity> aTarget = new ArrayList<>();
    public List<List<EffectEntity>> aListEffect = new ArrayList<>();
    public boolean isAoe = false;

    public AttackInfoEntity() {
        team = -1;
    }

    public AttackInfoEntity(HeroBattleEntity hero, int skillType, int skillIndex) {
        this.team = hero.team;
        this.position = hero.position;
        this.anger = (int) hero.point.getCurrentAnger();
        this.skillIndex = skillIndex;
        this.skillType = skillType;
    }

    public void addListEffect(List<EffectEntity> aEffectEntity) {
        aListEffect.add(aEffectEntity.stream().filter(Objects::nonNull).collect(Collectors.toList()));
    }

    public Pbmethod.PbHeroAttack.Builder toProto() {
        Pbmethod.PbHeroAttack.Builder builder = Pbmethod.PbHeroAttack.newBuilder();
        builder.setTeam(team);
        builder.setPosition(position);
        builder.setSkillIndex(skillIndex);
        aTarget.forEach(target -> builder.addATarget(target.toProto()));
//        for (List<EffectEntity> aEffect : aListEffect) {
//            Pbmethod.ListPbEffect.Builder effectBuilder = Pbmethod.ListPbEffect.newBuilder();
//            for (EffectEntity effect : aEffect) effectBuilder.addAEffect(effect.toProto());
//            builder.addAListEffect(effectBuilder);
//        }
        return builder;
    }

    public PbJHeroAttack toPbJ() {
        PbJHeroAttack aJHeroAttack = new PbJHeroAttack();
        aJHeroAttack.setTeam(team);
        aJHeroAttack.setPosition(position);
        aJHeroAttack.setSkillIndex(skillIndex);
        aTarget.forEach(target -> aJHeroAttack.addATarget(target.toPbj()));
//        for (List<EffectEntity> aEffect : aListEffect) {
//            Pbmethod.ListPbEffect.Builder effectBuilder = Pbmethod.ListPbEffect.newBuilder();
//            for (EffectEntity effect : aEffect) effectBuilder.addAEffect(effect.toProto());
//            builder.addAListEffect(effectBuilder);
//        }
        return aJHeroAttack;
    }

    public void toListEffectProto(Pbmethod.ListPbEffect.Builder listPbEffect) {
        for (List<EffectEntity> aEffect : aListEffect) {
            // remove effect killer bị lặp
            int index = aEffect.size();
            while (index > 0) {
                if (index < aEffect.size()) {
                    EffectEntity effect = aEffect.get(index);
                    if (effect.getEffectId() == BattleEffectType.EFFECT_KILLER.value) {
                        for (int i = index - 1; i >= 0; i--) {
                            EffectEntity tmp = aEffect.get(i);
                            if (tmp.getEffectId() == BattleEffectType.EFFECT_KILLER.value && effect.team == tmp.team && effect.position == tmp.position) {
                                aEffect.remove(i);
                            }
                        }
                    }
                }
                index--;
            }
            //
            for (EffectEntity effect : aEffect) listPbEffect.addAEffect(effect.toProto());
        }
    }

    public void toListEffectPbj(ListPbJEffect listPbJEffect) {
        for (List<EffectEntity> aEffect : aListEffect) {
            for (EffectEntity effect : aEffect) {
                listPbJEffect.addAEffect(effect.toPbJ());
            }
        }
    }

    public boolean isPassiveSkill() {
        return skillType == BattleConfig.SKILL_PASSIVE;
    }

    public boolean isActiveSkill() {
        return skillType == BattleConfig.SKILL_ACTIVE;
    }

    public boolean isNormalSkill() {
        return skillType == BattleConfig.SKILL_NORMAL;
    }

    @Override
    public String toString() {
        String output = String.format("team=%s, position=%s, anger=%s, skillIndex=%s\n", team, position, anger, skillIndex);
        for (TargetEntity target : aTarget) output += "      " + target.toString() + "\n";

        for (List<EffectEntity> aEffect : aListEffect) {
            output += "         --\n";
            for (EffectEntity effect : aEffect){
                if (effect == null) continue;
                output += "         " + effect.toString() + "\n";
            }
        }
        return output;
    }
}
