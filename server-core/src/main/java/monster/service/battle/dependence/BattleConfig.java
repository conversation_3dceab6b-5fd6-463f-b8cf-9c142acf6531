package monster.service.battle.dependence;

import java.util.Arrays;
import java.util.List;

public class BattleConfig {

    /**
     * Thứ tự đánh
     * 6 3    3 6
     * 5 2    2 5
     * 4 1    1 4
     */
    public static int[][] targetOrder = new int[][]{
            {3, 6, 2, 5, 1, 4},
            {2, 5, 1, 4, 3, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6}
    };

    public static int[][] targetOrderByRow = new int[][]{
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6},
            {1, 2, 3, 4, 5, 6}
    };

    public static int[] ROW_1_ORDER = new int[]{1, 2, 3};
    public static int[] ROW_2_ORDER = new int[]{4, 5, 6};
    public static int[] ROW_3_ORDER = new int[]{7, 8, 9};

    public static int[] COLUMN_0_ORDER = new int[]{1, 4};
    public static int[] COLUMN_1_ORDER = new int[]{2, 5};
    public static int[] COLUMN_2_ORDER = new int[]{3, 6};

    public static int SKILL_PASSIVE = 2;
    public static int SKILL_ACTIVE = 1;
    public static int SKILL_NORMAL = 0;

    public static int HP_NORMAL = 0;
    public static int HP_KHAC_HE_TANG_DAME = 1;
    public static int HP_CRIT = 2;
    public static int HP_END_ROUND = 3;
    public static int HP_SPECIAL = 4;
    public static int HP_KHAC_HE_GIAM_DAME = 5;
    public static int HP_RESIST = 6;

    public static final int STACK_NO_COUNTDOWN_ROUND = 1;
    public static final int STACK_COUNTDOWN_ROUND = 2;
    public static final int NOT_STACK_REPLACE_ROUND = 3;
    public static final int IGNORE_IF_CONTAIN = 4;

    public static final int TYPE_SHOW_STACK = 1;
    public static final int TYPE_SHOW_ROUND = 2;
    public static final int TYPE_SHOW_HP_CHANGED = 3;
    public static final int TYPE_SHOW_ANGER_CHANGED = 4;
    public static final int TYPE_SHOW_MAX_HP_CHANGED = 5;
    public static final int TYPE_SHOW_REVIVE = 6;
    public static final int TYPE_SPECIAL_EFFECT = 7;
    public static final int TYPE_SUMMON_EFFECT = 8;
    public static final int TYPE_SHIELD = 9;
    public static final int TYPE_SHOW_MAX_HP_CHANGED_STACK = 10;
    public static final int TYPE_MOVE_POSITION = 11;
    public static final int TYPE_CHANGE_BACKGROUND = 12;
    public static final int TYPE_CHANGE_HERO_KEY = 13;
    public static final int TYPE_CHANGE_MODEL = 14;
    public static final int TYPE_PLAY_ANIMATION = 15;
    public static final int ALL_EFFECT_COUNT = -1;

    public static final int TYPE_SUMMON_MAIN_FREE = -1;
    public static final int TYPE_SUMMON_SECONDARY_RIGHT_HERE = -2;
    //Summon vị trí trống random
    public static final int TYPE_SUMMON_SECONDARY_FREE = -3;
    //Summon vị trí trống random, ưu tiên summon vào vị trí chính và phụ trong cùng ô
    public static final int TYPE_SUMMON_SECONDARY_FREE_PRIORITIZE_SAME_CELL_AS_LAST_SUMMONED = -4;
    public static final int TYPE_SUMMON_FOR_CLIENT1 = 1;
    public static final int TYPE_SUMMON_FOR_CLIENT2 = 2;

    public static int MAX_HP = 4;

    public static int TRUE_HP = 5;
    public static int ANGER_START = 100;
    public static final int TEAM_INPUT = 7;
    public static final int TEAM_HERO_SIZE = 6;
    public static final int BREATH_ARTS_SIZE = 5;

    public static final int MAX_REDUCE_DAMAGE = 700;
    public static final float MAX_REDUCE_ATTACK = 0.9f;

    public static final int BAREA_10STAR = 241;

    public static int FORCE_NO_CRIT = -1; //
    public static int FORCE_CRIT_NORMAL = 1; // Bắt buộc crit nếu đánh thường hoặc kỹ năng
    public static int FORCE_CRIT_ALL = 3; // Bắt buộc crit trong mọi trường hợp
    public static final int MAX_NUMBER_ACTIVE_FIRE_MARK = 5;

    public static List<Long> getDefaultTeam() {
        return List.of(
                0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L
        );
    }

    public static List<Long> getDefault2Team() {
        return List.of(
                0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L,
                0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L
        );
    }

    public static int checkTypeHpResist(int typeHp, long resultTypeHpResist) {
        if (resultTypeHpResist == 1) return HP_RESIST;

        return typeHp;
    }
}
