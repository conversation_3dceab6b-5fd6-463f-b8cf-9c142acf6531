package monster.service.battle.dependence.entity;

import lombok.Data;
import lombok.NonNull;
import monster.service.battle.dependence.BattleHeroAnalysis;
import monster.service.battle.dependence.Point;

import java.io.Serializable;
import java.util.List;

@Data
public class SimulateHero implements Serializable {
    @NonNull
    public int heroId;
    public long id;
    public int team, pos, level, star, tier, numberKill, skin;
    public int bossType;
    public List<Long> point;
    public Point outputPoint;

    public float percentHp;
    public int type;
    public BattleHeroAnalysis heroAnalysis;
    public boolean isSummoned;

    public protocol.Pbmethod.PbBattleHero.Builder toProto() {
        return protocol.Pbmethod.PbBattleHero.newBuilder()
                .setPos(pos).setHeroId(heroId).setLevel(level).setStar(star)
                .setTier(tier).setSkin(skin).addAllPoint(point)
                .setTotalDamage(heroAnalysis.logDamage).setTotalDef(heroAnalysis.logDef).setTotalHeal(heroAnalysis.logHeal).setType(bossType);
    }
}