package monster.service.battle.dependence;

import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.penum.MonsterType;
import monster.dao.mapping.main.ResHeroEntity;
import monster.dao.mapping.main.ResMonsterEntity;
import monster.service.battle.common.config.SkillEffectType;
import monster.service.battle.common.config.TriggerType;
import monster.service.battle.common.entity.SkillEntity;
import monster.service.monitor.Telegram;
import monster.service.resource.ResHero;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class IMonster {

    /**
     * @param resMonster  -> trong bang res_monster
     * @param level       -> level config trong các tính năng
     * @param monsterType
     * @return
     */
    public static ResMonsterEntity get(ResMonsterEntity resMonster, int level, MonsterType monsterType) {
        if (resMonster.isFormula2()) {
            buffPassiveSkill(resMonster);
            resMonster.getPoint().setCurrentValue();
            resMonster.getPoint().setCalculatedValue();
            return resMonster;
        }
        formula1(resMonster);
//        switch (monsterType) {
//            case CAMPAIGN:
//            case TOWER_OBLIVION:
//            case ASPEN_DUNGEON:
//            case GUILD_BOSS:
//            case EVENT_RAID:
//            case FRIEND_BOSS:
//            case BROKEN_SPACE:
//            case BOSS_SERVER:
//            case BOSS_TOWER1:
//            case ARENA_BOT:
//            case SEAL_LAND:
//            case PEARL_MINE:
//            case BOSS_INTER_SERVER:
//            case TERRITORY:
//            case STORY_MISSION:
//                formula1(resMonster);
//                break;
//            case ISLAND_BOSS:
//                formulaBossIsland(resMonster, level);
//                break;
//            case ISLAND_CREEP:
//                formulaCreepIsland(resMonster);
//                break;
//            case TOWER2:
//                formulaOld(resMonster);
//                break;
//        }
        //        buffPassiveSkill(resMonster);
        resMonster.getPoint().setCurrentValue();
        resMonster.getPoint().setCalculatedValue();
        return resMonster;
    }

    private static void formula1(ResMonsterEntity resMonster) {
        try {
            int level = resMonster.getLevel();
            Point point = resMonster.getPoint();
            point.setBaseValue(Point.HP, (long) (resMonster.getHpScale() * level));
            point.setBaseValue(Point.ATTACK, (long) (resMonster.getAtkScale() * level));
            point.setBaseValue(Point.ARMOR, (long) (resMonster.getArmScale() * level));
        } catch (Exception ex) {
            Logs.error(String.format("monsterId=%s, heroLinkNew=%s", resMonster.getId(), resMonster.getHeroLinkNew()));
            throw ex;
        }
    }

    public static void buffPassiveSkill(ResMonsterEntity resMonster) {
        try {
            ResHeroEntity resHero = ResHero.getHero(resMonster.getHeroLinkNew());
            List<SkillEntity> listPassiveSkill = resHero.getSkills(resHero.getStar(), resMonster.getLevel());
            if (listPassiveSkill.isEmpty()) return;
            Point monsterPoint = resMonster.getPoint();
            listPassiveSkill.remove(0); // remove active skill
            buffPassiveSkill(resMonster.getId(), monsterPoint, listPassiveSkill);
        } catch (Exception ex) {
            Logs.error(String.format("Error buff passive monsterId=%s, heroLinkNew=%s", resMonster.getId(), resMonster.getHeroLinkNew()));
            Telegram.sendNotify(String.format("Error buff passive monsterId=%s, heroLinkNew=%s", resMonster.getId(), resMonster.getHeroLinkNew()));
            Logs.error(ex);
            throw ex;
        }
    }

    public static void buffPassiveSkill(int monsterId, Point monsterPoint, List<SkillEntity> listPassiveSkill) {
        int valuesIndex = Point.SKILL_VALUES_INDEX;
        for (int i = listPassiveSkill.size() - 1; i >= 0; i--) {
            SkillEntity buffSkill = listPassiveSkill.get(i);
            if (buffSkill.trigger == TriggerType.IMMEDIATELY.value) {
                buffSkill.effects.forEach(skillEffect -> {
                    skillEffect.listEffect.forEach(singleSkillEffect -> {
                        if (singleSkillEffect.type.equals("immortalWithDarkLight")) {
                            monsterPoint.setValue(valuesIndex, Point.SPEC_IMMORTAL_FOREVER, 1);
                        } else if (singleSkillEffect.type.equals("immortalWithNormalFaction")) {
                            monsterPoint.setValue(valuesIndex, Point.SPEC_IMMORTAL_FOREVER, 2);
                        } else if (!List.of("changeCombat", "changeCombat1", "addPassive").contains(singleSkillEffect.type)) {
                            if (singleSkillEffect.type.equals(SkillEffectType.dmgAndDot.value)) {
                                monsterPoint.add(valuesIndex, Point.SPEC_REDUCE_DAMAGE_DOT, (int) singleSkillEffect.num1);
                                monsterPoint.add(valuesIndex, Point.SPEC_REDUCE_DAMAGE_NOT_DOT, (int) singleSkillEffect.num);
                                return;
                            }

                            Integer pointIndex = SkillEffectType.mEffectPointIndex.get(singleSkillEffect.type);
                            if (singleSkillEffect.type.equals("buffpoint") || singleSkillEffect.type.equals("buffpointXFaction"))
                                pointIndex = singleSkillEffect.pointIndex;
                            if (pointIndex == null) {
                                Telegram.sendNotify("err pointIndex null monster_id = " + monsterId + " effect = " + singleSkillEffect.type);
                                return;
                            }

                            if (pointIndex < 3)
                                monsterPoint.addNotNegative(valuesIndex, pointIndex, (long) (monsterPoint.getBaseValue(pointIndex) * singleSkillEffect.num));
                            else if (List.of(Point.SPEC_IMMUNE_SILENCE, Point.SPEC_IMMUNE_STONE, Point.SPEC_IMMUNE_STUN, Point.SPEC_IMMUNE_FREEZE).contains(pointIndex))
                                monsterPoint.setValue(valuesIndex, pointIndex, 1);
                            else if (pointIndex == Point.ANTI_CRIT)
                                monsterPoint.setValue(valuesIndex, pointIndex, (int) singleSkillEffect.num);
                            else if (List.of(Point.SPEC_MORE_FINAL_DMG, Point.SPEC_TAKE_LESS_FINAL_DMG).contains(pointIndex))
                                monsterPoint.addNotNegative(valuesIndex, pointIndex, (int) singleSkillEffect.num);
                            else if (pointIndex >= 35)
                                monsterPoint.add(valuesIndex, pointIndex, (int) (singleSkillEffect.num * 1000));
                            else {
                                float addValue = (int) singleSkillEffect.num;
                                if (Arrays.asList(Point.CRIT, Point.CRIT_DAMAGE, Point.PRECISION, Point.BLOCK).contains(pointIndex))
                                    addValue = addValue / 2;

                                monsterPoint.add(valuesIndex, pointIndex, (int) addValue);
                            }
                        }
                    });
                });
            }
        }
    }
}
