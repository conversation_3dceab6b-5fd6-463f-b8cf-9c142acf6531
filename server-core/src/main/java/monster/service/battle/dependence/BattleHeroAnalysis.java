package monster.service.battle.dependence;

import lombok.NoArgsConstructor;
import monster.service.battle.common.config.BattleEffectType;
import monster.service.battle.common.entity.HeroBattleEntity;

import java.util.ArrayList;
import java.util.List;


@NoArgsConstructor
public class BattleHeroAnalysis {
    public int roundStillAlive, roundDead = 0;
    public long logDamage = 0, logHeal = 0, logDef = 0, logDOTDamage = 0, logNotDOTDamage = 0, bestDamage = 0, logCritDamage = 0, logShield = 0;
    public int cntStun = 0, cntStone = 0, cntFreeze = 0, cntFear = 0, cntWeaken = 0, cntExhaust = 0, cntLostTurn = 0, cntCounterAttack = 0;
    public int cntNormalAttack = 0, cntActiveSkill = 0, cntPassive1 = 0, cntPassive2 = 0, countControl = 0, countGoodEffectGoodMark = 0, countBadEffectBadMark = 0;
    public int numberKill = 0, numberDead = 0, logRemoveDebuffActive = 0, logRemoveDebuffPassive = 0, logRemoveBuffActive = 0, logRemoveBuffPassive = 0;
    public long pointCC = 0, cntMaxDamageTurn = 0, maxDamageDot = 0, maxDamageOfRound = 0;
    public List<Integer> listPercentHpAtStartRound = new ArrayList<>();
    public long[] maxPoint = new long[Point.number];


    public void addControlEffect(HeroBattleEntity targetHero, BattleEffectType battleEffect) {
        try {
            switch (battleEffect) {
                case EFFECT_STONE -> cntStone++;
                case EFFECT_STUN -> cntStun++;
                case EFFECT_FREEZE -> cntFreeze++;
                case EFFECT_FEAR -> cntFear++;
                case EFFECT_WEAKEN -> cntWeaken++;
                case EFFECT_SUY_NHUOC -> cntExhaust++;
            }

            switch (battleEffect) {
                case EFFECT_STONE, EFFECT_STUN, EFFECT_FREEZE -> this.pointCC += 3;
//                    case EFFECT_TAUNT, EFFECT_SILENCE, EFFECT_SLEEP -> atkHero.getHeroAnalysis().pointCC += 1;
            }
            targetHero.cntLostTurn(battleEffect);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void merge(BattleHeroAnalysis battleHeroAnalysis) {
        this.logDamage += battleHeroAnalysis.logDamage;
        this.logHeal += battleHeroAnalysis.logHeal;
        this.logDef += battleHeroAnalysis.logDef;
        this.logDOTDamage += battleHeroAnalysis.logDOTDamage;
        this.logNotDOTDamage += battleHeroAnalysis.logNotDOTDamage;
        this.bestDamage = battleHeroAnalysis.bestDamage;
        this.logCritDamage += battleHeroAnalysis.logCritDamage;
        this.logShield += battleHeroAnalysis.logShield;
        this.cntStun += battleHeroAnalysis.cntStun;
        this.cntStone += battleHeroAnalysis.cntStone;
        this.cntFreeze += battleHeroAnalysis.cntFreeze;
        this.cntFear += battleHeroAnalysis.cntFear;
        this.cntWeaken += battleHeroAnalysis.cntWeaken;
        this.cntExhaust += battleHeroAnalysis.cntExhaust;
        this.cntLostTurn += battleHeroAnalysis.cntLostTurn;
        this.cntCounterAttack += battleHeroAnalysis.cntCounterAttack;
        this.cntNormalAttack += battleHeroAnalysis.cntNormalAttack;
        this.cntActiveSkill += battleHeroAnalysis.cntActiveSkill;
        this.cntPassive1 += battleHeroAnalysis.cntPassive1;
        this.cntPassive2 += battleHeroAnalysis.cntPassive2;
        this.numberKill += battleHeroAnalysis.numberKill;
        this.numberDead += battleHeroAnalysis.numberDead;
        this.logRemoveDebuffActive += battleHeroAnalysis.logRemoveDebuffActive;
        this.logRemoveDebuffPassive += battleHeroAnalysis.logRemoveDebuffPassive;
        this.logRemoveBuffActive += battleHeroAnalysis.logRemoveBuffActive;
        this.logRemoveBuffPassive += battleHeroAnalysis.logRemoveBuffPassive;
        this.pointCC += battleHeroAnalysis.pointCC;
    }
}
