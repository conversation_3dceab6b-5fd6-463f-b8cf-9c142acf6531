package monster.service.common;

import monster.controller.AHandler;
import monster.object.MyUser;
import monster.object.ServiceResult;
import protocol.Pbmethod;

import java.util.List;

public interface CampaignService {

    List<Long> eventDropItem(MyUser mUser, int numberRandom);
    List<Long> eventTetDropItem(MyUser mUser, int numberRandom);
    void checkNotify(MyUser mUser, protocol.Pbmethod.CommonVector.Builder cmm);
    void autoPeakTeam(MyUser mUser, List<Long> heroIds, int numberTeam);
}
