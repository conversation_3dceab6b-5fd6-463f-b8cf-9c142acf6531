package monster.service.common;

import monster.config.penum.QuestType;
import monster.dao.mapping.UserQuestEntity;
import monster.object.MyUser;

public interface QuestService {

    UserQuestEntity getUserQuest(MyUser mUser);
    boolean hasDailyQuestNotify(MyUser mUser);
    boolean hasWeeklyQuestNotify(MyUser mUser);
    void checkQuestData(MyUser mUser, QuestType questType, int number);
    void addDailyPoint(MyUser mUser, long point);
    void addWeeklyPoint(MyUser mUser, long point);
}
