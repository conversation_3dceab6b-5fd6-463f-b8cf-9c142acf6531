package monster.service.common.impl;

import com.google.inject.Inject;
import grep.helper.DateTime;
import grep.helper.GUtil;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.cache.CacheStore;
import monster.cache.CacheStoreBeans;
import monster.config.CfgServer;
import monster.dao.WarningDAO;
import monster.dao.mapping.logs.GameApiLog;
import monster.dao.mapping.main.LogErrorEntity;
import monster.pubsub.PubSubService;
import monster.server.AppConfig;
import monster.service.common.TelegramService;

import java.net.URI;
import java.net.http.HttpClient;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class TelegramServiceImpl implements TelegramService {
    @Inject
    WarningDAO warningDAO;
    CacheStore<String> cacheMessage = CacheStoreBeans.getMinute(String.class, 10);

    @Override
    public void receiveMessage(String message) {
        int pos = message.indexOf("@");
        int service = pos >= 0 ? Integer.parseInt(message.substring(0, pos)) : 0;
        message = pos >= 0 ? message.substring(pos + 1) : message;

        PubSubService pubSubService = PubSubService.get(service) == null ? PubSubService.TELEGRAM_NOTIFY : PubSubService.get(service);
        switch (pubSubService) {
            case TELEGRAM_NOTIFY -> notifyTelegram(message);
            case TELEGRAM_ERROR -> notifyTelegramErr(message);
            case TELEGRAM_PRIVATE -> notifyPrivate(message);
            case TELEGRAM_GROUP_GD -> notifyGroupGD(message);
        }
    }

    private void notifyTelegram(String message) {
        sendMessage(String.valueOf(AppConfig.cfg.telegram.chatId), getNewMessage(message, "group_notify"), "group_notify");
    }

    private void notifyPrivate(String message) {
        sendMessage(String.valueOf(AppConfig.cfg.telegram.chatPrivate), getNewMessage(message, "group_private"), "group_private");
    }

    private void notifyGroupGD(String message) {
        sendMessage(String.valueOf(AppConfig.cfg.telegram.chatGroupGd), getNewMessage(message, "group_gd"), "group_gd");
    }

    private void sendMessage(String chatId, String message, String info) {
        if (message == null) return;
        //        String response = "";
        //        if (message.contains("upload") || message.contains("DEPLOY")) {
        //            response = ntfy("jjk_pack", message);
        //        } else if (chatId.equals(String.valueOf(AppConfig.cfg.telegram.chatId))) {
        //            response = ntfy("jjk", message);
        //        } else if (chatId.equals(String.valueOf(AppConfig.cfg.telegram.chatGroupGd))) {
        //            response = ntfy("gamedesign", message);
        //        } else if (chatId.equals(String.valueOf(AppConfig.cfg.telegram.chatPrivate))) {
        //            response = ntfy("secret", message);
        //        }

        String response = send(chatId, message);

        //        String doamin = "telegram-api-wrapper.grepgame.com"; // api.telegram.org
        //        String url = String.format("https://%s/%s/sendMessage?parse_mode=html", doamin, AppConfig.cfg.telegram.botId);
        //        Map<String, String> data = new HashMap<>();
        //        data.put("chat_id", chatId);
        //        data.put("text", message);
        //        String response = getJavaNetPostContent(url, data);

        warningDAO.save(GameApiLog.builder().timestamp(new Date()).apiType("telegram").message(message).response(response).info(info).build());
    }

    private String getNewMessage(String message, String info) {
        String title = "";
        if (message.contains(")")) {
            title = message.substring(message.indexOf("(") + 1, message.indexOf(")"));
            message = message.substring(message.indexOf(")") + 1).trim();
        }
        String newMessage = StringHelper.isEmpty(title) ? String.format("<b>%s:</b>\n%s", AppConfig.cfg.telegram.prefix, message) : String.format("<b>%s (%s):</b>\n%s", AppConfig.cfg.telegram.prefix, title, message);
        if (CfgServer.teleAllow(message) || cacheMessage.get(message) == null) {
            cacheMessage.add(message, title);
            return newMessage;
        }
        warningDAO.save(GameApiLog.builder().timestamp(new Date()).apiType("telegram").message(newMessage).response("duplicate").info(info).build());
        return null;
    }

    private void notifyTelegramErr(String message) {
        try {
            String showFullUrl = "<a href='https://jjk-gamedev.grepgame.com/admin/logs/error/%s/view'> [...] </a>";
            String title = "";
            if (message.contains(")")) {
                title = message.substring(message.indexOf("(") + 1, message.indexOf(")"));
                message = message.substring(message.indexOf(")") + 1).trim();
            }
            String newMessage = StringHelper.isEmpty(title) ? String.format("<b>%s:</b>\n%s", AppConfig.cfg.telegram.prefix, message) : String.format("<b>%s (%s):</b>\n%s", AppConfig.cfg.telegram.prefix, title, message);
            CacheStore<String> cacheMessage = CacheStoreBeans.getMinute(String.class, 60);
            CacheStore<String> cacheFormatMessage = CacheStoreBeans.getMinute(String.class, 60);
            CacheStore<LogErrorEntity> cacheCheckSum = CacheStoreBeans.getMinute(LogErrorEntity.class, (int) DateTime.DAY_MIN);
            if (cacheMessage.get(message) == null) {
                String checkSum = GUtil.getMD5(DateTime.getDateyyyyMMdd() + message);
                LogErrorEntity errorEntity = cacheCheckSum.get(checkSum) == null
                        ? LogErrorEntity.builder().sender(title).message(message).checkSum(checkSum).build()
                        : cacheCheckSum.get(checkSum);
                if (errorEntity.getId() == 0) {
                    if (warningDAO.saveErrorLog(errorEntity)) {
                        cacheCheckSum.add(checkSum, errorEntity);
                    } else {
                        errorEntity = warningDAO.getLogErrorEntity(checkSum);
                        if (errorEntity == null) return;
                    }
                }

                String formatMessage = formatErrorString(newMessage);
                if (cacheFormatMessage.get(formatMessage) == null) {
//                    String url = String.format("https://api.telegram.org/%s/sendMessage?parse_mode=html", AppConfig.cfg.telegram.botId);
//                    Map<String, String> data = new HashMap<>();
//                    data.put("disable_web_page_preview", "true");
//                    data.put("chat_id", String.valueOf(AppConfig.cfg.telegram.chatId2));
//                    data.put("text", formatErrorString(newMessage) + String.format(showFullUrl, errorEntity.getId()));
//
//                    getJavaNetPostContent(url, data);

                    String response = send(String.valueOf(AppConfig.cfg.telegram.chatId2),
                            formatErrorString(newMessage) + String.format(showFullUrl, errorEntity.getId()));

                    //                    ntfy("jjk_error", formatErrorString(newMessage) + String.format(showFullUrl, errorEntity.getId()));
                    cacheFormatMessage.add(formatMessage, "");
                }
                cacheMessage.add(message, title);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Logs.error(ex);
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(send("-956005697", "test"));
        } catch (Exception ex) {
            Logs.error(ex);
        }
    }

    public static String send(String chatId, String message) {
        try {
            String domain = "telegram-api-wrapper.grepgame.com"; // api.telegram.org
            String url = String.format("https://%s/%s/sendMessage", domain, "bot1183017738:AAGIm8dZxjpsXzKWZtqhLLbd7Pk5yyrUmeg");
            Map<String, String> data = new HashMap<>();
            data.put("disable_web_page_preview", "true");
            data.put("chat_id", chatId);
            data.put("text", message);

            return getJavaNetPostContent(url, data);
        } catch (Exception ex) {
            ex.printStackTrace();
            return ex.toString();
        }
    }

    public static String ntfy(String topic, String message) {
        try {
            HttpClient client = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(30)).build();
            java.net.http.HttpRequest request = java.net.http.HttpRequest.newBuilder()
                    .uri(URI.create("https://ntfy.grepgame.com/" + topic))
                    //                    .header("Content-Type", "application/json")
                    .POST(java.net.http.HttpRequest.BodyPublishers.ofString(message)).build();
            java.net.http.HttpResponse<String> response = client.send(request, java.net.http.HttpResponse.BodyHandlers.ofString());
            return response.body();
        } catch (Exception ex) {
            Logs.error(ex);
            return ex.toString();
        }
    }

    public static String getJavaNetPostContent(String url, Map<String, String> data) {
        try {
            HttpClient client = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(30)).build();
            java.net.http.HttpRequest request = java.net.http.HttpRequest.newBuilder().uri(URI.create(url)).header("Content-Type", "application/json").POST(java.net.http.HttpRequest.BodyPublishers.ofString(GsonUtil.toJson(data))).build();
            java.net.http.HttpResponse<String> response = client.send(request, java.net.http.HttpResponse.BodyHandlers.ofString());
            return response.body();
        } catch (Exception ex) {
            Logs.error(ex);
        }
        return null;
    }

    private String formatErrorString(String message) {
        String[] tmp = message.split("\n");
        String newMessage = "";
        for (String s : tmp) {
            newMessage += s + "\n";
            if (s.contains(".java")) {
                break;
            }
        }
        return newMessage;
    }
}
