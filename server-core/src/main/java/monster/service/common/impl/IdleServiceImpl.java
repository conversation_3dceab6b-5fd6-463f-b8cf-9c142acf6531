package monster.service.common.impl;

import com.google.inject.Inject;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.*;
import monster.config.lang.Lang;
import monster.config.penum.*;
import monster.dao.IdleDAO;
import monster.dao.mapping.UserCampaignEntity;
import monster.dao.mapping.UserEntity;
import monster.dao.mapping.UserIdleEntity;
import monster.dao.mapping.logs.UserPassFunction;
import monster.dao.mapping.main.ResCampaignEntity;
import monster.game.system.service.LogService;
import monster.game.truongevent.service.TruongProEventService;
import monster.object.BattleTeam;
import monster.object.ModePlay;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.service.Services;
import monster.service.aop.CacheInGame;
import monster.service.battle.dependence.BattleBuilder;
import monster.service.battle.dependence.BattleConfig;
import monster.service.battle.dependence.BattleResultEntityNew;
import monster.service.common.*;
import monster.service.monitor.EventMonitor;
import monster.service.monitor.UserOnline;
import monster.service.resource.ResCampaign;
import monster.service.resource.ResRanking;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.service.user.UserTeam;
import protocol.Pbmethod;

import java.util.*;

public class IdleServiceImpl implements IdleService {

    @Inject
    IdleDAO idleDAO;
    @Inject
    MaterialService materialService;
    @Inject
    TeamService teamService;
    @Inject
    LogService logService;
    @Inject
    TruongProEventService truongProEventService;
    List<Integer> processUserIds = new ArrayList<>();

    @CacheInGame(expire = 300)
    private UserIdleEntity getUserIdleFromDB(MyUser mUser, int gameType) {
        int userId = mUser.getUser().getId();
        UserIdleEntity idleEntity = idleDAO.getUserIdle(userId, gameType);
        if (idleEntity == null) {
            idleEntity = UserIdleEntity.builder().userId(userId).gameType(gameType).build();
            if (idleDAO.save(idleEntity)) return idleEntity;
            return null;
        }
        return idleEntity;
    }

    @Override
    public UserIdleEntity getUserIdle(MyUser mUser, int gameType) {
        return getUserIdleFromDB(mUser, gameType);
    }

    // "vLong: status(0: chưa làm gì, 1: đang chạy, 2: đã dừng), countdown(không hiện cho user, khi về 0 thì lấy status), [bonus]
    // vString: text bắt đầu, text kết thúc"
    @Override
    public ServiceResult<Pbmethod.CommonVector> status(MyUser mUser, int gameType) {
        UserIdleEntity idleEntity = getUserIdle(mUser, gameType);
        //        if (idleEntity.getStatus() == 1 && idleEntity.shouldAttackNow())
        //            CompletableFuture.runAsync(() -> checkIdleBattle(mUser));

        UserCampaignEntity campaign = Services.userService.getCampaign(mUser);

        Pbmethod.CommonVector.Builder builder = Pbmethod.CommonVector.newBuilder();
        builder.addALong(idleEntity.getStatus()).addALong(idleEntity.getCountdown());
        builder.addALong(idleEntity.getStartLevel()).addALong(campaign == null ? idleEntity.getStartLevel() : campaign.getLevel());
        builder.addAllALong(idleEntity.getListBonus());
        return ServiceResult.success(builder.build());
    }

    @Override
    public ServiceResult start(MyUser mUser, int gameType, List<Long> idHeroes) {
        UserIdleEntity idleEntity = getUserIdle(mUser, gameType);
        if (idleEntity.isIdling() || idleEntity.isFinish()) {
            return ServiceResult.error("Đang trong quá trình tự động đánh hoặc chưa nhận thưởng");
        }
        UserCampaignEntity campaign = Services.userService.getCampaign(mUser);
        if (campaign == null) {
            return ServiceResult.error(Lang.err_system_down);
        }
        if (campaign.getLevel() > CfgLimitFunction.config.limitCampaignLevel) {
            return ServiceResult.error(Lang.err_max_level);
        }

        BattleTeam team = teamService.getBattleTeam(mUser, idHeroes);
        if (!team.isOk()) return ServiceResult.error(Lang.err_team_invalid);

        UserTeam.saveOrUpdateUserTeam(mUser.getUser(), TeamType.CAMPAIGN, team, team.getPower());

        String userTeam = StringHelper.toDBString(idHeroes);

        var bitSet = truongProEventService.monthlyCardStatus(mUser);
        int speed = bitSet.get(0) || bitSet.get(1) ? 1 : 0;
        if (idleDAO.startIdle(idleEntity, userTeam, campaign.getLevel(), speed)) {
            idleEntity.startIdle(userTeam, campaign.getLevel(), speed);
            processUserIds.add(idleEntity.getUserId());
            return ServiceResult.success();
        }
        return ServiceResult.error(Lang.err_system_down);
    }

    @Override
    public ServiceResult<List<Long>> stopAndReward(MyUser mUser, int gameType) {
        UserIdleEntity userIdle = getUserIdle(mUser, gameType);
        if (userIdle.getStatus() == 0) {
            return ServiceResult.error("Chưa bắt đầu tính năng idle");
        }
        if (userIdle.getProcessFlag().get()) {
            return ServiceResult.error("Hệ thông đang tự động đánh, không nhận thưởng được");
        }
        if (idleDAO.stopIdle(userIdle.getId())) {
            userIdle.setStatus(0);
            userIdle.setSpeed(0);
            return materialService.addBonus(mUser, userIdle.getListBonus(), "idle_stop_" + gameType);
        }
        return ServiceResult.error(Lang.err_system_down);
    }

    /**
     * đánh mỗi 2 phút tới khi thua
     */
    @Override
    public UserIdleEntity checkIdleBattle(MyUser mUser) {
        UserIdleEntity userIdle = getUserIdle(mUser, CfgIdle.GAME_TYPE_CAMPAIGN);
        if (userIdle != null && userIdle.isIdling()) { // đang idle
            if (userIdle.getProcessFlag().compareAndSet(false, true)) {
                try {
                    while (userIdle.shouldAttackNow()) {
                        UserCampaignEntity campaign = Services.userService.getCampaign(mUser);
                        if (campaign != null) {
                            List<Long> heroIds = GsonUtil.strToListLong(userIdle.getUserTeam());
                            ResCampaignEntity resCampaign = ResCampaign.getCampaign(campaign.getLevel(), mUser.getUser().getServer());
                            if (resCampaign.getHaveToPlay() == 1) {
                                updateErrorAndStop(userIdle, "haveToPlay");
                                return userIdle;
                            }
                            ModePlay modePlay = resCampaign.getModePlay();
                            int numberTeam = modePlay == null ? ModePlayType.CAMPAIGN.numberTeam : modePlay.numberTeam;
                            BattleMode battleMode;
                            if (numberTeam == 1) battleMode = BattleMode.ENDING;
                            else battleMode = BattleMode.NORMAL_ONE;
                            if (campaign.getLevel() != campaign.getLevelAuto() || heroIds.size() != BattleConfig.TEAM_INPUT * numberTeam) {
                                updateErrorAndStop(userIdle, Lang.err_params);
                                return userIdle;
                            }
                            if (modePlay != null) {
                                if (!modePlay.checkHeroOk(heroIds, mUser, null)) {
                                    return userIdle;
                                }
                            }
                            if (mUser.getUser().getLevel() < resCampaign.getRequireLevel()) {
                                updateErrorAndStop(userIdle, Lang.user_function_level_required);
                                return userIdle;
                            }
                            BattleTeam myTeam = BattleTeam.getInstanceNew(mUser, heroIds);
                            if (myTeam == null) {
                                updateErrorAndStop(userIdle, Lang.err_hero_duplicate);
                                return userIdle;
                            }
                            if (!myTeam.isOk()) {
                                updateErrorAndStop(userIdle, Lang.err_team_invalid);
                                return userIdle;
                            }
                            if (myTeam.getPower() < resCampaign.getMinPower()) {
                                updateErrorAndStop(userIdle, Lang.err_power_attack);
                                return userIdle;
                            }

                            UserPassFunction passFunction = logService.getUserPassFunction(mUser.getUser().getServer(), BattleType.MODE_CAMPAIGN.value, resCampaign.getId());
                            BattleResultEntityNew battleResult = BattleBuilder.builder().setTeam(myTeam, resCampaign.getBattleTeam(mUser, campaign, myTeam.getPower(), passFunction.number)).setMode(BattleType.MODE_CAMPAIGN, battleMode).setInfo(userIdle.getUserId()).battle();
                            if (battleResult.isWinAbsolute()) {
                                String newBonus = StringHelper.toDBString(Bonus.merge(GsonUtil.strToListLong(userIdle.getBonusData()), resCampaign.getWinBonus()));
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(userIdle.getDateModified());
                                calendar.add(Calendar.SECOND, (int) (userIdle.getSpeed() == 0 ? CfgIdle.timeWait : CfgIdle.timeWaitVip));

                                if (idleDAO.attack(userIdle.getId(), newBonus, calendar.getTime())) {
                                    userIdle.setBonusData(newBonus);
                                    userIdle.setNumberAttack(userIdle.getNumberAttack() + 1);
                                    userIdle.setDateModified(calendar.getTime());
                                    if (campaign.getLevel() == campaign.getLevelAuto()) {
                                        EventType.CAMPAIGN_STAGE.addEvent(mUser, resCampaign.getChapter());
                                        campaign.update(Arrays.asList("level", campaign.getLevel() + 1, "level_auto", campaign.getLevel() + 1,
                                                "last_time_attack_win", DateTime.getFullDate(), "first_loose_power", 0));
                                        campaign.setLevel(campaign.getLevel() + 1);
                                        campaign.setLevelAuto(campaign.getLevelAuto() + 1);
                                        campaign.setFirstLoosePower(0);
                                    }
                                    EventMonitor.getInstance().addDropItem(userIdle.getUserId(), EventType.CAMPAIGN_LEVEL, resCampaign.getChapterId());
                                    EventType.CAMPAIGN_ATTACK.addEvent(mUser);
                                    Actions.save(mUser.getUser(), "idle", "campaign_win", "level", campaign.getLevel(), "win", true, "power", myTeam.getPower());
                                    if (userIdle.getNumberAttack() == 60) {
                                        updateErrorAndStop(userIdle, "complete");
                                    }
                                    // vượt qua ải tut mới dừng
                                    if (CfgCampaign.config.stopIdle.contains(campaign.getLevel() - 1)) {
                                        updateErrorAndStop(userIdle, "tutorial");
                                    }
                                    passFunction.addValue();
                                    ResRanking.addRank(RankType.CAMPAIGN, mUser.getUser(), campaign.getLevel());
                                }
                            } else if (updateErrorAndStop(userIdle, "loose")) {
                                EventType.CAMPAIGN_ATTACK.addEvent(mUser);
                                Actions.save(mUser.getUser(), "idle", "campaign_loose", "level", campaign.getLevel());
                                if (campaign.getFirstLoosePower() == 0) {
                                    campaign.setFirstLoosePower(myTeam.getPower());
                                    campaign.update("first_loose_power", campaign.getFirstLoosePower(), "first_loose_level", campaign.getLevel());
                                }
                            }
                        }
                    }
                } catch (Exception ex) {
                    Logs.error(ex);
                } finally {
                    userIdle.getProcessFlag().set(false);
                }
            } else {
                // Another thread is already performing the task, handle accordingly
            }
        }
        return userIdle;
    }

    @Override
    public boolean isIdling(MyUser mUser, int gameType) {
        UserIdleEntity idleEntity = getUserIdle(mUser, CfgIdle.GAME_TYPE_CAMPAIGN);
        return idleEntity != null && idleEntity.getStatus() != 0;
    }

    /**
     * Hiện tại cho free dùng thoải mái
     *
     * @param mUser
     * @return
     */
    @Override
    public boolean couldIdle(MyUser mUser) {
        if (true) return true;
        if (DateTime.numberDayPassed(mUser.getUser().getDateCreated()) == 0) return true;
        var bitSet = truongProEventService.monthlyCardStatus(mUser);
        return bitSet.get(0);
    }

    @Override
    public void addProcessId(int userId) {
        if (!processUserIds.contains(userId)) processUserIds.add(userId);
    }

    /**
     * Cứ 1s lại vào đây một lần để xử lý các battle chưa chạy
     * Đông quá liệu có lag không :(
     * <p>
     * Cần đảm bảo 2 server không chạy chung 1 userId không là toang
     * <p>
     * Using list for fix ConcurrentModificationException
     */
    @Override
    public void processIdleBattle() {
        int index = 0;
        while (index < processUserIds.size()) {
            Integer userId = processUserIds.get(index++);

            if (userId == null) {
                processUserIds.remove(userId);
                continue;
            }

            UserEntity user = UserOnline.getDbUser(userId);
            MyUser mUser = user == null ? null : UserOnline.getMyUserFromUsername(user.getUsername());
            if (user != null && mUser != null) {// user is online and process idle battle
                UserIdleEntity userIdle = checkIdleBattle(mUser);
                if (userIdle == null || !userIdle.isIdling()) processUserIds.remove(userId);
            } else processUserIds.remove(userId);
        }
    }

    @Override
    public int sizeOfIdle() {
        return processUserIds.size();
    }

    public boolean updateErrorAndStop(UserIdleEntity userIdle, String errorMessage) {
        if (idleDAO.stopErrorIdle(userIdle.getId(), errorMessage)) {
            userIdle.setNote(errorMessage);
            userIdle.setStatus(2);
            userIdle.setDateModified(new Date());
            return true;
        }
        return false;
    }

}
