package monster.service.common.impl;

import monster.config.penum.NotifyType;
import monster.object.MyUser;
import monster.object.UserNotify;
import monster.service.common.NotifyService;

public class NotifyServiceImpl implements NotifyService {

    /**
     * Set lại giá trị notify nếu thay đổi
     * + Update lại trạng thái notify cho client
     *
     * @param mUser
     * @param notifyType
     * @param values
     */
    @Override
    public void setNotify(MyUser mUser, NotifyType notifyType, long... values) {
        long value = values.length == 0 ? 1 : values[0];
        UserNotify uNotify = mUser.getUData().getUNotify();
        if (uNotify.getValue(notifyType) != value) {
            uNotify.setValue(notifyType, value).update();
            mUser.getSetNotify().add(value == 1 ? notifyType.value : -notifyType.value);
        }
    }

    @Override
    public void removeNotify(MyUser mUser, NotifyType notifyType, long... values) {
        setNotify(mUser, notifyType, 0);
    }

    @Override
    public void sendNotify(MyUser mUser, NotifyType notifyType, boolean add) {
        mUser.getSetNotify().add(add ? notifyType.value : -notifyType.value);
    }
}
