package monster.service.common.impl;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.inject.Inject;
import grep.database.DBJPA;
import grep.helper.DateTime;
import grep.helper.GsonUtil;
import grep.log.Logs;
import monster.config.CfgServer;
import monster.dao.SystemDAO;
import monster.service.aop.CacheInGame;
import monster.service.common.SystemService;

import java.util.*;
import java.util.stream.Collectors;

public class SystemServiceImpl implements SystemService {

    @Inject
    SystemDAO systemDAO;

    @Override
    public List<Integer> listServerOpen7Days() {
        Date date7Day = DateTime.getStartOfDay(DateTime.getCalendar(Calendar.DATE, -6).getTime());
        return CfgServer.serverOpenDate.keySet().stream()
                .filter(serverId -> CfgServer.serverOpenDate.get(serverId).before(date7Day))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> listAllServer() {
        List<Integer> serverIds = new ArrayList<>();
        String value = DBJPA.getUniqueColumn("dson_main.config_api", Arrays.asList("k", "server_list_open"), "v");
        JsonArray values = GsonUtil.parseJsonArray(value);
        for (JsonElement jsonElement : values) {
            try {
                serverIds.add(jsonElement.getAsJsonObject().get("id").getAsInt());
            } catch (Exception ex) {
                Logs.error(ex);
            }
        }
        Collections.sort(serverIds);
        return serverIds;
    }

    @CacheInGame(expire = 300)
    @Override
    public Integer getNumberServer() {
        String value = DBJPA.getUniqueColumn("dson_main.config_api", Arrays.asList("k", "server_list_count"), "v");
        return Integer.parseInt(value) - 3;
    }

    @Override
    public int getNumberDayServerOpen(int serverId) {
        Date dateOpen = CfgServer.serverOpenDate.get(serverId);
        if (dateOpen == null) dateOpen = DateTime.getCalendar(Calendar.DATE, -30).getTime();
        return (int) DateTime.getDayDiff(dateOpen, new Date());
    }
}
