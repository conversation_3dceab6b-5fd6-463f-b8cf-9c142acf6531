package monster.service.common.impl;

import com.google.inject.Inject;
import grep.database.DBJPA;
import grep.helper.StringHelper;
import grep.log.Logs;
import monster.config.lang.Lang;
import monster.config.penum.EventType;
import monster.config.penum.MaterialType;
import monster.dao.MaterialDAO;
import monster.dao.mapping.UserMaterialEntity;
import monster.dao.mapping.main.ResSkinEntity;
import monster.object.MyUser;
import monster.object.ServiceResult;
import monster.game.system.service.LogService;
import monster.service.common.MaterialService;
import monster.service.common.WarningService;
import monster.service.resource.ResSkin;
import monster.service.user.Actions;
import monster.service.user.Bonus;
import monster.task.dbcache.MaterialCache;

import java.util.ArrayList;
import java.util.List;

public class MaterialServiceImpl implements MaterialService {

    @Inject
    MaterialDAO dao;
    @Inject
    WarningService warningService;
    @Inject
    LogService logService;

    @Override
    public ServiceResult<List<Long>> addBonus(MyUser mUser, List<Long> bonus, String... logDetail) {
        return ServiceResult.success(Bonus.receiveListItem(mUser, logDetail.length == 0 ? "none" : logDetail[0], bonus));
    }

    @Override
    public ServiceResult<List<Long>> addGem(MyUser mUser, long number, String... logDetail) {
        if (mUser.getUser().getGem() + number < 0) return ServiceResult.error(Lang.err_not_enough_gem);
        if (dao.addGem(mUser.getUser().getId(), number)) {
            mUser.getUser().addGem(number, mUser);
            if (StringHelper.getFirstArrayValue(logDetail).contains(":1401")) { // check nhận thẻ tháng
                if (number > 500) EventType.MONTHLY_CARD_BIG.addEvent(mUser);
                else EventType.MONTHLY_CARD_SMALL.addEvent(mUser);
            }
            Actions.logGem(mUser.getUser(), StringHelper.getFirstArrayValue(logDetail), number, StringHelper.getSecondArrayValue(logDetail));
            warningService.checkBonusNumber(mUser, logDetail.length == 0 ? "none" : logDetail[0], Bonus.BONUS_GEM, number);
            //            Actions.save(mUser.getUser(), "user", "res", "check_point", 3, "session", mUser.getSession());
            logService.updateLastResource(mUser);
            return ServiceResult.success(Bonus.view(Bonus.BONUS_GEM, mUser.getUser().getGem(), number));
        }
        return ServiceResult.error(Lang.err_system_down);
    }

    @Override
    public ServiceResult<List<Long>> addRuby(MyUser mUser, long number, String... logDetail) {
        if (mUser.getUser().getRuby() + number < 0) return ServiceResult.error(Lang.err_not_enough_ruby);
        if (dao.addRuby(mUser.getUser().getId(), number)) {
            Actions.logRuby(mUser.getUser(), StringHelper.getFirstArrayValue(logDetail), number, StringHelper.getSecondArrayValue(logDetail));
            mUser.getUser().addRuby(number, mUser);
            warningService.checkBonusNumber(mUser, logDetail.length == 0 ? "none" : logDetail[0], Bonus.BONUS_RUBY, number);
            //            Actions.save(mUser.getUser(), "user", "res", "check_point", 4, "session", mUser.getSession());
            logService.updateLastResource(mUser);
            return ServiceResult.success(Bonus.view(Bonus.BONUS_RUBY, mUser.getUser().getRuby(), number));
        }
        return ServiceResult.error(Lang.err_system_down);
    }

    @Override
    public ServiceResult<List<Long>> addMaterial(MyUser mUser, MaterialType materialType, long number, String... logDetail) {
        return addMaterial(mUser, materialType.type, materialType.id, number, logDetail);
    }

    @Override
    public ServiceResult<List<Long>> addMaterial(MyUser mUser, int materialId, long number, String... logDetail) {
        return addMaterial(mUser, MaterialType.TYPE_USED_ITEM, materialId, number, logDetail);
    }

    @Override
    public ServiceResult<List<Long>> addMaterial(MyUser mUser, int typeId, int materialId, long number, String... logDetail) {
        if (typeId == 1) {
            MaterialType materialType = MaterialType.getUsedItem(materialId);
            if (materialType != null) {
                switch (materialType) {
                    default -> {
                        if (materialType.eventType != null) {
                            materialType.eventType.addEvent(mUser, number);
                            return ServiceResult.success(new ArrayList<>());
                        }
                    }
                }
            }
        }
        UserMaterialEntity userMaterial = mUser.getResources().getMaterial(typeId, materialId);
        if (userMaterial == null)
            Logs.error("%s material null typeId=%s id=%s".formatted(mUser.getUser().getId(), typeId, materialId));
        if (userMaterial.getNumber() + number < 0) {
            //            mUser.addPriorityAction(IAction.NOT_ENOUGH_MATERIAL, CommonProto.getCommonVectorProto(Bonus.viewMaterial(typeId, materialId, number)));
            //            return ServiceResult.error("none"); //
            return ServiceResult.error(Lang.err_not_enough_material);
        } else if (userMaterial != null && MaterialCache.getInstance().addValue(userMaterial, number)) {
            warningService.checkBonusMaterial(mUser, logDetail.length == 0 ? "none" : logDetail[0], typeId, materialId, number);
            //            MailNotify.alertMaterial(mUser, userMaterial, number);
            Actions.logMaterial(mUser.getUser(), logDetail.length == 0 ? "none" : logDetail[0], userMaterial, number);
            //            if (EventType.eventPoint.contains(materialId)) {
            //                EventMonitor.getInstance().addLeftDropItem(mUser.getUser().getId(), materialId, number);
            //                return ServiceResult.success(new ArrayList<>());
            //            }
            //            if (MaterialType.isEventItem(typeId, materialId)) {
            //                EventMonitor.getInstance().addDropItem(mUser.getUser().getId(), materialId, Math.abs(number));
            //            }
            return ServiceResult.success(Bonus.view(Bonus.BONUS_MATERIAL, typeId, materialId, userMaterial.getNumber(), number));
        }
        return ServiceResult.error(Lang.err_system_down);
    }

    @Override
    public ServiceResult<List<Long>> useMaterial(MyUser mUser, int materialId, long number, String... logDetail) {
        if (number < 0) return ServiceResult.error(Lang.err_number_positive);
        return addMaterial(mUser, materialId, -Math.abs(number), logDetail);
    }

    @Override
    public ServiceResult<List<Long>> useFragmentMaterial(MyUser mUser, int materialId, long number, String... logDetail) {
        if (number < 0) return ServiceResult.error(Lang.err_number_positive);
        return addMaterial(mUser, MaterialType.TYPE_HERO_SHARD, materialId, -Math.abs(number), logDetail);
    }

    @Override
    public ServiceResult<String> checkAvatarOwner(MyUser mUser, List<Long> values) {
        List<List<Long>> aBonus = Bonus.parse(values);
        for (List<Long> bonus : aBonus) {
            if (!bonus.isEmpty()) {
                switch (bonus.get(0).intValue()) {
                    case Bonus.BONUS_HERO_SKIN -> {
                        int skinId = bonus.get(1).intValue();
                        if (mUser.getResources().mHeroSkin.containsKey(skinId)) {
                            ResSkinEntity skin = ResSkin.mSkin.get(skinId);
                            return ServiceResult.error(String.format(mUser.getLang().get(Lang.hero_skin_already_own), skin.getName()));
                        }
                    }
                    case Bonus.BONUS_AVATAR_FRAME -> {
                        int frameId = bonus.get(1).intValue();
                        if (DBJPA.count("user_avatar_frame", "user_id", mUser.getUser().getId(), "frame_id", frameId) > 0) {
                            return ServiceResult.error(String.format(mUser.getLang().get(Lang.hero_skin_already_own), mUser.getLang().get("res_avatar_frame_name_" + frameId)));
                        }
                    }
                }
            }
        }
        return ServiceResult.success();
    }
}
