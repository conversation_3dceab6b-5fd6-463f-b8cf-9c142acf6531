package monster.service.common;

import monster.dao.mapping.UserLabyrinthEntity;
import monster.object.LabyrinthHero;
import monster.object.LabyrinthMap;
import monster.object.MyUser;
import monster.service.battle.common.entity.HeroInfoEntity;

import java.util.List;

public interface LabyrinthService {

    UserLabyrinthEntity getUserLabyrinth(MyUser mUser);
    boolean genMap(MyUser mUser);
    boolean genMyHeroes(MyUser mUser, int stageLevel);
    boolean addSupportHero(MyUser mUser, LabyrinthHero supportHero);
    void genMapData(MyUser mUser, LabyrinthMap map, UserLabyrinthEntity userLabyrinth);
    void checkNotify(MyUser mUser);
    List<Long> calculateSweepBonus(UserLabyrinthEntity userLabyrinth);
}
