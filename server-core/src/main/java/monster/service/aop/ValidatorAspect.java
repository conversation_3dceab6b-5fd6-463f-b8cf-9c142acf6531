package monster.service.aop;

import grep.helper.StringHelper;
import monster.config.lang.Lang;
import monster.controller.AHandler;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Parameter;

@Aspect
public class ValidatorAspect {

    @Around("@annotation(Validate) && execution(* *(..))")
    public Object validateParameters(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        Parameter[] parameters = signature.getMethod().getParameters();
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(NumberInput.class)) {
                Long value = Long.valueOf(args[i].toString().trim());
                if (value == null || value < 0 || value > 100) {
                    String message = parameters[i].getAnnotation(NumberInput.class).message();
                    throw new ValidateException(findHandler(joinPoint), StringHelper.isEmpty(message) ? Lang.err_number_positive : message);
                } else if (value > 1000) {
                    String message = parameters[i].getAnnotation(NumberInput.class).message();
                    throw new ValidateException(findHandler(joinPoint), StringHelper.isEmpty(message) ? Lang.err_params : message);
                }
            }
        }

        return joinPoint.proceed(); // Proceed if no validation errors
    }

    public AHandler findHandler(ProceedingJoinPoint joinPoint) {
        try {
            return (AHandler) joinPoint.getTarget();
        } catch (Exception ex) {
        }
        return null;
    }

}
