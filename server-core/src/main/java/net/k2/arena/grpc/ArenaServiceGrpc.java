package net.k2.arena.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.50.2)",
    comments = "Source: DragonService.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ArenaServiceGrpc {

  private ArenaServiceGrpc() {}

  public static final String SERVICE_NAME = "proto.ArenaService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<net.k2.arena.grpc.CommonInput,
      net.k2.arena.grpc.CommonResponse> getSwapTrialRankMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SwapTrialRank",
      requestType = net.k2.arena.grpc.CommonInput.class,
      responseType = net.k2.arena.grpc.CommonResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<net.k2.arena.grpc.CommonInput,
      net.k2.arena.grpc.CommonResponse> getSwapTrialRankMethod() {
    io.grpc.MethodDescriptor<net.k2.arena.grpc.CommonInput, net.k2.arena.grpc.CommonResponse> getSwapTrialRankMethod;
    if ((getSwapTrialRankMethod = ArenaServiceGrpc.getSwapTrialRankMethod) == null) {
      synchronized (ArenaServiceGrpc.class) {
        if ((getSwapTrialRankMethod = ArenaServiceGrpc.getSwapTrialRankMethod) == null) {
          ArenaServiceGrpc.getSwapTrialRankMethod = getSwapTrialRankMethod =
              io.grpc.MethodDescriptor.<net.k2.arena.grpc.CommonInput, net.k2.arena.grpc.CommonResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SwapTrialRank"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.arena.grpc.CommonInput.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.arena.grpc.CommonResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ArenaServiceMethodDescriptorSupplier("SwapTrialRank"))
              .build();
        }
      }
    }
    return getSwapTrialRankMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ArenaServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ArenaServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ArenaServiceStub>() {
        @Override
        public ArenaServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ArenaServiceStub(channel, callOptions);
        }
      };
    return ArenaServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ArenaServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ArenaServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ArenaServiceBlockingStub>() {
        @Override
        public ArenaServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ArenaServiceBlockingStub(channel, callOptions);
        }
      };
    return ArenaServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ArenaServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ArenaServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ArenaServiceFutureStub>() {
        @Override
        public ArenaServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ArenaServiceFutureStub(channel, callOptions);
        }
      };
    return ArenaServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class ArenaServiceImplBase implements io.grpc.BindableService {

    /**
     */
    public void swapTrialRank(net.k2.arena.grpc.CommonInput request,
        io.grpc.stub.StreamObserver<net.k2.arena.grpc.CommonResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSwapTrialRankMethod(), responseObserver);
    }

    @Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getSwapTrialRankMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                net.k2.arena.grpc.CommonInput,
                net.k2.arena.grpc.CommonResponse>(
                  this, METHODID_SWAP_TRIAL_RANK)))
          .build();
    }
  }

  /**
   */
  public static final class ArenaServiceStub extends io.grpc.stub.AbstractAsyncStub<ArenaServiceStub> {
    private ArenaServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected ArenaServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ArenaServiceStub(channel, callOptions);
    }

    /**
     */
    public void swapTrialRank(net.k2.arena.grpc.CommonInput request,
        io.grpc.stub.StreamObserver<net.k2.arena.grpc.CommonResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSwapTrialRankMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class ArenaServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<ArenaServiceBlockingStub> {
    private ArenaServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected ArenaServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ArenaServiceBlockingStub(channel, callOptions);
    }

    /**
     */
    public net.k2.arena.grpc.CommonResponse swapTrialRank(net.k2.arena.grpc.CommonInput request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSwapTrialRankMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class ArenaServiceFutureStub extends io.grpc.stub.AbstractFutureStub<ArenaServiceFutureStub> {
    private ArenaServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected ArenaServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ArenaServiceFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.arena.grpc.CommonResponse> swapTrialRank(
        net.k2.arena.grpc.CommonInput request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSwapTrialRankMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SWAP_TRIAL_RANK = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final ArenaServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(ArenaServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SWAP_TRIAL_RANK:
          serviceImpl.swapTrialRank((net.k2.arena.grpc.CommonInput) request,
              (io.grpc.stub.StreamObserver<net.k2.arena.grpc.CommonResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @Override
    @SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class ArenaServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ArenaServiceBaseDescriptorSupplier() {}

    @Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return DragonService.getDescriptor();
    }

    @Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ArenaService");
    }
  }

  private static final class ArenaServiceFileDescriptorSupplier
      extends ArenaServiceBaseDescriptorSupplier {
    ArenaServiceFileDescriptorSupplier() {}
  }

  private static final class ArenaServiceMethodDescriptorSupplier
      extends ArenaServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    ArenaServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ArenaServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ArenaServiceFileDescriptorSupplier())
              .addMethod(getSwapTrialRankMethod())
              .build();
        }
      }
    }
    return result;
  }
}
