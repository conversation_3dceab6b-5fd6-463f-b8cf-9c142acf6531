package net.k2.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;
import static io.grpc.stub.ClientCalls.asyncBidiStreamingCall;
import static io.grpc.stub.ClientCalls.asyncClientStreamingCall;
import static io.grpc.stub.ClientCalls.asyncServerStreamingCall;
import static io.grpc.stub.ClientCalls.asyncUnaryCall;
import static io.grpc.stub.ClientCalls.blockingServerStreamingCall;
import static io.grpc.stub.ClientCalls.blockingUnaryCall;
import static io.grpc.stub.ClientCalls.futureUnaryCall;
import static io.grpc.stub.ServerCalls.asyncBidiStreamingCall;
import static io.grpc.stub.ServerCalls.asyncClientStreamingCall;
import static io.grpc.stub.ServerCalls.asyncServerStreamingCall;
import static io.grpc.stub.ServerCalls.asyncUnaryCall;
import static io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall;
import static io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.10.0)",
    comments = "Source: dtq.proto")
public final class DtqGrpc {

  private DtqGrpc() {}

  public static final String SERVICE_NAME = "proto.Dtq";

  // Static method descriptors that strictly reflect the proto.
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getSendMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> METHOD_SEND = getSendMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> getSendMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> getSendMethod() {
    return getSendMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> getSendMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.CommonVector, net.k2.grpc.CommonVector> getSendMethod;
    if ((getSendMethod = DtqGrpc.getSendMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getSendMethod = DtqGrpc.getSendMethod) == null) {
          DtqGrpc.getSendMethod = getSendMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.CommonVector, net.k2.grpc.CommonVector>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "Send"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("Send"))
                  .build();
          }
        }
     }
     return getSendMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getSendCupMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> METHOD_SEND_CUP = getSendCupMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> getSendCupMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> getSendCupMethod() {
    return getSendCupMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.CommonVector,
      net.k2.grpc.CommonVector> getSendCupMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.CommonVector, net.k2.grpc.CommonVector> getSendCupMethod;
    if ((getSendCupMethod = DtqGrpc.getSendCupMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getSendCupMethod = DtqGrpc.getSendCupMethod) == null) {
          DtqGrpc.getSendCupMethod = getSendCupMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.CommonVector, net.k2.grpc.CommonVector>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "SendCup"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("SendCup"))
                  .build();
          }
        }
     }
     return getSendCupMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getSendListCupMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> METHOD_SEND_LIST_CUP = getSendListCupMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListCupMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListCupMethod() {
    return getSendListCupMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListCupMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector> getSendListCupMethod;
    if ((getSendListCupMethod = DtqGrpc.getSendListCupMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getSendListCupMethod = DtqGrpc.getSendListCupMethod) == null) {
          DtqGrpc.getSendListCupMethod = getSendListCupMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "SendListCup"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.ListCommonVector.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("SendListCup"))
                  .build();
          }
        }
     }
     return getSendListCupMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getSendListGoldSpendingMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> METHOD_SEND_LIST_GOLD_SPENDING = getSendListGoldSpendingMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListGoldSpendingMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListGoldSpendingMethod() {
    return getSendListGoldSpendingMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListGoldSpendingMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector> getSendListGoldSpendingMethod;
    if ((getSendListGoldSpendingMethod = DtqGrpc.getSendListGoldSpendingMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getSendListGoldSpendingMethod = DtqGrpc.getSendListGoldSpendingMethod) == null) {
          DtqGrpc.getSendListGoldSpendingMethod = getSendListGoldSpendingMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "SendListGoldSpending"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.ListCommonVector.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("SendListGoldSpending"))
                  .build();
          }
        }
     }
     return getSendListGoldSpendingMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getSendListItemMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> METHOD_SEND_LIST_ITEM = getSendListItemMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListItemMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListItemMethod() {
    return getSendListItemMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListItemMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector> getSendListItemMethod;
    if ((getSendListItemMethod = DtqGrpc.getSendListItemMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getSendListItemMethod = DtqGrpc.getSendListItemMethod) == null) {
          DtqGrpc.getSendListItemMethod = getSendListItemMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "SendListItem"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.ListCommonVector.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("SendListItem"))
                  .build();
          }
        }
     }
     return getSendListItemMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getSendListHeroWinMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> METHOD_SEND_LIST_HERO_WIN = getSendListHeroWinMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListHeroWinMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListHeroWinMethod() {
    return getSendListHeroWinMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector,
      net.k2.grpc.CommonVector> getSendListHeroWinMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector> getSendListHeroWinMethod;
    if ((getSendListHeroWinMethod = DtqGrpc.getSendListHeroWinMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getSendListHeroWinMethod = DtqGrpc.getSendListHeroWinMethod) == null) {
          DtqGrpc.getSendListHeroWinMethod = getSendListHeroWinMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.ListCommonVector, net.k2.grpc.CommonVector>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "SendListHeroWin"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.ListCommonVector.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.CommonVector.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("SendListHeroWin"))
                  .build();
          }
        }
     }
     return getSendListHeroWinMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getGetEventListMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbEventList> METHOD_GET_EVENT_LIST = getGetEventListMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbEventList> getGetEventListMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbEventList> getGetEventListMethod() {
    return getGetEventListMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbEventList> getGetEventListMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest, net.k2.grpc.PbEventList> getGetEventListMethod;
    if ((getGetEventListMethod = DtqGrpc.getGetEventListMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getGetEventListMethod = DtqGrpc.getGetEventListMethod) == null) {
          DtqGrpc.getGetEventListMethod = getGetEventListMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.PbEventRequest, net.k2.grpc.PbEventList>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "GetEventList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventList.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("GetEventList"))
                  .build();
          }
        }
     }
     return getGetEventListMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getGetTopCupListMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> METHOD_GET_TOP_CUP_LIST = getGetTopCupListMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> getGetTopCupListMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> getGetTopCupListMethod() {
    return getGetTopCupListMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> getGetTopCupListMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest, net.k2.grpc.PbTopList> getGetTopCupListMethod;
    if ((getGetTopCupListMethod = DtqGrpc.getGetTopCupListMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getGetTopCupListMethod = DtqGrpc.getGetTopCupListMethod) == null) {
          DtqGrpc.getGetTopCupListMethod = getGetTopCupListMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.PbEventRequest, net.k2.grpc.PbTopList>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "GetTopCupList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbTopList.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("GetTopCupList"))
                  .build();
          }
        }
     }
     return getGetTopCupListMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getGetTopGoldListMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> METHOD_GET_TOP_GOLD_LIST = getGetTopGoldListMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> getGetTopGoldListMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> getGetTopGoldListMethod() {
    return getGetTopGoldListMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbTopList> getGetTopGoldListMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest, net.k2.grpc.PbTopList> getGetTopGoldListMethod;
    if ((getGetTopGoldListMethod = DtqGrpc.getGetTopGoldListMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getGetTopGoldListMethod = DtqGrpc.getGetTopGoldListMethod) == null) {
          DtqGrpc.getGetTopGoldListMethod = getGetTopGoldListMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.PbEventRequest, net.k2.grpc.PbTopList>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "GetTopGoldList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbTopList.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("GetTopGoldList"))
                  .build();
          }
        }
     }
     return getGetTopGoldListMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getGetCupAccumulatedRewardListMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> METHOD_GET_CUP_ACCUMULATED_REWARD_LIST = getGetCupAccumulatedRewardListMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> getGetCupAccumulatedRewardListMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> getGetCupAccumulatedRewardListMethod() {
    return getGetCupAccumulatedRewardListMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> getGetCupAccumulatedRewardListMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest, net.k2.grpc.PbAccumulatedRewardList> getGetCupAccumulatedRewardListMethod;
    if ((getGetCupAccumulatedRewardListMethod = DtqGrpc.getGetCupAccumulatedRewardListMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getGetCupAccumulatedRewardListMethod = DtqGrpc.getGetCupAccumulatedRewardListMethod) == null) {
          DtqGrpc.getGetCupAccumulatedRewardListMethod = getGetCupAccumulatedRewardListMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.PbEventRequest, net.k2.grpc.PbAccumulatedRewardList>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "GetCupAccumulatedRewardList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbAccumulatedRewardList.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("GetCupAccumulatedRewardList"))
                  .build();
          }
        }
     }
     return getGetCupAccumulatedRewardListMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getGetGoldAccumulatedRewardListMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> METHOD_GET_GOLD_ACCUMULATED_REWARD_LIST = getGetGoldAccumulatedRewardListMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> getGetGoldAccumulatedRewardListMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> getGetGoldAccumulatedRewardListMethod() {
    return getGetGoldAccumulatedRewardListMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbAccumulatedRewardList> getGetGoldAccumulatedRewardListMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest, net.k2.grpc.PbAccumulatedRewardList> getGetGoldAccumulatedRewardListMethod;
    if ((getGetGoldAccumulatedRewardListMethod = DtqGrpc.getGetGoldAccumulatedRewardListMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getGetGoldAccumulatedRewardListMethod = DtqGrpc.getGetGoldAccumulatedRewardListMethod) == null) {
          DtqGrpc.getGetGoldAccumulatedRewardListMethod = getGetGoldAccumulatedRewardListMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.PbEventRequest, net.k2.grpc.PbAccumulatedRewardList>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "GetGoldAccumulatedRewardList"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbAccumulatedRewardList.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("GetGoldAccumulatedRewardList"))
                  .build();
          }
        }
     }
     return getGetGoldAccumulatedRewardListMethod;
  }
  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  @java.lang.Deprecated // Use {@link #getReceiveRewardMethod()} instead. 
  public static final io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbMessage> METHOD_RECEIVE_REWARD = getReceiveRewardMethodHelper();

  private static volatile io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbMessage> getReceiveRewardMethod;

  @io.grpc.ExperimentalApi("https://github.com/grpc/grpc-java/issues/1901")
  public static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbMessage> getReceiveRewardMethod() {
    return getReceiveRewardMethodHelper();
  }

  private static io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest,
      net.k2.grpc.PbMessage> getReceiveRewardMethodHelper() {
    io.grpc.MethodDescriptor<net.k2.grpc.PbEventRequest, net.k2.grpc.PbMessage> getReceiveRewardMethod;
    if ((getReceiveRewardMethod = DtqGrpc.getReceiveRewardMethod) == null) {
      synchronized (DtqGrpc.class) {
        if ((getReceiveRewardMethod = DtqGrpc.getReceiveRewardMethod) == null) {
          DtqGrpc.getReceiveRewardMethod = getReceiveRewardMethod = 
              io.grpc.MethodDescriptor.<net.k2.grpc.PbEventRequest, net.k2.grpc.PbMessage>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(
                  "proto.Dtq", "ReceiveReward"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbEventRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  net.k2.grpc.PbMessage.getDefaultInstance()))
                  .setSchemaDescriptor(new DtqMethodDescriptorSupplier("ReceiveReward"))
                  .build();
          }
        }
     }
     return getReceiveRewardMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static DtqStub newStub(io.grpc.Channel channel) {
    return new DtqStub(channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static DtqBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    return new DtqBlockingStub(channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static DtqFutureStub newFutureStub(
      io.grpc.Channel channel) {
    return new DtqFutureStub(channel);
  }

  /**
   */
  public static abstract class DtqImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * no use
     * </pre>
     */
    public void send(net.k2.grpc.CommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnimplementedUnaryCall(getSendMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public void sendCup(net.k2.grpc.CommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnimplementedUnaryCall(getSendCupMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public void sendListCup(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnimplementedUnaryCall(getSendListCupMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public void sendListGoldSpending(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnimplementedUnaryCall(getSendListGoldSpendingMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, itemId, quantity, time), (...), ...
     * </pre>
     */
    public void sendListItem(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnimplementedUnaryCall(getSendListItemMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, time, heroId1, heroId2, ...), (...), ...
     * </pre>
     */
    public void sendListHeroWin(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnimplementedUnaryCall(getSendListHeroWinMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/serverId/{serverId}/userId/{userId}" // Clients get this url format from config after login
     *&#92;&#92;        };
     * </pre>
     */
    public void getEventList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbEventList> responseObserver) {
      asyncUnimplementedUnaryCall(getGetEventListMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topCup/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getTopCupList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbTopList> responseObserver) {
      asyncUnimplementedUnaryCall(getGetTopCupListMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topGold/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getTopGoldList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbTopList> responseObserver) {
      asyncUnimplementedUnaryCall(getGetTopGoldListMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/cupAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getCupAccumulatedRewardList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbAccumulatedRewardList> responseObserver) {
      asyncUnimplementedUnaryCall(getGetCupAccumulatedRewardListMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/goldAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getGoldAccumulatedRewardList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbAccumulatedRewardList> responseObserver) {
      asyncUnimplementedUnaryCall(getGetGoldAccumulatedRewardListMethodHelper(), responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/receiveReward/userId/{userId}/eventId/{eventId}/sessionId/{sessionId}/buttonPos/{buttonPos}"
     *&#92;&#92;        };
     * </pre>
     */
    public void receiveReward(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbMessage> responseObserver) {
      asyncUnimplementedUnaryCall(getReceiveRewardMethodHelper(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getSendMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.CommonVector,
                net.k2.grpc.CommonVector>(
                  this, METHODID_SEND)))
          .addMethod(
            getSendCupMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.CommonVector,
                net.k2.grpc.CommonVector>(
                  this, METHODID_SEND_CUP)))
          .addMethod(
            getSendListCupMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.ListCommonVector,
                net.k2.grpc.CommonVector>(
                  this, METHODID_SEND_LIST_CUP)))
          .addMethod(
            getSendListGoldSpendingMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.ListCommonVector,
                net.k2.grpc.CommonVector>(
                  this, METHODID_SEND_LIST_GOLD_SPENDING)))
          .addMethod(
            getSendListItemMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.ListCommonVector,
                net.k2.grpc.CommonVector>(
                  this, METHODID_SEND_LIST_ITEM)))
          .addMethod(
            getSendListHeroWinMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.ListCommonVector,
                net.k2.grpc.CommonVector>(
                  this, METHODID_SEND_LIST_HERO_WIN)))
          .addMethod(
            getGetEventListMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.PbEventRequest,
                net.k2.grpc.PbEventList>(
                  this, METHODID_GET_EVENT_LIST)))
          .addMethod(
            getGetTopCupListMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.PbEventRequest,
                net.k2.grpc.PbTopList>(
                  this, METHODID_GET_TOP_CUP_LIST)))
          .addMethod(
            getGetTopGoldListMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.PbEventRequest,
                net.k2.grpc.PbTopList>(
                  this, METHODID_GET_TOP_GOLD_LIST)))
          .addMethod(
            getGetCupAccumulatedRewardListMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.PbEventRequest,
                net.k2.grpc.PbAccumulatedRewardList>(
                  this, METHODID_GET_CUP_ACCUMULATED_REWARD_LIST)))
          .addMethod(
            getGetGoldAccumulatedRewardListMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.PbEventRequest,
                net.k2.grpc.PbAccumulatedRewardList>(
                  this, METHODID_GET_GOLD_ACCUMULATED_REWARD_LIST)))
          .addMethod(
            getReceiveRewardMethodHelper(),
            asyncUnaryCall(
              new MethodHandlers<
                net.k2.grpc.PbEventRequest,
                net.k2.grpc.PbMessage>(
                  this, METHODID_RECEIVE_REWARD)))
          .build();
    }
  }

  /**
   */
  public static final class DtqStub extends io.grpc.stub.AbstractStub<DtqStub> {
    private DtqStub(io.grpc.Channel channel) {
      super(channel);
    }

    private DtqStub(io.grpc.Channel channel,
        io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected DtqStub build(io.grpc.Channel channel,
        io.grpc.CallOptions callOptions) {
      return new DtqStub(channel, callOptions);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public void send(net.k2.grpc.CommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getSendMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public void sendCup(net.k2.grpc.CommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getSendCupMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public void sendListCup(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getSendListCupMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public void sendListGoldSpending(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getSendListGoldSpendingMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, itemId, quantity, time), (...), ...
     * </pre>
     */
    public void sendListItem(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getSendListItemMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Input: (userId, time, heroId1, heroId2, ...), (...), ...
     * </pre>
     */
    public void sendListHeroWin(net.k2.grpc.ListCommonVector request,
        io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getSendListHeroWinMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/serverId/{serverId}/userId/{userId}" // Clients get this url format from config after login
     *&#92;&#92;        };
     * </pre>
     */
    public void getEventList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbEventList> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getGetEventListMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topCup/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getTopCupList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbTopList> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getGetTopCupListMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topGold/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getTopGoldList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbTopList> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getGetTopGoldListMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/cupAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getCupAccumulatedRewardList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbAccumulatedRewardList> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getGetCupAccumulatedRewardListMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/goldAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public void getGoldAccumulatedRewardList(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbAccumulatedRewardList> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getGetGoldAccumulatedRewardListMethodHelper(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/receiveReward/userId/{userId}/eventId/{eventId}/sessionId/{sessionId}/buttonPos/{buttonPos}"
     *&#92;&#92;        };
     * </pre>
     */
    public void receiveReward(net.k2.grpc.PbEventRequest request,
        io.grpc.stub.StreamObserver<net.k2.grpc.PbMessage> responseObserver) {
      asyncUnaryCall(
          getChannel().newCall(getReceiveRewardMethodHelper(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class DtqBlockingStub extends io.grpc.stub.AbstractStub<DtqBlockingStub> {
    private DtqBlockingStub(io.grpc.Channel channel) {
      super(channel);
    }

    private DtqBlockingStub(io.grpc.Channel channel,
        io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected DtqBlockingStub build(io.grpc.Channel channel,
        io.grpc.CallOptions callOptions) {
      return new DtqBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public net.k2.grpc.CommonVector send(net.k2.grpc.CommonVector request) {
      return blockingUnaryCall(
          getChannel(), getSendMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public net.k2.grpc.CommonVector sendCup(net.k2.grpc.CommonVector request) {
      return blockingUnaryCall(
          getChannel(), getSendCupMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public net.k2.grpc.CommonVector sendListCup(net.k2.grpc.ListCommonVector request) {
      return blockingUnaryCall(
          getChannel(), getSendListCupMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public net.k2.grpc.CommonVector sendListGoldSpending(net.k2.grpc.ListCommonVector request) {
      return blockingUnaryCall(
          getChannel(), getSendListGoldSpendingMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Input: (userId, itemId, quantity, time), (...), ...
     * </pre>
     */
    public net.k2.grpc.CommonVector sendListItem(net.k2.grpc.ListCommonVector request) {
      return blockingUnaryCall(
          getChannel(), getSendListItemMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Input: (userId, time, heroId1, heroId2, ...), (...), ...
     * </pre>
     */
    public net.k2.grpc.CommonVector sendListHeroWin(net.k2.grpc.ListCommonVector request) {
      return blockingUnaryCall(
          getChannel(), getSendListHeroWinMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/serverId/{serverId}/userId/{userId}" // Clients get this url format from config after login
     *&#92;&#92;        };
     * </pre>
     */
    public net.k2.grpc.PbEventList getEventList(net.k2.grpc.PbEventRequest request) {
      return blockingUnaryCall(
          getChannel(), getGetEventListMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topCup/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public net.k2.grpc.PbTopList getTopCupList(net.k2.grpc.PbEventRequest request) {
      return blockingUnaryCall(
          getChannel(), getGetTopCupListMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topGold/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public net.k2.grpc.PbTopList getTopGoldList(net.k2.grpc.PbEventRequest request) {
      return blockingUnaryCall(
          getChannel(), getGetTopGoldListMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/cupAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public net.k2.grpc.PbAccumulatedRewardList getCupAccumulatedRewardList(net.k2.grpc.PbEventRequest request) {
      return blockingUnaryCall(
          getChannel(), getGetCupAccumulatedRewardListMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/goldAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public net.k2.grpc.PbAccumulatedRewardList getGoldAccumulatedRewardList(net.k2.grpc.PbEventRequest request) {
      return blockingUnaryCall(
          getChannel(), getGetGoldAccumulatedRewardListMethodHelper(), getCallOptions(), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/receiveReward/userId/{userId}/eventId/{eventId}/sessionId/{sessionId}/buttonPos/{buttonPos}"
     *&#92;&#92;        };
     * </pre>
     */
    public net.k2.grpc.PbMessage receiveReward(net.k2.grpc.PbEventRequest request) {
      return blockingUnaryCall(
          getChannel(), getReceiveRewardMethodHelper(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class DtqFutureStub extends io.grpc.stub.AbstractStub<DtqFutureStub> {
    private DtqFutureStub(io.grpc.Channel channel) {
      super(channel);
    }

    private DtqFutureStub(io.grpc.Channel channel,
        io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected DtqFutureStub build(io.grpc.Channel channel,
        io.grpc.CallOptions callOptions) {
      return new DtqFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.CommonVector> send(
        net.k2.grpc.CommonVector request) {
      return futureUnaryCall(
          getChannel().newCall(getSendMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     * no use
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.CommonVector> sendCup(
        net.k2.grpc.CommonVector request) {
      return futureUnaryCall(
          getChannel().newCall(getSendCupMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.CommonVector> sendListCup(
        net.k2.grpc.ListCommonVector request) {
      return futureUnaryCall(
          getChannel().newCall(getSendListCupMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Input: (userId, quantity, time), (...), ...
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.CommonVector> sendListGoldSpending(
        net.k2.grpc.ListCommonVector request) {
      return futureUnaryCall(
          getChannel().newCall(getSendListGoldSpendingMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Input: (userId, itemId, quantity, time), (...), ...
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.CommonVector> sendListItem(
        net.k2.grpc.ListCommonVector request) {
      return futureUnaryCall(
          getChannel().newCall(getSendListItemMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Input: (userId, time, heroId1, heroId2, ...), (...), ...
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.CommonVector> sendListHeroWin(
        net.k2.grpc.ListCommonVector request) {
      return futureUnaryCall(
          getChannel().newCall(getSendListHeroWinMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/serverId/{serverId}/userId/{userId}" // Clients get this url format from config after login
     *&#92;&#92;        };
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.PbEventList> getEventList(
        net.k2.grpc.PbEventRequest request) {
      return futureUnaryCall(
          getChannel().newCall(getGetEventListMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topCup/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.PbTopList> getTopCupList(
        net.k2.grpc.PbEventRequest request) {
      return futureUnaryCall(
          getChannel().newCall(getGetTopCupListMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/topGold/serverId/{serverId}/eventId/{eventId}/userId/{userId}"
     *&#92;&#92;        };
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.PbTopList> getTopGoldList(
        net.k2.grpc.PbEventRequest request) {
      return futureUnaryCall(
          getChannel().newCall(getGetTopGoldListMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/cupAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.PbAccumulatedRewardList> getCupAccumulatedRewardList(
        net.k2.grpc.PbEventRequest request) {
      return futureUnaryCall(
          getChannel().newCall(getGetCupAccumulatedRewardListMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/goldAccumulatedRewards/userId/{userId}/eventId/{eventId}"
     *&#92;&#92;        };
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.PbAccumulatedRewardList> getGoldAccumulatedRewardList(
        net.k2.grpc.PbEventRequest request) {
      return futureUnaryCall(
          getChannel().newCall(getGetGoldAccumulatedRewardListMethodHelper(), getCallOptions()), request);
    }

    /**
     * <pre>
     *&#92;&#92;        option (google.api.http) = {
     *&#92;&#92;            get: "/events/receiveReward/userId/{userId}/eventId/{eventId}/sessionId/{sessionId}/buttonPos/{buttonPos}"
     *&#92;&#92;        };
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<net.k2.grpc.PbMessage> receiveReward(
        net.k2.grpc.PbEventRequest request) {
      return futureUnaryCall(
          getChannel().newCall(getReceiveRewardMethodHelper(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SEND = 0;
  private static final int METHODID_SEND_CUP = 1;
  private static final int METHODID_SEND_LIST_CUP = 2;
  private static final int METHODID_SEND_LIST_GOLD_SPENDING = 3;
  private static final int METHODID_SEND_LIST_ITEM = 4;
  private static final int METHODID_SEND_LIST_HERO_WIN = 5;
  private static final int METHODID_GET_EVENT_LIST = 6;
  private static final int METHODID_GET_TOP_CUP_LIST = 7;
  private static final int METHODID_GET_TOP_GOLD_LIST = 8;
  private static final int METHODID_GET_CUP_ACCUMULATED_REWARD_LIST = 9;
  private static final int METHODID_GET_GOLD_ACCUMULATED_REWARD_LIST = 10;
  private static final int METHODID_RECEIVE_REWARD = 11;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final DtqImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(DtqImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SEND:
          serviceImpl.send((net.k2.grpc.CommonVector) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector>) responseObserver);
          break;
        case METHODID_SEND_CUP:
          serviceImpl.sendCup((net.k2.grpc.CommonVector) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector>) responseObserver);
          break;
        case METHODID_SEND_LIST_CUP:
          serviceImpl.sendListCup((net.k2.grpc.ListCommonVector) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector>) responseObserver);
          break;
        case METHODID_SEND_LIST_GOLD_SPENDING:
          serviceImpl.sendListGoldSpending((net.k2.grpc.ListCommonVector) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector>) responseObserver);
          break;
        case METHODID_SEND_LIST_ITEM:
          serviceImpl.sendListItem((net.k2.grpc.ListCommonVector) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector>) responseObserver);
          break;
        case METHODID_SEND_LIST_HERO_WIN:
          serviceImpl.sendListHeroWin((net.k2.grpc.ListCommonVector) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.CommonVector>) responseObserver);
          break;
        case METHODID_GET_EVENT_LIST:
          serviceImpl.getEventList((net.k2.grpc.PbEventRequest) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.PbEventList>) responseObserver);
          break;
        case METHODID_GET_TOP_CUP_LIST:
          serviceImpl.getTopCupList((net.k2.grpc.PbEventRequest) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.PbTopList>) responseObserver);
          break;
        case METHODID_GET_TOP_GOLD_LIST:
          serviceImpl.getTopGoldList((net.k2.grpc.PbEventRequest) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.PbTopList>) responseObserver);
          break;
        case METHODID_GET_CUP_ACCUMULATED_REWARD_LIST:
          serviceImpl.getCupAccumulatedRewardList((net.k2.grpc.PbEventRequest) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.PbAccumulatedRewardList>) responseObserver);
          break;
        case METHODID_GET_GOLD_ACCUMULATED_REWARD_LIST:
          serviceImpl.getGoldAccumulatedRewardList((net.k2.grpc.PbEventRequest) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.PbAccumulatedRewardList>) responseObserver);
          break;
        case METHODID_RECEIVE_REWARD:
          serviceImpl.receiveReward((net.k2.grpc.PbEventRequest) request,
              (io.grpc.stub.StreamObserver<net.k2.grpc.PbMessage>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class DtqBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    DtqBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return net.k2.grpc.DtqOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Dtq");
    }
  }

  private static final class DtqFileDescriptorSupplier
      extends DtqBaseDescriptorSupplier {
    DtqFileDescriptorSupplier() {}
  }

  private static final class DtqMethodDescriptorSupplier
      extends DtqBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    DtqMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (DtqGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new DtqFileDescriptorSupplier())
              .addMethod(getSendMethodHelper())
              .addMethod(getSendCupMethodHelper())
              .addMethod(getSendListCupMethodHelper())
              .addMethod(getSendListGoldSpendingMethodHelper())
              .addMethod(getSendListItemMethodHelper())
              .addMethod(getSendListHeroWinMethodHelper())
              .addMethod(getGetEventListMethodHelper())
              .addMethod(getGetTopCupListMethodHelper())
              .addMethod(getGetTopGoldListMethodHelper())
              .addMethod(getGetCupAccumulatedRewardListMethodHelper())
              .addMethod(getGetGoldAccumulatedRewardListMethodHelper())
              .addMethod(getReceiveRewardMethodHelper())
              .build();
        }
      }
    }
    return result;
  }
}
