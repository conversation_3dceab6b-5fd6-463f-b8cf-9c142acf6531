// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: dtq.proto

package net.k2.grpc;

/**
 * Protobuf type {@code proto.PbEventList}
 */
public  final class PbEventList extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:proto.PbEventList)
    PbEventListOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PbEventList.newBuilder() to construct.
  private PbEventList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PbEventList() {
    eventList_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PbEventList(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
              eventList_ = new java.util.ArrayList<net.k2.grpc.PbEvent>();
              mutable_bitField0_ |= 0x00000001;
            }
            eventList_.add(
                input.readMessage(net.k2.grpc.PbEvent.parser(), extensionRegistry));
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
        eventList_ = java.util.Collections.unmodifiableList(eventList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return net.k2.grpc.DtqOuterClass.internal_static_proto_PbEventList_descriptor;
  }

  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return net.k2.grpc.DtqOuterClass.internal_static_proto_PbEventList_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            net.k2.grpc.PbEventList.class, net.k2.grpc.PbEventList.Builder.class);
  }

  public static final int EVENTLIST_FIELD_NUMBER = 1;
  private java.util.List<net.k2.grpc.PbEvent> eventList_;
  /**
   * <code>repeated .proto.PbEvent eventList = 1;</code>
   */
  public java.util.List<net.k2.grpc.PbEvent> getEventListList() {
    return eventList_;
  }
  /**
   * <code>repeated .proto.PbEvent eventList = 1;</code>
   */
  public java.util.List<? extends net.k2.grpc.PbEventOrBuilder> 
      getEventListOrBuilderList() {
    return eventList_;
  }
  /**
   * <code>repeated .proto.PbEvent eventList = 1;</code>
   */
  public int getEventListCount() {
    return eventList_.size();
  }
  /**
   * <code>repeated .proto.PbEvent eventList = 1;</code>
   */
  public net.k2.grpc.PbEvent getEventList(int index) {
    return eventList_.get(index);
  }
  /**
   * <code>repeated .proto.PbEvent eventList = 1;</code>
   */
  public net.k2.grpc.PbEventOrBuilder getEventListOrBuilder(
      int index) {
    return eventList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < eventList_.size(); i++) {
      output.writeMessage(1, eventList_.get(i));
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < eventList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, eventList_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof net.k2.grpc.PbEventList)) {
      return super.equals(obj);
    }
    net.k2.grpc.PbEventList other = (net.k2.grpc.PbEventList) obj;

    boolean result = true;
    result = result && getEventListList()
        .equals(other.getEventListList());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getEventListCount() > 0) {
      hash = (37 * hash) + EVENTLIST_FIELD_NUMBER;
      hash = (53 * hash) + getEventListList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static net.k2.grpc.PbEventList parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static net.k2.grpc.PbEventList parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static net.k2.grpc.PbEventList parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static net.k2.grpc.PbEventList parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static net.k2.grpc.PbEventList parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static net.k2.grpc.PbEventList parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(net.k2.grpc.PbEventList prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code proto.PbEventList}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:proto.PbEventList)
      net.k2.grpc.PbEventListOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return net.k2.grpc.DtqOuterClass.internal_static_proto_PbEventList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return net.k2.grpc.DtqOuterClass.internal_static_proto_PbEventList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              net.k2.grpc.PbEventList.class, net.k2.grpc.PbEventList.Builder.class);
    }

    // Construct using net.k2.grpc.PbEventList.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getEventListFieldBuilder();
      }
    }
    public Builder clear() {
      super.clear();
      if (eventListBuilder_ == null) {
        eventList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        eventListBuilder_.clear();
      }
      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return net.k2.grpc.DtqOuterClass.internal_static_proto_PbEventList_descriptor;
    }

    public net.k2.grpc.PbEventList getDefaultInstanceForType() {
      return net.k2.grpc.PbEventList.getDefaultInstance();
    }

    public net.k2.grpc.PbEventList build() {
      net.k2.grpc.PbEventList result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public net.k2.grpc.PbEventList buildPartial() {
      net.k2.grpc.PbEventList result = new net.k2.grpc.PbEventList(this);
      int from_bitField0_ = bitField0_;
      if (eventListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          eventList_ = java.util.Collections.unmodifiableList(eventList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.eventList_ = eventList_;
      } else {
        result.eventList_ = eventListBuilder_.build();
      }
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof net.k2.grpc.PbEventList) {
        return mergeFrom((net.k2.grpc.PbEventList)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(net.k2.grpc.PbEventList other) {
      if (other == net.k2.grpc.PbEventList.getDefaultInstance()) return this;
      if (eventListBuilder_ == null) {
        if (!other.eventList_.isEmpty()) {
          if (eventList_.isEmpty()) {
            eventList_ = other.eventList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureEventListIsMutable();
            eventList_.addAll(other.eventList_);
          }
          onChanged();
        }
      } else {
        if (!other.eventList_.isEmpty()) {
          if (eventListBuilder_.isEmpty()) {
            eventListBuilder_.dispose();
            eventListBuilder_ = null;
            eventList_ = other.eventList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            eventListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getEventListFieldBuilder() : null;
          } else {
            eventListBuilder_.addAllMessages(other.eventList_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      net.k2.grpc.PbEventList parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (net.k2.grpc.PbEventList) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<net.k2.grpc.PbEvent> eventList_ =
      java.util.Collections.emptyList();
    private void ensureEventListIsMutable() {
      if (!((bitField0_ & 0x00000001) == 0x00000001)) {
        eventList_ = new java.util.ArrayList<net.k2.grpc.PbEvent>(eventList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        net.k2.grpc.PbEvent, net.k2.grpc.PbEvent.Builder, net.k2.grpc.PbEventOrBuilder> eventListBuilder_;

    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public java.util.List<net.k2.grpc.PbEvent> getEventListList() {
      if (eventListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(eventList_);
      } else {
        return eventListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public int getEventListCount() {
      if (eventListBuilder_ == null) {
        return eventList_.size();
      } else {
        return eventListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public net.k2.grpc.PbEvent getEventList(int index) {
      if (eventListBuilder_ == null) {
        return eventList_.get(index);
      } else {
        return eventListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder setEventList(
        int index, net.k2.grpc.PbEvent value) {
      if (eventListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventListIsMutable();
        eventList_.set(index, value);
        onChanged();
      } else {
        eventListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder setEventList(
        int index, net.k2.grpc.PbEvent.Builder builderForValue) {
      if (eventListBuilder_ == null) {
        ensureEventListIsMutable();
        eventList_.set(index, builderForValue.build());
        onChanged();
      } else {
        eventListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder addEventList(net.k2.grpc.PbEvent value) {
      if (eventListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventListIsMutable();
        eventList_.add(value);
        onChanged();
      } else {
        eventListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder addEventList(
        int index, net.k2.grpc.PbEvent value) {
      if (eventListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEventListIsMutable();
        eventList_.add(index, value);
        onChanged();
      } else {
        eventListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder addEventList(
        net.k2.grpc.PbEvent.Builder builderForValue) {
      if (eventListBuilder_ == null) {
        ensureEventListIsMutable();
        eventList_.add(builderForValue.build());
        onChanged();
      } else {
        eventListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder addEventList(
        int index, net.k2.grpc.PbEvent.Builder builderForValue) {
      if (eventListBuilder_ == null) {
        ensureEventListIsMutable();
        eventList_.add(index, builderForValue.build());
        onChanged();
      } else {
        eventListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder addAllEventList(
        java.lang.Iterable<? extends net.k2.grpc.PbEvent> values) {
      if (eventListBuilder_ == null) {
        ensureEventListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, eventList_);
        onChanged();
      } else {
        eventListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder clearEventList() {
      if (eventListBuilder_ == null) {
        eventList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        eventListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public Builder removeEventList(int index) {
      if (eventListBuilder_ == null) {
        ensureEventListIsMutable();
        eventList_.remove(index);
        onChanged();
      } else {
        eventListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public net.k2.grpc.PbEvent.Builder getEventListBuilder(
        int index) {
      return getEventListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public net.k2.grpc.PbEventOrBuilder getEventListOrBuilder(
        int index) {
      if (eventListBuilder_ == null) {
        return eventList_.get(index);  } else {
        return eventListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public java.util.List<? extends net.k2.grpc.PbEventOrBuilder> 
         getEventListOrBuilderList() {
      if (eventListBuilder_ != null) {
        return eventListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(eventList_);
      }
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public net.k2.grpc.PbEvent.Builder addEventListBuilder() {
      return getEventListFieldBuilder().addBuilder(
          net.k2.grpc.PbEvent.getDefaultInstance());
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public net.k2.grpc.PbEvent.Builder addEventListBuilder(
        int index) {
      return getEventListFieldBuilder().addBuilder(
          index, net.k2.grpc.PbEvent.getDefaultInstance());
    }
    /**
     * <code>repeated .proto.PbEvent eventList = 1;</code>
     */
    public java.util.List<net.k2.grpc.PbEvent.Builder> 
         getEventListBuilderList() {
      return getEventListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        net.k2.grpc.PbEvent, net.k2.grpc.PbEvent.Builder, net.k2.grpc.PbEventOrBuilder> 
        getEventListFieldBuilder() {
      if (eventListBuilder_ == null) {
        eventListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            net.k2.grpc.PbEvent, net.k2.grpc.PbEvent.Builder, net.k2.grpc.PbEventOrBuilder>(
                eventList_,
                ((bitField0_ & 0x00000001) == 0x00000001),
                getParentForChildren(),
                isClean());
        eventList_ = null;
      }
      return eventListBuilder_;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:proto.PbEventList)
  }

  // @@protoc_insertion_point(class_scope:proto.PbEventList)
  private static final net.k2.grpc.PbEventList DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new net.k2.grpc.PbEventList();
  }

  public static net.k2.grpc.PbEventList getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PbEventList>
      PARSER = new com.google.protobuf.AbstractParser<PbEventList>() {
    public PbEventList parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PbEventList(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PbEventList> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PbEventList> getParserForType() {
    return PARSER;
  }

  public net.k2.grpc.PbEventList getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

