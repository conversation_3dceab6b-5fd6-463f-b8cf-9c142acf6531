// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: dtq.proto

package net.k2.grpc;

public interface PbTopOrBuilder extends
    // @@protoc_insertion_point(interface_extends:proto.PbTop)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * like &lt;userId&gt;, ...
   * </pre>
   *
   * <code>int64 id = 1;</code>
   */
  long getId();

  /**
   * <pre>
   * screen name
   * </pre>
   *
   * <code>string name = 2;</code>
   */
  java.lang.String getName();
  /**
   * <pre>
   * screen name
   * </pre>
   *
   * <code>string name = 2;</code>
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * sum of cup
   * </pre>
   *
   * <code>int32 sum = 3;</code>
   */
  int getSum();

  /**
   * <pre>
   * /Pack/Resources/head_{1}
   * </pre>
   *
   * <code>string avatar = 4;</code>
   */
  java.lang.String getAvatar();
  /**
   * <pre>
   * /Pack/Resources/head_{1}
   * </pre>
   *
   * <code>string avatar = 4;</code>
   */
  com.google.protobuf.ByteString
      getAvatarBytes();
}
