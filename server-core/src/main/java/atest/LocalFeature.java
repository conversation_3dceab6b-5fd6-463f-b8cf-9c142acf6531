package atest;

import monster.dao.MaterialDAO;
import monster.dao.mapping.UserMaterialEntity;

public class LocalFeature extends AbstractTest {

    public static void main(String[] args) throws Exception {
        LocalFeature localFeature = new LocalFeature();
        localFeature.setupLocal();

        MaterialDAO dao = new MaterialDAO();
        UserMaterialEntity userMaterial = new UserMaterialEntity();
        userMaterial.setUserId(19);
        userMaterial.setMaterialId(181);
        userMaterial.setTypeId(1);
        userMaterial.setNumber(4);
        dao.update(userMaterial);





        //        Scheduler scheduler = StdSchedulerFactory.getDefaultScheduler();
//        scheduler.start();
//        scheduler.scheduleJob(QuartzUtil.getJob(FeatureTestTask.class, "featureTestTask"), QuartzUtil.getTriggerSecond("featureTestTask", 1));
    }

}
