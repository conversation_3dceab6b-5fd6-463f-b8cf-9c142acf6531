package atest;

import grep.database.DBJPA;
import grep.helper.Filer;
import monster.config.CfgOblivion;
import monster.dao.MaterialDAO;
import monster.dao.UserDAO;
import monster.dao.mapping.UserMaterialEntity;
import monster.dao.mapping.main.ResOblivionTowerEntity;
import monster.util.DBHelper;
import tmp.logs.LogObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FileProcess extends AbstractTest {

    MaterialDAO materialDAO = new MaterialDAO();

    public static void main(String[] args) {
        new FileProcess().buVatPham();
    }

    void buVatPhamLogClickHouse() {
        Map<Integer, UserLogInfo> map = new HashMap<>();
        var values = Filer.realAllFile("d:/item106.txt");
        for (String value : values) {
            LogObject logObject = LogObject.parseFromLog(value);
            int matValue = logObject.getObj().get("value").getAsInt();
            int matAddValue = logObject.getObj().get("addValue").getAsInt();
            if (!map.containsKey(logObject.getUserId())) {
                map.put(logObject.getUserId(), new UserLogInfo(logObject.getUserId(), matValue));
            } else {
                UserLogInfo userLogInfo = map.get(logObject.getUserId());
                if (userLogInfo.value + matAddValue != matValue) { // err
                    System.out.println(DBHelper.sqlMail(userLogInfo.userId, "Bù Vé Triệu Hồi Đặc Biệt V4", "[3,1,106,%s]".formatted(userLogInfo.value)) + ";");
                }
                userLogInfo.value = matValue;
            }
            //            String[] data = value.split("\t");
            //            int userId = Integer.parseInt(data[0]);
            //            int number = GsonUtil.strToListInt(data[1]).get(1) / 100000;
            //            System.out.println(DBHelper.sqlMail(userId, "Bù Đá thời trang V2", "[3,1,103,%s]".formatted(number)) + ";");
        }
    }

    void buVatPham() {
        long size = 0;
        var values = Filer.realAllFile("d:/bubu.tsv");
        for (String value : values) {
            String[] data = value.split("\t");
            String[] userIds = data[0].split(",");
            String bonus = data[1];
            size += userIds.length;
            //            System.out.println(bonus + " " + GsonUtil.toJson(userIds));
            //            int number = GsonUtil.strToListInt(data[1]).get(1) / 100000;
            //            System.out.println();
            for (String userId : userIds) {
                Filer.append("d:/sql.txt", DBHelper.sqlMail(Integer.parseInt(userId), "Bù sự kiện Trả giấy khen đặc biệt", "Từ nay học viện Kimesu sẽ chỉ dùng giấy khen đặc biệt nên hoàn trả cho bạn số giấy khen đã đổi.", bonus).replace("user_mail", "dson.user_mail") + ";");
            }
        }
        //        System.out.println("size = " + size);
    }

    private void test1() {
        setupLocal();
        UserDAO userDAO = new UserDAO();
        var values = Filer.realAllFile("d:/Result_15.tsv");
        for (String value : values) {
            String[] data = value.split("\t");
            int userId = Integer.parseInt(data[0]);
            int newLevel = Integer.parseInt(data[4]);
            int oldLevel = Integer.parseInt(data[5]);
            long gold = 0, promotion = 0;
            String item = "";
            String shard = "";
            String itemUse = "";
            for (int i = oldLevel; i < newLevel; i++) {
                ResOblivionTowerEntity tower = CfgOblivion.getOblivion(i);
                gold += tower.getGold() != null ? tower.getGold() : 0;
                promotion += tower.getPromostone() != null ? tower.getPromostone() : 0;
                if (tower.getItem() != null) {
                    item += "," + tower.getItem();
                }
                if (tower.getShard() != null && tower.getShard() > 0 && tower.getShardNumber() != null && tower.getShardNumber() > 0) {
                    shard += "," + tower.getShard() + ":" + tower.getShardNumber();
                }
                if (tower.getItemUseNumber() != null && tower.getItemUseNumber() > 0 && tower.getItemUse() != null && tower.getItemUse() > 0) {
                    itemUse += "," + tower.getItemUse() + ":" + tower.getItemUseNumber();
                }
            }
            if (itemUse.length() > 0) {
                String newValue = "";
                String[] items = itemUse.substring(1).split(",");
                for (String itemId : items) {
                    String[] tmp = itemId.split(":");
                    int materialId = Integer.parseInt(tmp[0]), number = Integer.parseInt(tmp[1]);
                    UserMaterialEntity userMaterial = DBJPA.getUnique("user_material", UserMaterialEntity.class, "user_id", userId, "type_id", 1, "material_id", materialId);
                    if (userMaterial.getNumber() > number) {
                        DBJPA.update("user_material", List.of("number", userMaterial.getNumber() - number), List.of("user_id", userId, "type_id", 1, "material_id", materialId));
                    } else {
                        DBJPA.update("user_material", List.of("number", 0), List.of("user_id", userId, "type_id", 1, "material_id", materialId));
                        newValue += "," + materialId + ":" + (number - userMaterial.getNumber());
                    }
                }
                System.out.println(newValue.length() > 0 ? newValue.substring(1) : newValue);
            } else {
                System.out.println("");
            }
            //            UserMaterialEntity userMaterial = DBJPA.getUnique("user_material", UserMaterialEntity.class, "user_id", userId, "type_id", 1, "material_id", MaterialType.PROMOTION_STONE.id);
            //            if (userMaterial.getNumber() > promotion) {
            //                DBJPA.update("user_material", List.of("number", userMaterial.getNumber() - promotion), List.of("user_id", userId, "type_id", 1, "material_id", MaterialType.PROMOTION_STONE.id));
            //                System.out.println(0);
            //            } else {
            //                DBJPA.update("user_material", List.of("number", 0), List.of("user_id", userId, "type_id", 1, "material_id", MaterialType.PROMOTION_STONE.id));
            //                System.out.println(promotion - userMaterial.getNumber());
            //            }
        }
    }

    public class UserLogInfo {
        int userId;
        int value;

        public UserLogInfo(int userId, int value) {
            this.userId = userId;
            this.value = value;
        }
    }

}
