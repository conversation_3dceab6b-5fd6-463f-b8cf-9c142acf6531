<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>grep.slayer</groupId>
        <artifactId>main</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>grep.slayer</groupId>
    <artifactId>server-core</artifactId>
    <version>1.1-SNAPSHOT</version>
    <name>server-core</name>
    <url>http://maven.apache.org</url>

    <properties>
        <java.version>16</java.version>
        <c3p0.version>5.4.2.Final</c3p0.version>
        <!-- before 4.3.5.Final -->
        <hibernate.version>5.4.14.Final</hibernate.version>
        <!-- before 4.3.5.Final -->
        <jedis.version>4.4.0</jedis.version>
        <!-- before 2.5.1  -->
        <undertow.version>2.1.0.Final</undertow.version>
        <mongo.version>4.0.6</mongo.version>
        <memcached.version>2.12.3</memcached.version>
        <!--        <kotlin.version>1.9.0</kotlin.version>-->
        <aspectj.version>1.9.20</aspectj.version>
        <apache.httpclient.version>4.5.13</apache.httpclient.version>
        <grpc.version>1.50.2</grpc.version>
        <protobuf.version>3.21.7</protobuf.version>
        <gson.version>2.10.1</gson.version>
    </properties>

    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.6.1</version>
            </extension>
        </extensions>
        <plugins>
            <!--            <plugin>-->
            <!--                <groupId>org.xolstice.maven.plugins</groupId>-->
            <!--                <artifactId>protobuf-maven-plugin</artifactId>-->
            <!--                <version>0.6.1</version>-->
            <!--                <configuration>-->
            <!--                    <protocArtifact>-->
            <!--                        com.google.protobuf:protoc:3.3.0:exe:${os.detected.classifier}-->
            <!--                    </protocArtifact>-->
            <!--                    <pluginId>grpc-java</pluginId>-->
            <!--                    <pluginArtifact>-->
            <!--                        io.grpc:protoc-gen-grpc-java:1.4.0:exe:${os.detected.classifier}-->
            <!--                    </pluginArtifact>-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>compile</goal>-->
            <!--                            <goal>compile-custom</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <!--            <plugin>-->
            <!--                <groupId>org.jetbrains.kotlin</groupId>-->
            <!--                <artifactId>kotlin-maven-plugin</artifactId>-->
            <!--                <version>${kotlin.version}</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>compile</id>-->
            <!--                        <phase>compile</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>compile</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                    <execution>-->
            <!--                        <id>test-compile</id>-->
            <!--                        <phase>test-compile</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>test-compile</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <configuration>-->
            <!--                    <jvmTarget>16</jvmTarget>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>unwovenClassesFolder</id>
                        <phase>generate-resources</phase>
                        <configuration>
                            <target>
                                <delete dir="${project.build.directory}/unwoven-classes"/>
                                <mkdir dir="${project.build.directory}/unwoven-classes"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <executions>
                    <execution>
                        <!-- Modifying output directory of default compile because non-weaved classes must be stored
                                         in separate folder to not confuse ajc by reweaving already woven classes (which leads to
                                         to ajc error message like "bad weaverState.Kind: -115") -->
                        <id>default-compile</id>
                        <configuration>
                            <source>16</source>
                            <target>16</target>
                            <outputDirectory>${project.build.directory}/unwoven-classes</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>1.14.0</version>
                <dependencies>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjtools</artifactId>
                        <version>${aspectj.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <showWeaveInfo>true</showWeaveInfo>
                    <verbose>true</verbose>
                    <Xlint>ignore</Xlint>
                    <encoding>UTF-8</encoding>
                    <weaveDirectories>
                        <weaveDirectory>${project.build.directory}/unwoven-classes</weaveDirectory>
                    </weaveDirectories>
                    <!-- IMPORTANT-->
                    <excludes>
                        <exclude>**/*.java</exclude>
                    </excludes>
                    <forceAjcCompile>true</forceAjcCompile>
                    <sources/>
                    <!-- IMPORTANT-->
                    <complianceLevel>16</complianceLevel>
                    <source>16</source>
                    <target>16</target>
                </configuration>
                <executions>
                    <execution>
                        <!-- Compile and weave aspects after all classes compiled by javac -->
                        <phase>process-classes</phase>
                        <configuration>
                            <complianceLevel>16</complianceLevel>
                        </configuration>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>io.github.classgraph</groupId>
            <artifactId>classgraph</artifactId>
            <version>4.8.157</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>${protobuf.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <!-- Undertow -->
        <dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-core</artifactId>
            <version>${undertow.version}</version>
        </dependency>
        <dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-servlet</artifactId>
            <version>${undertow.version}</version>
        </dependency>
        <!-- Mongo -->
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
            <version>${mongo.version}</version>
        </dependency>
        <!-- hiraki -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-hikaricp</artifactId>
            <version>5.4.22.Final</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-c3p0</artifactId>
            <version>${c3p0.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>${hibernate.version}</version>
        </dependency>
        <!-- CORE -->
        <!--        <dependency>-->
        <!--            <groupId>io.netty</groupId>-->
        <!--            <artifactId>netty-all</artifactId>-->
        <!--            <version>4.1.41.Final</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
            <version>3.9.4.Final</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.18</version>
        </dependency>
        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>spymemcached</artifactId>
            <version>${memcached.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.kie.modules</groupId>-->
        <!--            <artifactId>org-apache-commons-httpclient</artifactId>-->
        <!--            <version>6.1.0.CR1</version>-->
        <!--            <type>pom</type>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zeroturnaround</groupId>
            <artifactId>jrebel-maven-plugin</artifactId>
            <version>1.1.10</version>
            <type>maven-plugin</type>
        </dependency>
        <dependency>
            <groupId>commons-validator</groupId>
            <artifactId>commons-validator</artifactId>
            <version>1.4.1</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>
        <!-- LOG -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.30</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>2.13.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.14.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.14.1</version>
        </dependency>
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.2</version>
        </dependency>
        <!-- MISC -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.28</version>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.6</version>
        </dependency>
        <!-- TEST -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.9.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>5.9.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>27.1-jre</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.jetbrains.kotlin</groupId>-->
        <!--            <artifactId>kotlin-stdlib-jdk8</artifactId>-->
        <!--            <version>${kotlin.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.jetbrains.kotlin</groupId>-->
        <!--            <artifactId>kotlin-test</artifactId>-->
        <!--            <version>${kotlin.version}</version>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${apache.httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <version>3.5.3</version>
        </dependency>
        <!-- Quartz Core -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.httpcomponents</groupId>-->
        <!--            <artifactId>httpmime</artifactId>-->
        <!--            <version>4.3.1</version>-->
        <!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.21.7</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>github</id>
            <name>Grepgame Slayer</name>
            <url>https://maven.pkg.github.com/grepgame/dragonslayeridle</url>
        </repository>
    </distributionManagement>
</project>
