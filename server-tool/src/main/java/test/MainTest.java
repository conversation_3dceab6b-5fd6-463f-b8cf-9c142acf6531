//package test;
//
//import grep.database.DBJPA;
//import monster.dao.mapping.main.ConfigLanguage;
//
//import jakarta.persistence.EntityManager;
//import java.util.List;
//
//public class MainTest {
//
//    public static void main(String[] args) {
//        System.out.println(String.format("seal_land", 1, 2));
//        DBJPA.init("localtest");
//        EntityManager em = DBJPA.getEntityManager();
//        //        var list = em.createQuery("select c from ConfigLanguage c").getResultList();
//        //        System.out.println(list.size());
//        System.out.println("--");
//        List<ConfigLanguage> languages = em.createNativeQuery("select * from dson_main.config_help", ConfigLanguage.class).getResultList();
//        System.out.println(languages.size());
//        System.out.println("bbbbbbbb");
//        var b = DBJPA.getSelectQuery("select * from dson_main.config_help", ConfigLanguage.class);
//        System.out.println(b.size());
//    }
//
//}
