package grep.bug;

import atest.AbstractTest;
import grep.bug.entity.LogObject;
import grep.helper.Filer;
import grep.helper.StringHelper;
import monster.config.CfgOblivion;
import monster.config.CfgTower;
import monster.dao.MaterialDAO;
import monster.dao.mapping.main.ResOblivionTowerEntity;

public class FileProcess extends AbstractTest {

    MaterialDAO materialDAO = new MaterialDAO();

    public static void main(String[] args) {
        new FileProcess().test1();
    }

    private void test() {
        //        setupLocal();
        //        DBJPA.getUnique("user", UserEntity.class, "id", 398619);

        var values = Filer.realAllFile("d:/badge.tsv");
        for (String value : values) {
            LogObject obj = LogObject.parseFromTsv(value);
            if (obj.getDetail().equals("upgrade_badge")) {
                String item = obj.getObj().get("key").getAsString();
                System.out.println(item.split("_")[1]);
                Filer.append("d:/bu.txt", StringHelper.toDBString(obj));
                Filer.append("d:/sql.txt", String.format("update dson.user_material set number=if(number<1, 0, number-1) where user_id=%s and material_type=1 and material_id=%s;", obj.getUserId(), item.split("_")[1]));
            }
        }
    }

    private void test1() {
        setupLocal();
        System.out.println(CfgOblivion.getOblivion(1));
//        var values = Filer.realAllFile("d:/Result_15.tsv");
//        for (String value : values) {
//            String[] data = value.split("\t");
//            int userId = Integer.parseInt(data[0]);
//            int newLevel = Integer.parseInt(data[4]);
//            int oldLevel = Integer.parseInt(data[5]);
//            System.out.println(userId);
//            System.out.println("newLevel = " + newLevel);
//            System.out.println("oldLevel = " + oldLevel);
//        }
    }

}
