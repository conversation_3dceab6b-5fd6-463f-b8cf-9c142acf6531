package grep.bug.entity;

import com.google.gson.JsonObject;
import grep.helper.GsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class LogObject {

    Date date;
    int userId, serverId;
    String action, detail;
    JsonObject obj;

    public LogObject(int userId, String strObj) {
        this.userId = userId;
        this.obj = GsonUtil.parseJsonObject(strObj.replace("\"{", "{").replace("}\"", "}"));
    }

    // 2021-05-10 07:14:01	23u615740 receive mail {"type":"material","key":"1_6","value":"15203","addValue":"7000"}
    public LogObject(String data) {
        List<String> tmp = Arrays.asList(data.split(" "));
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(tmp.get(0) + " " + tmp.get(1));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        String strUserId = tmp.get(2);
        serverId = Integer.parseInt(strUserId.substring(strUserId.indexOf("\t") + 1, strUserId.indexOf("u")));
        userId = Integer.parseInt(strUserId.substring(strUserId.indexOf("u") + 1));
        action = tmp.get(3);
        detail = tmp.get(4);
        String lastValue = "";
        for (int i = 5; i < tmp.size(); i++) {
            lastValue += tmp.get(i);
        }
        obj = GsonUtil.parseJsonObject(lastValue);
    }

    public LogObject(String data, boolean abc) {
        String[] tmp = data.split("\t");
        this.userId = Integer.parseInt(tmp[1]);
        this.obj = GsonUtil.parseJsonObject(tmp[2]);
    }

    public static LogObject parseFromTsv(String value) {
        LogObject logObject = new LogObject();
        String[] data = value.split("\t");
        logObject.userId = Integer.parseInt(data[2]);
        logObject.action = data[3];
        logObject.detail = data[4];
        data[5] = data[5].substring(1, data[5].length() - 1);
        logObject.obj = GsonUtil.parseJsonObject(data[5].replace("\"\"", "\""));
        return logObject;
    }

    public static void main(String[] args) {
        String data = "2024-03-22 06:56:56\t10\t328265\treceive\tupgrade_badge\t\"{\"\"type\"\":\"\"material\"\",\"\"key\"\":\"\"1_7059\"\",\"\"value\"\":\"\"0\"\",\"\"addValue\"\":\"\"-2\"\"}\"\tlayer-game8";
        parseFromTsv(data);
    }

}
