package grep.translate;

import com.google.gson.Gson;
import grep.database.DBJPA;
import grep.helper.GsonUtil;
import grep.helper.StringHelper;
import grep.log.Config;
import monster.config.CfgServer;
import monster.dao.mapping.main.ConfigHelp;
import monster.dao.mapping.main.ConfigLanguage;
import monster.dao.mapping.main.ConfigResLanguage;
import monster.server.AppConfig;
import org.json.simple.parser.ParseException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MashiTranslateHandler {
    public static String translate(String input, GoogleTranslator.LANGUAGE srcLang, GoogleTranslator.LANGUAGE destLang) throws IOException, ParseException {
        if (input.equalsIgnoreCase(" ")) return " ";
        GoogleTranslator translator = new GoogleTranslator();
        translator.setSrcLang(srcLang);
        translator.setDestLang(destLang);

        String result = translator.translate(input);
        return result;
    }

    public static void main(String[] args) throws Exception {
        //        var values = Filer.realAllFile("d:/viet.txt");
        //        for (String value : values) {
        //            Filer.append("d:/eng.txt", translate(value, GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH));
        //        }

        initLocalSeaTw();
        //        translateToEnglish();
        translateFromEnglish();

        //        translateFromEnglish2();
        // translateConfigResLanguage();
        //        translateTables();
        //        translateResSkillView();
    }

    private static void translateFromEnglish2() {
        List<String> tables = List.of("config_res_language");
        for (String table : tables) {
            System.out.println("table = " + table);
            int count = 0;
            List<ConfigLanguage> languages = DBJPA.getSelectQuery("select * from dson_main.%s".formatted(table), ConfigLanguage.class);
            for (ConfigLanguage language : languages) {
                if (language.getK().contains("client")) continue;
                if (!StringHelper.isEmpty(language.getId())) {
                    count++;
                    continue;
                }
                if (++count % 10 == 0) System.out.println("count = " + count + " " + (languages.size() - count));
                try {
                    if (!StringHelper.isEmpty(language.getEn())) {
                        //                        String th = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH);
                        //                        String zh = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH);
                        //                        String fr = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR);
                        //                        String es = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP);
                        //                        String pt = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT);
                        //                        String de = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE);
                        //                        String ru = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU);
                        String id = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ID);
                        DBJPA.update(CfgServer.DB_MAIN + table, Arrays.asList("en", language.getEn(), "id", id), Arrays.asList("k", language.getK()));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("err language.getK() = " + language.getK());
                }
            }
        }
    }

    private static void translateFromEnglish() {
        // "config_language","res_title",
        List<String> tables = List.of("config_res_language");
        for (String table : tables) {
            System.out.println("table = " + table);
            int count = 0;
            List<ConfigLanguage> languages = DBJPA.getSelectQuery("select * from dson_main." + table + " where k like 'clan_mi%'", ConfigLanguage.class);
            for (ConfigLanguage language : languages) {
                if (++count % 10 == 0) System.out.println("count = " + count + " " + (languages.size() - count));
                try {
                    if (!StringHelper.isEmpty(language.getEn())) {
                        String th = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH);
                        String zh = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH);
                        String fr = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR);
                        String es = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP);
                        String pt = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT);
                        String de = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE);
                        String ru = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU);
                        String zhtw = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.zhtw);
                        String ko = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.KO);
                        String id = translate(language.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ID);
                        DBJPA.update(CfgServer.DB_MAIN + table, Arrays.asList("en", language.getEn(), "th", th, "zh", zh, "fr", fr, "es", es, "pt", pt, "de", de, "ru", ru,
                                "zhtw", zhtw, "ko", ko, "id", id), Arrays.asList("k", language.getK()));
                        //                        DBJPA.update(CfgServer.DB_MAIN + table, Arrays.asList("en", language.getEn(), "zh", zh), Arrays.asList("k", language.getK()));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("err language.getK() = " + language.getK());
                }
            }
        }
    }

    private static void translateToEnglish() {
        // "config_language","res_title",
        List<String> tables = List.of("config_res_language");
        for (String table : tables) {
            System.out.println("table = " + table);
            int count = 0;
            List<ConfigLanguage> languages = DBJPA.getSelectQuery("select * from dson_main." + table + " where k like 'clan_mi%'", ConfigLanguage.class);
            System.out.println("languages = " + languages.size());
            for (ConfigLanguage language : languages) {
                if (++count % 10 == 0) System.out.println("count = " + count + " " + (languages.size() - count));
                try {
                    if (!StringHelper.isEmpty(language.getVi())) {
                        String en = translate(language.getVi(), GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH);
                        DBJPA.update(CfgServer.DB_MAIN + table, Arrays.asList("en", en), Arrays.asList("k", language.getK()));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    System.out.println("err language.getK() = " + language.getK());
                }
            }
        }
    }

    private static void init() throws Exception {
        AppConfig.load("config.json");
        Config.load("config.xml");
        CfgServer.serverId = Config.getInt("config.server.id");
        CfgServer.runningPort = Config.getInt("config.server.port");
        CfgServer.serverType = Config.getString("config.server.type");

        DBJPA.init("grepjob1");

        AppConfig.setDbConfig();
    }

    private static void initLocal() throws Exception {
        AppConfig.load("config.json");
        Config.load("config.xml");
        CfgServer.serverId = Config.getInt("config.server.id");
        CfgServer.runningPort = Config.getInt("config.server.port");
        CfgServer.serverType = Config.getString("config.server.type");

        DBJPA.init("localtest");

        AppConfig.setDbConfig();
    }

    private static void initLocalSea() throws Exception {
        AppConfig.load("config.json");
        Config.load("config.xml");
        CfgServer.serverId = Config.getInt("config.server.id");
        CfgServer.runningPort = Config.getInt("config.server.port");
        CfgServer.serverType = Config.getString("config.server.type");

        DBJPA.init("localtestsea");

        AppConfig.setDbConfig();
    }

    private static void initLocalSeaTw() throws Exception {
        AppConfig.load("config.json");
        Config.load("config.xml");
        CfgServer.serverId = Config.getInt("config.server.id");
        CfgServer.runningPort = Config.getInt("config.server.port");
        CfgServer.serverType = Config.getString("config.server.type");

        DBJPA.init("localtesttw");

        AppConfig.setDbConfig();
    }


    public static void test() {
        List<ConfigLanguage> tmp = DBJPA.getList(CfgServer.DB_MAIN + "config_language", ConfigLanguage.class);
        System.out.println("tmp.size() = " + tmp.size());
    }

    public static void translateTables() throws Exception {

        try {
            System.out.println("\"start\" = " + "start");

            List<ConfigLanguage> tmp = DBJPA.getList(CfgServer.DB_MAIN + "config_language", ConfigLanguage.class);


            for (int i = 0; i < tmp.size(); i++) {
                ConfigLanguage cLang = tmp.get(i);
                if (cLang.getVi() != null && !cLang.getVi().isEmpty()) {
                    boolean hasUpdate = false;
                    String endEn = "";
                    if (cLang.getEn() == null || cLang.getEn().equalsIgnoreCase("NA") || cLang.getEn().equalsIgnoreCase("en") || cLang.getEn().isEmpty()) {
                        String en = translate(cLang.getVi(), GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH);
                        cLang.setEn(en);
                        endEn = "*";
                        hasUpdate = true;
                    }

                    if (cLang.getTh() == null || cLang.getTh().equalsIgnoreCase("NA") || cLang.getTh().isEmpty()) {
                        String th = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH) + "*";
                        cLang.setTh(th);
                        hasUpdate = true;
                    }

                    if (cLang.getZh() == null || cLang.getZh().equalsIgnoreCase("NA") || cLang.getZh().isEmpty()) {
                        String zh = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH) + "*";
                        cLang.setZh(zh);
                        hasUpdate = true;
                    }

                    if (cLang.getFr() == null || cLang.getFr().equalsIgnoreCase("NA") || cLang.getFr().isEmpty()) {
                        String fr = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR) + "*";
                        cLang.setFr(fr);
                        hasUpdate = true;
                    }

                    if (cLang.getEs() == null || cLang.getEs().equalsIgnoreCase("NA") || cLang.getEs().isEmpty()) {
                        String sp = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP) + "*";
                        cLang.setEs(sp);
                        hasUpdate = true;
                    }

                    if (cLang.getPt() == null || cLang.getPt().equalsIgnoreCase("NA") || cLang.getPt().isEmpty()) {
                        String pt = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT) + "*";
                        cLang.setPt(pt);
                        hasUpdate = true;
                    }

                    if (cLang.getDe() == null || cLang.getDe().equalsIgnoreCase("NA") || cLang.getDe().isEmpty()) {
                        String de = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE) + "*";
                        cLang.setDe(de);
                        hasUpdate = true;
                    }

                    if (cLang.getRu() == null || cLang.getRu().equalsIgnoreCase("NA") || cLang.getRu().isEmpty()) {
                        String ru = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU) + "*";
                        cLang.setRu(ru);
                        hasUpdate = true;
                    }
                    if (hasUpdate) {
                        cLang.setEn(cLang.getEn() + endEn);
                        DBJPA.update(CfgServer.DB_MAIN + "config_language", Arrays.asList("en", cLang.getEn(), "th", cLang.getTh(), "zh", cLang.getZh(), "fr", cLang.getFr(), "es", cLang.getEs(), "pt", cLang.getPt(), "de", cLang.getDe(), "ru", cLang.getRu()), Arrays.asList("k", cLang.getK()));
                    }
                }
            }

            System.out.println("\"done config_language\" = " + "done config_language");
            //
            //            List<ResTitleEntity> listTitle = DBJPA.getList(CfgServer.DB_MAIN + "res_title", ResTitleEntity.class);
            //            for (int i = 0; i < listTitle.size(); i++) {
            //
            //                ResTitleEntity cLang = listTitle.get(i);
            //                if (cLang.getVi() != null && !cLang.getVi().isEmpty()) {
            //                    boolean hasUpdate = false;
            //                    String endEn = "";
            //                    if (cLang.getEn() == null || cLang.getEn().equalsIgnoreCase("NA") || cLang.getEn().equalsIgnoreCase("en") || cLang.getEn().isEmpty()) {
            //                        String en = translate(cLang.getVi(), GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH);
            //                        cLang.setEn(en);
            //                        endEn = "*";
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getTh() == null || cLang.getTh().equalsIgnoreCase("NA") || cLang.getTh().isEmpty()) {
            //                        String th = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH) + "*";
            //                        cLang.setTh(th);
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getZh() == null || cLang.getZh().equalsIgnoreCase("NA") || cLang.getZh().isEmpty()) {
            //                        String zh = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH) + "*";
            //                        cLang.setZh(zh);
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getFr() == null || cLang.getFr().equalsIgnoreCase("NA") || cLang.getFr().isEmpty()) {
            //                        String fr = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR) + "*";
            //                        cLang.setFr(fr);
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getEs() == null || cLang.getEs().equalsIgnoreCase("NA") || cLang.getEs().isEmpty()) {
            //                        String sp = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP) + "*";
            //                        cLang.setEs(sp);
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getPt() == null || cLang.getPt().equalsIgnoreCase("NA") || cLang.getPt().isEmpty()) {
            //                        String pt = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT) + "*";
            //                        cLang.setPt(pt);
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getDe() == null || cLang.getDe().equalsIgnoreCase("NA") || cLang.getDe().isEmpty()) {
            //                        String de = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE) + "*";
            //                        cLang.setDe(de);
            //                        hasUpdate = true;
            //                    }
            //
            //                    if (cLang.getRu() == null || cLang.getRu().equalsIgnoreCase("NA") || cLang.getRu().isEmpty()) {
            //                        String ru = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU) + "*";
            //                        cLang.setRu(ru);
            //                        hasUpdate = true;
            //                    }
            //                    if (hasUpdate) {
            //                        cLang.setEn(cLang.getEn() + endEn);
            //
            //                        DBJPA.update(CfgServer.DB_MAIN + "res_title", Arrays.asList("en", cLang.getEn(), "th", cLang.getTh(), "zh", cLang.getZh(), "fr", cLang.getFr(), "es", cLang.getEs(), "pt", cLang.getPt(), "de", cLang.getDe(), "ru", cLang.getRu()), Arrays.asList("k", cLang.getK()));
            //                    }
            //                }
            //            }

            System.out.println("\"done_res_title\" = " + "done_res_title");

            List<ConfigHelp> resHelp = DBJPA.getList(CfgServer.DB_MAIN + "config_help", ConfigHelp.class);
            for (int i = 0; i < resHelp.size(); i++) {

                ConfigHelp cLang = resHelp.get(i);

                if (cLang.getVi() != null) {
                    if (cLang.getK().equalsIgnoreCase("config_pack")) {
                        cLang.setEn(cLang.getVi());
                        cLang.setTh(cLang.getVi());
                        cLang.setZh(cLang.getVi());
                        cLang.setFr(cLang.getVi());
                        cLang.setEs(cLang.getVi());
                        cLang.setPt(cLang.getVi());
                        cLang.setDe(cLang.getVi());
                        cLang.setRu(cLang.getVi());

                    }
                }

                if (cLang.getVi() != null && !cLang.getVi().isEmpty()) {
                    boolean hasUpdate = false;
                    String endEn = "";
                    if (cLang.getEn() == null || cLang.getEn().equalsIgnoreCase("NA") || cLang.getEn().equalsIgnoreCase("en") || cLang.getEn().isEmpty()) {
                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);

                            String en = translate(str1, GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH) + translate(str2, GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH);
                            cLang.setEn(en);
                        } else {
                            String en = translate(cLang.getVi(), GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH);
                            cLang.setEn(en);
                        }
                        endEn = "*";
                        hasUpdate = true;
                    }

                    if (cLang.getTh() == null || cLang.getTh().equalsIgnoreCase("NA") || cLang.getTh().isEmpty()) {

                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String th = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH) + "*";
                            cLang.setTh(th);
                        } else {
                            String th = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH) + "*";
                            cLang.setTh(th);
                        }
                        hasUpdate = true;
                    }

                    if (cLang.getZh() == null || cLang.getZh().equalsIgnoreCase("NA") || cLang.getZh().isEmpty()) {
                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String zh = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH) + "*";
                            cLang.setZh(zh);
                        } else {
                            String zh = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH) + "*";
                            cLang.setZh(zh);
                        }
                        hasUpdate = true;
                    }

                    if (cLang.getFr() == null || cLang.getFr().equalsIgnoreCase("NA") || cLang.getFr().isEmpty()) {
                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String fr = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR) + "*";
                            cLang.setFr(fr);
                        } else {
                            String fr = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR) + "*";
                            cLang.setFr(fr);
                        }
                        hasUpdate = true;
                    }

                    if (cLang.getEs() == null || cLang.getEs().equalsIgnoreCase("NA") || cLang.getEs().isEmpty()) {

                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String sp = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP) + "*";
                            cLang.setEs(sp);
                        } else {
                            String sp = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP) + "*";
                            cLang.setEs(sp);
                        }
                        hasUpdate = true;
                    }

                    if (cLang.getPt() == null || cLang.getPt().equalsIgnoreCase("NA") || cLang.getPt().isEmpty()) {
                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String pt = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT) + "*";
                            cLang.setPt(pt);
                        } else {
                            String pt = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT) + "*";
                            cLang.setPt(pt);
                        }
                        hasUpdate = true;
                    }

                    if (cLang.getDe() == null || cLang.getDe().equalsIgnoreCase("NA") || cLang.getDe().isEmpty()) {
                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String de = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE) + "*";
                            cLang.setDe(de);
                        } else {
                            String de = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE) + "*";
                            cLang.setDe(de);
                        }
                        hasUpdate = true;
                    }

                    if (cLang.getRu() == null || cLang.getRu().equalsIgnoreCase("NA") || cLang.getRu().isEmpty()) {
                        if (cLang.getVi().length() > 3500) {
                            int last = 0;
                            for (int j = 2800; j < 3500; j++) {
                                if (cLang.getVi().charAt(j) == '.' || cLang.getVi().charAt(j) == ':' || cLang.getVi().charAt(j) == ',') {
                                    last = j;
                                    break;
                                }
                            }
                            String str1 = cLang.getVi().substring(0, last);
                            String str2 = cLang.getVi().substring(last + 1, cLang.getVi().length() - last);
                            String ru = translate(str1, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU) + translate(str2, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU) + "*";
                            cLang.setRu(ru);
                        } else {
                            String ru = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU) + "*";
                            cLang.setRu(ru);
                        }
                        hasUpdate = true;
                    }
                    if (hasUpdate) {
                        cLang.setEn(cLang.getEn() + endEn);


                        DBJPA.update(CfgServer.DB_MAIN + "config_help", Arrays.asList("en", cLang.getEn(), "th", cLang.getTh(), "zh", cLang.getZh(), "fr", cLang.getFr(), "es", cLang.getEs(), "pt", cLang.getPt(), "de", cLang.getDe(), "ru", cLang.getRu()), Arrays.asList("k", cLang.getK()));
                    }
                }
            }

            System.out.println("\"done_config_help\" = " + "done_config_help");

            System.out.println("\"done all\" = " + "done all");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void translateConfigResLanguage() throws Exception {
        String table = CfgServer.DB_MAIN + "config_res_language";
        List<ConfigResLanguage> resLanguages = DBJPA.getQueryList("select * from " + table + " where k like '%res_skill_%'", ConfigResLanguage.class);
        for (int i = 0; i < resLanguages.size(); i++) {
            ConfigResLanguage cLang = resLanguages.get(i);
            if (cLang.getVi() != null && !cLang.getVi().isEmpty()) {
                boolean hasUpdate = false;
                String endEn = "";
                if (cLang.getEn() == null || cLang.getEn().equalsIgnoreCase("NA") || cLang.getEn().equalsIgnoreCase("en") || cLang.getEn().isEmpty()) {
                    String en = translate(cLang.getVi(), GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH);
                    cLang.setEn(en);
                    endEn = "*";
                    hasUpdate = true;
                }
                if (cLang.getTh() == null || cLang.getTh().trim().equalsIgnoreCase("NA") || cLang.getTh().isEmpty()) {
                    String th = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH) + "*";
                    cLang.setTh(th);
                    hasUpdate = true;
                }

                if (cLang.getZh() == null || cLang.getZh().equalsIgnoreCase("NA") || cLang.getZh().isEmpty()) {
                    String zh = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH) + "*";
                    cLang.setZh(zh);
                    hasUpdate = true;
                }

                if (cLang.getFr() == null || cLang.getFr().equalsIgnoreCase("NA") || cLang.getFr().isEmpty()) {
                    String fr = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR) + "*";
                    cLang.setFr(fr);
                    hasUpdate = true;
                }

                if (cLang.getEs() == null || cLang.getEs().equalsIgnoreCase("NA") || cLang.getEs().isEmpty()) {
                    String sp = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP) + "*";
                    cLang.setEs(sp);
                    hasUpdate = true;
                }

                if (cLang.getPt() == null || cLang.getPt().equalsIgnoreCase("NA") || cLang.getPt().isEmpty()) {
                    String pt = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT) + "*";
                    cLang.setPt(pt);
                    hasUpdate = true;
                }

                if (cLang.getDe() == null || cLang.getDe().equalsIgnoreCase("NA") || cLang.getDe().isEmpty()) {
                    String de = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE) + "*";
                    cLang.setDe(de);
                    hasUpdate = true;
                }

                if (cLang.getRu() == null || cLang.getRu().equalsIgnoreCase("NA") || cLang.getRu().isEmpty()) {
                    String ru = translate(cLang.getEn(), GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU) + "*";
                    cLang.setRu(ru);
                    hasUpdate = true;
                }
                if (hasUpdate) {
                    cLang.setEn(cLang.getEn() + endEn);

                    DBJPA.update(CfgServer.DB_MAIN + "config_res_language", Arrays.asList("en", cLang.getEn(), "th", cLang.getTh(), "zh", cLang.getZh(), "fr", cLang.getFr(), "es", cLang.getEs(), "pt", cLang.getPt(), "de", cLang.getDe(), "ru", cLang.getRu()), Arrays.asList("k", cLang.getK()));
                }
            }
        }

        System.out.println("\"done_config_res_language\" = " + "done_config_res_language");
    }

    public static void translateResSkillView() throws Exception {
        Gson gson = new Gson();
        try {
            System.out.println("\"start\" = " + "start");

            List<ConfigResLanguage> resLanguages = DBJPA.getQueryList("select * from dson_main.config_res_language where k like 'res_skill_view_%'", ConfigResLanguage.class);
            System.out.println("resLanguages = " + resLanguages.size());
            for (int i = 0; i < resLanguages.size(); i++) {
                ConfigResLanguage cLang = resLanguages.get(i);
                if (cLang.getVi() != null && !cLang.getVi().isEmpty()) {
                    boolean hasUpdate = false;
                    String endEn = "";
                    if (cLang.getEn() == null || cLang.getEn().equalsIgnoreCase("NA") || cLang.getEn().equalsIgnoreCase("en") || cLang.getEn().isEmpty()) {
                        List<String> viValues = GsonUtil.strToListString(cLang.getVi());
                        List<String> values = new ArrayList<>();
                        for (String value : viValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.VIETNAMESE, GoogleTranslator.LANGUAGE.ENGLISH));
                        }
                        cLang.setEn(gson.toJson(values));
                        hasUpdate = true;
                    }
                    if (cLang.getTh() == null || cLang.getTh().trim().equalsIgnoreCase("NA") || cLang.getTh().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.TH));
                        }
                        cLang.setTh(gson.toJson(values));
                        hasUpdate = true;
                    }

                    if (cLang.getZh() == null || cLang.getZh().equalsIgnoreCase("NA") || cLang.getZh().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.ZH));
                        }
                        cLang.setZh(gson.toJson(values));
                        hasUpdate = true;
                    }

                    if (cLang.getFr() == null || cLang.getFr().equalsIgnoreCase("NA") || cLang.getFr().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.FR));
                        }
                        cLang.setFr(gson.toJson(values));
                        hasUpdate = true;
                    }

                    if (cLang.getEs() == null || cLang.getEs().equalsIgnoreCase("NA") || cLang.getEs().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.SP));
                        }
                        cLang.setEs(gson.toJson(values));
                        hasUpdate = true;
                    }

                    if (cLang.getPt() == null || cLang.getPt().equalsIgnoreCase("NA") || cLang.getPt().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.PT));
                        }
                        cLang.setPt(gson.toJson(values));
                        hasUpdate = true;
                    }

                    if (cLang.getDe() == null || cLang.getDe().equalsIgnoreCase("NA") || cLang.getDe().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.DE));
                        }
                        cLang.setDe(gson.toJson(values));
                        hasUpdate = true;
                    }

                    if (cLang.getRu() == null || cLang.getRu().equalsIgnoreCase("NA") || cLang.getRu().isEmpty()) {
                        List<String> enValues = GsonUtil.strToListString(cLang.getEn());
                        List<String> values = new ArrayList<>();
                        for (String value : enValues) {
                            values.add(translate(value, GoogleTranslator.LANGUAGE.ENGLISH, GoogleTranslator.LANGUAGE.RU));
                        }
                        cLang.setRu(gson.toJson(values));
                        hasUpdate = true;
                    }
                    if (hasUpdate) {
                        cLang.setEn(cLang.getEn() + endEn);

                        DBJPA.update(CfgServer.DB_MAIN + "config_res_language", Arrays.asList("en", cLang.getEn(), "th", cLang.getTh(), "zh", cLang.getZh(), "fr", cLang.getFr(), "es", cLang.getEs(), "pt", cLang.getPt(), "de", cLang.getDe(), "ru", cLang.getRu()), Arrays.asList("k", cLang.getK()));
                    }
                }
            }

            System.out.println("\"done_config_res_language\" = " + "done_config_res_language");

            System.out.println("\"done all\" = " + "done all");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
